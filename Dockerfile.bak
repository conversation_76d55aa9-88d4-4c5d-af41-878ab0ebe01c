FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build-service
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY ./nuget.config .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

#copy csproj and restore as distinct layers
COPY ./*.sln .
COPY ./src/CoverGo.Cases.Application/*.csproj ./src/CoverGo.Cases.Application/
COPY ./src/CoverGo.Cases.Application.VersionBridge/*.csproj ./src/CoverGo.Cases.Application.VersionBridge/
COPY ./src/CoverGo.Cases.Domain/*.csproj ./src/CoverGo.Cases.Domain/
COPY ./src/CoverGo.Cases.Infrasctructure/*.csproj ./src/CoverGo.Cases.Infrasctructure/
COPY ./src/CoverGo.Cases.Infrastructure.Decorators/*.csproj ./src/CoverGo.Cases.Infrastructure.Decorators/
COPY Directory.Packages.props .

RUN dotnet restore ./src/CoverGo.Cases.Application/CoverGo.Cases.Application.csproj
RUN dotnet restore ./src/CoverGo.Cases.Application.VersionBridge/CoverGo.Cases.Application.VersionBridge.csproj
RUN dotnet restore ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj
RUN dotnet restore ./src/CoverGo.Cases.Infrasctructure/CoverGo.Cases.Infrastructure.csproj
RUN dotnet restore ./src/CoverGo.Cases.Infrastructure.Decorators/CoverGo.Cases.Infrastructure.Decorators.csproj

COPY ./src/CoverGo.Cases.Domain ./src/CoverGo.Cases.Domain
COPY ./src/CoverGo.Cases.Infrasctructure ./src/CoverGo.Cases.Infrasctructure
COPY ./src/CoverGo.Cases.Infrastructure.Decorators ./src/CoverGo.Cases.Infrastructure.Decorators
COPY ./src/CoverGo.Cases.Application ./src/CoverGo.Cases.Application
COPY ./src/CoverGo.Cases.Application.VersionBridge ./src/CoverGo.Cases.Application.VersionBridge

ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG APP_VERSION=1.0.0
RUN dotnet publish ./src/CoverGo.Cases.Application/CoverGo.Cases.Application.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:7.0-alpine AS service-runtime

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
RUN apk add --no-cache icu-libs

WORKDIR /app
COPY --from=build-service /app/out ./

RUN adduser --disabled-password buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin
EXPOSE 8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
ENV ASPNETCORE_URLS http://*:8080
ENTRYPOINT ["dotnet", "CoverGo.Cases.Application.dll"]

FROM build-service as build-tests-unit

COPY ./src/CoverGo.Cases.Tests.Unit/*.csproj ./src/CoverGo.Cases.Tests.Unit/
COPY ./src/CoverGo.Cases.Tests.Unit ./src/CoverGo.Cases.Tests.Unit/
RUN dotnet restore ./src/CoverGo.Cases.Tests.Unit/CoverGo.Cases.Tests.Unit.csproj
RUN dotnet publish ./src/CoverGo.Cases.Tests.Unit/CoverGo.Cases.Tests.Unit.csproj -c $BUILDCONFIG -o ./out /p:Version=$VERSION --no-restore

ARG ALPINE="-alpine"
FROM mcr.microsoft.com/dotnet/sdk:8.0$ALPINE AS run-tests-unit
WORKDIR /app
COPY --from=build-tests-unit /app/out ./
ENTRYPOINT ["dotnet", "test", "CoverGo.Cases.Tests.Unit.dll"]
CMD ["--logger","junit;LogFileName=TestResults.xml"]

FROM build-service as build-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
COPY ./src/CoverGo.Cases.Client.Rest/*.csproj ./src/CoverGo.Cases.Client.Rest/
COPY ./src/CoverGo.Cases.Client.Rest ./src/CoverGo.Cases.Client.Rest/
RUN dotnet restore ./src/CoverGo.Cases.Client.Rest/CoverGo.Cases.Client.Rest.csproj
RUN dotnet build ./src/CoverGo.Cases.Client.Rest/CoverGo.Cases.Client.Rest.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION" /p:BuildApplicationProjectForRestClientGeneration=true
RUN dotnet build ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client as pack-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
RUN dotnet pack  ./src/CoverGo.Cases.Client.Rest/CoverGo.Cases.Client.Rest.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"
RUN dotnet pack  ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client as build-tests-integration
ARG BUILDCONFIG="Release"
ARG VERSION="1.0.0"
COPY ./src/CoverGo.Cases.Tests.Integration/*.csproj ./src/CoverGo.Cases.Tests.Integration/
COPY ./src/CoverGo.Cases.Tests.Integration ./src/CoverGo.Cases.Tests.Integration/
RUN dotnet restore ./src/CoverGo.Cases.Tests.Integration/CoverGo.Cases.Tests.Integration.csproj
RUN dotnet publish ./src/CoverGo.Cases.Tests.Integration/CoverGo.Cases.Tests.Integration.csproj -c $BUILDCONFIG -o ./out /p:Version=$VERSION /p:BuildApplicationProjectForRestClientGeneration=true --no-restore

ARG ALPINE="-alpine"
FROM mcr.microsoft.com/dotnet/sdk:7.0$ALPINE AS run-tests-integration
WORKDIR /app
COPY --from=build-tests-integration /app/out ./
ENTRYPOINT ["dotnet", "test", "CoverGo.Cases.Tests.Integration.dll"]
CMD ["--logger","junit;LogFileName=TestResults.xml"]

FROM build-client as build-tests-integration-mariadb
ARG BUILDCONFIG="Release"
ARG VERSION="1.0.0"
COPY ./src/CoverGo.Cases.Tests.MariaDb.Integration/*.csproj ./src/CoverGo.Cases.Tests.MariaDb.Integration/
COPY ./src/CoverGo.Cases.Tests.MariaDb.Integration ./src/CoverGo.Cases.Tests.MariaDb.Integration/
RUN dotnet restore ./src/CoverGo.Cases.Tests.MariaDb.Integration/CoverGo.Cases.Tests.MariaDb.Integration.csproj
RUN dotnet publish ./src/CoverGo.Cases.Tests.MariaDb.Integration/CoverGo.Cases.Tests.MariaDb.Integration.csproj -c $BUILDCONFIG -o ./out /p:Version=$VERSION /p:BuildApplicationProjectForRestClientGeneration=true --no-restore

ARG ALPINE="-alpine"
FROM mcr.microsoft.com/dotnet/sdk:7.0$ALPINE AS run-tests-integration-mariadb
WORKDIR /app
COPY --from=build-tests-integration-mariadb /app/out ./
ENTRYPOINT ["dotnet", "test", "CoverGo.Cases.Tests.MariaDb.Integration.dll"]
CMD ["--logger","junit;LogFileName=TestResults.xml"]
