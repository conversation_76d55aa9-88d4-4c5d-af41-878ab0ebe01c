version: '2.4'
services:
  mariadb:
    image: mariadb:10.3.24
    restart: always
    environment:
      - MYSQL_USER=root
      - MYSQL_ROOT_PASSWORD=local_dev
      - MYSQL_DATABASE=cases
    ports:
      - 3306:3306

  cases:
    image: ghcr.io/covergo/cases:master
    restart: always
    build:
      dockerfile: ./Dockerfile.bak
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=dbs-hk
      - DATABASE_CONNECT_STRING=server=mariadb;database=dbs_uat-cases;user=root;password=local_dev;
      - serviceUrls__users=http://users:8080
    ports:
      - 8080:8080
    depends_on:
      - mariadb
      - users

  #alpine-based asp.net core does not have neither apk neither curl\wget,
  #so need to replace it somehow
  cases-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command: ["/bin/sh", "-c", "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"]
    healthcheck:
      test: "wget http://cases:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - cases

  cases-tests-mariadb-integration:
    image: ghcr.io/covergo/cases-test-mariadb-integration:master
    restart: "no"
    build:
      dockerfile: ./Tests.MariaDb.Integration.Dockerfile
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
    environment:
      - CASES_MARIADB_INTEGRATION_TEST-CasesUrl=http://cases:8080
    depends_on:
      cases-health:
        condition: service_healthy

  users:
    image: ghcr.io/covergo/users:master
    restart: always
    build:
      dockerfile: ./Dockerfile.bak
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=dbs-hk
      - DATABASE_CONNECT_STRING=server=mariadb;database=dbs_uat-cases;user=root;password=local_dev;
    ports:
      - "8080"
    depends_on:
      - mariadb

