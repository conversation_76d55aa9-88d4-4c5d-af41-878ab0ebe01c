<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" Sdk="Microsoft.Docker.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectVersion>2.1</ProjectVersion>
    <DockerTargetOS>Linux</DockerTargetOS>
    <DockerComposeProjectName>covergoworkspace</DockerComposeProjectName>
    <DockerComposeBaseFilePath>../../../../docker-compose</DockerComposeBaseFilePath>
    <ProjectGuid>************************************</ProjectGuid>
    <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
    <DockerServiceUrl>{Scheme}://{ServiceIPAddress}{ServicePort}/graphql</DockerServiceUrl>
    <DockerServiceName>quotation</DockerServiceName>
  </PropertyGroup>
  <ItemGroup>
    <None Include="../../../../docker-compose.override.yml">
      <DependentUpon>../../../../docker-compose.yml</DependentUpon>
    </None>
    <None Include="../../../../docker-compose.vs.debug.yml">
      <DependentUpon>../../../../docker-compose.yml</DependentUpon>
    </None>
    <None Include="../.dockerignore" />
  </ItemGroup>
</Project>
