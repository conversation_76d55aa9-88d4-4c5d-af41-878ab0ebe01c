{"profiles": {"Docker Compose": {"commandName": "DockerCompose", "commandVersion": "1.0", "composeLaunchAction": "None", "composeLaunchServiceName": "quotation", "serviceActions": {"auth": "DoNotStart", "channel-management": "DoNotStart", "claim-investigation": "DoNotStart", "claims": "DoNotStart", "coverhealth-portal-hr": "DoNotStart", "coverhealth-portal-member": "DoNotStart", "entity-diary": "DoNotStart", "entity-management": "DoNotStart", "eventstore": "DoNotStart", "external-member-validation": "DoNotStart", "finance": "DoNotStart", "gateway": "DoNotStart", "gateway-v2": "DoNotStart", "l10n": "DoNotStart", "migration-tool": "DoNotStart", "mongo": "DoNotStart", "mongo-express": "DoNotStart", "notifications": "DoNotStart", "otel-collector": "DoNotStart", "placement": "DoNotStart", "policies": "DoNotStart", "policies-dapr": "DoNotStart", "policies-predeployment": "DoNotStart", "pricing": "DoNotStart", "premium": "DoNotStart", "product-builder": "DoNotStart", "products": "DoNotStart", "quotation": "StartDebugging", "quotation-dapr": "StartWithoutDebugging", "rabbitmq": "DoNotStart", "redis": "DoNotStart", "reference": "DoNotStart", "request-manager": "DoNotStart", "scheduler": "DoNotStart", "scripts": "DoNotStart", "transactions": "DoNotStart", "users": "DoNotStart", "cases": "DoNotStart", "file-system": "DoNotStart", "minio": "DoNotStart", "templates": "DoNotStart", "zipkin": "DoNotStart"}}}}