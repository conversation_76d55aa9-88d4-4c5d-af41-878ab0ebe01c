#!/bin/bash

#----------------------------------------------------------------------
# IMPORT PRIVATE KEY
#----------------------------------------------------------------------
if [ -z "$GPG_PRIVATE_KEY" ]
then
      echo "No private key imported"
else
      echo "Start importing private key"

      echo ${GPG_PRIVATE_KEY} > privateKey.txt
      awk '{gsub(/\\n/,"\n")}1' privateKey.txt | gpg --import
fi

#----------------------------------------------------------------------
# DECRYPT DATABASE PASSWORD
#----------------------------------------------------------------------
if [ -z "$ENCRYPTED_DB_PASSWORD" ]
then
      echo "No encrypted password"
else
      echo "Start decrypting password"

      echo ${ENCRYPTED_DB_PASSWORD} > encryptedPwd.txt
      export DB_PASSWORD="$(awk '{gsub(/\\n/,"\n")}1' encryptedPwd.txt | gpg --decrypt)"
fi

#----------------------------------------------------------------------
# BUILD DATABASE_CONNECT_STRING
#----------------------------------------------------------------------
export DATABASE_CONNECT_STRING="server=${DB_SERVER};port=${DB_PORT};database=${DB_NAME};user=${DB_USER};password=${DB_PASSWORD}"

#----------------------------------------------------------------------
# DECRYPT RMHK_KEY
#----------------------------------------------------------------------
if [ -z "$ENCRYPTED_RMHK_KEY" ]
then
      echo "No encrypted RMHK_KEY"
else
      echo "Start decrypting RMHK_KEY"

      echo ${ENCRYPTED_RMHK_KEY} > encryptedPwd.txt
      export RMHK_KEY="$(awk '{gsub(/\\n/,"\n")}1' encryptedPwd.txt | gpg --decrypt)"
fi

#----------------------------------------------------------------------
# UPDATE DBS NAMESERVER AND START MICROSERVICE
#----------------------------------------------------------------------
export ASPNETCORE_ENVIRONMENT="DBS-${ENVIRONMENT}"
echo "ASPNETCORE_ENVIRONMENT ${ASPNETCORE_ENVIRONMENT}"
if [ -z "$RMHK_KEY" ]
then
      echo "No RMHK_KEY, skip DNS update"
      dotnet $APP_DLL
else
      echo "RMHK_KEY existed, start DNS update"

      pwd="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
      ns_script="updateNS.sh"

      dbs_nameserver=$DNSSERVER
      export DNS_HOSTNAME="$(echo "${IMAGE_NAME}-${ENVIRONMENT}")"
      echo "DNS_HOSTNAME ${DNS_HOSTNAME}"

      dbs_zone=$DNSNAMESPACE
      dbs_hostname=$DNS_HOSTNAME

      echo "> Updating nameserver"
      $pwd/$ns_script $dbs_nameserver $dbs_hostname $dbs_zone add

      if [ $? -eq 0 ]; then
      echo "[✓] NS Updated"
      dotnet $APP_DLL
      else
      echo "[x] Failed to update the nameserver"
      fi
fi
