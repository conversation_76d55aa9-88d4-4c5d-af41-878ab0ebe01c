version: '3.8'
services:
  covergo-mongo:
    image: mongo:4.4.18
    restart: always
    environment:
      - MONGO_INITDB_ROOT_USERNAME=root
      - MONGO_INITDB_ROOT_PASSWORD=local_dev
      - MONGO_INITDB_DATABASE=cases
    ports:
      - 27017:27017

  rabbitmq:
    image: rabbitmq:latest
    restart: always
    ports:
      - "15672:15672"
      - "5672:5672"
    healthcheck:
      test: rabbitmq-diagnostics -q ping
      interval: 30s
      timeout: 30s
      retries: 3

  covergo-auth:
    image: ghcr.io/covergo/auth:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DBCONFIG-providerId=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - OTP_LOGIN_CIPHER_KEY=zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=
      - OTP_LOGIN_CIPHER_IV=94jCf53NO1acZ3pO7UE+gA==
      - OTP_LOGIN_HASHER_KEY=key
      - COVERGO_PASSWORD=V9K&KobcZO3
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-auth-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-auth:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-auth

  covergo-gateway:
    image: ghcr.io/covergo/gateway:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - FeatureManagement__Reference=true
    ports:
      - "60060:8080" # To access localhost:60060/graphql
    depends_on:
      covergo-cases:
        condition: service_started
      covergo-auth:
        condition: service_started
      covergo-auth-health:
        condition: service_healthy
      covergo-products:
        condition: service_started
      covergo-scripts:
        condition: service_started
      covergo-users:
        condition: service_started
      covergo-pricing:
        condition: service_started

  covergo-cases:
    image: ghcr.io/covergo/cases:master
    restart: always
    build:
      dockerfile: ./Dockerfile.bak
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
      target: service-runtime
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_CONNECT_STRING=**************************************
      - DATABASE_DRIVER=mongoDb
    ports:
      - 8080:8080
    depends_on:
      - covergo-mongo
      - covergo-auth
      - covergo-users

  #alpine-based asp.net core does not have neither apk neither curl\wget,
  #so need to replace it somehow
  covergo-cases-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-cases:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-cases

  covergo-policies:
    image: ghcr.io/covergo/policies:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ASPNETCORE_URLS=http://*:8080
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - datacenterId=developer-machine
      - POLICY_AGGREGATE_ENABLED_ON=covergo,coverHealth_dev
    ports:
      - "8080"
    depends_on:
      covergo-mongo:
        condition: service_started
      rabbitmq:
        condition: service_healthy

  covergo-policies-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-policies:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-policies

  covergo-users:
    image: ghcr.io/covergo/users:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - ASPNETCORE_URLS=http://*:8080
      - DATABASE_DRIVER=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - datacenterId=developer-machine
    ports:
      - "8080"
    depends_on:
      - covergo-mongo

  covergo-pricing:
    image: ghcr.io/covergo/pricing:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_DRIVER=mongoDb
      - DBCONFIG-providerId=mongoDb
      - DBCONFIG-endpoint=covergo-mongo
      - DBCONFIG-username=root
      - DBCONFIG-password=local_dev
    ports:
      - "8080"
    depends_on:
      - covergo-mongo
  covergo-reference:
    image: ghcr.io/covergo/reference:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DATABASE_DRIVER=mongo
      - DATABASE_CONNECT_STRING=**************************************
      - WAIT_HOSTS=covergo-mongo:27017,covergo-auth:8080
    ports:
      - "80"
    depends_on:
      - covergo-mongo
  covergo-channel-management:
    image: ghcr.io/covergo/channel-management:master
    environment:
      - DATABASE_CONNECT_STRING=**************************************
      - DATABASE_DRIVER=mongo
      - HOST_ENVIRONMENT=Staging
      - WAIT_HOSTS=covergo-mongo:27017,covergo-auth:8080,covergo-reference:80
      - FeatureManagement__SkipSendNotifications__EnabledFor__0__Name=Tenants
      - FeatureManagement__SkipSendNotifications__EnabledFor__0__Parameters__Tenants__0=covergo
    ports:
      - "80"
    depends_on:
      - covergo-mongo
      - covergo-auth
      - covergo-reference

  covergo-cases-tests-integration:
    image: ghcr.io/covergo/cases-test-integration:master
    restart: "no"
    build:
      dockerfile: ./Dockerfile.bak
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
      target: run-tests-integration
    environment:
      - CASES_INTEGRATION_TEST-CasesUrl=http://covergo-cases:8080
      - CASES_INTEGRATION_TEST-AuthUrl=http://covergo-auth:8080
      - CASES_INTEGRATION_TEST-ProductsUrl=http://covergo-products:8080/
      - CASES_INTEGRATION_TEST-GatewayUrl=http://covergo-gateway:8080
      - CASES_INTEGRATION_TEST-UsersUrl=http://covergo-users:8080
      - CASES_INTEGRATION_TEST-PoliciesUrl=http://covergo-policies:8080/
      - CASES_INTEGRATION_TEST-ReferenceUrl=http://covergo-reference/
      - CASES_INTEGRATION_TEST-ChannelManagementUrl=http://covergo-channel-management/
      - DATABASE_CONNECT_STRING=**************************************
    depends_on:
      covergo-cases-health:
        condition: service_healthy
      covergo-auth-health:
        condition: service_healthy
      covergo-products-health:
        condition: service_healthy
      covergo-scripts:
        condition: service_started
      covergo-gateway:
        condition: service_started
      covergo-policies-health:
        condition: service_healthy
      covergo-users:
        condition: service_started
      covergo-reference:
        condition: service_started
      covergo-channel-management:
        condition: service_started

  covergo-products:
    image: ghcr.io/covergo/products:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo
      - covergo-scripts

  covergo-products-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      ["/bin/sh", "-c", 'trap "echo exiting; exit 0" TERM; sleep 1d & wait']
    healthcheck:
      test: "wget http://covergo-products:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 30
    depends_on:
      - covergo-products

  covergo-scripts:
    image: ghcr.io/covergo/scripts-node:master
    environment:
      - datacenterId=covergo-dockerComposeOnJenkins-hk
      - DATABASE_CONNECT_STRING=**************************************
    ports:
      - "8080"
    depends_on:
      - covergo-mongo
