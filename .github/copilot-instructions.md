# BDD & TDD

- Always cover functionality with tests
- Always write tests first
- Always add `Trait("Ticket", "{{CurrentTicketFromTheBranch}}")` attribute to the test methods
- Always use FluentAssertions over Assert
- Always use `Arrange (Given)`, `Act (When)`, `Assert (Then)` comments in tests.

# DDD

- Always use Tactical DDD. Entities, Aggregates, ValueObjects, Domain Services, Application Services

# Code Style

- Format the code
- Remove unused usings
