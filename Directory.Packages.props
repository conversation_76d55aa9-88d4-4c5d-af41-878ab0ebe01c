<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>false</CentralPackageTransitivePinningEnabled>
    <CoverGoInternalsVersion>3.74.8</CoverGoInternalsVersion>
    <HotChocolateVersion>12.18.0</HotChocolateVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="Azure.Messaging.ServiceBus" Version="7.17.4" />
    <PackageVersion Include="CloudNative.CloudEvents" Version="2.7.1" />
    <PackageVersion Include="CoverGo.BuildingBlocks.MessageBus.Dapr" Version="5.1.1" />
    <PackageVersion Include="CoverGo.FeatureManagement" Version="3.74.8" />
    <PackageVersion Include="CoverGo.Multitenancy" Version="4.0.1-rc.2" />
    <PackageVersion Include="CoverGo.Multitenancy.AspNetCore" Version="4.0.1-rc.2" />
    <PackageVersion Include="CoverGo.Multitenancy.Autofac" Version="4.0.1-rc.2" />
    <PackageVersion Include="CoverGo.Quotation.Domain" Version="1.46.0-ASIA-3023.29" />
    <PackageVersion Include="coverlet.collector" Version="1.3.0" />
    <PackageVersion Include="FluentAssertions" Version="5.10.3" />
    <PackageVersion Include="GraphQL.Client" Version="5.1.1" />
    <PackageVersion Include="GraphQL.Client.Serializer.Newtonsoft" Version="5.1.1" />
    <PackageVersion Include="GraphQL.Client.Serializer.SystemTextJson" Version="5.1.1" />
    <PackageVersion Include="IdentityModel" Version="6.0.0" />
    <PackageVersion Include="JunitXml.TestLogger" Version="2.1.81" />
    <PackageVersion Include="Microsoft.AspNetCore.Hosting" Version="2.2.7" />
    <PackageVersion Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.7" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="6.0.1" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Abstractions" Version="6.0.1" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.1" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="5.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="5.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Configuration" Version="7.0.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="16.7.1" />
    <PackageVersion Include="Moq" Version="4.16.1" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageVersion Include="NSwag.MSBuild" Version="14.0.7" />
    <PackageVersion Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.0" />
    <PackageVersion Include="ProductBuilder.Client" Version="1.0.5" />
    <PackageVersion Include="prometheus-net" Version="4.0.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="3.4.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="5.5.1" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="6.26.0" />
    <PackageVersion Include="xunit" Version="2.4.1" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.4.3" />
    <!-- HotChocolate -->
    <PackageVersion Include="HotChocolate" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.AspNetCore" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.AspNetCore.Authorization" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Data.MongoDb" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Stitching" Version="$(HotChocolateVersion)" />
    <PackageVersion Include="HotChocolate.Types" Version="$(HotChocolateVersion)" />
    <!-- CoverGoInternals -->
    <PackageVersion Include="CoverGo.Applications.AttachedRules" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.AttachedRules.Services" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Domain" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.HealthCheck" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Http.GraphQl.Services" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Http.Rest" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Infrastructure" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Applications.Startup" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.DomainUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.JsonUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.MongoUtils" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Proxies.Auth" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Sentry" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.SettableValues" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Threading.Tasks" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="Covergo.BuildingBlocks.Auth" Version="1.0.5" />
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="CoverGo.Gateway.Client" Version="2.241.1" />
    <PackageVersion Include="CoverGo.Policies.Client" Version="2.295.1-rc.9" />
    <PackageVersion Include="CoverGo.Products.Client" Version="2.19.0" />
    <PackageVersion Include="CoverGo.Templates.Client.Rest" Version="2.1.141" />
    <PackageVersion Include="CoverGo.Users.Client" Version="2.45.2-rc.9" />
    <PackageVersion Include="CoverGo.Reference.Client" Version="1.1.0-rc.49" />
    <PackageVersion Include="CoverGo.ChannelManagement.Client" Version="1.38.0-CH-20441-update-agentById-client-query.1" />
  </ItemGroup>
</Project>