using System;
using System.Collections.Generic;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Infrastructure.Adapters.Mongo;
using FluentAssertions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Infrastructure
{
    public class MongoExternalSynchronizationRepositoryTests
    {
        private readonly TestableMongoExternalSynchronizationRepository _repository;

        public MongoExternalSynchronizationRepositoryTests()
        {
            _repository = new TestableMongoExternalSynchronizationRepository();
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_all_properties_WHEN_creating_entity_THEN_all_properties_should_be_mapped_correctly()
        {
            // Arrange
            var lastSyncTime = DateTime.UtcNow.AddMinutes(-30);
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "COMPLETED",
                Payload = "{\"caseData\":{\"name\":\"Test Case\"}}",
                Response = "{\"success\":true,\"externalId\":\"ext-123\"}",
                LastSuccessfullySyncAt = lastSyncTime,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Id.Should().Be("sync-123");
            entity.CaseId.Should().Be("case-456");
            entity.OfferId.Should().Be("offer-789");
            entity.TargetLogicalId.Should().Be("external-system-1");
            entity.Status.Should().Be("COMPLETED");
            entity.Payload.Should().BeEquivalentTo(JToken.Parse(upsert.Payload));
            entity.Response.Should().BeEquivalentTo(JToken.Parse(upsert.Response));
            entity.LastSuccessfullySyncAt.Should().Be(lastSyncTime);
            entity.CreatedById.Should().Be("user-1");
            entity.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_null_id_WHEN_creating_entity_THEN_guid_should_be_generated()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = null,
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "PENDING",
                Payload = "{}",
                Response = "{}",
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Id.Should().NotBeNullOrEmpty();
            Guid.TryParse(entity.Id, out _).Should().BeTrue();
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_null_json_fields_WHEN_creating_entity_THEN_empty_json_should_be_parsed()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "PENDING",
                Payload = null,
                Response = null,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Payload.Should().BeEquivalentTo(JToken.Parse("{}"));
            entity.Response.Should().BeEquivalentTo(JToken.Parse("{}"));
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_null_last_sync_time_WHEN_creating_entity_THEN_null_should_be_preserved()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "PENDING",
                Payload = "{}",
                Response = "{}",
                LastSuccessfullySyncAt = null,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.LastSuccessfullySyncAt.Should().BeNull();
        }

        [Fact]
        public void GIVEN_synchronization_upsert_for_update_WHEN_updating_entity_THEN_update_definition_should_be_created()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                CaseId = "updated-case",
                OfferId = "updated-offer",
                TargetLogicalId = "updated-system",
                Status = "FAILED",
                Payload = "{\"updatedData\":true}",
                Response = "{\"error\":\"Connection timeout\"}",
                LastSuccessfullySyncAt = DateTime.UtcNow,
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // Note: Testing MongoDB UpdateDefinition internals is complex,
            // but we can verify the method doesn't throw and returns a valid definition
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_null_properties_WHEN_updating_entity_THEN_only_modified_by_should_be_updated()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                CaseId = null,
                OfferId = null,
                TargetLogicalId = null,
                Status = null,
                Payload = null,
                Response = null,
                LastSuccessfullySyncAt = null,
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // The update should only contain LastModifiedAt and LastModifiedById
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_has_value_timestamp_WHEN_updating_entity_THEN_timestamp_should_be_updated()
        {
            // Arrange
            var syncTime = DateTime.UtcNow.AddMinutes(-15);
            var upsert = new ExternalSynchronizationUpsert
            {
                Status = "COMPLETED",
                LastSuccessfullySyncAt = syncTime,
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // The LastSuccessfullySyncAt should be included in the update since it has a value
        }

        [Fact]
        public void GIVEN_repository_WHEN_getting_collection_name_THEN_tenant_prefix_should_be_applied()
        {
            // Arrange
            var tenantId = "tenant-456";

            // Act
            var collectionName = _repository.TestGetCollectionName(tenantId);

            // Assert
            collectionName.Should().Be("tenant-456-external-synchronizations");
        }

        [Fact]
        public void GIVEN_repository_WHEN_getting_database_name_THEN_cases_database_should_be_used()
        {
            // Act
            var dbName = _repository.TestGetDbName();

            // Assert
            dbName.Should().Be("cases");
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_complex_json_WHEN_creating_entity_THEN_complex_json_should_be_parsed_correctly()
        {
            // Arrange
            var complexPayload = @"{
                ""case"": {
                    ""id"": ""case-123"",
                    ""name"": ""Complex Test Case"",
                    ""details"": {
                        ""products"": [""product1"", ""product2""],
                        ""amounts"": [100.50, 200.75],
                        ""metadata"": {
                            ""source"": ""api"",
                            ""version"": ""1.2.3""
                        }
                    }
                }
            }";

            var complexResponse = @"{
                ""status"": ""success"",
                ""data"": {
                    ""externalId"": ""ext-abc-123"",
                    ""processedAt"": ""2023-12-01T10:30:00Z"",
                    ""warnings"": [""Field 'optional_field' was ignored""]
                }
            }";

            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-complex",
                CaseId = "case-123",
                OfferId = "offer-123",
                TargetLogicalId = "external-system-1",
                Status = "COMPLETED",
                Payload = complexPayload,
                Response = complexResponse,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Payload.Should().BeEquivalentTo(JToken.Parse(complexPayload));
            entity.Response.Should().BeEquivalentTo(JToken.Parse(complexResponse));
            entity.Payload["case"]["details"]["products"].Should().HaveCount(2);
            entity.Response["data"]["externalId"].Value<string>().Should().Be("ext-abc-123");
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_errors_json_WHEN_creating_entity_THEN_errors_should_be_parsed_correctly()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "DataCollection",
                    Code = "TIMEOUT_ERROR",
                    Message = "Database connection timeout",
                    Details = "Connection failed after 30 seconds",
                    OccurredAt = DateTime.UtcNow
                },
                new ExternalSynchronizationError
                {
                    Step = "Transform",
                    Code = "VALIDATION_ERROR",
                    Message = "Invalid data format",
                    Details = "Required field 'OfferId' is missing",
                    OccurredAt = DateTime.UtcNow.AddMinutes(1)
                }
            };

            var errorsJson = JsonConvert.SerializeObject(errors);

            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "Failed",
                Payload = "{}",
                Response = "{}",
                Errors = errorsJson,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Errors.Should().HaveCount(2);
            entity.Errors[0].Step.Should().Be("DataCollection");
            entity.Errors[0].Code.Should().Be("TIMEOUT_ERROR");
            entity.Errors[0].Message.Should().Be("Database connection timeout");
            entity.Errors[0].Details.Should().Be("Connection failed after 30 seconds");

            entity.Errors[1].Step.Should().Be("Transform");
            entity.Errors[1].Code.Should().Be("VALIDATION_ERROR");
            entity.Errors[1].Message.Should().Be("Invalid data format");
            entity.Errors[1].Details.Should().Be("Required field 'OfferId' is missing");
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_null_errors_WHEN_creating_entity_THEN_empty_errors_list_should_be_created()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "Initialized",
                Payload = "{}",
                Response = "{}",
                Errors = null,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Errors.Should().NotBeNull();
            entity.Errors.Should().BeEmpty();
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_empty_errors_json_WHEN_creating_entity_THEN_empty_errors_list_should_be_created()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "Initialized",
                Payload = "{}",
                Response = "{}",
                Errors = "[]",
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Errors.Should().NotBeNull();
            entity.Errors.Should().BeEmpty();
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_invalid_errors_json_WHEN_creating_entity_THEN_empty_errors_list_should_be_created()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "Failed",
                Payload = "{}",
                Response = "{}",
                Errors = "{ invalid json }",
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Errors.Should().NotBeNull();
            entity.Errors.Should().BeEmpty();
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_single_error_WHEN_creating_entity_THEN_single_error_should_be_parsed()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "Send",
                    Code = "NETWORK_ERROR",
                    Message = "External API is unavailable",
                    Details = "HTTP 503 Service Unavailable",
                    OccurredAt = DateTime.UtcNow
                }
            };

            var errorsJson = JsonConvert.SerializeObject(errors);

            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-456",
                OfferId = "offer-789",
                TargetLogicalId = "external-system-1",
                Status = "Failed",
                Payload = "{}",
                Response = "{}",
                Errors = errorsJson,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Errors.Should().HaveCount(1);
            entity.Errors[0].Step.Should().Be("Send");
            entity.Errors[0].Code.Should().Be("NETWORK_ERROR");
            entity.Errors[0].Message.Should().Be("External API is unavailable");
            entity.Errors[0].Details.Should().Be("HTTP 503 Service Unavailable");
            entity.Errors[0].OccurredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_errors_for_update_WHEN_updating_entity_THEN_update_definition_should_include_errors()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "DataCollection",
                    Code = "TIMEOUT_ERROR",
                    Message = "Database connection failed",
                    OccurredAt = DateTime.UtcNow
                }
            };

            var errorsJson = JsonConvert.SerializeObject(errors);

            var upsert = new ExternalSynchronizationUpsert
            {
                Status = "Failed",
                Errors = errorsJson,
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // The update should include the Errors field since it's not null
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_null_errors_for_update_WHEN_updating_entity_THEN_errors_should_not_be_updated()
        {
            // Arrange
            var upsert = new ExternalSynchronizationUpsert
            {
                Status = "DataCollected",
                Errors = null,
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // The update should not include the Errors field since it's null
        }

        [Fact]
        public void GIVEN_synchronization_upsert_with_complex_errors_WHEN_creating_entity_THEN_complex_errors_should_be_preserved()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "DataCollection",
                    Code = "SQL_ERROR",
                    Message = "Database query failed",
                    Details = "SELECT * FROM cases WHERE id = 'case-123' - Timeout expired. The timeout period elapsed prior to completion of the operation.",
                    OccurredAt = DateTime.UtcNow.AddMinutes(-5)
                },
                new ExternalSynchronizationError
                {
                    Step = "Transform",
                    Code = "MAPPING_ERROR",
                    Message = "Field mapping failed",
                    Details = "Unable to map field 'customField' from case data to external format. Field type mismatch: expected string, got number.",
                    OccurredAt = DateTime.UtcNow.AddMinutes(-3)
                },
                new ExternalSynchronizationError
                {
                    Step = "Send",
                    Code = "HTTP_ERROR",
                    Message = "HTTP request failed",
                    Details = "POST https://api.external-system.com/sync returned 422 Unprocessable Entity: {\"error\": \"Invalid offer format\", \"field\": \"offer.amount\"}",
                    OccurredAt = DateTime.UtcNow.AddMinutes(-1)
                }
            };

            var errorsJson = JsonConvert.SerializeObject(errors);

            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-complex-errors",
                CaseId = "case-123",
                OfferId = "offer-456",
                TargetLogicalId = "external-system-1",
                Status = "Failed",
                Payload = "{\"case\":{\"id\":\"case-123\"}}",
                Response = "{\"error\":\"Validation failed\"}",
                Errors = errorsJson,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Errors.Should().HaveCount(3);

            // Verify first error
            entity.Errors[0].Step.Should().Be("DataCollection");
            entity.Errors[0].Code.Should().Be("SQL_ERROR");
            entity.Errors[0].Message.Should().Be("Database query failed");
            entity.Errors[0].Details.Should().Contain("Timeout expired");

            // Verify second error
            entity.Errors[1].Step.Should().Be("Transform");
            entity.Errors[1].Code.Should().Be("MAPPING_ERROR");
            entity.Errors[1].Message.Should().Be("Field mapping failed");
            entity.Errors[1].Details.Should().Contain("Field type mismatch");

            // Verify third error
            entity.Errors[2].Step.Should().Be("Send");
            entity.Errors[2].Code.Should().Be("HTTP_ERROR");
            entity.Errors[2].Message.Should().Be("HTTP request failed");
            entity.Errors[2].Details.Should().Contain("422 Unprocessable Entity");

            // Verify timestamps are preserved
            entity.Errors[0].OccurredAt.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(-5), TimeSpan.FromSeconds(10));
            entity.Errors[1].OccurredAt.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(-3), TimeSpan.FromSeconds(10));
            entity.Errors[2].OccurredAt.Should().BeCloseTo(DateTime.UtcNow.AddMinutes(-1), TimeSpan.FromSeconds(10));
        }
    }

    // Testable wrapper to expose protected methods for unit testing
    public class TestableMongoExternalSynchronizationRepository : MongoExternalSynchronizationRepository
    {
        public ExternalSynchronization TestEntityCreate(ExternalSynchronizationUpsert create)
        {
            return EntityCreate(create);
        }

        public MongoDB.Driver.UpdateDefinition<ExternalSynchronization> TestEntityUpdate(ExternalSynchronizationUpsert update)
        {
            return EntityUpdate(update);
        }

        public string TestGetCollectionName(string tenantId)
        {
            return CollectionNameGet(tenantId);
        }

        public string TestGetDbName()
        {
            return DbName;
        }
    }
}