using System;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Infrastructure.Adapters.Mongo;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Infrastructure
{
    public class MongoExternalSynchronizationTargetRepositoryTests
    {
        private readonly TestableMongoExternalSynchronizationTargetRepository _repository;

        public MongoExternalSynchronizationTargetRepositoryTests()
        {
            _repository = new TestableMongoExternalSynchronizationTargetRepository();
        }

        [Fact]
        public void GIVEN_target_upsert_with_all_properties_WHEN_creating_entity_THEN_all_properties_should_be_mapped_correctly()
        {
            // Arrange
            var urls = JToken.Parse("{\"authorization\":\"https://api.external-system.com/auth\",\"validation\":\"https://api.external-system.com/validate\",\"confirmation\":\"https://api.external-system.com/confirm\"}");
            var upsert = new ExternalSynchronizationTargetUpsert
            {
                Id = "target-123",
                LogicalId = "external-system-1",
                Credentials = "{\"apiKey\":\"test123\",\"secret\":\"secret456\"}",
                Urls = urls,
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Id.Should().Be("target-123");
            entity.LogicalId.Should().Be("external-system-1");
            entity.Credentials.Should().BeEquivalentTo(JToken.Parse(upsert.Credentials));
            entity.Urls.Should().BeEquivalentTo(urls);
            entity.CreatedById.Should().Be("user-1");
            entity.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_target_upsert_with_null_id_WHEN_creating_entity_THEN_guid_should_be_generated()
        {
            // Arrange
            var upsert = new ExternalSynchronizationTargetUpsert
            {
                Id = null,
                LogicalId = "external-system-1",
                Credentials = "{}",
                Urls = JToken.Parse("{\"test\":\"https://api.external-system.com/webhook\"}"),
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Id.Should().NotBeNullOrEmpty();
            Guid.TryParse(entity.Id, out _).Should().BeTrue();
        }

        [Fact]
        public void GIVEN_target_upsert_with_empty_credentials_WHEN_creating_entity_THEN_empty_json_should_be_parsed()
        {
            // Arrange
            var upsert = new ExternalSynchronizationTargetUpsert
            {
                Id = "target-123",
                LogicalId = "external-system-1",
                Credentials = null,
                Urls = JToken.Parse("{\"default\":\"https://api.external-system.com/webhook\"}"),
                ById = "user-1"
            };

            // Act
            var entity = _repository.TestEntityCreate(upsert);

            // Assert
            entity.Credentials.Should().BeEquivalentTo(JToken.Parse("{}"));
        }

        [Fact]
        public void GIVEN_target_upsert_for_update_WHEN_updating_entity_THEN_update_definition_should_be_created()
        {
            // Arrange
            var upsert = new ExternalSynchronizationTargetUpsert
            {
                LogicalId = "updated-system",
                Credentials = "{\"apiKey\":\"updated123\"}",
                Urls = JToken.Parse("{\"default\":\"https://updated-api.external-system.com/webhook\"}"),
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // Note: Testing MongoDB UpdateDefinition internals is complex,
            // but we can verify the method doesn't throw and returns a valid definition
        }

        [Fact]
        public void GIVEN_target_upsert_with_null_properties_WHEN_updating_entity_THEN_only_modified_by_should_be_updated()
        {
            // Arrange
            var upsert = new ExternalSynchronizationTargetUpsert
            {
                LogicalId = null,
                Credentials = null,
                Urls = null,
                ById = "user-2"
            };

            // Act
            var updateDefinition = _repository.TestEntityUpdate(upsert);

            // Assert
            updateDefinition.Should().NotBeNull();
            // The update should only contain LastModifiedAt and LastModifiedById
        }

        [Fact]
        public void GIVEN_repository_WHEN_getting_collection_name_THEN_tenant_prefix_should_be_applied()
        {
            // Arrange
            var tenantId = "tenant-123";

            // Act
            var collectionName = _repository.TestGetCollectionName(tenantId);

            // Assert
            collectionName.Should().Be("tenant-123-external-synchronization-targets");
        }

        [Fact]
        public void GIVEN_repository_WHEN_getting_database_name_THEN_cases_database_should_be_used()
        {
            // Act
            var dbName = _repository.TestGetDbName();

            // Assert
            dbName.Should().Be("cases");
        }
    }

    // Testable wrapper to expose protected methods for unit testing
    public class TestableMongoExternalSynchronizationTargetRepository : MongoExternalSynchronizationTargetRepository
    {
        public ExternalSynchronizationTarget TestEntityCreate(ExternalSynchronizationTargetUpsert create)
        {
            return EntityCreate(create);
        }

        public MongoDB.Driver.UpdateDefinition<ExternalSynchronizationTarget> TestEntityUpdate(ExternalSynchronizationTargetUpsert update)
        {
            return EntityUpdate(update);
        }

        public string TestGetCollectionName(string tenantId)
        {
            return CollectionNameGet(tenantId);
        }

        public string TestGetDbName()
        {
            return DbName;
        }
    }
}