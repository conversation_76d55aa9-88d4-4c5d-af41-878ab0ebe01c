using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using CoverGo.Cases.Application.Filters;
using CoverGo.Cases.Domain;
using CoverGo.Users.Client;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Moq;
using Xunit;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Controllers;
using System.IO;
using System.Text;
using CoverGo.Cases.Application.Controllers;

namespace CoverGo.Cases.Tests.Unit.Application.Filters
{
    public class ChannelIdFilterAttributeTests
    {
        [Fact]
        public async Task OnAuthorization_WithValidCaseIdAndMatchingChannelIds_ShouldNotSetResult()
        {
            // Arrange
            var factoryMock = new Mock<ICaseServiceFactory>();
            var serviceMock = new Mock<ICaseService>();
            serviceMock.Setup(s => s.GetAsync(It.IsAny<string>(), It.IsAny<DomainUtils.QueryArguments<CaseWhere>>(), default))
                .Returns(Task.FromResult((new List<Case>()).AsEnumerable())); // Provide a mock case object

            factoryMock.Setup(x => x.Build())
                .Returns(serviceMock.Object);

            var filter = new ChannelIdFilterAttribute(factoryMock.Object);

            // Act
            filter.OnAuthorization(BuildContext(new List<Claim>
            {
                new Claim("accessChannels", "channel1"),
                new Claim("accessChannels", "channel2")
            }));
        }

        [Fact]
        public async Task OnAuthorization_WithValidCaseIdAndNonMatchingChannelIds_ShouldSetResultToForbidden()
        {
            // Arrange
            var factoryMock = new Mock<ICaseServiceFactory>();
            var serviceMock = new Mock<ICaseService>();
            serviceMock.Setup(s => s.GetAsync(It.IsAny<string>(), It.IsAny<DomainUtils.QueryArguments<CaseWhere>>(), default))
                .Returns(Task.FromResult((new List<Case>()).AsEnumerable())); // Provide a mock case object

            factoryMock.Setup(x => x.Build())
                .Returns(serviceMock.Object);

            var filter = new ChannelIdFilterAttribute(factoryMock.Object);

            // Act
            filter.OnAuthorization(BuildContext(new List<Claim>
            {
                new Claim("accessChannels", "channel1"),
                new Claim("accessChannels", "channel2")
            }));
        }

        [Fact]
        public async Task OnAuthorization_WithInvalidCaseId_ShouldNotSetResult()
        {
            // Arrange
            var factoryMock = new Mock<ICaseServiceFactory>();
            var serviceMock = new Mock<ICaseService>();
            serviceMock.Setup(s => s.GetAsync(It.IsAny<string>(), It.IsAny<DomainUtils.QueryArguments<CaseWhere>>(), default))
                .Returns(Task.FromResult((new List<Case>()).AsEnumerable())); // Provide a mock case object

            factoryMock.Setup(x => x.Build())
                .Returns(serviceMock.Object);

            var filter = new ChannelIdFilterAttribute(factoryMock.Object);

            // Act
            filter.OnAuthorization(BuildContext(new List<Claim>()));
        }

        private AuthorizationFilterContext BuildContext(List<Claim> claims)
        {
            var httpContext = new DefaultHttpContext();

            // add User with claims to HttpContext
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            httpContext.User = new ClaimsPrincipal(identity);

            var stream = new MemoryStream(Encoding.UTF8.GetBytes("{}"));
            httpContext.Request.Body = stream;
            httpContext.Request.ContentLength = stream.Length;
            httpContext.Request.ContentType = "application/json";

            var actionDescriptor = new ControllerActionDescriptor
            {
                ActionName = nameof(CaseController.UpdateAsync)
            };
            var actionContext = new ActionContext(httpContext, new Microsoft.AspNetCore.Routing.RouteData(new Microsoft.AspNetCore.Routing.RouteValueDictionary
            {
                { "tenantId", "covergo" },
                { "caseId", "case2" }
            }), actionDescriptor);
            return new AuthorizationFilterContext(actionContext, new List<IFilterMetadata>());
        }
    }
}