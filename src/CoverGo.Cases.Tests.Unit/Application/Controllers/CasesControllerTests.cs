﻿using CoverGo.Cases.Application.Controllers;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.DomainUtils;
using CoverGo.Users.Client;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using AccessPolicy = CoverGo.BuildingBlocks.Auth.Common.AccessPolicy;

namespace CoverGo.Cases.Tests.Unit.Application.Controllers;

public class CasesControllerTests
{
    private const string TenantId = "covergo";

    private readonly Mock<ICaseService> _mockCaseService;
    private readonly Mock<IUsersService> _mockUsersService;

    private readonly CaseController _controller;

    public CasesControllerTests()
    {
        _mockCaseService = new Mock<ICaseService>();
        _mockUsersService = new Mock<IUsersService>();

        var mockCaseServiceFactory = new Mock<ICaseServiceFactory>();
        mockCaseServiceFactory.Setup(x => x.Build()).Returns(_mockCaseService.Object);

        _controller = new CaseController(mockCaseServiceFactory.Object, _mockUsersService.Object);
    }

    [Theory]
    [InlineData(Users.Client.AccessPolicy.Restricted, AccessPolicy.Restricted)]
    [InlineData(Users.Client.AccessPolicy.Standard, AccessPolicy.Standard)]
    [InlineData(null, null)]
    public async Task GIVEN_existing_company_WHEN_create_case_THEN_should_inherit_accessPolicy_from_company(Users.Client.AccessPolicy? companyAccessPolicy, AccessPolicy? expectedCaseAccessPolicy)
    {
        _mockUsersService.Setup(x => x.GetCompanyByIdAsync(TenantId, It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Company { AccessPolicy = companyAccessPolicy });

        ActionResult<Result<string>> actionResult = await _controller.CreateAsync(TenantId, new CreateCaseCommand());

        _mockCaseService.Verify(svc =>
            svc.CreateAsync(
                TenantId,
                It.Is<CreateCaseCommand>(c => c.AccessPolicy == expectedCaseAccessPolicy),
                It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GIVEN_not_existing_company_WHEN_create_case_THEN_accessPolicy_should_be_null()
    {
        _mockUsersService.Setup(x => x.GetCompanyByIdAsync(TenantId, It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Company)null);

        ActionResult<Result<string>> actionResult = await _controller.CreateAsync(TenantId, new CreateCaseCommand());

        _mockCaseService.Verify(svc =>
            svc.CreateAsync(
                TenantId,
                It.Is<CreateCaseCommand>(c => c.AccessPolicy == null),
                It.IsAny<CancellationToken>()), Times.Once);
    }
}