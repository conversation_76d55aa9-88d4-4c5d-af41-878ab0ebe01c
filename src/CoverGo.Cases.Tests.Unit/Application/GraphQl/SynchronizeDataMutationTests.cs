using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Cases.Application.GraphQl.ExternalSync;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Application.GraphQl
{
    public class SynchronizeDataMutationTests
    {
        private readonly Mock<ISynchronizeDataService> _mockBupaSyncService;
        private readonly Mock<IExternalSynchronizationService> _mockExternalSyncService;
        private readonly Mock<ILogger<SynchronizeDataMutation>> _mockLogger;
        private readonly SynchronizeDataMutation _mutation;

        public SynchronizeDataMutationTests()
        {
            _mockBupaSyncService = new Mock<ISynchronizeDataService>();
            _mockExternalSyncService = new Mock<IExternalSynchronizationService>();
            _mockLogger = new Mock<ILogger<SynchronizeDataMutation>>();

            _mockBupaSyncService.Setup(x => x.LogicalId).Returns("bupa");

            // Setup mock to return empty list by default (no existing synchronizations)
            _mockExternalSyncService
                .Setup(x => x.QueryAsync(It.IsAny<string>(), It.IsAny<QueryArguments<Filter<ExternalSynchronizationFilter>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ExternalSynchronization>());

            var syncServices = new List<ISynchronizeDataService> { _mockBupaSyncService.Object };

            _mutation = new SynchronizeDataMutation(
                syncServices,
                _mockExternalSyncService.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task GIVEN_null_caseId_WHEN_synchronizing_data_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = null,
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("CaseId is required");
        }

        [Fact]
        public async Task GIVEN_empty_offerId_WHEN_synchronizing_data_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("OfferId is required");
        }

        [Fact]
        public async Task GIVEN_null_targetLogicalId_WHEN_synchronizing_data_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = null
            };

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("ExternalSynchronizationTargetLogicalId is required");
        }

        [Fact]
        public async Task GIVEN_case_not_found_WHEN_synchronizing_data_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            _mockBupaSyncService
                .Setup(x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()))
                .ReturnsAsync(Result<ExternalSynchronization>.Failure("Case with ID 'case-1' not found"));

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain(e => e.Contains("Case with ID 'case-1' not found"));
        }

        [Fact]
        public async Task GIVEN_offer_not_found_WHEN_synchronizing_data_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            _mockBupaSyncService
                .Setup(x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()))
                .ReturnsAsync(Result<ExternalSynchronization>.Failure("Offer with ID 'offer-1' not found in case"));

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain(e => e.Contains("Offer with ID 'offer-1' not found in case"));
        }

        [Fact]
        public async Task GIVEN_valid_case_and_offer_WHEN_synchronization_succeeds_THEN_should_return_success()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            var expectedSync = new ExternalSynchronization
            {
                Id = "sync-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                Status = "Sent",
                LastSuccessfullySyncAt = DateTime.UtcNow
            };

            _mockBupaSyncService
                .Setup(x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()))
                .ReturnsAsync(Result<ExternalSynchronization>.Success(expectedSync));

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();
            result.Value.ExternalSynchronization.Should().BeEquivalentTo(expectedSync);
            result.Value.Message.Should().Be("Data synchronized successfully");
        }

        [Fact]
        public async Task GIVEN_synchronization_service_failure_WHEN_synchronizing_data_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            _mockBupaSyncService
                .Setup(x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()))
                .ReturnsAsync(Result<ExternalSynchronization>.Failure("External service unavailable"));

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain(e => e.Contains("Synchronization failed: External service unavailable"));
        }

        [Fact]
        public async Task GIVEN_mutation_with_dependencies_WHEN_created_THEN_should_not_be_null()
        {
            // Arrange & Act & Assert
            _mutation.Should().NotBeNull();
            _mutation.Should().BeOfType<SynchronizeDataMutation>();
        }

        [Fact]
        public void GIVEN_synchronize_data_input_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange & Act
            var input = new SynchronizeDataInput
            {
                CaseId = "case-123",
                OfferId = "offer-456",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            // Assert
            input.CaseId.Should().Be("case-123");
            input.OfferId.Should().Be("offer-456");
            input.ExternalSynchronizationTargetLogicalId.Should().Be("bupa");
        }

        [Fact]
        public void GIVEN_synchronize_data_payload_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange
            var sync = new ExternalSynchronization
            {
                Id = "sync-1",
                Status = "Sent"
            };

            // Act
            var payload = new SynchronizeDataPayload
            {
                ExternalSynchronization = sync,
                Message = "Success message"
            };

            // Assert
            payload.ExternalSynchronization.Should().BeEquivalentTo(sync);
            payload.Message.Should().Be("Success message");
        }

        [Fact]
        public async Task GIVEN_case_with_minimal_data_WHEN_synchronizing_THEN_should_handle_missing_fields_gracefully()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            var expectedSync = new ExternalSynchronization
            {
                Id = "sync-1",
                Status = "Sent"
            };

            _mockBupaSyncService
                .Setup(x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()))
                .ReturnsAsync(Result<ExternalSynchronization>.Success(expectedSync));

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();

            // Verify that the synchronization service was called
            _mockBupaSyncService.Verify(
                x => x.ProcessAsync(It.Is<SynchronizeDataRequest>(req =>
                    req.TenantId == "tenant-1" &&
                    req.CaseId == "case-1" &&
                    req.OfferId == "offer-1" &&
                    req.TargetLogicalId == "bupa")),
                Times.Once);
        }

        [Fact]
        public async Task GIVEN_already_synchronized_data_WHEN_attempting_to_synchronize_again_THEN_should_return_failure()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            var existingSync = new ExternalSynchronization
            {
                Id = "existing-sync-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                Status = "Sent",
                LastSuccessfullySyncAt = DateTime.UtcNow.AddHours(-1)
            };

            // Setup mock to return existing synchronization
            _mockExternalSyncService
                .Setup(x => x.QueryAsync("tenant-1", It.Is<QueryArguments<Filter<ExternalSynchronizationFilter>>>(f =>
                    f.Where.Where.CaseId == "case-1" &&
                    f.Where.Where.OfferId == "offer-1" &&
                    f.Where.Where.TargetLogicalId == "bupa" &&
                    f.Where.Where.Status == "Sent"), CancellationToken.None))
                .ReturnsAsync(new List<ExternalSynchronization> { existingSync });

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Data has already been synchronized for this case, offer, and target system");

            // Verify that the synchronization service was NOT called
            _mockBupaSyncService.Verify(
                x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()),
                Times.Never);

            // Verify that the external sync service was called to check for existing data
            _mockExternalSyncService.Verify(
                x => x.QueryAsync("tenant-1", It.IsAny<QueryArguments<Filter<ExternalSynchronizationFilter>>>(), CancellationToken.None),
                Times.Once);
        }

        [Fact]
        public async Task GIVEN_existing_failed_synchronization_WHEN_attempting_to_synchronize_again_THEN_should_proceed()
        {
            // Arrange
            var input = new SynchronizeDataInput
            {
                CaseId = "case-1",
                OfferId = "offer-1",
                ExternalSynchronizationTargetLogicalId = "bupa"
            };

            var existingFailedSync = new ExternalSynchronization
            {
                Id = "failed-sync-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                Status = "Failed"
            };

            var newSuccessfulSync = new ExternalSynchronization
            {
                Id = "new-sync-1",
                Status = "Sent"
            };

            // Setup mock to return existing failed synchronization (which should not block new attempts)
            _mockExternalSyncService
                .Setup(x => x.QueryAsync("tenant-1", It.Is<QueryArguments<Filter<ExternalSynchronizationFilter>>>(f =>
                    f.Where.Where.CaseId == "case-1" &&
                    f.Where.Where.OfferId == "offer-1" &&
                    f.Where.Where.TargetLogicalId == "bupa" &&
                    f.Where.Where.Status == "Sent"), CancellationToken.None))
                .ReturnsAsync(new List<ExternalSynchronization>()); // No "Sent" status records

            _mockBupaSyncService
                .Setup(x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()))
                .ReturnsAsync(Result<ExternalSynchronization>.Success(newSuccessfulSync));

            // Act
            var result = await _mutation.SynchronizeData("tenant-1", input, CancellationToken.None);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();
            result.Value.ExternalSynchronization.Should().BeEquivalentTo(newSuccessfulSync);

            // Verify that the synchronization service was called
            _mockBupaSyncService.Verify(
                x => x.ProcessAsync(It.IsAny<SynchronizeDataRequest>()),
                Times.Once);
        }
    }
}