using System;
using System.Collections.Generic;
using CoverGo.Cases.Domain;
using FluentAssertions;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class ExternalSynchronizationServiceTests
    {
        private readonly Mock<IExternalSynchronizationRepository> _mockRepository;
        private readonly ExternalSynchronizationService _service;

        public ExternalSynchronizationServiceTests()
        {
            _mockRepository = new Mock<IExternalSynchronizationRepository>();
            _service = new ExternalSynchronizationService(_mockRepository.Object);
        }

        [Fact]
        public void GIVEN_new_synchronization_entity_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange
            var payload = JToken.Parse("{\"caseData\":{\"name\":\"Test Case\",\"status\":\"SUBMITTED\"}}");
            var response = JToken.Parse("{\"success\":true,\"externalId\":\"ext-123\"}");
            var lastSyncTime = DateTime.UtcNow.AddMinutes(-30);

            // Act
            var synchronization = new ExternalSynchronization
            {
                Id = "sync-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "external-system-1",
                Status = "COMPLETED",
                Payload = payload,
                Response = response,
                LastSuccessfullySyncAt = lastSyncTime,
                CreatedAt = DateTime.UtcNow,
                CreatedById = "user-1"
            };

            // Assert
            synchronization.Id.Should().Be("sync-1");
            synchronization.CaseId.Should().Be("case-1");
            synchronization.OfferId.Should().Be("offer-1");
            synchronization.TargetLogicalId.Should().Be("external-system-1");
            synchronization.Status.Should().Be("COMPLETED");
            synchronization.Payload.Should().BeEquivalentTo(payload);
            synchronization.Response.Should().BeEquivalentTo(response);
            synchronization.LastSuccessfullySyncAt.Should().Be(lastSyncTime);
            synchronization.CreatedById.Should().Be("user-1");
        }

        [Fact]
        public void GIVEN_new_synchronization_upsert_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange & Act
            var syncUpsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "external-system-1",
                Status = "PENDING",
                Payload = "{\"caseData\":{\"name\":\"Test Case\"}}",
                Response = "{}",
                LastSuccessfullySyncAt = DateTime.UtcNow,
                ById = "user-1"
            };

            // Assert
            syncUpsert.Id.Should().Be("sync-1");
            syncUpsert.CaseId.Should().Be("case-1");
            syncUpsert.OfferId.Should().Be("offer-1");
            syncUpsert.TargetLogicalId.Should().Be("external-system-1");
            syncUpsert.Status.Should().Be("PENDING");
            syncUpsert.Payload.Should().Be("{\"caseData\":{\"name\":\"Test Case\"}}");
            syncUpsert.Response.Should().Be("{}");
            syncUpsert.ById.Should().Be("user-1");
        }

        [Fact]
        public void GIVEN_synchronization_filter_WHEN_properties_are_set_THEN_all_filter_conditions_should_be_accessible()
        {
            // Arrange & Act
            var filter = new ExternalSynchronizationFilter
            {
                Id = "sync-1",
                CaseId_in = new List<string> { "case-1", "case-2" },
                OfferId_contains = "offer",
                TargetLogicalId = "external-system-1",
                Status_in = new List<string> { "COMPLETED", "FAILED" },
                LastSuccessfullySyncAt_gte = DateTime.UtcNow.AddDays(-7),
                LastSuccessfullySyncAt_lte = DateTime.UtcNow,
                CreatedAt_gte = DateTime.UtcNow.AddDays(-30),
                UpdatedAt_gte = DateTime.UtcNow.AddHours(-1)
            };

            // Assert
            filter.Id.Should().Be("sync-1");
            filter.CaseId_in.Should().Contain("case-1", "case-2");
            filter.OfferId_contains.Should().Be("offer");
            filter.TargetLogicalId.Should().Be("external-system-1");
            filter.Status_in.Should().Contain("COMPLETED", "FAILED");
            filter.LastSuccessfullySyncAt_gte.Should().BeCloseTo(DateTime.UtcNow.AddDays(-7), TimeSpan.FromMinutes(1));
            filter.LastSuccessfullySyncAt_lte.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
            filter.CreatedAt_gte.Should().BeCloseTo(DateTime.UtcNow.AddDays(-30), TimeSpan.FromMinutes(1));
            filter.UpdatedAt_gte.Should().BeCloseTo(DateTime.UtcNow.AddHours(-1), TimeSpan.FromMinutes(1));
        }

        [Fact]
        public void GIVEN_service_with_repository_WHEN_service_is_created_THEN_repository_should_be_injected()
        {
            // Arrange & Act
            var service = new ExternalSynchronizationService(_mockRepository.Object);

            // Assert
            service.Should().NotBeNull();
            service.Should().BeOfType<ExternalSynchronizationService>();
        }

        [Fact]
        public void GIVEN_synchronization_with_complex_payload_and_response_WHEN_json_is_parsed_THEN_structure_should_be_preserved()
        {
            // Arrange
            var complexPayloadJson = @"{
                ""case"": {
                    ""id"": ""case-123"",
                    ""name"": ""Complex Test Case"",
                    ""details"": {
                        ""products"": [""product1"", ""product2""],
                        ""amounts"": [100.50, 200.75]
                    }
                },
                ""offer"": {
                    ""id"": ""offer-456"",
                    ""status"": ""active""
                }
            }";

            var complexResponseJson = @"{
                ""status"": ""success"",
                ""data"": {
                    ""externalId"": ""ext-abc-123"",
                    ""processedAt"": ""2023-12-01T10:30:00Z"",
                    ""validationResults"": {
                        ""errors"": [],
                        ""warnings"": [""Optional field missing""]
                    }
                }
            }";

            var payload = JToken.Parse(complexPayloadJson);
            var response = JToken.Parse(complexResponseJson);

            // Act
            var sync = new ExternalSynchronization
            {
                Id = "sync-123",
                CaseId = "case-123",
                OfferId = "offer-456",
                TargetLogicalId = "external-system-1",
                Status = "COMPLETED",
                Payload = payload,
                Response = response
            };

            // Assert
            sync.Payload["case"]["details"]["products"].Should().HaveCount(2);
            sync.Payload["case"]["details"]["amounts"][0].Value<decimal>().Should().Be(100.50m);
            sync.Response["data"]["externalId"].Value<string>().Should().Be("ext-abc-123");
            sync.Response["data"]["validationResults"]["warnings"].Should().HaveCount(1);
        }

        [Fact]
        public void GIVEN_synchronization_with_null_timestamps_WHEN_entity_is_created_THEN_nullable_properties_should_handle_null_correctly()
        {
            // Arrange & Act
            var synchronization = new ExternalSynchronization
            {
                Id = "sync-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "external-system-1",
                Status = "PENDING",
                Payload = JToken.Parse("{}"),
                Response = JToken.Parse("{}"),
                LastSuccessfullySyncAt = null,
                CreatedAt = DateTime.UtcNow
            };

            // Assert
            synchronization.LastSuccessfullySyncAt.Should().BeNull();
            synchronization.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_synchronization_inheriting_from_system_object_WHEN_checking_inheritance_THEN_should_have_system_properties()
        {
            // Arrange & Act
            var sync = new ExternalSynchronization
            {
                Id = "sync-123",
                CreatedAt = DateTime.UtcNow,
                CreatedById = "user-1",
                LastModifiedAt = DateTime.UtcNow,
                LastModifiedById = "user-2"
            };

            // Assert
            sync.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            sync.CreatedById.Should().Be("user-1");
            sync.LastModifiedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            sync.LastModifiedById.Should().Be("user-2");
        }

        [Fact]
        public void GIVEN_filter_with_multiple_status_values_WHEN_using_status_in_filter_THEN_multiple_statuses_should_be_supported()
        {
            // Arrange & Act
            var filter = new ExternalSynchronizationFilter
            {
                Status_in = new List<string> { "PENDING", "COMPLETED", "FAILED", "RETRYING" }
            };

            // Assert
            filter.Status_in.Should().HaveCount(4);
            filter.Status_in.Should().Contain("PENDING", "COMPLETED", "FAILED", "RETRYING");
        }

        [Fact]
        public void GIVEN_filter_with_date_range_WHEN_using_date_filters_THEN_date_ranges_should_be_supported()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;

            // Act
            var filter = new ExternalSynchronizationFilter
            {
                CreatedAt_gte = startDate,
                UpdatedAt_lte = endDate,
                LastSuccessfullySyncAt_gte = startDate.AddDays(10),
                LastSuccessfullySyncAt_lte = endDate.AddHours(-1)
            };

            // Assert
            filter.CreatedAt_gte.Should().Be(startDate);
            filter.UpdatedAt_lte.Should().Be(endDate);
            filter.LastSuccessfullySyncAt_gte.Should().Be(startDate.AddDays(10));
            filter.LastSuccessfullySyncAt_lte.Should().Be(endDate.AddHours(-1));
        }
    }
}