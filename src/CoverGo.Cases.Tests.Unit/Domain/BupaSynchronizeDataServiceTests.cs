using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class BupaSynchronizeDataServiceTests
    {
        private readonly Mock<ICaseServiceFactory> _mockCaseServiceFactory;
        private readonly Mock<ICaseService> _mockCaseService;
        private readonly Mock<IProductsClient> _mockProductsClient;
        private readonly Mock<IExternalSynchronizationTargetService> _mockExternalSyncTargetService;
        private readonly Mock<IExternalSynchronizationService> _mockExternalSyncService;
        private readonly Mock<ILogger<BupaSynchronizeDataService>> _mockLogger;
        private readonly BupaSynchronizeDataService _service;

        public BupaSynchronizeDataServiceTests()
        {
            _mockCaseServiceFactory = new Mock<ICaseServiceFactory>();
            _mockCaseService = new Mock<ICaseService>();
            _mockProductsClient = new Mock<IProductsClient>();
            _mockExternalSyncTargetService = new Mock<IExternalSynchronizationTargetService>();
            _mockExternalSyncService = new Mock<IExternalSynchronizationService>();
            _mockLogger = new Mock<ILogger<BupaSynchronizeDataService>>();

            _mockCaseServiceFactory.Setup(x => x.Build()).Returns(_mockCaseService.Object);

            // Setup ExternalSynchronizationTarget mock with BUPA URLs
            var bupaTarget = new ExternalSynchronizationTarget
            {
                Id = "bupa-target-1",
                LogicalId = "bupa",
                Credentials = JToken.Parse("{\"apiKey\":\"test-key\"}"),
                Urls = JToken.Parse("{\"authorization\":\"https://api.bupa.com/auth\",\"validation\":\"https://api.bupa.com/validate\",\"confirmation\":\"https://api.bupa.com/confirm\"}")
            };

            _mockExternalSyncTargetService
                .Setup(x => x.QueryAsync(It.IsAny<string>(), It.IsAny<QueryArguments<Filter<ExternalSynchronizationTargetFilter>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ExternalSynchronizationTarget> { bupaTarget });

            // Setup ExternalSynchronizationService mock for status tracking
            var mockExternalSyncRecord = new ExternalSynchronization
            {
                Id = "sync-record-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                Status = "Initialized"
            };

            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success());

            _service = new BupaSynchronizeDataService(_mockCaseServiceFactory.Object, _mockProductsClient.Object, _mockExternalSyncTargetService.Object, _mockExternalSyncService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GIVEN_null_request_WHEN_sending_data_THEN_should_return_failure()
        {
            // Arrange & Act
            var result = await _service.ProcessAsync(null);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Request is required");
        }

        [Fact]
        public async Task GIVEN_null_tenantId_WHEN_sending_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = null,
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("TenantId is required");
        }

        [Fact]
        public async Task GIVEN_empty_caseId_WHEN_sending_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("CaseId is required");
        }

        [Fact]
        public async Task GIVEN_empty_offerId_WHEN_sending_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("OfferId is required");
        }

        [Fact]
        public async Task GIVEN_empty_targetLogicalId_WHEN_sending_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "",
                CancellationToken = CancellationToken.None
            };

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("TargetLogicalId is required");
        }

        [Fact]
        public async Task GIVEN_non_bupa_targetLogicalId_WHEN_sending_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "other-target",
                CancellationToken = CancellationToken.None
            };

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("This service only supports bupa target");
        }

        [Fact]
        public async Task GIVEN_valid_bupa_request_WHEN_sending_data_THEN_should_return_success()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();
            result.Value.CaseId.Should().Be("case-1");
            result.Value.OfferId.Should().Be("offer-1");
            result.Value.TargetLogicalId.Should().Be("bupa");
            result.Value.Status.Should().Be("Sent");
            result.Value.LastSuccessfullySyncAt.Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_case_not_found_WHEN_processing_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case>());

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Case with ID 'case-1' not found");
        }

        [Fact]
        public async Task GIVEN_offer_not_found_WHEN_processing_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer>
                        {
                            new Offer { Id = "different-offer-id", OfferNumber = "Different Offer" }
                        }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Offer with ID 'offer-1' not found in case");
        }

        [Fact]
        public async Task GIVEN_valid_case_and_offer_WHEN_processing_data_THEN_should_return_success()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();
            result.Value.CaseId.Should().Be("case-1");
            result.Value.OfferId.Should().Be("offer-1");
            result.Value.Status.Should().Be("Sent");
        }



        [Fact]
        public void GIVEN_service_WHEN_checking_logical_id_THEN_should_return_bupa()
        {
            // Act & Assert
            _service.LogicalId.Should().Be("bupa");
        }

        [Fact]
        public async Task GIVEN_service_with_dependencies_WHEN_service_is_created_THEN_should_not_be_null()
        {
            // Arrange & Act & Assert
            _service.Should().NotBeNull();
            _service.Should().BeOfType<BupaSynchronizeDataService>();
        }


        [Fact]
        public async Task GIVEN_complex_data_WHEN_end_to_end_sync_THEN_should_preserve_data_structure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-123",
                OfferId = "offer-456",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-456", OfferNumber = "Complex Offer", Status = "Sent" };
            var caseData = new Case
            {
                Id = "case-123",
                Name = "Complex Case",
                Status = "Active",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Payload["case"]?["id"]?.Value<string>().Should().Be("case-123");
            result.Value.Payload["case"]?["name"]?.Value<string>().Should().Be("Complex Case");
            result.Value.Payload["case"]?["status"]?.Value<string>().Should().Be("Active");
            result.Value.Payload["offer"]?["id"]?.Value<string>().Should().Be("offer-456");
            result.Value.Payload["offer"]?["offerNumber"]?.Value<string>().Should().Be("Complex Offer");
            result.Value.Payload["offer"]?["status"]?.Value<string>().Should().Be("Sent");
        }

        [Fact]
        public async Task GIVEN_offer_with_productId_WHEN_processing_data_THEN_should_include_product_data()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer
            {
                Id = "offer-1",
                OfferNumber = "Test Offer",
                ProductId = new CoverGo.Cases.Domain.ProductId { Plan = "test-plan", Type = "test-type", Version = "1.0" }
            };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            var productData = new CoverGo.Products.Client.Product
            {
                Id = new CoverGo.Products.Client.ProductId { Plan = "test-plan", Type = "test-type", Version = "1.0" }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            _mockProductsClient
                .Setup(x => x.Products_GetAllAsync("tenant-1", It.IsAny<CoverGo.Products.Client.ProductQuery>(), It.IsAny<string>()))
                .ReturnsAsync(new List<CoverGo.Products.Client.Product> { productData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Payload["validation"]["data"]["product"].Should().NotBeNull();
            result.Value.Payload["validation"]["data"]["product"]?["id"]?["plan"]?.Value<string>().Should().Be("test-plan");
        }

        [Fact]
        public async Task GIVEN_offer_with_productId_but_product_not_found_WHEN_processing_data_THEN_should_continue_without_product()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer
            {
                Id = "offer-1",
                OfferNumber = "Test Offer",
                ProductId = new CoverGo.Cases.Domain.ProductId { Plan = "test-plan", Type = "test-type", Version = "1.0" }
            };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            _mockProductsClient
                .Setup(x => x.Products_GetAllAsync("tenant-1", It.IsAny<CoverGo.Products.Client.ProductQuery>(), It.IsAny<string>()))
                .ReturnsAsync(new List<CoverGo.Products.Client.Product>()); // Empty list - no products found

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Payload["validation"]["data"]["product"].Should().BeOfType<JObject>();
        }

        [Fact]
        public async Task GIVEN_offer_with_null_productId_WHEN_processing_data_THEN_should_continue_without_product()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer
            {
                Id = "offer-1",
                OfferNumber = "Test Offer",
                ProductId = null // No product ID
            };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Payload["validation"]["data"]["product"].Should().BeOfType<JObject>();
            // Verify ProductsClient was never called
            _mockProductsClient.Verify(x => x.Products_GetAllAsync(It.IsAny<string>(), It.IsAny<CoverGo.Products.Client.ProductQuery>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task GIVEN_offer_with_empty_productId_plan_WHEN_processing_data_THEN_should_continue_without_product()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer
            {
                Id = "offer-1",
                OfferNumber = "Test Offer",
                ProductId = new CoverGo.Cases.Domain.ProductId { Plan = "", Type = "test-type", Version = "1.0" } // Empty plan
            };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Payload["validation"]["data"]["product"].Should().BeOfType<JObject>();
            // Verify ProductsClient was never called
            _mockProductsClient.Verify(x => x.Products_GetAllAsync(It.IsAny<string>(), It.IsAny<CoverGo.Products.Client.ProductQuery>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task GIVEN_products_client_throws_exception_WHEN_processing_data_THEN_should_continue_without_product()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer
            {
                Id = "offer-1",
                OfferNumber = "Test Offer",
                ProductId = new CoverGo.Cases.Domain.ProductId { Plan = "test-plan", Type = "test-type", Version = "1.0" }
            };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            _mockProductsClient
                .Setup(x => x.Products_GetAllAsync("tenant-1", It.IsAny<CoverGo.Products.Client.ProductQuery>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Product service unavailable"));

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert - Should succeed even without product data
            result.IsSuccess.Should().BeTrue();
            result.Value.Payload["validation"]["data"]["product"].Should().BeOfType<JObject>();
        }

        [Fact]
        public async Task GIVEN_case_service_throws_exception_WHEN_processing_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var exceptionMessage = "Database connection failed";
            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain($"Failed to collect case and offer data: {exceptionMessage}");
        }

        [Fact]
        public async Task GIVEN_valid_request_WHEN_processing_data_THEN_should_track_status_progression()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Track status calls
            var statusCalls = new List<string>();
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add($"Create-{upsert.Status}");
                })
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add($"Update-{upsert.Status}");
                })
                .ReturnsAsync(Result.Success());

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();

            // Verify status progression: Initialized -> DataCollected -> Transformed -> Sent
            statusCalls.Should().HaveCount(4);
            statusCalls[0].Should().Be("Create-Initialized");
            statusCalls[1].Should().Be("Update-DataCollected");
            statusCalls[2].Should().Be("Update-Transformed");
            statusCalls[3].Should().Be("Update-Sent");
        }

        [Fact]
        public async Task GIVEN_case_service_throws_exception_WHEN_processing_data_THEN_should_track_failure_status()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Track status calls
            var statusCalls = new List<(string Action, string Status, string Errors)>();
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add(("Create", upsert.Status, upsert.Errors));
                })
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add(("Update", upsert.Status, upsert.Errors));
                })
                .ReturnsAsync(Result.Success());

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();

            // Verify status progression: Initialized -> Failed (with error details)
            statusCalls.Should().HaveCount(2);
            statusCalls[0].Should().Be(("Create", "Initialized", null));
            statusCalls[1].Action.Should().Be("Update");
            statusCalls[1].Status.Should().Be("Failed");
            statusCalls[1].Errors.Should().NotBeNullOrEmpty();

            // Verify error details are serialized correctly
            var errors = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(statusCalls[1].Errors);
            errors.Should().HaveCount(1);
            errors[0].Step.Should().Be("Failed");
            errors[0].Code.Should().Be("SYNC_ERROR");
            errors[0].Message.Should().Be("Failed to collect case and offer data: Database connection failed");
            errors[0].OccurredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task GIVEN_external_sync_service_create_fails_WHEN_processing_data_THEN_should_return_failure()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Failure("Failed to create sync record"));

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Failed to create sync record");
        }

        [Fact]
        public async Task GIVEN_external_sync_service_update_fails_WHEN_processing_data_THEN_should_continue_with_warning()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            var updateCallCount = 0;
            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Returns(() =>
                {
                    updateCallCount++;
                    // Simulate final update failure
                    if (updateCallCount == 3) // Final update call
                        return Task.FromResult(Result.Failure("Failed to update final status"));
                    return Task.FromResult(Result.Success());
                });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Failed to update final status");
        }

        [Fact]
        public async Task GIVEN_transform_step_fails_WHEN_processing_data_THEN_should_track_failure_at_transform_step()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Create a custom service that fails at transform step
            var customService = new TestBupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object,
                shouldFailTransform: true);

            // Track status calls
            var statusCalls = new List<(string Action, string Status, string Errors)>();
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add(("Create", upsert.Status, upsert.Errors));
                })
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add(("Update", upsert.Status, upsert.Errors));
                })
                .ReturnsAsync(Result.Success());

            // Act
            var result = await customService.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();

            // Verify status progression: Initialized -> DataCollected -> Failed
            statusCalls.Should().HaveCount(3);
            statusCalls[0].Should().Be(("Create", "Initialized", null));
            statusCalls[1].Should().Be(("Update", "DataCollected", null));
            statusCalls[2].Action.Should().Be("Update");
            statusCalls[2].Status.Should().Be("Failed");

            // Verify error details
            var errors = Newtonsoft.Json.JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(statusCalls[2].Errors);
            errors.Should().HaveCount(1);
            errors[0].Step.Should().Be("Failed");
            errors[0].Message.Should().Be("Transform step failed");
        }

        [Fact]
        public async Task GIVEN_unexpected_exception_during_processing_WHEN_processing_data_THEN_should_track_failure_status()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            // Set up successful data collection first so we reach the transform step where exception is thrown
            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Create a custom service that throws unexpected exception
            var customService = new TestBupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object,
                shouldThrowUnexpectedException: true);

            var syncRecord = new ExternalSynchronization { Id = "sync-1" };

            // Track update calls to verify error status is set
            var statusCalls = new List<(string Status, string Errors)>();
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    statusCalls.Add((upsert.Status, upsert.Errors));
                })
                .ReturnsAsync(Result.Success());

            // Act
            var result = await customService.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Unexpected error: Unexpected exception occurred");

            // Should have recorded the failure status
            var failureCall = statusCalls.FirstOrDefault(x => x.Status == "Failed");
            failureCall.Status.Should().Be("Failed");
            failureCall.Errors.Should().NotBeNullOrEmpty();
        }
    }

    // Test helper class to simulate different failure scenarios
    public class TestBupaSynchronizeDataService : BupaSynchronizeDataService
    {
        private readonly bool _shouldFailTransform;
        private readonly bool _shouldThrowUnexpectedException;

        public TestBupaSynchronizeDataService(
            ICaseServiceFactory caseServiceFactory,
            IProductsClient productsClient,
            IExternalSynchronizationTargetService externalSyncTargetService,
            IExternalSynchronizationService externalSyncService,
            ILogger<BupaSynchronizeDataService> logger,
            bool shouldFailTransform = false,
            bool shouldThrowUnexpectedException = false)
            : base(caseServiceFactory, productsClient, externalSyncTargetService, externalSyncService, logger)
        {
            _shouldFailTransform = shouldFailTransform;
            _shouldThrowUnexpectedException = shouldThrowUnexpectedException;
        }

        protected override Task<Result<JToken>> TransformAsync(string tenantId, JToken collectedData, CancellationToken cancellationToken)
        {
            if (_shouldFailTransform)
                return Task.FromResult(Result<JToken>.Failure("Transform step failed"));

            if (_shouldThrowUnexpectedException)
                throw new Exception("Unexpected exception occurred");

            return base.TransformAsync(tenantId, collectedData, cancellationToken);
        }
    }
}