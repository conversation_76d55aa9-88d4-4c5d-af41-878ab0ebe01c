using System;
using CoverGo.Cases.Domain;
using FluentAssertions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain;

public class OfferTests
{
    [Fact]
    public void GIVEN_null_fields2_WHEN_inject_meta_fields_to_fields2_THEN_field2_remain_null()
    {
        Offer offer = new() { OfferNumber = Guid.NewGuid().ToString() };
        offer.InjectMetaFieldsToFields2(JToken.Parse(@"{""meta"":""testing""}"));

        offer.Fields2.Should().BeNull();
    }
    
    [Fact]
    public void GIVEN_non_null_fields2_WHEN_inject_meta_fields_to_fields2_THEN_field2_contain_injected_meta_fields()
    {
        Offer offer = new()
        {
            OfferNumber = Guid.NewGuid().ToString(),
            Fields2 = JToken.Parse(@"{""fields"":""value""}")
        };
        offer.InjectMetaFieldsToFields2(JToken.Parse(@"{""meta"":""testing""}"));

        offer.Fields2["fields"]!.Value<string>().Should().Be("value");
        offer.Fields2[Offer.MetaFieldsKey].Should().NotBeNull();
        offer.Fields2[Offer.MetaFieldsKey]!.ToString(Formatting.None).Should().Be(@"{""meta"":""testing""}");
    }
    
    [Fact]
    public void GIVEN_null_meta_fields_WHEN_inject_meta_fields_to_non_null_fields2_THEN_field2_should_remain_unchanged()
    {
        JToken fields2 = JToken.Parse(@"{""fields"":""value""}");
        
        Offer offer = new()
        {
            OfferNumber = Guid.NewGuid().ToString(),
            Fields2 = fields2
        };

        offer.InjectMetaFieldsToFields2(null);
        
        offer.Fields2[Offer.MetaFieldsKey].Should().BeNull();
        offer.Fields2.Should().BeEquivalentTo(fields2);
    }
    
    [Fact]
    public void GIVEN_existing_meta_fields_WHEN_inject_meta_fields_to_non_null_fields2_THEN_field2_meta_fields_should_be_overriden()
    {
        JToken fields2 = JToken.Parse(@"{""__metaFields"":{""meta"":""before update""}}");
        
        Offer offer = new()
        {
            OfferNumber = Guid.NewGuid().ToString(),
            Fields2 = fields2
        };
        offer.Fields2[Offer.MetaFieldsKey]!.ToString(Formatting.None).Should().Be(@"{""meta"":""before update""}");

        offer.InjectMetaFieldsToFields2(JToken.Parse(@"{""meta"":""after update""}"));

        offer.Fields2[Offer.MetaFieldsKey].Should().NotBeNull();
        offer.Fields2[Offer.MetaFieldsKey]!.ToString(Formatting.None).Should().Be(@"{""meta"":""after update""}");
    }
    
    [Fact]
    public void GIVEN_existing_meta_fields_WHEN_inject_null_meta_fields_to_non_null_fields2_THEN_field2_meta_fields_should_be_null()
    {
        Offer offer = new()
        {
            OfferNumber = Guid.NewGuid().ToString(),
            Fields2 = JToken.Parse(@"{""__metaFields"":{""meta"":""before update""}}")
        };
        offer.Fields2[Offer.MetaFieldsKey]!.ToString(Formatting.None).Should().Be(@"{""meta"":""before update""}");

        offer.InjectMetaFieldsToFields2(null);

        offer.Fields2[Offer.MetaFieldsKey].Should().BeNull();
    }
}