using System;
using System.Collections.Generic;
using CoverGo.Cases.Domain;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class ExternalSynchronizationModelTests
    {
        [Fact]
        public void GIVEN_new_external_synchronization_WHEN_setting_properties_THEN_all_properties_should_be_accessible()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "DataCollection",
                    Code = "TIMEOUT_ERROR",
                    Message = "Connection timeout",
                    OccurredAt = DateTime.UtcNow
                }
            };

            var payload = JToken.Parse("{\"case\": {\"id\": \"case-1\"}, \"offer\": {\"id\": \"offer-1\"}}");
            var response = JToken.Parse("{\"status\": \"success\", \"externalId\": \"ext-123\"}");

            // Act
            var sync = new ExternalSynchronization
            {
                Id = "sync-123",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                Status = "Sent",
                Payload = payload,
                Response = response,
                Errors = errors,
                LastSuccessfullySyncAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                CreatedById = "user-1"
            };

            // Assert
            sync.Id.Should().Be("sync-123");
            sync.CaseId.Should().Be("case-1");
            sync.OfferId.Should().Be("offer-1");
            sync.TargetLogicalId.Should().Be("bupa");
            sync.Status.Should().Be("Sent");
            sync.Payload.Should().BeEquivalentTo(payload);
            sync.Response.Should().BeEquivalentTo(response);
            sync.Errors.Should().HaveCount(1);
            sync.Errors[0].Step.Should().Be("DataCollection");
            sync.LastSuccessfullySyncAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            sync.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            sync.CreatedById.Should().Be("user-1");
        }

        [Fact]
        public void GIVEN_external_synchronization_with_empty_errors_WHEN_initializing_THEN_should_have_empty_errors_list()
        {
            // Act
            var sync = new ExternalSynchronization
            {
                Id = "sync-123",
                Status = "Initialized"
            };

            // Assert
            sync.Errors.Should().NotBeNull();
            sync.Errors.Should().BeEmpty();
        }

        [Fact]
        public void GIVEN_external_synchronization_with_null_payload_WHEN_setting_null_THEN_should_handle_gracefully()
        {
            // Act
            var sync = new ExternalSynchronization
            {
                Id = "sync-123",
                Payload = null,
                Response = null
            };

            // Assert
            sync.Payload.Should().BeNull();
            sync.Response.Should().BeNull();
        }

        [Fact]
        public void GIVEN_external_synchronization_with_multiple_errors_WHEN_adding_errors_THEN_should_contain_all_errors()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "DataCollection",
                    Code = "DB_ERROR",
                    Message = "Database connection failed",
                    OccurredAt = DateTime.UtcNow.AddMinutes(-5)
                },
                new ExternalSynchronizationError
                {
                    Step = "Transform",
                    Code = "VALIDATION_ERROR",
                    Message = "Invalid data format",
                    OccurredAt = DateTime.UtcNow.AddMinutes(-3)
                },
                new ExternalSynchronizationError
                {
                    Step = "Send",
                    Code = "NETWORK_ERROR",
                    Message = "External API unavailable",
                    OccurredAt = DateTime.UtcNow.AddMinutes(-1)
                }
            };

            // Act
            var sync = new ExternalSynchronization
            {
                Id = "sync-123",
                Status = "Failed",
                Errors = errors
            };

            // Assert
            sync.Errors.Should().HaveCount(3);
            sync.Errors[0].Step.Should().Be("DataCollection");
            sync.Errors[0].Code.Should().Be("DB_ERROR");
            sync.Errors[1].Step.Should().Be("Transform");
            sync.Errors[1].Code.Should().Be("VALIDATION_ERROR");
            sync.Errors[2].Step.Should().Be("Send");
            sync.Errors[2].Code.Should().Be("NETWORK_ERROR");
        }

        [Fact]
        public void GIVEN_external_synchronization_upsert_WHEN_setting_properties_THEN_all_properties_should_be_accessible()
        {
            // Arrange
            var errorsJson = "[{\"Step\":\"DataCollection\",\"Code\":\"TIMEOUT\",\"Message\":\"Timeout occurred\"}]";
            var payloadJson = "{\"case\":{\"id\":\"case-1\"}}";
            var responseJson = "{\"status\":\"success\"}";

            // Act
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                Status = "Sent",
                Payload = payloadJson,
                Response = responseJson,
                Errors = errorsJson,
                LastSuccessfullySyncAt = DateTime.UtcNow,
                ById = "user-1"
            };

            // Assert
            upsert.Id.Should().Be("sync-123");
            upsert.CaseId.Should().Be("case-1");
            upsert.OfferId.Should().Be("offer-1");
            upsert.TargetLogicalId.Should().Be("bupa");
            upsert.Status.Should().Be("Sent");
            upsert.Payload.Should().Be(payloadJson);
            upsert.Response.Should().Be(responseJson);
            upsert.Errors.Should().Be(errorsJson);
            upsert.LastSuccessfullySyncAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            upsert.ById.Should().Be("user-1");
        }

        [Fact]
        public void GIVEN_external_synchronization_upsert_with_null_errors_WHEN_setting_null_THEN_should_handle_gracefully()
        {
            // Act
            var upsert = new ExternalSynchronizationUpsert
            {
                Id = "sync-123",
                Errors = null
            };

            // Assert
            upsert.Errors.Should().BeNull();
        }

        [Fact]
        public void GIVEN_external_synchronization_filter_WHEN_setting_filter_conditions_THEN_should_support_all_filter_types()
        {
            // Act
            var filter = new ExternalSynchronizationFilter
            {
                Id = "sync-123",
                Id_neq = "sync-456",
                Id_in = new List<string> { "sync-1", "sync-2", "sync-3" },
                Id_contains = "sync",
                CaseId = "case-1",
                CaseId_in = new List<string> { "case-1", "case-2" },
                CaseId_contains = "case",
                OfferId = "offer-1",
                OfferId_in = new List<string> { "offer-1", "offer-2" },
                OfferId_contains = "offer",
                TargetLogicalId = "bupa",
                TargetLogicalId_in = new List<string> { "bupa", "axa" },
                TargetLogicalId_contains = "upa",
                Status = "Sent",
                Status_in = new List<string> { "Sent", "Failed" },
                Status_contains = "ent",
                LastSuccessfullySyncAt_gte = DateTime.UtcNow.AddDays(-7),
                LastSuccessfullySyncAt_lte = DateTime.UtcNow,
                CreatedAt_gte = DateTime.UtcNow.AddDays(-30),
                CreatedAt_lte = DateTime.UtcNow,
                UpdatedAt_gte = DateTime.UtcNow.AddDays(-1),
                UpdatedAt_lte = DateTime.UtcNow
            };

            // Assert
            filter.Id.Should().Be("sync-123");
            filter.Id_neq.Should().Be("sync-456");
            filter.Id_in.Should().HaveCount(3);
            filter.Id_contains.Should().Be("sync");
            filter.CaseId.Should().Be("case-1");
            filter.CaseId_in.Should().HaveCount(2);
            filter.CaseId_contains.Should().Be("case");
            filter.OfferId.Should().Be("offer-1");
            filter.OfferId_in.Should().HaveCount(2);
            filter.OfferId_contains.Should().Be("offer");
            filter.TargetLogicalId.Should().Be("bupa");
            filter.TargetLogicalId_in.Should().HaveCount(2);
            filter.TargetLogicalId_contains.Should().Be("upa");
            filter.Status.Should().Be("Sent");
            filter.Status_in.Should().HaveCount(2);
            filter.Status_contains.Should().Be("ent");
            filter.LastSuccessfullySyncAt_gte.Should().BeCloseTo(DateTime.UtcNow.AddDays(-7), TimeSpan.FromSeconds(1));
            filter.LastSuccessfullySyncAt_lte.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            filter.CreatedAt_gte.Should().BeCloseTo(DateTime.UtcNow.AddDays(-30), TimeSpan.FromSeconds(1));
            filter.CreatedAt_lte.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            filter.UpdatedAt_gte.Should().BeCloseTo(DateTime.UtcNow.AddDays(-1), TimeSpan.FromSeconds(1));
            filter.UpdatedAt_lte.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_external_synchronization_with_different_statuses_WHEN_checking_status_progression_THEN_should_support_all_expected_statuses()
        {
            // Arrange
            var statuses = new[] { "Initialized", "DataCollected", "Transformed", "Sent", "Failed" };

            foreach (var status in statuses)
            {
                // Act
                var sync = new ExternalSynchronization
                {
                    Id = $"sync-{status}",
                    Status = status
                };

                // Assert
                sync.Status.Should().Be(status);
            }
        }
    }
}