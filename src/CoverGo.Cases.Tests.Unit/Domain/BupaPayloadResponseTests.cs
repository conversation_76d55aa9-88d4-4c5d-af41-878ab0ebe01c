using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class BupaPayloadResponseTests
    {
        private readonly Mock<ICaseServiceFactory> _mockCaseServiceFactory;
        private readonly Mock<ICaseService> _mockCaseService;
        private readonly Mock<IProductsClient> _mockProductsClient;
        private readonly Mock<IExternalSynchronizationTargetService> _mockExternalSyncTargetService;
        private readonly Mock<IExternalSynchronizationService> _mockExternalSyncService;
        private readonly Mock<ILogger<BupaSynchronizeDataService>> _mockLogger;
        private readonly BupaSynchronizeDataService _service;

        public BupaPayloadResponseTests()
        {
            _mockCaseServiceFactory = new Mock<ICaseServiceFactory>();
            _mockCaseService = new Mock<ICaseService>();
            _mockProductsClient = new Mock<IProductsClient>();
            _mockExternalSyncTargetService = new Mock<IExternalSynchronizationTargetService>();
            _mockExternalSyncService = new Mock<IExternalSynchronizationService>();
            _mockLogger = new Mock<ILogger<BupaSynchronizeDataService>>();

            _mockCaseServiceFactory.Setup(x => x.Build()).Returns(_mockCaseService.Object);

            // Setup ExternalSynchronizationTarget mock with BUPA URLs
            var bupaTarget = new ExternalSynchronizationTarget
            {
                Id = "bupa-target-1",
                LogicalId = "bupa",
                Urls = JToken.FromObject(new
                {
                    authorization = "https://bupa-auth.example.com/auth",
                    validation = "https://bupa-validation.example.com/validate",
                    confirmation = "https://bupa-confirmation.example.com/confirm"
                })
            };

            _mockExternalSyncTargetService
                .Setup(x => x.QueryAsync(It.IsAny<string>(), It.IsAny<QueryArguments<Filter<ExternalSynchronizationTargetFilter>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ExternalSynchronizationTarget> { bupaTarget });

            // Setup external sync service
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result.Success());

            _service = new BupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task GIVEN_valid_bupa_request_WHEN_processing_THEN_should_store_detailed_payload_and_response()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();

            // Verify the payload structure
            var payload = result.Value.Payload as JObject;
            payload.Should().NotBeNull();
            payload.Should().ContainKey("authorization");
            payload.Should().ContainKey("validation");
            payload.Should().ContainKey("confirmation");

            // Verify authorization payload
            var authPayload = payload["authorization"] as JObject;
            authPayload.Should().NotBeNull();
            authPayload["requestType"].ToString().Should().Be("authorization");
            authPayload["tenantId"].ToString().Should().Be("tenant-1");
            authPayload["caseId"].ToString().Should().Be("case-1");
            authPayload["offerId"].ToString().Should().Be("offer-1");
            authPayload.Should().ContainKey("credentials");
            authPayload.Should().ContainKey("metadata");

            // Verify validation payload
            var validationPayload = payload["validation"] as JObject;
            validationPayload.Should().NotBeNull();
            validationPayload["requestType"].ToString().Should().Be("validation");
            validationPayload["tenantId"].ToString().Should().Be("tenant-1");
            validationPayload["caseId"].ToString().Should().Be("case-1");
            validationPayload["offerId"].ToString().Should().Be("offer-1");
            validationPayload.Should().ContainKey("data");
            validationPayload.Should().ContainKey("validationRules");
            validationPayload.Should().ContainKey("checksum");

            // Verify confirmation payload
            var confirmationPayload = payload["confirmation"] as JObject;
            confirmationPayload.Should().NotBeNull();
            confirmationPayload["requestType"].ToString().Should().Be("confirmation");
            confirmationPayload["tenantId"].ToString().Should().Be("tenant-1");
            confirmationPayload["caseId"].ToString().Should().Be("case-1");
            confirmationPayload["offerId"].ToString().Should().Be("offer-1");
            confirmationPayload.Should().ContainKey("finalData");
            confirmationPayload.Should().ContainKey("processingOptions");
            confirmationPayload.Should().ContainKey("acknowledgment");

            // Verify the response structure
            var response = result.Value.Response as JObject;
            response.Should().NotBeNull();
            response.Should().ContainKey("authorization");
            response.Should().ContainKey("validation");
            response.Should().ContainKey("confirmation");
            response.Should().ContainKey("summary");

            // Verify authorization response
            var authResponse = response["authorization"] as JObject;
            authResponse.Should().NotBeNull();
            authResponse["status"].ToString().Should().Be("success");
            authResponse.Should().ContainKey("authToken");
            authResponse.Should().ContainKey("sessionId");
            authResponse.Should().ContainKey("expiresIn");

            // Verify validation response
            var validationResponse = response["validation"] as JObject;
            validationResponse.Should().NotBeNull();
            validationResponse["status"].ToString().Should().Be("success");
            validationResponse.Should().ContainKey("validationId");
            validationResponse.Should().ContainKey("validationResults");

            // Verify confirmation response
            var confirmationResponse = response["confirmation"] as JObject;
            confirmationResponse.Should().NotBeNull();
            confirmationResponse["status"].ToString().Should().Be("success");
            confirmationResponse.Should().ContainKey("confirmationId");
            confirmationResponse.Should().ContainKey("processingResults");
            confirmationResponse.Should().ContainKey("acknowledgment");

            // Verify summary response
            var summary = response["summary"] as JObject;
            summary.Should().NotBeNull();
            summary["status"].ToString().Should().Be("success");
            summary["totalSteps"].Value<int>().Should().Be(3);
            summary.Should().ContainKey("externalId");
            summary.Should().ContainKey("urlsUsed");

            // Verify URLs used in summary
            var urlsUsed = summary["urlsUsed"] as JObject;
            urlsUsed.Should().NotBeNull();
            urlsUsed["authorization"].ToString().Should().Be("https://bupa-auth.example.com/auth");
            urlsUsed["validation"].ToString().Should().Be("https://bupa-validation.example.com/validate");
            urlsUsed["confirmation"].ToString().Should().Be("https://bupa-confirmation.example.com/confirm");
        }

        [Fact]
        public async Task GIVEN_payload_structure_WHEN_examining_details_THEN_should_contain_proper_request_metadata()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-test",
                CaseId = "case-test",
                OfferId = "offer-test",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-test", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-test",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-test",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-test", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();
            var payload = result.Value.Payload as JObject;

            // Verify authorization payload details
            var authPayload = payload["authorization"] as JObject;
            var credentials = authPayload["credentials"] as JObject;
            credentials["clientId"].ToString().Should().Be("bupa-client-001");
            credentials["scope"].ToString().Should().Be("case-synchronization");
            credentials["version"].ToString().Should().Be("v1.0");

            var metadata = authPayload["metadata"] as JObject;
            metadata["source"].ToString().Should().Be("CoverGo");
            metadata["targetSystem"].ToString().Should().Be("BUPA");
            metadata["dataClassification"].ToString().Should().Be("confidential");

            // Verify validation payload details
            var validationPayload = payload["validation"] as JObject;
            var validationRules = validationPayload["validationRules"] as JArray;
            validationRules.Should().HaveCount(4);
            validationRules[0].ToString().Should().Be("mandatory-fields");
            validationRules[1].ToString().Should().Be("data-format");
            validationRules[2].ToString().Should().Be("business-rules");
            validationRules[3].ToString().Should().Be("bupa-compliance");

            // Verify that validation payload includes auth context from authorization response
            var validationAuthContext = validationPayload["authContext"] as JObject;
            validationAuthContext.Should().NotBeNull();
            validationAuthContext["authToken"].Should().NotBeNull();
            validationAuthContext["sessionId"].Should().NotBeNull();
            validationAuthContext["scope"].Should().NotBeNull();

            // Verify confirmation payload details
            var confirmationPayload = payload["confirmation"] as JObject;
            var processingOptions = confirmationPayload["processingOptions"] as JObject;
            processingOptions["async"].Value<bool>().Should().BeFalse();
            processingOptions["priority"].ToString().Should().Be("normal");
            processingOptions["retryPolicy"].ToString().Should().Be("standard");

            var acknowledgment = confirmationPayload["acknowledgment"] as JObject;
            acknowledgment["required"].Value<bool>().Should().BeTrue();
            acknowledgment["format"].ToString().Should().Be("detailed");

            // Verify that confirmation payload includes context from both authorization and validation responses
            var confirmationAuthContext = confirmationPayload["authContext"] as JObject;
            confirmationAuthContext.Should().NotBeNull();
            confirmationAuthContext["authToken"].Should().NotBeNull();
            confirmationAuthContext["sessionId"].Should().NotBeNull();

            var confirmationValidationContext = confirmationPayload["validationContext"] as JObject;
            confirmationValidationContext.Should().NotBeNull();
            confirmationValidationContext["validationId"].Should().NotBeNull();
            confirmationValidationContext["validationScore"].Should().NotBeNull();
            confirmationValidationContext["validationPassed"].Should().NotBeNull();
        }

        [Fact]
        public async Task GIVEN_partial_failure_scenario_WHEN_processing_THEN_should_store_successful_steps_payload_and_response()
        {
            // Arrange
            var request = new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = "case-1",
                OfferId = "offer-1",
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };

            var offer = new Offer { Id = "offer-1", OfferNumber = "Test Offer" };
            var caseData = new Case
            {
                Id = "case-1",
                Name = "Test Case",
                Proposals = new List<Proposal>
                {
                    new Proposal
                    {
                        Id = "proposal-1",
                        Basket = new List<Offer> { offer }
                    }
                }
            };

            _mockCaseService
                .Setup(x => x.GetAsync("tenant-1", It.IsAny<QueryArguments<CaseWhere>>(), CancellationToken.None))
                .ReturnsAsync(new List<Case> { caseData });

            // Create a custom service that will simulate partial failure
            // (In a real scenario, this would happen due to network issues, API failures, etc.)

            // Act
            var result = await _service.ProcessAsync(request);

            // Assert - Even with partial success, we should get the successful parts stored
            result.IsSuccess.Should().BeTrue();
            result.Value.Should().NotBeNull();

            var payload = result.Value.Payload as JObject;
            var response = result.Value.Response as JObject;

            payload.Should().NotBeNull();
            response.Should().NotBeNull();

            // Verify that we have at least the authorization step (minimum expected)
            payload.Should().ContainKey("authorization");
            response.Should().ContainKey("authorization");

            // Verify authorization payload structure
            var authPayload = payload["authorization"] as JObject;
            authPayload.Should().NotBeNull();
            authPayload["requestType"].ToString().Should().Be("authorization");
            authPayload["tenantId"].ToString().Should().Be("tenant-1");
            authPayload["caseId"].ToString().Should().Be("case-1");
            authPayload["offerId"].ToString().Should().Be("offer-1");

            // Verify authorization response structure
            var authResponse = response["authorization"] as JObject;
            authResponse.Should().NotBeNull();
            authResponse["status"].ToString().Should().Be("success");
            authResponse.Should().ContainKey("authToken");
            authResponse.Should().ContainKey("sessionId");

            // Verify summary includes completion information
            var summary = response["summary"] as JObject;
            summary.Should().NotBeNull();
            summary.Should().ContainKey("completedSteps");
            summary.Should().ContainKey("totalSteps");
            summary["totalSteps"].Value<int>().Should().Be(3);

            // In normal success case, we should have all 3 steps
            summary["completedSteps"].Value<int>().Should().Be(3);
            summary["status"].ToString().Should().Be("success");

            // But the test demonstrates that if there were partial failures,
            // we would still have the successful steps' data preserved
        }
    }
}