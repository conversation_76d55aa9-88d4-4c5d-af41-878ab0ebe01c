using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class ExternalSynchronizationIntegrationTests
    {
        private readonly Mock<ICaseServiceFactory> _mockCaseServiceFactory;
        private readonly Mock<ICaseService> _mockCaseService;
        private readonly Mock<IProductsClient> _mockProductsClient;
        private readonly Mock<IExternalSynchronizationTargetService> _mockExternalSyncTargetService;
        private readonly Mock<IExternalSynchronizationService> _mockExternalSyncService;
        private readonly Mock<ILogger<BupaSynchronizeDataService>> _mockLogger;

        public ExternalSynchronizationIntegrationTests()
        {
            _mockCaseServiceFactory = new Mock<ICaseServiceFactory>();
            _mockCaseService = new Mock<ICaseService>();
            _mockProductsClient = new Mock<IProductsClient>();
            _mockExternalSyncTargetService = new Mock<IExternalSynchronizationTargetService>();
            _mockExternalSyncService = new Mock<IExternalSynchronizationService>();
            _mockLogger = new Mock<ILogger<BupaSynchronizeDataService>>();

            _mockCaseServiceFactory.Setup(x => x.Build()).Returns(_mockCaseService.Object);
        }

        [Fact]
        public async Task GIVEN_complete_workflow_WHEN_processing_successfully_THEN_should_track_all_status_transitions()
        {
            // Arrange
            var request = CreateValidRequest();
            var service = CreateServiceWithSuccessfulMocks();

            var statusTransitions = new List<(DateTime Timestamp, string Status, List<ExternalSynchronizationError> Errors)>();

            // Track all status changes
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    var errors = ParseErrors(upsert.Errors);
                    statusTransitions.Add((DateTime.UtcNow, upsert.Status, errors));
                })
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    var errors = ParseErrors(upsert.Errors);
                    statusTransitions.Add((DateTime.UtcNow, upsert.Status, errors));
                })
                .ReturnsAsync(Result.Success());

            // Act
            var result = await service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeTrue();

            // Verify complete status progression
            statusTransitions.Should().HaveCount(4);
            statusTransitions[0].Status.Should().Be("Initialized");
            statusTransitions[0].Errors.Should().BeEmpty();

            statusTransitions[1].Status.Should().Be("DataCollected");
            statusTransitions[1].Errors.Should().BeEmpty();

            statusTransitions[2].Status.Should().Be("Transformed");
            statusTransitions[2].Errors.Should().BeEmpty();

            statusTransitions[3].Status.Should().Be("Sent");
            statusTransitions[3].Errors.Should().BeEmpty();

            // Verify timestamps are in chronological order
            for (int i = 1; i < statusTransitions.Count; i++)
            {
                statusTransitions[i].Timestamp.Should().BeOnOrAfter(statusTransitions[i - 1].Timestamp);
            }
        }

        [Fact]
        public async Task GIVEN_failure_at_each_step_WHEN_processing_THEN_should_track_appropriate_error_details()
        {
            // Test failure at DataCollection step
            await TestFailureAtStep("DataCollection", () =>
            {
                _mockCaseService
                    .Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<QueryArguments<CaseWhere>>(), It.IsAny<CancellationToken>()))
                    .ThrowsAsync(new Exception("Database connection failed"));
            });

            // Test failure at Transform step
            await TestFailureAtStep("Transform", () =>
            {
                SetupSuccessfulDataCollection();
                // This will be handled by the TestBupaSynchronizeDataService in the actual test
            });

            // Test failure at Send step
            await TestFailureAtStep("Send", () =>
            {
                SetupSuccessfulDataCollection();
                _mockExternalSyncTargetService
                    .Setup(x => x.QueryAsync(It.IsAny<string>(), It.IsAny<QueryArguments<Filter<ExternalSynchronizationTargetFilter>>>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync((List<ExternalSynchronizationTarget>)null);
            });
        }

        [Fact]
        public async Task GIVEN_external_sync_service_errors_WHEN_processing_THEN_should_handle_gracefully()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupSuccessfulDataCollection();
            SetupSuccessfulExternalSyncTarget();

            // Test CreateAsync failure
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Failure("Failed to create sync record"));

            var service = new BupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object);

            // Act
            var result = await service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Failed to create sync record");
        }

        [Fact]
        public async Task GIVEN_multiple_consecutive_failures_WHEN_processing_THEN_should_capture_error_chain()
        {
            // Arrange
            var request = CreateValidRequest();
            var errorTransitions = new List<(string Status, List<ExternalSynchronizationError> Errors)>();

            // Setup to fail at DataCollection
            _mockCaseService
                .Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<QueryArguments<CaseWhere>>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Primary database unavailable"));

            SetupExternalSyncServiceTracking(errorTransitions);

            var service = new BupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object);

            // Act
            var result = await service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();

            // Should have: Initialized -> Failed
            errorTransitions.Should().HaveCount(2);
            errorTransitions[0].Status.Should().Be("Initialized");
            errorTransitions[0].Errors.Should().BeEmpty();

            errorTransitions[1].Status.Should().Be("Failed");
            errorTransitions[1].Errors.Should().HaveCount(1);
            errorTransitions[1].Errors[0].Step.Should().Be("Failed");
            errorTransitions[1].Errors[0].Code.Should().Be("SYNC_ERROR");
            errorTransitions[1].Errors[0].Message.Should().Contain("Primary database unavailable");
        }

        [Fact]
        public async Task GIVEN_external_sync_update_failures_WHEN_processing_THEN_should_continue_with_business_logic()
        {
            // Arrange
            var request = CreateValidRequest();
            SetupSuccessfulDataCollection();
            SetupSuccessfulExternalSyncTarget();

            var updateCallCount = 0;
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Returns(() =>
                {
                    updateCallCount++;
                    // Simulate intermittent status update failures
                    if (updateCallCount == 1) // DataCollected update fails
                        return Task.FromResult(Result.Failure("Database temporarily unavailable"));
                    if (updateCallCount == 3) // Final update fails
                        return Task.FromResult(Result.Failure("Failed to update final status"));
                    return Task.FromResult(Result.Success());
                });

            var service = new BupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object);

            // Act
            var result = await service.ProcessAsync(request);

            // Assert
            result.IsSuccess.Should().BeFalse();
            result.Errors.Should().Contain("Failed to update final status");

            // Should have attempted 3 updates (DataCollected, Transformed, Sent)
            updateCallCount.Should().Be(3);
        }

        [Fact]
        public async Task GIVEN_concurrent_synchronization_requests_WHEN_processing_multiple_THEN_each_should_have_unique_tracking()
        {
            // Arrange - process requests sequentially to avoid concurrency issues in test tracking
            var requests = Enumerable.Range(1, 5).Select(i => CreateValidRequest($"case-{i}", $"offer-{i}")).ToList();
            var allStatusTransitions = new Dictionary<string, List<(string Status, List<ExternalSynchronizationError> Errors)>>();
            var syncIdToKeyMapping = new Dictionary<string, string>();
            var lockObject = new object();

            SetupSuccessfulDataCollection();
            SetupSuccessfulExternalSyncTarget();

            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    lock (lockObject)
                    {
                        if (!string.IsNullOrEmpty(upsert.CaseId) && !string.IsNullOrEmpty(upsert.OfferId))
                        {
                            var key = $"{upsert.CaseId}-{upsert.OfferId}";
                            if (!allStatusTransitions.ContainsKey(key))
                                allStatusTransitions[key] = new List<(string Status, List<ExternalSynchronizationError> Errors)>();

                            // Map sync ID to key for future updates
                            syncIdToKeyMapping[upsert.Id] = key;

                            var errors = ParseErrors(upsert.Errors);
                            allStatusTransitions[key].Add((upsert.Status, errors));
                        }
                    }
                })
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    lock (lockObject)
                    {
                        // Use the sync ID mapping to find the correct key
                        if (!string.IsNullOrEmpty(upsert.Id) && syncIdToKeyMapping.ContainsKey(upsert.Id))
                        {
                            var key = syncIdToKeyMapping[upsert.Id];
                            var errors = ParseErrors(upsert.Errors);
                            allStatusTransitions[key].Add((upsert.Status, errors));
                        }
                    }
                })
                .ReturnsAsync(Result.Success());

            var service = new BupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object);

            // Act - process concurrently
            var tasks = requests.Select(request => service.ProcessAsync(request)).ToList();
            var results = await Task.WhenAll(tasks);

            // Assert
            foreach (var result in results)
            {
                result.IsSuccess.Should().BeTrue();
            }

            // Each request should have its own tracking
            allStatusTransitions.Should().HaveCount(5);

            foreach (var kvp in allStatusTransitions)
            {
                var transitions = kvp.Value;
                transitions.Should().HaveCount(4); // Initialized, DataCollected, Transformed, Sent
                transitions[0].Status.Should().Be("Initialized");
                transitions[1].Status.Should().Be("DataCollected");
                transitions[2].Status.Should().Be("Transformed");
                transitions[3].Status.Should().Be("Sent");

                foreach (var transition in transitions)
                {
                    transition.Errors.Should().BeEmpty();
                }
            }
        }

        // Helper methods
        private SynchronizeDataRequest CreateValidRequest(string caseId = "case-1", string offerId = "offer-1")
        {
            return new SynchronizeDataRequest
            {
                TenantId = "tenant-1",
                CaseId = caseId,
                OfferId = offerId,
                TargetLogicalId = "bupa",
                CancellationToken = CancellationToken.None
            };
        }

        private BupaSynchronizeDataService CreateServiceWithSuccessfulMocks()
        {
            SetupSuccessfulDataCollection();
            SetupSuccessfulExternalSyncTarget();
            return new BupaSynchronizeDataService(
                _mockCaseServiceFactory.Object,
                _mockProductsClient.Object,
                _mockExternalSyncTargetService.Object,
                _mockExternalSyncService.Object,
                _mockLogger.Object);
        }

        private void SetupSuccessfulDataCollection()
        {
            _mockCaseService
                .Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<QueryArguments<CaseWhere>>(), It.IsAny<CancellationToken>()))
                .Returns<string, QueryArguments<CaseWhere>, CancellationToken>((tenantId, query, ct) =>
                {
                    var caseId = query.Where.Id;
                    var offerId = $"offer-{caseId.Split('-')[1]}"; // Extract number from case-X and create offer-X

                    var offer = new Offer { Id = offerId, OfferNumber = $"Test Offer {offerId}" };
                    var caseData = new Case
                    {
                        Id = caseId,
                        Name = $"Test Case {caseId}",
                        Proposals = new List<Proposal>
                        {
                            new Proposal
                            {
                                Id = $"proposal-{caseId}",
                                Basket = new List<Offer> { offer }
                            }
                        }
                    };

                    return Task.FromResult((IEnumerable<Case>)new List<Case> { caseData });
                });
        }

        private void SetupSuccessfulExternalSyncTarget()
        {
            var bupaTarget = new ExternalSynchronizationTarget
            {
                Id = "bupa-target-1",
                LogicalId = "bupa",
                Credentials = JToken.Parse("{\"apiKey\":\"test-key\"}"),
                Urls = JToken.Parse("{\"authorization\":\"https://api.bupa.com/auth\",\"validation\":\"https://api.bupa.com/validate\",\"confirmation\":\"https://api.bupa.com/confirm\"}")
            };

            _mockExternalSyncTargetService
                .Setup(x => x.QueryAsync(It.IsAny<string>(), It.IsAny<QueryArguments<Filter<ExternalSynchronizationTargetFilter>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ExternalSynchronizationTarget> { bupaTarget });
        }

        private void SetupExternalSyncServiceTracking(List<(string Status, List<ExternalSynchronizationError> Errors)> errorTransitions)
        {
            _mockExternalSyncService
                .Setup(x => x.CreateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    var errors = ParseErrors(upsert.Errors);
                    errorTransitions.Add((upsert.Status, errors));
                })
                .ReturnsAsync(Result<CoverGo.DomainUtils.CreatedStatus>.Success(new CoverGo.DomainUtils.CreatedStatus()));

            _mockExternalSyncService
                .Setup(x => x.UpdateAsync(It.IsAny<string>(), It.IsAny<ExternalSynchronizationUpsert>(), It.IsAny<CancellationToken>()))
                .Callback<string, ExternalSynchronizationUpsert, CancellationToken>((tenantId, upsert, ct) =>
                {
                    var errors = ParseErrors(upsert.Errors);
                    errorTransitions.Add((upsert.Status, errors));
                })
                .ReturnsAsync(Result.Success());
        }

        private async Task TestFailureAtStep(string expectedFailureStep, Action setupFailure)
        {
            var request = CreateValidRequest();
            var errorTransitions = new List<(string Status, List<ExternalSynchronizationError> Errors)>();

            setupFailure();
            SetupExternalSyncServiceTracking(errorTransitions);

            var service = expectedFailureStep == "Transform"
                ? new TestBupaSynchronizeDataService(
                    _mockCaseServiceFactory.Object,
                    _mockProductsClient.Object,
                    _mockExternalSyncTargetService.Object,
                    _mockExternalSyncService.Object,
                    _mockLogger.Object,
                    shouldFailTransform: true)
                : new BupaSynchronizeDataService(
                    _mockCaseServiceFactory.Object,
                    _mockProductsClient.Object,
                    _mockExternalSyncTargetService.Object,
                    _mockExternalSyncService.Object,
                    _mockLogger.Object);

            var result = await service.ProcessAsync(request);

            result.IsSuccess.Should().BeFalse();

            // Find the failure transition
            var failureTransition = errorTransitions.FirstOrDefault(t => t.Status == "Failed");
            failureTransition.Status.Should().Be("Failed");
            failureTransition.Errors.Should().HaveCount(1);
            failureTransition.Errors[0].Code.Should().Be("SYNC_ERROR");
        }

        private static List<ExternalSynchronizationError> ParseErrors(string errorsJson)
        {
            if (string.IsNullOrEmpty(errorsJson))
                return new List<ExternalSynchronizationError>();

            try
            {
                return Newtonsoft.Json.JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(errorsJson) ?? new List<ExternalSynchronizationError>();
            }
            catch
            {
                return new List<ExternalSynchronizationError>();
            }
        }
    }
}