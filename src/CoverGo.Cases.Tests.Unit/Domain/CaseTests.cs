using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using CoverGo.Cases.Domain;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain;

public class CaseTests
{
    [Fact]
    public void GIVEN_case_without_proposal_WHEN_inject_meta_fields_to_offer_fields2_THEN_proposal_should_remain_null()
    {
        Case @case = new()
        {
            CaseNumber = "Test Case Number"
        };
        @case.InjectMetaFieldsToOfferFields2();

        @case.Proposals.Should().BeEmpty();
    }
    
    [Fact]
    public void GIVEN_no_issued_proposal_in_case_WHEN_inject_meta_fields_to_offer_fields2_THEN_proposal_offer_fields2_metaFields_should_have_no_proposalIssuedAt()
    {
        string originalFields2 = @"
{
    ""fields"":""value"", 
    ""__metaFields"":{""proposalIssuedAt"":""original time"", ""someOtherFields"":""testing testing""}
}";
        
        Case @case = new()
        {
            CaseNumber = "Test Case Number",
            Proposals = new List<Proposal>
            {
                new ()
                {
                    IsIssued = false,
                    Basket = new List<Offer>
                    {
                        new () { Fields2 = JToken.Parse(originalFields2) }
                    }
                }
            },
        };

        @case.InjectMetaFieldsToOfferFields2();

        Offer offer = @case.Proposals.FirstOrDefault()!.Basket!.FirstOrDefault();

        offer!.Fields2["fields"]!.Value<string>().Should().Be("value");
        offer!.Fields2[Offer.MetaFieldsKey]?["someOtherFields"]?.Should().BeNull();
        offer!.Fields2[Offer.MetaFieldsKey]?[Proposal.MetaFieldsIssuedAtKey]?.Should().BeNull();
        
        // This assertion could be removed in case that we are injecting more info other than proposal issued at in the future
        offer!.Fields2[Offer.MetaFieldsKey].Should().BeNull();
    }
    
    [Fact]
    public void GIVEN_issued_proposal_in_case_WHEN_inject_meta_fields_to_offer_fields2_with_existing_meta_fields_THEN_proposal_offer_fields2_metaFields_should_have_proposalIssuedAt()
    {
        DateTime currentTime = DateTime.Now;
        string currentTimeStr = currentTime.ToString("yyyy/MM/dd HH:mm:ss", CultureInfo.InvariantCulture);
        string originalFields2 = @"
{
    ""fields"":""value"", 
    ""__metaFields"":{""proposalIssuedAt"":""original time"", ""someOtherFields"":""testing testing""}
}";
        
        Case @case = new()
        {
            CaseNumber = "Test Case Number",
            Proposals = new List<Proposal>
            {
                new ()
                {
                    IsIssued = true,
                    IssuedAt = currentTime,
                    Basket = new List<Offer>
                    {
                        new () { Fields2 = JToken.Parse(originalFields2) }
                    }
                }
            }
        };

        @case.InjectMetaFieldsToOfferFields2();

        Offer offer = @case.Proposals.FirstOrDefault()!.Basket!.FirstOrDefault();

        offer!.Fields2["fields"]!.Value<string>().Should().Be("value");
        offer!.Fields2[Offer.MetaFieldsKey]?["someOtherFields"]?.Should().BeNull();
        offer!.Fields2[Offer.MetaFieldsKey]![Proposal.MetaFieldsIssuedAtKey]!.Value<string>().Should().Be(currentTimeStr);
    }
}