﻿using System.Security.Claims;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.OfferStatusPermissions;
using CoverGo.Cases.Domain.OfferStatusPermissions.Bupa;
using FluentAssertions;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class BupaOfferStatusTransitionPermissionValidatorTests
    {
        private const string AdminRoleName = "admin";
        private const string PresalesRoleName = "BD/Pre-Sales";
        private const string SalesUserRoleName = "Sales";

        private record TestScenario(string CurrentStatus, string NextStatus, ClaimsIdentity Identity, BupaOfferStatusTransitionPermissionValidator BupaOfferStatusTransitionPermissionValidator);

        [Fact]
        public void GIVEN_IdentityWithNoPermissions_WHEN_ChangingStatus_THEN_ValidationResultIsInvalid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.UnderwriterRequested, "none");

            // Assert
            result.IsValid.Should().BeFalse();
            result.Message.Should().Be($"Change of offer status from {BupaOfferStatus.Added} to {BupaOfferStatus.UnderwriterRequested} is not allowed by your user role.");
        }

        [Fact]
        public void GIVEN_IdentityWithSalesSuperUserRole_WHEN_ChangingStatusFromAddedToUnderwriterRequested_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.UnderwriterRequested, PresalesRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesSuperUserRole_WHEN_ChangingStatusFromAddedToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.Added, PresalesRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesSuperUserRole_WHEN_NotChangingStatus_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, null, PresalesRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesSuperUserRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterRequested_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterRequested, PresalesRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesSuperUserRole_WHEN_ChangingStatusFromUnderwriterRequestedToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.Added, PresalesRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesSuperUserRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterInProgress_THEN_ValidationResultIsInvalid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterInProgress, PresalesRoleName);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Message.Should().Be($"Change of offer status from {BupaOfferStatus.UnderwriterRequested} to {BupaOfferStatus.UnderwriterInProgress} is not allowed by your user role.");
        }

        [Fact]
        public void GIVEN_IdentityWithSalesUserRole_WHEN_ChangingStatusFromAddedToUnderwriterRequested_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.UnderwriterRequested, SalesUserRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesUserRole_WHEN_ChangingStatusFromAddedToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.Added, SalesUserRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesUserRole_WHEN_NotChangingStatus_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, null, SalesUserRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesUserRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterRequested_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterRequested, SalesUserRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesUserRole_WHEN_ChangingStatusFromUnderwriterRequestedToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.Added, SalesUserRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesUserRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterInProgress_THEN_ValidationResultIsInvalid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterInProgress, SalesUserRoleName);

            // Assert
            result.IsValid.Should().BeFalse();
            result.Message.Should().Be($"Change of offer status from {BupaOfferStatus.UnderwriterRequested} to {BupaOfferStatus.UnderwriterInProgress} is not allowed by your user role.");
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterInProgress_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterInProgress, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUnderwriterInProgressToUnderwriterApproved_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.UnderwriterApproved, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUnderwriterInProgressToUnderwriterInProgress_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.UnderwriterInProgress, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUnderwriterInProgressToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.Added, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUUnderwriterApprovedToUnderwriterApproved_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.UnderwriterApproved, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUUnderwriterApprovedToUnderwriterInProgress_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.UnderwriterInProgress, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromUnderwriterApprovedToSent_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.Sent, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithUnderwriterRole_WHEN_ChangingStatusFromAddedToUnderwriterReqquested_THEN_ValidationResultIsNotValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.UnderwriterRequested, "Underwriting/Pricing");

            // Assert
            result.IsValid.Should().BeFalse();
            result.Message.Should().Be($"Change of offer status from {BupaOfferStatus.Added} to {BupaOfferStatus.UnderwriterRequested} is not allowed by your user role.");
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromAddedToUnderwriterRequested_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.UnderwriterRequested, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromAddedToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, BupaOfferStatus.Added, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithSalesAdminRole_WHEN_NotChangingStatus_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.Added, null, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterRequested_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterRequested, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterRequestedToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.Added, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterRequestedToUnderwriterInProgress_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterInProgress, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterInProgressToUnderwriterApproved_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.UnderwriterApproved, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterInProgressToUnderwriterInProgress_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.UnderwriterInProgress, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterInProgressToAdded_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.Added, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterApprovedToUnderwriterApproved_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.UnderwriterApproved, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterApprovedToUnderwriterInProgress_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.UnderwriterInProgress, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        [Fact]
        public void GIVEN_IdentityWithAdminRole_WHEN_ChangingStatusFromUnderwriterApprovedToSent_THEN_ValidationResultIsValid()
        {
            // Act
            TransitionValidationResult result = ExecuteTestCase(BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.Sent, AdminRoleName);

            // Assert
            result.IsValid.Should().BeTrue();
        }

        private static TransitionValidationResult ExecuteTestCase(string currentStatus, string nextStatus, string userRole)
        {
            TestScenario testScenario = new TestScenario(currentStatus, nextStatus, new ClaimsIdentity(new Claim[] { new Claim("role", userRole) }, null, null, "role"), new BupaOfferStatusTransitionPermissionValidator());

            return testScenario.BupaOfferStatusTransitionPermissionValidator.IsValidStatusTransitionForOffer(testScenario.CurrentStatus, testScenario.NextStatus, testScenario.Identity);
        }
    }
}
