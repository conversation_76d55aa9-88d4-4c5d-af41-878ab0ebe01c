using System;
using System.Collections.Generic;
using CoverGo.Cases.Domain;
using FluentAssertions;
using Newtonsoft.Json;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class ExternalSynchronizationErrorTests
    {
        [Fact]
        public void GIVEN_new_error_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange & Act
            var error = new ExternalSynchronizationError
            {
                Step = "DataCollection",
                Code = "SYNC_ERROR",
                Message = "Failed to retrieve case data",
                Details = "Database connection timeout occurred",
                OccurredAt = DateTime.UtcNow
            };

            // Assert
            error.Step.Should().Be("DataCollection");
            error.Code.Should().Be("SYNC_ERROR");
            error.Message.Should().Be("Failed to retrieve case data");
            error.Details.Should().Be("Database connection timeout occurred");
            error.OccurredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_error_with_null_properties_WHEN_setting_nulls_THEN_should_handle_gracefully()
        {
            // Arrange & Act
            var error = new ExternalSynchronizationError
            {
                Step = null,
                Code = null,
                Message = null,
                Details = null,
                OccurredAt = DateTime.UtcNow
            };

            // Assert
            error.Step.Should().BeNull();
            error.Code.Should().BeNull();
            error.Message.Should().BeNull();
            error.Details.Should().BeNull();
            error.OccurredAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public void GIVEN_error_list_WHEN_serializing_to_json_THEN_should_serialize_correctly()
        {
            // Arrange
            var errors = new List<ExternalSynchronizationError>
            {
                new ExternalSynchronizationError
                {
                    Step = "DataCollection",
                    Code = "TIMEOUT_ERROR",
                    Message = "Connection timeout",
                    Details = "Database connection failed after 30 seconds",
                    OccurredAt = new DateTime(2023, 10, 1, 12, 0, 0, DateTimeKind.Utc)
                },
                new ExternalSynchronizationError
                {
                    Step = "Transform",
                    Code = "VALIDATION_ERROR",
                    Message = "Invalid data format",
                    Details = "Required field 'OfferId' is missing",
                    OccurredAt = new DateTime(2023, 10, 1, 12, 1, 0, DateTimeKind.Utc)
                }
            };

            // Act
            var json = JsonConvert.SerializeObject(errors);

            // Assert
            json.Should().NotBeNullOrEmpty();
            json.Should().Contain("DataCollection");
            json.Should().Contain("TIMEOUT_ERROR");
            json.Should().Contain("Connection timeout");
            json.Should().Contain("Transform");
            json.Should().Contain("VALIDATION_ERROR");
            json.Should().Contain("Invalid data format");
        }

        [Fact]
        public void GIVEN_json_string_WHEN_deserializing_to_error_list_THEN_should_deserialize_correctly()
        {
            // Arrange
            var json = @"[
                {
                    ""Step"": ""DataCollection"",
                    ""Code"": ""TIMEOUT_ERROR"",
                    ""Message"": ""Connection timeout"",
                    ""Details"": ""Database connection failed after 30 seconds"",
                    ""OccurredAt"": ""2023-10-01T12:00:00Z""
                },
                {
                    ""Step"": ""Transform"",
                    ""Code"": ""VALIDATION_ERROR"",
                    ""Message"": ""Invalid data format"",
                    ""Details"": ""Required field 'OfferId' is missing"",
                    ""OccurredAt"": ""2023-10-01T12:01:00Z""
                }
            ]";

            // Act
            var errors = JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(json);

            // Assert
            errors.Should().HaveCount(2);

            errors[0].Step.Should().Be("DataCollection");
            errors[0].Code.Should().Be("TIMEOUT_ERROR");
            errors[0].Message.Should().Be("Connection timeout");
            errors[0].Details.Should().Be("Database connection failed after 30 seconds");
            errors[0].OccurredAt.Should().Be(new DateTime(2023, 10, 1, 12, 0, 0, DateTimeKind.Utc));

            errors[1].Step.Should().Be("Transform");
            errors[1].Code.Should().Be("VALIDATION_ERROR");
            errors[1].Message.Should().Be("Invalid data format");
            errors[1].Details.Should().Be("Required field 'OfferId' is missing");
            errors[1].OccurredAt.Should().Be(new DateTime(2023, 10, 1, 12, 1, 0, DateTimeKind.Utc));
        }

        [Fact]
        public void GIVEN_empty_json_array_WHEN_deserializing_THEN_should_return_empty_list()
        {
            // Arrange
            var json = "[]";

            // Act
            var errors = JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(json);

            // Assert
            errors.Should().NotBeNull();
            errors.Should().BeEmpty();
        }

        [Fact]
        public void GIVEN_null_json_WHEN_deserializing_THEN_should_throw_exception()
        {
            // Arrange
            string json = null;

            // Act & Assert
            Action act = () => JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(json);
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void GIVEN_invalid_json_WHEN_deserializing_THEN_should_throw_exception()
        {
            // Arrange
            var invalidJson = "{ invalid json }";

            // Act & Assert
            Action act = () => JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(invalidJson);
            act.Should().Throw<JsonException>();
        }
    }
}