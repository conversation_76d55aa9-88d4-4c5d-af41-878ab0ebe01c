using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using FluentAssertions;
using Moq;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain
{
    public class ExternalSynchronizationTargetServiceTests
    {
        private readonly Mock<IExternalSynchronizationTargetRepository> _mockRepository;
        private readonly ExternalSynchronizationTargetService _service;

        public ExternalSynchronizationTargetServiceTests()
        {
            _mockRepository = new Mock<IExternalSynchronizationTargetRepository>();
            _service = new ExternalSynchronizationTargetService(_mockRepository.Object);
        }

        [Fact]
        public void GIVEN_new_target_entity_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange
            var credentials = JToken.Parse("{\"apiKey\":\"test123\",\"secret\":\"secret456\"}");
            var urls = JToken.Parse("{\"authorization\":\"https://api.external-system.com/auth\",\"validation\":\"https://api.external-system.com/validate\",\"confirmation\":\"https://api.external-system.com/confirm\"}");

            // Act
            var target = new ExternalSynchronizationTarget
            {
                Id = "target-1",
                LogicalId = "external-system-1",
                Credentials = credentials,
                Urls = urls,
                CreatedAt = DateTime.UtcNow,
                CreatedById = "user-1"
            };

            // Assert
            target.Id.Should().Be("target-1");
            target.LogicalId.Should().Be("external-system-1");
            target.Credentials.Should().BeEquivalentTo(credentials);
            target.Urls.Should().BeEquivalentTo(urls);
            target.CreatedById.Should().Be("user-1");
        }

        [Fact]
        public void GIVEN_new_target_upsert_WHEN_properties_are_set_THEN_all_properties_should_be_accessible()
        {
            // Arrange & Act
            var urls = JToken.Parse("{\"authorization\":\"https://api.external-system.com/auth\",\"validation\":\"https://api.external-system.com/validate\"}");
            var targetUpsert = new ExternalSynchronizationTargetUpsert
            {
                Id = "target-1",
                LogicalId = "external-system-1",
                Credentials = "{\"apiKey\":\"test123\"}",
                Urls = urls,
                ById = "user-1"
            };

            // Assert
            targetUpsert.Id.Should().Be("target-1");
            targetUpsert.LogicalId.Should().Be("external-system-1");
            targetUpsert.Credentials.Should().Be("{\"apiKey\":\"test123\"}");
            targetUpsert.Urls.Should().BeEquivalentTo(urls);
            targetUpsert.ById.Should().Be("user-1");
        }

        [Fact]
        public void GIVEN_target_filter_WHEN_properties_are_set_THEN_all_filter_conditions_should_be_accessible()
        {
            // Arrange & Act
            var filter = new ExternalSynchronizationTargetFilter
            {
                Id = "target-1",
                Id_neq = "target-2",
                Id_in = new List<string> { "target-1", "target-3" },
                Id_contains = "target",
                LogicalId = "external-system-1",
                LogicalId_in = new List<string> { "external-system-1", "external-system-2" },
                LogicalId_contains = "external",
                Urls_contains = "external-system"
            };

            // Assert
            filter.Id.Should().Be("target-1");
            filter.Id_neq.Should().Be("target-2");
            filter.Id_in.Should().Contain("target-1", "target-3");
            filter.Id_contains.Should().Be("target");
            filter.LogicalId.Should().Be("external-system-1");
            filter.LogicalId_in.Should().Contain("external-system-1", "external-system-2");
            filter.LogicalId_contains.Should().Be("external");
            filter.Urls_contains.Should().Be("external-system");
        }

        [Fact]
        public void GIVEN_service_with_repository_WHEN_service_is_created_THEN_repository_should_be_injected()
        {
            // Arrange & Act
            var service = new ExternalSynchronizationTargetService(_mockRepository.Object);

            // Assert
            service.Should().NotBeNull();
            service.Should().BeOfType<ExternalSynchronizationTargetService>();
        }

        [Fact]
        public void GIVEN_target_with_complex_credentials_WHEN_credentials_are_parsed_THEN_structure_should_be_preserved()
        {
            // Arrange
            var complexCredentialsJson = @"{
                ""auth"": {
                    ""type"": ""oauth2"",
                    ""clientId"": ""client123"",
                    ""clientSecret"": ""secret456"",
                    ""tokenUrl"": ""https://auth.external-system.com/token"",
                    ""scopes"": [""read"", ""write""]
                },
                ""endpoints"": {
                    ""webhook"": ""/webhook/cases"",
                    ""status"": ""/api/status""
                },
                ""metadata"": {
                    ""version"": ""1.2.3"",
                    ""timeout"": 30
                }
            }";

            var credentials = JToken.Parse(complexCredentialsJson);

            // Act
            var target = new ExternalSynchronizationTarget
            {
                Id = "target-123",
                LogicalId = "external-system-1",
                Credentials = credentials,
                Urls = JToken.Parse("{\"default\":\"https://api.external-system.com\"}")
            };

            // Assert
            target.Credentials["auth"]["type"].Value<string>().Should().Be("oauth2");
            target.Credentials["auth"]["scopes"].Should().HaveCount(2);
            target.Credentials["endpoints"]["webhook"].Value<string>().Should().Be("/webhook/cases");
            target.Credentials["metadata"]["timeout"].Value<int>().Should().Be(30);
        }

        [Fact]
        public void GIVEN_target_upsert_with_json_credentials_string_WHEN_creating_upsert_THEN_json_string_should_be_stored()
        {
            // Arrange
            var credentialsJson = "{\"apiKey\":\"test123\",\"secret\":\"secret456\",\"environment\":\"production\"}";

            // Act
            var upsert = new ExternalSynchronizationTargetUpsert
            {
                Id = "target-123",
                LogicalId = "external-system-1",
                Credentials = credentialsJson,
                Urls = JToken.Parse("{\"default\":\"https://api.external-system.com/webhook\"}"),
                ById = "user-1"
            };

            // Assert
            upsert.Credentials.Should().Be(credentialsJson);

            // Verify it's valid JSON by parsing it
            var parsed = JToken.Parse(upsert.Credentials);
            parsed["apiKey"].Value<string>().Should().Be("test123");
            parsed["secret"].Value<string>().Should().Be("secret456");
            parsed["environment"].Value<string>().Should().Be("production");
        }

        [Fact]
        public void GIVEN_filter_with_multiple_conditions_WHEN_combining_filters_THEN_all_conditions_should_be_set()
        {
            // Arrange & Act
            var filter = new ExternalSynchronizationTargetFilter
            {
                LogicalId_contains = "external",
                Urls_contains = "api.external",
                Id_in = new List<string> { "target-1", "target-2", "target-3" }
            };

            // Assert
            filter.LogicalId_contains.Should().Be("external");
            filter.Urls_contains.Should().Be("api.external");
            filter.Id_in.Should().HaveCount(3);
            filter.Id_in.Should().Contain("target-1", "target-2", "target-3");
        }

        [Fact]
        public void GIVEN_target_with_null_credentials_WHEN_setting_credentials_THEN_null_should_be_allowed()
        {
            // Arrange & Act
            var target = new ExternalSynchronizationTarget
            {
                Id = "target-123",
                LogicalId = "external-system-1",
                Credentials = null,
                Urls = JToken.Parse("{\"default\":\"https://api.external-system.com\"}")
            };

            // Assert
            target.Credentials.Should().BeNull();
        }

        [Fact]
        public void GIVEN_target_inheriting_from_system_object_WHEN_checking_inheritance_THEN_should_have_system_properties()
        {
            // Arrange & Act
            var target = new ExternalSynchronizationTarget
            {
                Id = "target-123",
                CreatedAt = DateTime.UtcNow,
                CreatedById = "user-1",
                LastModifiedAt = DateTime.UtcNow,
                LastModifiedById = "user-2"
            };

            // Assert
            target.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            target.CreatedById.Should().Be("user-1");
            target.LastModifiedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            target.LastModifiedById.Should().Be("user-2");
        }
    }
}