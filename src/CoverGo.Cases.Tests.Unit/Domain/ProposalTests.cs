using System;
using System.Globalization;
using CoverGo.Cases.Domain;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using Xunit;

namespace CoverGo.Cases.Tests.Unit.Domain;

public class ProposalTests
{
    [Fact]
    public void GIVEN_proposal_not_issued_WHEN_build_proposal_issued_at_jtoken_THEN_return_null()
    {
        Proposal proposal = new()
        {
            Name = "Test Proposal",
            IsIssued = false
        };
        JToken output = proposal.BuildProposalIssuedAtJProperty();
        
        output.Should().BeNull();
    }
    
    [Fact]
    public void GIVEN_issued_proposal_WHEN_build_proposal_issued_at_jtoken_THEN_return_null()
    {
        DateTime currentTime = DateTime.Now;
        string currentTimeStr = currentTime.ToString("yyyy/MM/dd HH:mm:ss", CultureInfo.InvariantCulture);
        
        Proposal proposal = new()
        {
            Name = "Test Proposal",
            IsIssued = true,
            IssuedAt = currentTime
        };
        JProperty output = proposal.BuildProposalIssuedAtJProperty();

        output.Name.Should().Be(Proposal.MetaFieldsIssuedAtKey);
        output.Value.Value<string>().Should().Be(currentTimeStr);
    }
}