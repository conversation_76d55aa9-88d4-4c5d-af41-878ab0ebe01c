<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>

        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.NET.Test.Sdk" />
        <PackageReference Include="xunit" />
        <PackageReference Include="FluentAssertions" />
        <PackageReference Include="JunitXml.TestLogger" />
        <PackageReference Include="xunit.runner.visualstudio">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Moq" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\CoverGo.Cases.Application\CoverGo.Cases.Application.csproj" />
      <ProjectReference Include="..\CoverGo.Cases.Domain\CoverGo.Cases.Domain.csproj" />
    </ItemGroup>

</Project>
