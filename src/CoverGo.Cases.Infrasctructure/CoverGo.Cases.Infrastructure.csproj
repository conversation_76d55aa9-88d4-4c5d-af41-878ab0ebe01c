<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" />
    <PackageReference Include="CloudNative.CloudEvents" />
    <PackageReference Include="CoverGo.Applications.Infrastructure" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="IdentityModel" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Cases.Domain\CoverGo.Cases.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="MessageBroker\" />
  </ItemGroup>

</Project>
