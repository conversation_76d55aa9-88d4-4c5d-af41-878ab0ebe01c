﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace CoverGo.Cases.Infrastructure.Migrations
{
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Cases",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(255) CHARACTER SET utf8mb4", nullable: false),
                    CaseNumber = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Name = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Description = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Source = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Facts = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Notes = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    HolderId = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    OtherHolderIds = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    InsuredIds = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Status = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Proposals = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Stakeholders = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    BeneficiaryEligibilities = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    PaymentInfos = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    ComponentId = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    FieldsSchemaId = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    WorkflowSchemaId = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    LastModifiedAt = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatedById = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    LastModifiedById = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cases", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Configs",
                columns: table => new
                {
                    Type = table.Column<string>(type: "varchar(255) CHARACTER SET utf8mb4", nullable: false),
                    Format = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Arguments = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Configs", x => x.Type);
                });

            migrationBuilder.CreateTable(
                name: "Events",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(255) CHARACTER SET utf8mb4", nullable: false),
                    CaseId = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Values = table.Column<string>(type: "longtext CHARACTER SET utf8mb4", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime(6)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Events", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "Configs",
                columns: new[] { "Type", "Arguments", "Format" },
                values: new object[] { "createCase", "[{\"order\":0,\"format\":\"yyMMdd\",\"type\":\"currentDate\",\"addOpOnResult\":0,\"splitIndex\":0,\"currentIncrement\":0},{\"order\":1,\"format\":\"D2\",\"type\":\"createdCasesOfCurrentDay\",\"addOpOnResult\":0,\"splitIndex\":0,\"currentIncrement\":0}]", "APP-{0}{1}" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Cases");

            migrationBuilder.DropTable(
                name: "Configs");

            migrationBuilder.DropTable(
                name: "Events");
        }
    }
}
