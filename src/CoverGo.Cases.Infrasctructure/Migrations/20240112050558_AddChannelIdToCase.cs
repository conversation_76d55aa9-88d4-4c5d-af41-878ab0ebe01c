﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CoverGo.Cases.Infrastructure.Migrations
{
    public partial class AddChannelIdToCase : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ChannelId",
                table: "Cases",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChannelId",
                table: "Cases");
        }
    }
}
