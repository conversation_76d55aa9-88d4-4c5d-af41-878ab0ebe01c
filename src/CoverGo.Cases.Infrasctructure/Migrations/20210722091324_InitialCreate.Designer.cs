﻿// <auto-generated />
using System;
using CoverGo.Cases.Infrastructure.Adapters.EF;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace CoverGo.Cases.Infrastructure.Migrations
{
    [DbContext(typeof(CasesDbContext))]
    [Migration("20210722091324_InitialCreate")]
    partial class InitialCreate
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 64)
                .HasAnnotation("ProductVersion", "5.0.3");

            modelBuilder.Entity("CoverGo.Cases.Domain.Case", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255) CHARACTER SET utf8mb4");

                    b.Property<string>("BeneficiaryEligibilities")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("CaseNumber")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("ComponentId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Description")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Facts")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("FieldsSchemaId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("HolderId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("InsuredIds")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("LastModifiedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LastModifiedById")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Name")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("OtherHolderIds")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("PaymentInfos")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Proposals")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Source")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Stakeholders")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Status")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("WorkflowSchemaId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.ToTable("Cases");
                });

            modelBuilder.Entity("CoverGo.Cases.Domain.CaseEvent", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255) CHARACTER SET utf8mb4");

                    b.Property<string>("CaseId")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Values")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Id");

                    b.ToTable("Events");
                });

            modelBuilder.Entity("CoverGo.Cases.Domain.ReferenceGeneratorConfig", b =>
                {
                    b.Property<string>("Type")
                        .HasColumnType("varchar(255) CHARACTER SET utf8mb4");

                    b.Property<string>("Arguments")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.Property<string>("Format")
                        .HasColumnType("longtext CHARACTER SET utf8mb4");

                    b.HasKey("Type");

                    b.ToTable("Configs");

                    b.HasData(
                        new
                        {
                            Type = "createCase",
                            Arguments = "[{\"order\":0,\"format\":\"yyMMdd\",\"type\":\"currentDate\",\"addOpOnResult\":0,\"splitIndex\":0,\"currentIncrement\":0},{\"order\":1,\"format\":\"D2\",\"type\":\"createdCasesOfCurrentDay\",\"addOpOnResult\":0,\"splitIndex\":0,\"currentIncrement\":0}]",
                            Format = "APP-{0}{1}"
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
