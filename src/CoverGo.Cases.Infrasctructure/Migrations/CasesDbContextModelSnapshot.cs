﻿// <auto-generated />
using System;
using CoverGo.Cases.Infrastructure.Adapters.EF;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CoverGo.Cases.Infrastructure.Migrations
{
    [DbContext(typeof(CasesDbContext))]
    partial class CasesDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.HasCharSet(modelBuilder, "utf8mb4", DelegationModes.ApplyToAll);

            modelBuilder.Entity("CoverGo.Cases.Domain.Case", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)");

                    b.Property<int?>("AccessPolicy")
                        .HasColumnType("int");

                    b.Property<string>("BeneficiaryEligibilities")
                        .HasColumnType("longtext");

                    b.Property<string>("CaseNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("ChannelId")
                        .HasColumnType("longtext");

                    b.Property<string>("ComponentId")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("longtext");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<string>("Facts")
                        .HasColumnType("longtext");

                    b.Property<string>("FieldsSchemaId")
                        .HasColumnType("longtext");

                    b.Property<string>("HolderId")
                        .HasColumnType("longtext");

                    b.Property<string>("InsuredIds")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("LastModifiedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LastModifiedById")
                        .HasColumnType("longtext");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<string>("OtherHolderIds")
                        .HasColumnType("longtext");

                    b.Property<string>("PaymentInfos")
                        .HasColumnType("longtext");

                    b.Property<string>("Proposals")
                        .HasColumnType("longtext");

                    b.Property<string>("Source")
                        .HasColumnType("longtext");

                    b.Property<string>("Stakeholders")
                        .HasColumnType("longtext");

                    b.Property<string>("Status")
                        .HasColumnType("longtext");

                    b.Property<string>("WorkflowSchemaId")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("Cases");
                });

            modelBuilder.Entity("CoverGo.Cases.Domain.CaseEvent", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("CaseId")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Values")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("Events");
                });

            modelBuilder.Entity("CoverGo.Cases.Domain.ReferenceGeneratorConfig", b =>
                {
                    b.Property<string>("Type")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("Arguments")
                        .HasColumnType("longtext");

                    b.Property<string>("Format")
                        .HasColumnType("longtext");

                    b.HasKey("Type");

                    b.ToTable("Configs");

                    b.HasData(
                        new
                        {
                            Type = "createCase",
                            Arguments = "[{\"order\":0,\"format\":\"yyMMdd\",\"type\":\"currentDate\",\"addOpOnResult\":0,\"splitIndex\":0,\"currentIncrement\":0},{\"order\":1,\"format\":\"D2\",\"type\":\"createdCasesOfCurrentDay\",\"addOpOnResult\":0,\"splitIndex\":0,\"currentIncrement\":0}]",
                            Format = "APP-{0}{1}"
                        });
                });
#pragma warning restore 612, 618
        }
    }
}
