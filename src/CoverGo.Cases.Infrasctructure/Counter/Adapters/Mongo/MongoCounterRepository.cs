using MongoDB.Driver;
using System;
using CoverGo.Applications.Infrastructure;
using System.Threading.Tasks;
using System.Threading;
using CoverGo.Cases.Domain.Counters;

namespace CoverGo.Products.Infrastructure.Counters.Adapters.Mongo
{
    public class MongoCounterRepository : CoverGoGenericMongoRepositoryBase<Counter, CounterUpsert, CounterFilter>, ICounterRepository
    {
        protected override string DbName => "cases";

        protected override string CollectionNameGet(string tenantId) => $"{tenantId}-counters";

        public async Task<long> GetNextAsync(
            string tenantId,
            CounterUpsert upsert,
            CancellationToken cancellationToken)
        {
            IMongoCollection<Counter> collection = MongoCollectionGet(tenantId);

            FilterDefinitionBuilder<Counter> filterBuilder = Builders<Counter>.Filter;
            UpdateDefinitionBuilder<Counter> updateBuilder = Builders<Counter>.Update;

            DateTime now = DateTime.UtcNow;

            Counter update = await collection.FindOneAndUpdateAsync(
                filterBuilder.Eq(x => x.Scope, upsert.Scope),
                updateBuilder
                    .SetOnInsert(x => x.Id, upsert.Id ?? Guid.NewGuid().ToString())
                    .SetOnInsert(x => x.CreatedAt, now)
                    .SetOnInsert(x => x.CreatedById, upsert.ById)
                    .Set(x => x.LastModifiedById, upsert.ById)
                    .Set(x => x.LastModifiedAt, now)
                    .SetOnInsert(x => x.Scope, upsert.Scope)
                    .Inc(x => x.Counters[upsert.CounterKey], 1),
                new FindOneAndUpdateOptions<Counter, Counter>
                {
                    IsUpsert = true,
                    ReturnDocument = ReturnDocument.After
                }, cancellationToken);

            return update.Counters[upsert.CounterKey];
        }

        protected override Counter EntityCreate(CounterUpsert create) => throw new NotSupportedException();

        protected override UpdateDefinition<Counter> EntityUpdate(CounterUpsert update) => throw new NotSupportedException();
    }
}