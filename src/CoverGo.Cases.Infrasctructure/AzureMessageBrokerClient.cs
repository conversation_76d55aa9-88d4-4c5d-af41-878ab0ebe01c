using Azure.Messaging.ServiceBus;
using CoverGo.Cases.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure;


public class AzureMessageBrokerClient : IMessageBrokerClient
{
    public bool? IsEnabled { get; }
    private readonly ILogger<AzureMessageBrokerClient> _logger;
    private readonly string _connectionString;
    private readonly string _queueOrTopicName;
    
    public AzureMessageBrokerClient(bool? isEnabled, string connectionString, string queueOrTopicName,
        ILogger<AzureMessageBrokerClient> logger)
    {
        IsEnabled = isEnabled;
        _logger = logger;
        _connectionString = connectionString;
        _queueOrTopicName = queueOrTopicName;
    }

    public async Task SendMessageAsync<T>(T message, string tenantId, string eventType)
    {
        try
        {
            if (IsEnabled == null || IsEnabled == false)
            {
                return;
            }
            
            await using var client = new ServiceBusClient(_connectionString);
            var sender = client.CreateSender(_queueOrTopicName);

            var cloudEventData = new
                                 {
                                     Source = "covergo",
                                     Type = eventType,
                                     SpecVersion = "1.0",
                                     Id = Guid.NewGuid().ToString(),
                                     Time = DateTime.UtcNow,
                                     DataContentType = "application/json",
                                     Data = message,
                                     extensions = new
                                                  {
                                                      tenantId
                                                  }
                                 };

            string messageBody = JsonSerializer.Serialize(cloudEventData);
            var serviceBusMessage = new ServiceBusMessage(Encoding.UTF8.GetBytes(messageBody))
                                    {
                                        ContentType = "application/cloudevents+json; charset=UTF-8"
                                    };
            await sender.SendMessageAsync(serviceBusMessage);
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
        }
    }
}