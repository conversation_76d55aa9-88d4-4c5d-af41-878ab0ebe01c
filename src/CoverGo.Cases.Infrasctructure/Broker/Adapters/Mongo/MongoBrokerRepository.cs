using System;
using System.Linq.Expressions;
using CoverGo.Applications.Infrastructure;
using CoverGo.Cases.Domain.Brokers;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.Brokers.Adapters.Mongo
{
    public class MongoBrokerRepository : CoverGoGenericMongoRepositoryBase<Broker, BrokerUpsert, BrokerFilter>, IBrokerRepository
    {
        protected override string DbName => "cases";

        protected override string CollectionNameGet(string tenantId) => $"{tenantId}-brokers";

        protected override Broker EntityCreate(BrokerUpsert create) => new()
        {
            Id = create.Id ?? Guid.NewGuid().ToString(),
            Code = create.Code ?? default,
            NormalizedCode = create.Code?.ToUpperInvariant() ?? default,
            Name = create.Name ?? default,
            NormalizedName = create.Name?.ToUpperInvariant() ?? default,
            Channel = create.Channel ?? default,
            Group = create.Group ?? default,
            ContactPerson = create.ContactPerson ?? default,
            Description = create.Description ?? default,
            ContactPersonTelNo = create.ContactPersonTelNo ?? default,
            ContactPersonFaxNo = create.ContactPersonFaxNo ?? default,
            ContactPersonEmail = create.ContactPersonEmail ?? default,
            Fields = JToken.Parse(create.Fields ?? "{}"),
            CreatedAt = DateTime.UtcNow,
            CreatedById = create.ById
        };

        protected override UpdateDefinition<Broker> EntityUpdate(BrokerUpsert update)
        {
            UpdateDefinition<Broker> result = UpdateField(null, x => x.LastModifiedAt, DateTime.UtcNow);

            if (update.ById != null) result = UpdateField(result, x => x.LastModifiedById, update.ById);
            if (update.Code != null) {
                result = UpdateField(result, x => x.Code, update.Code);
                result = UpdateField(result, x => x.NormalizedCode, update.Code?.ToUpperInvariant());
            }
            if (update.Name != null) {
                result = UpdateField(result, x => x.Name, update.Name);
                result = UpdateField(result, x => x.NormalizedName, update.Name?.ToUpperInvariant());
            }
            if (update.Description != null) result = UpdateField(result, x => x.Description, update.Description);
            if (update.Channel != null) result = UpdateField(result, x => x.Channel, update.Channel);
            if (update.Group != null) result = UpdateField(result, x => x.Group, update.Group);
            if (update.ContactPerson != null) result = UpdateField(result, x => x.ContactPerson, update.ContactPerson);
            if (update.ContactPersonTelNo != null) result = UpdateField(result, x => x.ContactPersonTelNo, update.ContactPersonTelNo);
            if (update.ContactPersonFaxNo != null) result = UpdateField(result, x => x.ContactPersonFaxNo, update.ContactPersonFaxNo);
            if (update.ContactPersonEmail != null) result = UpdateField(result, x => x.ContactPersonEmail, update.ContactPersonEmail);
            if (update.Fields != null) result = UpdateField(result, x => x.Fields, JToken.Parse(update.Fields));

            return result;

            UpdateDefinition<Broker> UpdateField<TField>(UpdateDefinition<Broker> updateDefinition, Expression<Func<Broker, TField>> field, TField value)
            {
                return updateDefinition == null ? Update.Set(field, value) : updateDefinition.Set(field, value);
            }
        }
    }
}