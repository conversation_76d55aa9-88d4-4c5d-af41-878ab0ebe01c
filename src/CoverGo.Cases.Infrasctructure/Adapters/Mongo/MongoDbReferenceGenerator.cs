﻿using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.Counters;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using MongoDB.Driver;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MongoDbReferenceGenerator : IReferenceGenerator
    {
        public string ProviderId { get; } = "mongoDb";
        private const string dbName = "cases";

        private readonly IGatewayService _gatewayService;
        private readonly ICounterRepository _counterRepository;
        private readonly MongoDbEventStore _eventStore;

        public MongoDbReferenceGenerator(IGatewayService gatewayService, ICounterRepository counterRepository, MongoDbEventStore eventStore)
        {
            _gatewayService = gatewayService;
            _counterRepository = counterRepository;
            _eventStore = eventStore;
        }

        public async Task<string> GenerateAsync(string tenantId, string type, JToken input, string accessToken = null, GraphQlVariables graphQlVariables = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<ReferenceGeneratorConfig> clientCollection = db.GetCollection<ReferenceGeneratorConfig>($"{tenantId}-refgen");
            FilterDefinition<ReferenceGeneratorConfig> filter = Builders<ReferenceGeneratorConfig>.Filter.Eq(p => p.Type, type);
            ReferenceGeneratorConfig config = await clientCollection.Find(filter).FirstOrDefaultAsync(cancellationToken);
            if (config == null)
                return AlphaNumericStringGenerator.GetRandomUppercaseAlphaNumericValue(6);

            JObject variables = null;
            if (graphQlVariables != null)
                variables = JObject.FromObject(graphQlVariables, new JsonSerializer { ContractResolver = new CamelCasePropertyNamesContractResolver(), NullValueHandling = NullValueHandling.Ignore });

            string[] formattedArguments = new string[config.Arguments.Max(a => a.Order) + 1];
            for (int i=0; i<config.Arguments.Count; i++)
            {
                FormatArgument argument = config.Arguments[i];
                string formattedArgument = null;
                DateTime currentDate = DateTime.Now;

                if (argument.Type == "graphql")
                {
                    JToken jToken = await _gatewayService.GetContentAsync(accessToken, argument.GraphQlQuery, variables, cancellationToken);
                    if (jToken == null)
                        continue;

                    //// HACK for jsonpath length
                    //bool hacked = false;
                    //if (argument.JsonPath.EndsWith(".length"))
                    //{
                    //    hacked = true;
                    //    argument.JsonPath = argument.JsonPath.Replace(".length", "");
                    //}
                    //// HACK for jsonpath length

                    JToken targetJToken = jToken?.SelectTokens(argument.JsonPath).FirstOrDefault();

                    //// HACK for jsonpath length
                    //if (hacked)
                    //{
                    //    targetJToken = targetJToken.Count();
                    //}
                    //// HACK for jsonpath length

                    if (argument.ExpectedType == "date")
                    {
                        if (targetJToken == null)
                            continue;

                        DateTime date = targetJToken.Value<DateTime>();
                        formattedArgument = date.ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "int")
                    {
                        int integer = (targetJToken?.Value<int>() ?? -1) + argument.AddOpOnResult;
                        formattedArgument = integer.ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "string")
                    {
                        string theString = targetJToken?.Value<string>() ?? "";
                        if (argument.SplitOn == null)
                            formattedArgument = theString;
                        else
                        {
                            try
                            {
                                formattedArgument = theString.Split(argument.SplitOn)[argument.SplitIndex];
                            }
                            catch { }
                        }
                    }

                    else
                        formattedArgument = targetJToken.ToString();
                }

                else if (argument.Type == "incrementor") //this being used for TCB for case number, changing this code with proper locking mechanism would slow down creating case for tcb (very bad!!)
                {
                    int newIncrement = ++argument.CurrentIncrement;

                    FilterDefinition<ReferenceGeneratorConfig> argFilter = Builders<ReferenceGeneratorConfig>.Filter.And(filter,
                        Builders<ReferenceGeneratorConfig>.Filter.ElemMatch(x => x.Arguments, f => f.Order == argument.Order));

                    UpdateDefinition<ReferenceGeneratorConfig> update = Builders<ReferenceGeneratorConfig>.Update.Set(config => config.Arguments[-1].CurrentIncrement, newIncrement);
                    await clientCollection.UpdateOneAsync(argFilter, update, null, cancellationToken);

                    formattedArgument = newIncrement.ToString(argument.Format);
                }

                else if (argument.Type == "input")
                {
                    JToken targetJToken = argument.JsonPath != null ? input?.SelectTokens(argument.JsonPath).FirstOrDefault() : null;

                    if (argument.ExpectedType == "date")
                    {
                        if (targetJToken == null)
                            continue;

                        DateTime date = targetJToken.Value<DateTime>();
                        formattedArgument = date.ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "dateTimeUtcNow")
                    {
                        var timeSpan = new TimeSpan();
                        if (argument.DateTimeOffset != null)
                        {
                            var durationSplits = argument.DateTimeOffset?.Split(" ").Select(s => int.Parse(s))?.ToList();

                            timeSpan = new TimeSpan(durationSplits.ElementAtOrDefault(0), durationSplits.ElementAtOrDefault(1), durationSplits.ElementAtOrDefault(2), durationSplits.ElementAtOrDefault(3), durationSplits.ElementAtOrDefault(4));
                        }

                        formattedArgument = DateTime.UtcNow.Add(timeSpan).ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "int")
                    {
                        int integer = (targetJToken?.Value<int>() ?? 0) + argument.AddOpOnResult;
                        formattedArgument = integer.ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "string")
                    {
                        string theString = targetJToken?.Value<string>() ?? "";
                        formattedArgument = argument.SplitOn == null
                            ? theString
                            : theString.Split(argument.SplitOn)[argument.SplitIndex];
                    }

                    else
                        formattedArgument = targetJToken.ToString();
                }

                else if (argument.Type == "currentDate")
                {
                    formattedArgument = currentDate.ToString(argument.Format);
                }

                else if (argument.Type == "createdCasesOfCurrentDay")
                {
                    long count = await RefGenArgumentExtensions.GetCasesOfTodayCount(_eventStore, tenantId, cancellationToken);
                    count++;

                    formattedArgument = count.ToString(argument.Format);
                }

                else if (argument.Type == "remainderOfFirstSevenDigitsSumDividedByTen")
                {
                    string firstSevenDigits = formattedArguments[argument.Order - 1];
                    if (ContainsDigitFour(firstSevenDigits, ref i, ref formattedArguments))
                        continue;
                    
                    int sumOfFirstSevenDigits = firstSevenDigits
                        .ToCharArray()
                        .ToList()
                        .Select(c => int.Parse(c.ToString()))
                        .ToArray()
                        .Sum();

                    formattedArgument = (sumOfFirstSevenDigits % 10).ToString();
                    if (ContainsDigitFour(formattedArgument, ref i, ref formattedArguments))
                        continue;
                }

                else if (argument.Type == "dictionary")
                {
                    JToken targetJToken = argument.JsonPath != null ? input?.SelectTokens(argument.JsonPath).FirstOrDefault() : null;
                    if (targetJToken == null)
                        continue;

                    switch (argument.ExpectedType)
                    {
                        case "JObjectString":
                            {
                                string targetAsString = targetJToken?.Value<string>();
                                JObject property = targetAsString != null ? JObject.Parse(targetAsString) : null;
                                string key = property?.SelectToken(argument.Format)?.Value<string>();
                                formattedArgument = key != null ? argument.Dictionary?.GetValueOrDefault(key) : null;
                                if (formattedArgument == null)
                                    continue;
                            }
                            break;
                    }
                }

                else if (argument.Type == "sharedCounter" && !string.IsNullOrWhiteSpace(argument.SharedCounterKey))
                {
                    string counterKey = argument.SharedCounterKey;
                    string scope = !string.IsNullOrWhiteSpace(argument.SharedCounterScope) ? argument.SharedCounterScope : counterKey;
                    long newValue = await _counterRepository.GetNextAsync(tenantId, new CounterUpsert { Scope = scope, CounterKey = counterKey }, cancellationToken);
                    formattedArgument = newValue.ToString(argument.Format);
                }

                formattedArguments[argument.Order] = formattedArgument;
            }

            foreach (FormatArgument argument in config.Arguments.OrderBy(a => a.Order))
            {
                switch (argument.Type)
                {
                    case "positionalCounter":
                        string counterKey = string.Format($"{type}::{config.Format}", formattedArguments);
                        long newValue = await _counterRepository.GetNextAsync(tenantId, new CounterUpsert { Scope = "positionalCounter", CounterKey = counterKey }, cancellationToken);
                        string formattedArgument = newValue.ToString(argument.Format);
                        formattedArguments[argument.Order] = formattedArgument;
                        break;
                }
            }

            string formattedNumber = string.Format(config.Format, formattedArguments);
            return formattedNumber;
        }

        public bool ContainsDigitFour(string argument, ref int loopCount, ref string[] formattedArguments)
        {
            if (!argument.Contains("4")) return false;
            Array.Clear(formattedArguments, 0, formattedArguments.Length);
            loopCount = -1;

            return true;

        }
    }
}