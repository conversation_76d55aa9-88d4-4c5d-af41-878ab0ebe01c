using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using CoverGo.Applications.Infrastructure;
using CoverGo.Cases.Domain;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MongoExternalSynchronizationRepository : CoverGoGenericMongoRepositoryBase<ExternalSynchronization, ExternalSynchronizationUpsert, ExternalSynchronizationFilter>, IExternalSynchronizationRepository
    {
        protected override string DbName => "cases";

        protected override string CollectionNameGet(string tenantId) => $"{tenantId}-external-synchronizations";

        protected override ExternalSynchronization EntityCreate(ExternalSynchronizationUpsert create) => new()
        {
            Id = create.Id ?? Guid.NewGuid().ToString(),
            CaseId = create.CaseId ?? default,
            OfferId = create.OfferId ?? default,
            TargetLogicalId = create.TargetLogicalId ?? default,
            Status = create.Status ?? default,
            Payload = JToken.Parse(create.Payload ?? "{}"),
            Response = JToken.Parse(create.Response ?? "{}"),
            Errors = ParseErrors(create.Errors),
            LastSuccessfullySyncAt = create.LastSuccessfullySyncAt,
            CreatedAt = DateTime.UtcNow,
            CreatedById = create.ById
        };

        protected override UpdateDefinition<ExternalSynchronization> EntityUpdate(ExternalSynchronizationUpsert update)
        {
            UpdateDefinition<ExternalSynchronization> result = UpdateField(null, x => x.LastModifiedAt, DateTime.UtcNow);

            if (update.ById != null) result = UpdateField(result, x => x.LastModifiedById, update.ById);
            if (update.CaseId != null) result = UpdateField(result, x => x.CaseId, update.CaseId);
            if (update.OfferId != null) result = UpdateField(result, x => x.OfferId, update.OfferId);
            if (update.TargetLogicalId != null) result = UpdateField(result, x => x.TargetLogicalId, update.TargetLogicalId);
            if (update.Status != null) result = UpdateField(result, x => x.Status, update.Status);
            if (update.Payload != null) result = UpdateField(result, x => x.Payload, JToken.Parse(update.Payload));
            if (update.Response != null) result = UpdateField(result, x => x.Response, JToken.Parse(update.Response));
            if (update.Errors != null) result = UpdateField(result, x => x.Errors, ParseErrors(update.Errors));
            if (update.LastSuccessfullySyncAt.HasValue) result = UpdateField(result, x => x.LastSuccessfullySyncAt, update.LastSuccessfullySyncAt);

            return result;

            UpdateDefinition<ExternalSynchronization> UpdateField<TField>(UpdateDefinition<ExternalSynchronization> updateDefinition, Expression<Func<ExternalSynchronization, TField>> field, TField value)
            {
                return updateDefinition == null ? Update.Set(field, value) : updateDefinition.Set(field, value);
            }
        }

        private static List<ExternalSynchronizationError> ParseErrors(string errorsJson)
        {
            if (string.IsNullOrEmpty(errorsJson))
                return new List<ExternalSynchronizationError>();

            try
            {
                return Newtonsoft.Json.JsonConvert.DeserializeObject<List<ExternalSynchronizationError>>(errorsJson)
                       ?? new List<ExternalSynchronizationError>();
            }
            catch
            {
                return new List<ExternalSynchronizationError>();
            }
        }
    }
}