using System;
using System.Linq.Expressions;
using CoverGo.Applications.Infrastructure;
using CoverGo.Cases.Domain;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MongoExternalSynchronizationTargetRepository : CoverGoGenericMongoRepositoryBase<ExternalSynchronizationTarget, ExternalSynchronizationTargetUpsert, ExternalSynchronizationTargetFilter>, IExternalSynchronizationTargetRepository
    {
        protected override string DbName => "cases";

        protected override string CollectionNameGet(string tenantId) => $"{tenantId}-external-synchronization-targets";

        protected override ExternalSynchronizationTarget EntityCreate(ExternalSynchronizationTargetUpsert create) => new()
        {
            Id = create.Id ?? Guid.NewGuid().ToString(),
            LogicalId = create.LogicalId ?? default,
            Credentials = JToken.Parse(create.Credentials ?? "{}"),
            Urls = create.Urls ?? JToken.FromObject(new {}),
            CreatedAt = DateTime.UtcNow,
            CreatedById = create.ById
        };

        protected override UpdateDefinition<ExternalSynchronizationTarget> EntityUpdate(ExternalSynchronizationTargetUpsert update)
        {
            UpdateDefinition<ExternalSynchronizationTarget> result = UpdateField(null, x => x.LastModifiedAt, DateTime.UtcNow);

            if (update.ById != null) result = UpdateField(result, x => x.LastModifiedById, update.ById);
            if (update.LogicalId != null) result = UpdateField(result, x => x.LogicalId, update.LogicalId);
            if (update.Credentials != null) result = UpdateField(result, x => x.Credentials, JToken.Parse(update.Credentials));
            if (update.Urls != null) result = UpdateField(result, x => x.Urls, update.Urls);

            return result;

            UpdateDefinition<ExternalSynchronizationTarget> UpdateField<TField>(UpdateDefinition<ExternalSynchronizationTarget> updateDefinition, Expression<Func<ExternalSynchronizationTarget, TField>> field, TField value)
            {
                return updateDefinition == null ? Update.Set(field, value) : updateDefinition.Set(field, value);
            }
        }
    }
}