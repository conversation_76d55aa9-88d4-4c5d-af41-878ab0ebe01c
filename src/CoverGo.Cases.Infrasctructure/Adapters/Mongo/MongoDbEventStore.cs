﻿using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using MongoDB.Bson;
using MongoDB.Driver;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;

using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Configuration;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MongoDbEventStore : IEventStore
    {
        public string ProviderId { get; } = "mongoDb";
        private const string dbName = "cases";

        private readonly JsonSerializerSettings _settings;

        public MongoDbEventStore()
        {
            _settings = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, ContractResolver = new CamelCasePropertyNamesContractResolver() };
            _settings.Converters.Add(new StringEnumConverter());
        }

        public async Task<Result> AddEventAsync(string tenantId, CaseEvent @event, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<CaseEvent> clientCollection = db.GetCollection<CaseEvent>($"{tenantId}-events");
            await clientCollection.Indexes.CreateManyAsync(new[]
            {
                new CreateIndexModel<CaseEvent>(Builders<CaseEvent>.IndexKeys.Ascending(c => c.CaseId)),
                new CreateIndexModel<CaseEvent>(Builders<CaseEvent>.IndexKeys.Ascending(c => c.Timestamp))
            });

            await clientCollection.InsertOneAsync(@event, cancellationToken:cancellationToken);

            return new Result { Status = "success" };
        }

        public async Task<IEnumerable<CaseEvent>> GetEventsAsync(
            string tenantId,
            IEnumerable<CaseEventType> types,
            IEnumerable<string> caseIds,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<CaseEvent> clientCollection = db.GetCollection<CaseEvent>($"{tenantId}-events");
            FilterDefinition<CaseEvent> mongoFilter = FilterDefinition<CaseEvent>.Empty;  
            if (types != null)
                mongoFilter &= Builders<CaseEvent>.Filter.In(c => c.Type, types);
            if (caseIds != null)
                mongoFilter &= Builders<CaseEvent>.Filter.In(c => c.CaseId, caseIds);
            if (fromDate.HasValue)
                mongoFilter &= Builders<CaseEvent>.Filter.Gte(c => c.Timestamp, fromDate.Value);
            if (toDate.HasValue)
                mongoFilter &= Builders<CaseEvent>.Filter.Lte(c => c.Timestamp, toDate.Value);

            IEnumerable<CaseEvent> events = await clientCollection
               .Find(mongoFilter)
               .SortBy(e => e.Timestamp)
               .ToListAsync(cancellationToken);

            return events;
        }

        public async Task<Result> DeleteInvalidEventAsync(string tenantId, string eventId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<CaseEvent> clientCollection = db.GetCollection<CaseEvent>($"{tenantId}-events");
            var ret = await clientCollection.DeleteOneAsync(e => e.Id == eventId, cancellationToken: cancellationToken);
            return ret.DeletedCount > 0 ? Result.Success() : Result.Failure("Event not found");
        }

        public async Task<IEnumerable<CaseEvent>> GetCaseEventsStartingWith(string tenantId, string startsWith, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<CaseEvent> clientCollection = db.GetCollection<CaseEvent>($"{tenantId}-events");

            IEnumerable<CaseEvent> events = await clientCollection.Find(
                Builders<CaseEvent>.Filter.Regex(e => e.CaseId, new BsonRegularExpression(new Regex($"^{startsWith}")))).ToListAsync(cancellationToken);

            return events;
        }
    }
}
