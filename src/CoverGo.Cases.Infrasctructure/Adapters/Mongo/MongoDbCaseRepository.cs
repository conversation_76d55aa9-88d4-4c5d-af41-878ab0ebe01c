﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Helpers;
using CoverGo.Cases.Domain;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Case = CoverGo.Cases.Domain.Case;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MongoDbCaseRepository : ICaseRepository
    {
        public string ProviderId { get; } = "mongoDb";
        private const string dbName = "cases";

        private readonly MongoDbOfferRepository _offerRepository;
        private readonly JsonSerializer _jsonSerializer;

        public MongoDbCaseRepository(MongoDbOfferRepository offerRepository)
        {
            _offerRepository = offerRepository;
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
                NullValueHandling = NullValueHandling.Ignore,
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
        }

        public async Task<IEnumerable<Case>> GetAsync(string tenantId, CaseWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");

            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IFindFluent<Case, Case> find = clientCollection.Find(mongoFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Case>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Case>.Sort.Descending(orderBy.FieldName));

            return await PopulateOfferData(tenantId, await find.Skip(skip).Limit(first).ToListAsync(cancellationToken), cancellationToken);
        }

        public async Task<IEnumerable<JToken>> GetReportAsync(string tenantId, CaseWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");

            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            FilterDefinition<Case> bsonFilter = mongoFilter.Render(BsonSerializer.SerializerRegistry.GetSerializer<Case>(), BsonSerializer.SerializerRegistry).ToJson();

            IFindFluent<Case, Case> find = clientCollection.Find(bsonFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Case>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Case>.Sort.Descending(orderBy.FieldName));

            List<Case> bsonDocuments = await PopulateOfferData(tenantId, await find.Skip(skip).Limit(first).ToListAsync(cancellationToken), cancellationToken);

            List<CasesReport> result;
            if (tenantId == "tcb" || tenantId == "tcb_uat")
                result = GetTcbReportCasesField(bsonDocuments);
            else
                result = GetReportCasesField(bsonDocuments);

            return result.Select(d => JToken.Parse(JsonConvert.SerializeObject(d)));
        }

        private static List<CasesReport> GetTcbReportCasesField(List<Case> objectList)
        {
            var casesReport = new List<CasesReport>();
            var dateAggDataList = objectList.GroupBy(t => t.CreatedAt.Date).ToList();
            var caseReportList = new List<TcbReport>();
            foreach (IGrouping<DateTime, Case> dateAggData in dateAggDataList)
            {
                var sourceAggData = dateAggData.GroupBy(c => c.Source).ToList();
                foreach (IGrouping<string, Case> data in sourceAggData)
                {
                    IEnumerable<Case> listConversationApe = data.Where(x =>
                        x.Facts.Any(f =>
                            f.Type == "conversationResult" && (f.Value?.ToString() == "referToTIS" ||
                                                               f.Value?.ToString() == "agreeToApply")) &&
                        x.Facts.Any(a => a.Type == "annualPremium" && a.Value != null)).ToList();

                    caseReportList.Add(new TcbReport
                    {
                        Source = data.First().Source ?? "",
                        CreatedDate = DateTime.Parse(dateAggData.Key.Date.ToString(CultureInfo.CurrentCulture)),
                        NumberOfCases = data.Count(),
                        NumberOfCompletedCases = data.Count(x => x.Status == "COMPLETED"),
                        LeadGcm = data.Count(x => x.Facts.Any(f => f.Type == "leadGCM" && f.Value != null && f.Value?.ToString().Trim(' ') != string.Empty)),
                        Premium = (double)listConversationApe?.Sum(x => (double)x.Facts.FirstOrDefault(a => a.Type == "annualPremium")?.Value),
                        TotalConversationAPE = listConversationApe.Count()
                    });
                }
            }

            casesReport.Add(new CasesReport { ReportFields = JsonConvert.SerializeObject(caseReportList) });
            return casesReport;
        }

        private static List<CasesReport> GetReportCasesField(List<Case> objectList)
        {
            var casesReport = new List<CasesReport>();
            var dateAggDataList = objectList.GroupBy(t => t.CreatedAt.Date).ToList();
            var caseReportList = dateAggDataList.Select(dateAggData
                =>
            {
                var report = new BaseReport
                {
                    NumberOfCases = dateAggData.Count(),
                    NumberOfCompletedCases = dateAggData.Count(x => x.Status == "COMPLETED"),
                    CreatedDate = dateAggData.Key.Date,
                };
                return report;
            }).ToList();

            casesReport.Add(new CasesReport { ReportFields = JsonConvert.SerializeObject(caseReportList) });
            return casesReport;
        }

        public async Task<IEnumerable<string>> GetIdsAsync(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            // where
            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");
            IFindFluent<Case, Case> find = clientCollection.Find(mongoFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Case>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Case>.Sort.Descending(orderBy.FieldName));

            List<string> ids = await find.Project(p => p.Id).Skip(skip).Limit(first).ToListAsync(cancellationToken);
            return ids;
        }

        public IEnumerable<string> GetIds(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            // where
            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");
            IFindFluent<Case, Case> find = clientCollection.Find(mongoFilter);

            // orderBy
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Case>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Case>.Sort.Descending(orderBy.FieldName));

            IEnumerable<string> ids = find.Project(p => p.Id).Skip(skip).Limit(first).ToEnumerable();
            return ids;
        }

        public async Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            // where
            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");
            IFindFluent<Case, Case> find = clientCollection.Find(mongoFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Case>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Case>.Sort.Descending(orderBy.FieldName));


            var casesProposalIds = await find.Project(@case => @case.Proposals.Select(proposal => proposal.Id)).Skip(skip).Limit(first).ToListAsync(cancellationToken);

            IEnumerable<string> ids = casesProposalIds.SelectMany(x => x);
            return ids;
        }

        public async Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            // where
            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");
            IFindFluent<Case, Case> find = clientCollection.Find(mongoFilter);

            // orderby
            if (orderBy != null)
                find = orderBy.Type == OrderByType.ASC
                    ? find.Sort(Builders<Case>.Sort.Ascending(orderBy.FieldName))
                    : find.Sort(Builders<Case>.Sort.Descending(orderBy.FieldName));


            var proposals = await find.Project(@case => @case.Proposals.Select(proposal => proposal)).Skip(skip).Limit(first).ToListAsync(cancellationToken);
            var offerIds = proposals.SelectMany(x => x).SelectMany(x => x.Basket).Select(x => x?.Id);

            return offerIds;
        }

        public async Task<long> GetTotalCountAsync(string tenantId, CaseWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");

            // where
            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;
            if (where != null)
                mongoFilter = where.GetFilterDefinition();

            long totalCount = await clientCollection.CountDocumentsAsync(mongoFilter, cancellationToken: cancellationToken);
            return totalCount;
        }

        public virtual async Task UpsertAsync(string tenantId, string id, Case @case, CancellationToken cancellationToken)
        {
            if (@case.Proposals?.SelectMany(x => x.Basket)?.Any() == true)
            {
                foreach (var proposal in @case.Proposals)
                {
                    if (proposal.Basket?.Any() == true)
                    {
                        foreach (var offer in proposal.Basket)
                        {
                            offer.Id ??= Guid.NewGuid().ToString();
                            offer.CaseId = @case.Id;
                            offer.ProposalId = proposal.Id;

                            await _offerRepository.UpsertAsync(tenantId, offer.Id, offer, cancellationToken);

                            offer.CaseId = null;
                            offer.ProposalId = null;
                            offer.Values = null;
                            offer.BenefitOptions = null;
                            offer.Premium = null;
                            offer.Exclusions = null;
                            offer.Clauses = null;
                            offer.Jackets = null;
                            offer.Facts = null;
                            offer.Stakeholders = null;
                            offer.Events = null;
                            offer.AssociatedContracts = null;
                            offer.Commissions = null;
                            offer.Pricing = null;
                            offer.Underwriting = null;
                            offer.Fields = null;
                            offer.Fields2 = null;
                            offer.FieldsSchemaId = null;
                            offer.ProductTreeId = null;
                            offer.ProductTreeRecords = null;
                        }
                    }
                }
            }
            JToken jToken = JToken.FromObject(@case, _jsonSerializer);
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<BsonDocument> clientCollection = db.GetCollection<BsonDocument>($"{tenantId}-cases");

            string json = jToken.ToString(Formatting.None, new StringEnumConverter(), new DateToMongoIsoDateConverter())
                .Replace("\"id\"", "\"_id\"");  //To follow MongoDB practice of serializing _id for id property

            BsonDocument bsonDocument = BsonSerializer.Deserialize<BsonDocument>(json);

            await clientCollection.ReplaceOneAsync(
                Builders<BsonDocument>.Filter.Eq("_id", id),
                bsonDocument,
                new ReplaceOptions { IsUpsert = true },
                cancellationToken);
        }

        public virtual async Task DeleteAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Case> clientCollection = db.GetCollection<Case>($"{tenantId}-cases");
            await clientCollection.DeleteOneAsync(Builders<Case>.Filter.Eq(p => p.Id, id), cancellationToken);
            await _offerRepository.DeleteAsync(tenantId, id, cancellationToken);
        }

        private async Task<List<Case>> PopulateOfferData(string tenantId, List<Case> @cases, CancellationToken cancellationToken)
        {
            if (@cases.Any())
            {
                var offerIds = @cases.SelectMany(x => x.Proposals.SelectMany(p => p.Basket.Select(o => o.Id))).ToList();
                IEnumerable<Offer> offers = offerIds.Any() ? await _offerRepository.GetAsync(tenantId, new OfferWhere { Id_in = offerIds }, cancellationToken) : Enumerable.Empty<Offer>();

                if (!offers.Any())
                    return @cases;

                foreach (Case @case in @cases)
                {
                    foreach (Proposal proposal in @case.Proposals)
                    {
                        if (offers.Any(o => o.ProposalId == proposal.Id))
                            proposal.Basket = offers.Where(o => o.ProposalId == proposal.Id).ToList();
                    }
                }
            }

            return @cases;
        }
    }
    public static class FilterDefinitionExtensions
    {
        private static readonly List<string> EmptyList = new List<string>();

        public static FilterDefinition<Case> GetFilterDefinition(this CaseWhere where)
        {
            FilterDefinition<Case> mongoFilter = FilterDefinition<Case>.Empty;

            if (where.Or?.Any() == true)
                foreach (CaseWhere orFilter in where.Or)
                    mongoFilter = mongoFilter != FilterDefinition<Case>.Empty
                        ? mongoFilter | GetFilterDefinition(orFilter)
                        : GetFilterDefinition(orFilter);

            else if (where.And?.Any() == true)
                foreach (CaseWhere andFilter in where.And)
                    mongoFilter &= GetFilterDefinition(andFilter);

            else
                mongoFilter = mongoFilter.AddToFilterDefinition(where);

            if (where.AccessPolicy.HasValue)
                mongoFilter &= AccessPolicyFilterHelper.BuildAccessPolicyFilter<Case>(where.AccessPolicy.Value);

            return mongoFilter;
        }

        public static FilterDefinition<Case> AddToFilterDefinition(this FilterDefinition<Case> mongoFilter, CaseWhere where)
        {
            if (where.Id != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.Id, where.Id);

            else if (where.Id_in != null)
                mongoFilter &= Builders<Case>.Filter.In(p => p.Id, where.Id_in);

            else if (where.Name_contains != null)
                mongoFilter &= Builders<Case>.Filter.Regex(c => c.Name, new BsonRegularExpression(where.Name_contains, "i"));

            else if (where.CaseNumber != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.CaseNumber, where.CaseNumber);

            else if (where.CaseNumber_contains != null)
                mongoFilter &= Builders<Case>.Filter.Regex(c => c.CaseNumber, new BsonRegularExpression(where.CaseNumber_contains, "i"));

            else if (where.Source != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.Source, where.Source);

            if (where.Source_contains != null)
                mongoFilter &= Builders<Case>.Filter.Regex(c => c.Source, new BsonRegularExpression(where.Source_contains, "i"));

            else if (where.Status != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.Status, where.Status);

            else if (where.HolderId != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.HolderId, where.HolderId);
            else if (where.HolderId_in != null)
                mongoFilter &= Builders<Case>.Filter.In(p => p.HolderId, where.HolderId_in);

            else if (!string.IsNullOrEmpty(where.ChannelId))
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.ChannelId, where.ChannelId);

            else if (where.CreatedAt_gt != null)
                mongoFilter &= Builders<Case>.Filter.Gt(c => c.CreatedAt, where.CreatedAt_gt);
            else if (where.CreatedAt_lt != null)
                mongoFilter &= Builders<Case>.Filter.Lt(c => c.CreatedAt, where.CreatedAt_lt);
            else if (where.LastModifiedAt_gt != null)
                mongoFilter &= Builders<Case>.Filter.Gt(c => c.LastModifiedAt, where.LastModifiedAt_gt);
            else if (where.LastModifiedAt_lt != null)
                mongoFilter &= Builders<Case>.Filter.Lt(c => c.LastModifiedAt, where.LastModifiedAt_lt);

            else if (where.CreatedById != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.CreatedById, where.CreatedById);
            else if (where.CreatedById_in != null)
                mongoFilter &= Builders<Case>.Filter.In(p => p.CreatedById, where.CreatedById_in);
            else if (where.LastModifiedById != null)
                mongoFilter &= Builders<Case>.Filter.Eq(p => p.LastModifiedById, where.LastModifiedById);
            else if (where.LastModifiedById_in != null)
                mongoFilter &= Builders<Case>.Filter.In(p => p.LastModifiedById, where.LastModifiedById_in);

            else if (where.Proposal != null)
            {
                if (where.Proposal.Id != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Id == where.Proposal.Id);
                else if (where.Proposal.Id_in != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => where.Proposal.Id_in.Contains(b.Id));

                else if (where.Proposal.ProposalNumber != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.ProposalNumber == where.Proposal.ProposalNumber);
                else if (where.Proposal.ProposalNumber_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.ProposalNumber.Contains(where.Proposal.ProposalNumber_contains));

                else if (where.Proposal.Name != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Name == where.Proposal.Name);
                else if (where.Proposal.Name_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Name.Contains(where.Proposal.Name_contains));

                else if (where.Proposal.PolicyId_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, p => p.PolicyIds.Contains(where.Proposal.PolicyId_contains));
                else if (where.Proposal.PolicyId_contains_every != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.All(p => p.PolicyIds, where.Proposal.PolicyId_contains_every));
                else if (where.Proposal.PolicyId_contains_some != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.AnyIn(p => p.PolicyIds, where.Proposal.PolicyId_contains_some));
                else if (where.Proposal.ExpiryDate_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(p => p.ExpiryDate, where.Proposal.ExpiryDate_lt));
                else if (where.Proposal.ExpiryDate_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(p => p.ExpiryDate, where.Proposal.ExpiryDate_gt));
                else if (where.Proposal.IssuedAt_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(p => p.IssuedAt, where.Proposal.IssuedAt_lt));
                else if (where.Proposal.IssuedAt_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(p => p.IssuedAt, where.Proposal.IssuedAt_gt));
                else if (!string.IsNullOrEmpty(where.Proposal.ChannelId))
                    mongoFilter &= Builders<Case>.Filter.Eq(p => p.ChannelId, where.ChannelId);


                else if (where.Proposal.Offers_contains != null)
                {
                    if (where.Proposal.Offers_contains.Id != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.Id == where.Proposal.Offers_contains.Id));
                    else if (where.Proposal.Offers_contains.Id_in != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => where.Proposal.Offers_contains.Id_in.Contains(b.Id)));

                    else if (where.Proposal.Offers_contains.OfferNumber != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.OfferNumber == where.Proposal.Offers_contains.OfferNumber));
                    else if (where.Proposal.Offers_contains.OfferNumber_in != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => where.Proposal.Offers_contains.OfferNumber_in.Contains(b.OfferNumber)));

                    else if (where.Proposal.Offers_contains.Status != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.Status == where.Proposal.Offers_contains.Status));
                    else if (where.Proposal.Offers_contains.StartDate_gte != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.StartDate >= where.Proposal.Offers_contains.StartDate_gte));
                    else if (where.Proposal.Offers_contains.StartDate_lte != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.StartDate <= where.Proposal.Offers_contains.StartDate_lte));

                    if (where.Proposal.Offers_contains.CreatedById != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.CreatedById == where.Proposal.Offers_contains.CreatedById));

                    if (where.Proposal.Offers_contains.ProductId_in is { } productIds && productIds.Count > 0)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket,
                            Builders<Offer>.Filter.Or(productIds.Select(productId => string.IsNullOrEmpty(productId.Version)
                                ? Builders<Offer>.Filter.Where(b => b.ProductId.Type == productId.Type && b.ProductId.Plan == productId.Plan)
                                : Builders<Offer>.Filter.Where(b => b.ProductId.Type == productId.Type && b.ProductId.Plan == productId.Plan && b.ProductId.Version == productId.Version)).ToArray())));
                }

                if (where.Proposal.Status != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Status == where.Proposal.Status);

                if (where.Proposal.IsIssued != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.IsIssued == where.Proposal.IsIssued);

                if (where.Proposal.IsApprovalNeeded != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.IsApprovalNeeded == where.Proposal.IsApprovalNeeded);

                if (where.Proposal.CreatedAt_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(c => c.CreatedAt, where.Proposal.CreatedAt_gt));
                if (where.Proposal.CreatedAt_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(c => c.CreatedAt, where.Proposal.CreatedAt_lt));
                if (where.Proposal.LastModifiedAt_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(c => c.LastModifiedAt, where.Proposal.LastModifiedAt_gt));
                if (where.Proposal.LastModifiedAt_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(c => c.LastModifiedAt, where.Proposal.LastModifiedAt_lt));
            }

            else if (where.Proposals_contains != null)
            {
                if (where.Proposals_contains.Id != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Id == where.Proposals_contains.Id);
                else if (where.Proposals_contains.Id_in != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => where.Proposals_contains.Id_in.Contains(b.Id));

                else if (where.Proposals_contains.ProposalNumber != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.ProposalNumber == where.Proposals_contains.ProposalNumber);
                else if (where.Proposals_contains.ProposalNumber_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.ProposalNumber.Contains(where.Proposals_contains.ProposalNumber_contains));

                else if (where.Proposals_contains.Name != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Name == where.Proposals_contains.Name);
                else if (where.Proposals_contains.Name_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Name.Contains(where.Proposals_contains.Name_contains));

                if (where.Proposals_contains.PolicyId_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, p => p.PolicyIds.Contains(where.Proposals_contains.PolicyId_contains));
                if (where.Proposals_contains.PolicyId_contains_every != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.All(p => p.PolicyIds, where.Proposals_contains.PolicyId_contains_every));
                if (where.Proposals_contains.PolicyId_contains_some != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.AnyIn(p => p.PolicyIds, where.Proposals_contains.PolicyId_contains_some));
                if (where.Proposals_contains.PolicyIds_contains_any != null)
                {
                    if (where.Proposals_contains.PolicyIds_contains_any == true)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Ne(p => p.PolicyIds, new List<string> { }));
                    else
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Size(p => p.PolicyIds, 0));
                }

                else if (where.Proposals_contains.ExpiryDate_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(p => p.ExpiryDate, where.Proposals_contains.ExpiryDate_lt));
                else if (where.Proposals_contains.ExpiryDate_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(p => p.ExpiryDate, where.Proposals_contains.ExpiryDate_gt));

                if (where.Proposals_contains.Offers_contains != null)
                {
                    if (where.Proposals_contains.Offers_contains.Id != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.Id == where.Proposals_contains.Offers_contains.Id));
                    else if (where.Proposals_contains.Offers_contains.Id_in != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => where.Proposals_contains.Offers_contains.Id_in.Contains(b.Id)));

                    else if (where.Proposals_contains.Offers_contains.OfferNumber != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.OfferNumber == where.Proposals_contains.Offers_contains.OfferNumber));
                    else if (where.Proposals_contains.Offers_contains.OfferNumber_in != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => where.Proposals_contains.Offers_contains.OfferNumber_in.Contains(b.OfferNumber)));

                    else if (where.Proposals_contains.Offers_contains.Status != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.Status == where.Proposals_contains.Offers_contains.Status));
                    else if (where.Proposals_contains.Offers_contains.StartDate_gte != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.StartDate >= where.Proposals_contains.Offers_contains.StartDate_gte));
                    else if (where.Proposals_contains.Offers_contains.StartDate_lte != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.StartDate <= where.Proposals_contains.Offers_contains.StartDate_lte));

                    if (where.Proposals_contains.Offers_contains.CreatedById != null)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket, b => b.CreatedById == where.Proposals_contains.Offers_contains.CreatedById));

                    if (where.Proposals_contains.Offers_contains.ProductId_in is { } productIds && productIds.Count > 0)
                        mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.ElemMatch(p => p.Basket,
                            Builders<Offer>.Filter.Or(productIds.Select(productId => string.IsNullOrEmpty(productId.Version)
                                ? Builders<Offer>.Filter.Where(b => b.ProductId.Type == productId.Type && b.ProductId.Plan == productId.Plan)
                                : Builders<Offer>.Filter.Where(b => b.ProductId.Type == productId.Type && b.ProductId.Plan == productId.Plan && b.ProductId.Version == productId.Version)).ToArray())));
                }

                if (where.Proposals_contains.Status != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.Status == where.Proposals_contains.Status);

                if (where.Proposals_contains.IsIssued != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.IsIssued == where.Proposals_contains.IsIssued);

                if (where.Proposals_contains.IsApprovalNeeded != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.IsApprovalNeeded == where.Proposals_contains.IsApprovalNeeded);

                if (where.Proposals_contains.ReferralCode != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.ReferralCode == where.Proposals_contains.ReferralCode);

                if (where.Proposals_contains.CreatedById != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, b => b.CreatedById == where.Proposals_contains.CreatedById);

                if (where.Proposals_contains.CreatedAt_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(c => c.CreatedAt, where.Proposals_contains.CreatedAt_gt));
                if (where.Proposals_contains.CreatedAt_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(c => c.CreatedAt, where.Proposals_contains.CreatedAt_lt));
                if (where.Proposals_contains.LastModifiedAt_gt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Gt(c => c.LastModifiedAt, where.Proposals_contains.LastModifiedAt_gt));
                if (where.Proposals_contains.LastModifiedAt_lt != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Proposals, Builders<Proposal>.Filter.Lt(c => c.LastModifiedAt, where.Proposals_contains.LastModifiedAt_lt));

            }
            else if (where.Proposals_exist == true)
            {
                mongoFilter &= Builders<Case>.Filter.Exists(a => a.Proposals);
                mongoFilter &= Builders<Case>.Filter.Not(Builders<Case>.Filter.Size(a => a.Proposals, 0));
            }
            else if (where.Proposals_exist == false)
            {
                mongoFilter &= (Builders<Case>.Filter.Not(Builders<Case>.Filter.Exists(a => a.Proposals)) |
                    Builders<Case>.Filter.Size(a => a.Proposals, 0));
            }
            else if (where.Proposals_every != null)
            {
                if (where.Proposals_every.Status != null)
                    mongoFilter &= Builders<Case>.Filter.Not(Builders<Case>.Filter
                        .ElemMatch(a => a.Proposals, p => p.Status != where.Proposals_every.Status));
                else if (where.Proposals_every.Offers_exist != null)
                {
                    if (where.Proposals_every.Offers_exist == true)
                        mongoFilter &= Builders<Case>.Filter
                            .Not(Builders<Case>.Filter.ElemMatch(a => a.Proposals, p => p.Basket == null || !p.Basket.Any()));
                    else if (where.Proposals_every.Offers_exist == false)
                        mongoFilter &= Builders<Case>.Filter
                            .Not(Builders<Case>.Filter.ElemMatch(a => a.Proposals, p => p.Basket != null && p.Basket.Any()));
                }
                else if (where.Proposals_every.Offers_every?.Status != null)
                    mongoFilter &= Builders<Case>.Filter
                        .Not(Builders<Case>.Filter
                            .ElemMatch(a => a.Proposals, p => p.Basket != null && p.Basket
                                .Any(o => o.Status != where.Proposals_every.Offers_every.Status)));
            }

            else if (where.Stakeholders_contains != null)
            {
                if (where.Stakeholders_contains.Id != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => b.Id == where.Stakeholders_contains.Id);
                else if (where.Stakeholders_contains.Id_in != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => where.Stakeholders_contains.Id_in.Contains(b.Id));
                else if (where.Stakeholders_contains.EntityId != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => b.EntityId == where.Stakeholders_contains.EntityId);
                else if (where.Stakeholders_contains.EntityId_in != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, Builders<Stakeholder>.Filter.In(s => s.EntityId, where.Stakeholders_contains.EntityId_in));
                else if (where.Stakeholders_contains.Type != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => b.Type == where.Stakeholders_contains.Type);
                else if (where.Stakeholders_contains.Type_in != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => where.Stakeholders_contains.Type_in.Contains(b.Type));
                else if (where.Stakeholders_contains.Name_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => b.Name.Contains(where.Stakeholders_contains.Name_contains));
                else if (where.Stakeholders_contains.Code_contains != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(a => a.Stakeholders, b => b.Code.Contains(where.Stakeholders_contains.Code_contains));
            }

            else if (where.Facts_contains != null)
            {
                if (where.Facts_contains.Id != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(p => p.Facts, b => b.Id == where.Facts_contains.Id);
                if (where.Facts_contains.Type != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(p => p.Facts, b => b.Type == where.Facts_contains.Type);
                if (where.Facts_contains.Value != null)
                    mongoFilter &= Builders<Case>.Filter.ElemMatch(p => p.Facts, b => b.Value == where.Facts_contains.Value);
            }

            if (where.FieldsWhere != null)
                mongoFilter &= where.FieldsWhere.Build<Case>();

            if (where.HavingIssuedPolicies.HasValue)
            {
                FilterDefinitionBuilder<Case> filterBuilder = Builders<Case>.Filter;
                FilterDefinition<Case> condition = filterBuilder.Exists(x => x.IssuedPoliciesIds)
                    & filterBuilder.Ne(x => x.IssuedPoliciesIds, null)
                    & filterBuilder.Ne(x => x.IssuedPoliciesIds, EmptyList);

                mongoFilter &= (where.HavingIssuedPolicies.Value ? condition : filterBuilder.Not(condition));
            }

            return mongoFilter;
        }
    }

    public class DateToMongoIsoDateConverter : JsonConverter
    {
        //solution from https://stackoverflow.com/questions/43866941/when-converting-json-to-bson-document-fails-to-correctly-convert-date-value
        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer) =>
            new JRaw($"new {value.ToJson()}").WriteTo(writer);

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer) =>
            throw new NotImplementedException("Unnecessary because CanRead is false. The type will skip the converter.");

        public override bool CanRead => false;

        public override bool CanConvert(Type objectType) => objectType == typeof(DateTime) || objectType == typeof(DateTime?);
    }
}
