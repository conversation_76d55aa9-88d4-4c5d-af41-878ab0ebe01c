using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Cases.Domain;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MongoDbOfferRepository
    {
        public string ProviderId { get; } = "mongoDb";
        private const string dbName = "cases";
        private readonly JsonSerializer _jsonSerializer;

        public MongoDbOfferRepository()
        {
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
                NullValueHandling = NullValueHandling.Ignore,
            };
            settings.Converters.Add(new StringEnumConverter());

            _jsonSerializer = JsonSerializer.Create(settings);
        }

        public async Task<IEnumerable<Offer>> GetByProposalIdAsync(string tenantId, string proposalId, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Offer> clientCollection = db.GetCollection<Offer>($"{tenantId}-offers");

            FilterDefinition<Offer> mongoFilter = Builders<Offer>.Filter.Eq(p => p.ProposalId, proposalId);

            IFindFluent<Offer, Offer> find = clientCollection.Find(mongoFilter);

            return await find.ToListAsync(cancellationToken);
        }

        public async Task<IEnumerable<Offer>> GetAsync(string tenantId, OfferWhere where, CancellationToken cancellationToken = default)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Offer> clientCollection = db.GetCollection<Offer>($"{tenantId}-offers");

            FilterDefinition<Offer> mongoFilter = FilterDefinition<Offer>.Empty;
            if (where != null)
                mongoFilter = where.GetOfferFilterDefinition();

            IFindFluent<Offer, Offer> find = clientCollection.Find(mongoFilter);


            return await find.ToListAsync(cancellationToken);
        }

        public async Task UpsertAsync(string tenantId, string id, Offer offer, CancellationToken cancellationToken)
        {
            JToken jToken = JToken.FromObject(offer, _jsonSerializer);
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<BsonDocument> clientCollection = db.GetCollection<BsonDocument>($"{tenantId}-offers");

            string json = jToken.ToString(Formatting.None, new StringEnumConverter(), new DateToMongoIsoDateConverter())
                .Replace("\"id\"", "\"_id\"");  //To follow MongoDB practice of serializing _id for id property

            BsonDocument bsonDocument = BsonSerializer.Deserialize<BsonDocument>(json);

            await clientCollection.ReplaceOneAsync(
                Builders<BsonDocument>.Filter.Eq("_id", id),
                bsonDocument,
                new ReplaceOptions { IsUpsert = true },
                cancellationToken);
        }

        public async Task DeleteAsync(string tenantId, string caseId, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId));
            IMongoDatabase db = client.GetDatabase(dbName);

            IMongoCollection<Offer> clientCollection = db.GetCollection<Offer>($"{tenantId}-offers");

            await clientCollection.DeleteManyAsync(Builders<Offer>.Filter.Eq(p => p.CaseId, caseId), cancellationToken);
        }
    }

    public static class OfferFilterDefinitionExtensions
    {
        public static FilterDefinition<Offer> GetOfferFilterDefinition(this OfferWhere where)
        {
            FilterDefinition<Offer> mongoFilter = FilterDefinition<Offer>.Empty;

            mongoFilter = mongoFilter.AddToFilterDefinition(where);

            return mongoFilter;
        }

        public static FilterDefinition<Offer> AddToFilterDefinition(this FilterDefinition<Offer> mongoFilter, OfferWhere where)
        {
            if (where.Id != null)
                mongoFilter &= Builders<Offer>.Filter.Eq(p => p.Id, where.Id);

            else if (where.Id_in != null)
                mongoFilter &= Builders<Offer>.Filter.In(p => p.Id, where.Id_in);

            return mongoFilter;
        }
    }
}