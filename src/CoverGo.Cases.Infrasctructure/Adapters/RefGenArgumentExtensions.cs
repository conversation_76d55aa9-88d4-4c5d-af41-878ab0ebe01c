﻿using CoverGo.Cases.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.Adapters
{
    public static class RefGenArgumentExtensions
    {
        public static async Task<long> GetCasesOfTodayCount(IEventStore eventStore, string tenantId, CancellationToken cancellationToken)
        {
            DateTimeWithZone hkTimeZone = new DateTimeWithZone(DateTime.Now.Date, ResolveHKTimeZone());

            IEnumerable<CaseEvent> @events = await eventStore.GetEventsAsync(tenantId,
                new List<CaseEventType> { CaseEventType.creation },
                default, 
                hkTimeZone.UniversalTime, 
                cancellationToken: cancellationToken);
            return @events.LongCount();

            static TimeZoneInfo ResolveHKTimeZone()
            {
                try
                {
                    return TimeZoneInfo.FindSystemTimeZoneById("Asia/Hong_Kong");
                }
                catch
                {
                    try
                    {
                        return TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");
                    }
                    catch
                    {
                        return TimeZoneInfo.Local;
                    }
                }
            }
        }
    }
}
