using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.Adapters.EF
{
    public class MariaDbCaseRepository : ICaseRepository
    {
        public string ProviderId { get; } = "mariaDb";

        private CasesDbContextFactory _contextFactory { get; }

        public MariaDbCaseRepository(CasesDbContextFactory contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task UpsertAsync(string tenantId, string id, Case @case, CancellationToken cancellationToken)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);
            Case existing = await ctx.Cases.FindAsync(new object[] { id }, cancellationToken);

            if (existing != null)
                ctx.Entry(existing).CurrentValues.SetValues(@case);
            else
                await ctx.Cases.AddAsync(@case, cancellationToken);

            await ctx.SaveChangesAsync(cancellationToken);
        }

        public async Task<IEnumerable<Case>> GetAsync(string tenantId, CaseWhere where, OrderBy orderBy = null,
            int? skip = null, int? first = null, CancellationToken cancellationToken = default)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);

            return ctx.BuildCasesQueryArguments(where, orderBy, skip, first);
        }

        public Task<IEnumerable<JToken>> GetReportAsync(string tenantId, CaseWhere @where, OrderBy orderBy = null, int? skip = null,
            int? first = null, CancellationToken cancellationToken = default) =>
            throw new System.NotImplementedException();

        public async Task<IEnumerable<string>> GetIdsAsync(string tenantId, CaseWhere @where, OrderBy orderBy,
            int? skip,
            int? first,
            CancellationToken cancellationToken)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);

            List<Case> find = ctx.BuildCasesQueryArguments(where, orderBy, skip, first);

            var ids = find.Select(c => c.Id).ToList();

            return ids;
        }
        
        public IEnumerable<string> GetIds(string tenantId, CaseWhere @where, OrderBy orderBy,
            int? skip,
            int? first)
        {
            using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);

            List<Case> find = ctx.BuildCasesQueryArguments(where, orderBy, skip, first);

            IEnumerable<string> ids = find.Select(c => c.Id);

            return ids;
        }

        public async Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, CaseWhere @where, OrderBy orderBy,
            int? skip,
            int? first,
            CancellationToken cancellationToken)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);

            List<Case> find = ctx.BuildCasesQueryArguments(where, orderBy, skip, first);

            var casesProposalIds = find.Select(c => c.Proposals.Select(p=> p.Id)).ToList();

            List<string> ids = casesProposalIds.SelectMany(x => x).ToList();
            return ids;
        }

        public async Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, CaseWhere @where, OrderBy orderBy,
            int? skip,
            int? first,
            CancellationToken cancellationToken)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);

            List<Case> find = ctx.BuildCasesQueryArguments(where, orderBy, skip, first);

            var casesProposalsOfferIds = find.Select(c => c.Proposals.SelectMany(p => p.Basket.Select(b => b.Id))).ToList();

            List<string> ids = casesProposalsOfferIds.SelectMany(x => x).ToList();
            return ids;
        }

        public async Task<long> GetTotalCountAsync(string tenantId, CaseWhere @where, CancellationToken cancellationToken)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);
            var items = ctx.BuildCasesSearchFilter(where);

            return items.Count();
        }

        public async Task DeleteAsync(string tenantId, string id, CancellationToken cancellationToken)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);
            Case caseToRemove = ctx.BuildCasesSearchFilter(new CaseWhere { Id = id }).FirstOrDefault();
            ctx.Cases.Remove(caseToRemove);

            await ctx.SaveChangesAsync(cancellationToken);
        }
    }
}
