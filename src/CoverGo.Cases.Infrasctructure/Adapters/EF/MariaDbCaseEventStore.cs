﻿using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.Adapters.EF
{
    public class MariaDbCaseEventStore : IEventStore
    {
        public string ProviderId { get; } = "mariaDb";

        private CasesDbContextFactory _contextFactory { get; set; }

        public MariaDbCaseEventStore(CasesDbContextFactory contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<Result> AddEventAsync(string tenantId, CaseEvent caseEvent, CancellationToken cancellationToken)
        {
            caseEvent.Id = Guid.NewGuid().ToString();
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);
            await ctx.Events.AddAsync(caseEvent, cancellationToken);
            await ctx.SaveChangesAsync(cancellationToken);

            return Result.Success();
        }

        public async Task<IEnumerable<CaseEvent>> GetEventsAsync(string tenantId,
            IEnumerable<CaseEventType> types,
            IEnumerable<string> caseIds,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            CancellationToken cancellationToken = default)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);

            Expression<Func<CaseEvent, bool>> predicate = PredicateBuilder.True<CaseEvent>();

            if (types?.Any() == true)
                predicate = predicate.And(e => types.Contains(e.Type));
            if (caseIds?.Any() == true)
                predicate = predicate.And(e => caseIds.Contains(e.CaseId));
            if (fromDate != null)
                predicate = predicate.And(e => e.Timestamp >= fromDate);
            if (toDate != null)
                predicate = predicate.And(e => e.Timestamp <= toDate);

            List<CaseEvent> events = await ctx.Events.Where(predicate).OrderBy(e => e.Timestamp).ToListAsync(cancellationToken);
            return events;
        }

        //only needed for replay all, will not implement unless requested for some reason
        public Task<IEnumerable<CaseEvent>> GetCaseEventsStartingWith(string tenantId, string startsWith, CancellationToken cancellationToken) => throw new NotImplementedException();

        public Task<Result> DeleteInvalidEventAsync(string tenantId, string eventId, CancellationToken cancellationToken) => throw new NotImplementedException();
    }
}
