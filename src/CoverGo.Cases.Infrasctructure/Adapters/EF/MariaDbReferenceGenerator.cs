﻿using CoverGo.Cases.Domain;
using CoverGo.Cases.Infrastructure.Adapters.EF;
using CoverGo.DomainUtils;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.Adapters.Mongo
{
    public class MariaDbReferenceGenerator : IReferenceGenerator
    {
        private readonly CasesDbContextFactory _contextFactory;
        private readonly MariaDbCaseEventStore _eventStore;

        public MariaDbReferenceGenerator(
            CasesDbContextFactory contextFactory,
            MariaDbCaseEventStore eventStore
            )
        {
            _contextFactory = contextFactory;
            _eventStore = eventStore;
        }

        public string ProviderId { get; } = "mariaDb";

        public async Task<string> GenerateAsync(string tenantId, string type, JToken input, string accessToken = null,
            GraphQlVariables graphQlVariables = null, CancellationToken cancellationToken = default)
        {
            await using CasesDbContext ctx = _contextFactory.CreateDbContext(tenantId);
            ReferenceGeneratorConfig config = await ctx.Configs.FirstOrDefaultAsync(x => x.Type == type, cancellationToken);

            if (config == null)
                return AlphaNumericStringGenerator.GetRandomUppercaseAlphaNumericValue(6);

            string[] formattedArguments = new string[config.Arguments.Max(a => a.Order) + 1];
            foreach (FormatArgument argument in config.Arguments)
            {
                string formattedArgument = null;
                DateTime currentDate = DateTime.Now;

                if (argument.Type == "input")
                {
                    JToken targetJToken = argument.JsonPath != null ? input?.SelectTokens(argument.JsonPath).FirstOrDefault() : null;

                    if (argument.ExpectedType == "date")
                    {
                        if (targetJToken == null)
                            continue;

                        DateTime date = targetJToken.Value<DateTime>();
                        formattedArgument = date.ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "dateTimeUtcNow")
                    {
                        var timeSpan = new TimeSpan();
                        if (argument.DateTimeOffset != null)
                        {
                            var durationSplits = argument.DateTimeOffset?.Split(" ").Select(s => int.Parse(s))?.ToList();

                            timeSpan = new TimeSpan(durationSplits.ElementAtOrDefault(0), durationSplits.ElementAtOrDefault(1), durationSplits.ElementAtOrDefault(2), durationSplits.ElementAtOrDefault(3), durationSplits.ElementAtOrDefault(4));
                        }

                        formattedArgument = DateTime.UtcNow.Add(timeSpan).ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "int")
                    {
                        int integer = (targetJToken?.Value<int>() ?? 0) + argument.AddOpOnResult;
                        formattedArgument = integer.ToString(argument.Format);
                    }

                    else if (argument.ExpectedType == "string")
                    {
                        string theString = targetJToken?.Value<string>() ?? "";
                        formattedArgument = argument.SplitOn == null
                            ? theString
                            : theString.Split(argument.SplitOn)[argument.SplitIndex];
                    }

                    else
                        formattedArgument = targetJToken.ToString();
                }

                else if (argument.Type == "currentDate")
                    formattedArgument = currentDate.ToString(argument.Format);

                else if (argument.Type == "createdCasesOfCurrentDay")
                {
                    long count = await RefGenArgumentExtensions.GetCasesOfTodayCount(_eventStore, tenantId, cancellationToken);
                    count++;

                    formattedArgument = count.ToString(argument.Format);
                }

                formattedArguments[argument.Order] = formattedArgument;
            }

            string formattedNumber = string.Format(config.Format, formattedArguments);
            return formattedNumber;
        }
    }
}
