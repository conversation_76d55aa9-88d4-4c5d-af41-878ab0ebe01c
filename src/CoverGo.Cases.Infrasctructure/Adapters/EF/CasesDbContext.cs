﻿using System.Collections.Generic;
using System.Linq;
using CoverGo.Cases.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

namespace CoverGo.Cases.Infrastructure.Adapters.EF
{
    public class CasesDbContext : DbContext
    {
        private readonly JsonSerializerSettings _settings;

        public DbSet<Case> Cases { get; set; }
        public DbSet<CaseEvent> Events { get; set; }
        public DbSet<ReferenceGeneratorConfig> Configs { get; set; }

        private static readonly List<ReferenceGeneratorConfig> SeedGeneratorConfigs = new List<ReferenceGeneratorConfig>
        {
            new ReferenceGeneratorConfig()
            {
                Type = "createCase",
                Format = "APP-{0}{1}",
                Arguments = new List<FormatArgument>()
                {
                    new FormatArgument()
                    {
                        Order = 0,
                        Format = "yyMMdd",
                        Type = "currentDate"
                    },
                    new FormatArgument()
                    {
                        Order = 1,
                        Format = "D2",
                        Type = "createdCasesOfCurrentDay"
                    }
                },
            }
        };

        public CasesDbContext(DbContextOptions options) : base(options)
        {
            _settings = new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore, ContractResolver = new CamelCasePropertyNamesContractResolver() };
            _settings.Converters.Add(new StringEnumConverter());
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            #region Cases

            modelBuilder.HasCharSet("utf8mb4", DelegationModes.ApplyToAll);

            EntityTypeBuilder<Case> caseTypeBuilder = modelBuilder.Entity<Case>();

            caseTypeBuilder.HasKey(p => p.Id);

            caseTypeBuilder.Property(p => p.Name);
            caseTypeBuilder.Property(p => p.Description);
            caseTypeBuilder.Property(p => p.Source);
            caseTypeBuilder.Property(p => p.CaseNumber);
            caseTypeBuilder.Property(b => b.HolderId);
            caseTypeBuilder.Property(p => p.Status);

            caseTypeBuilder.Property(b => b.OtherHolderIds).HasConversion(
                otherHolderIds => otherHolderIds.Any() ? JsonConvert.SerializeObject(otherHolderIds, _settings) : null,
                otherHolderIdsJsonString => JsonConvert.DeserializeObject<List<string>>(otherHolderIdsJsonString, _settings)
            );

            caseTypeBuilder.Property(b => b.InsuredIds).HasConversion(
                insuredIds => insuredIds.Any() ? JsonConvert.SerializeObject(insuredIds, _settings) : null,
                insuredIdsJsonString => JsonConvert.DeserializeObject<List<string>>(insuredIdsJsonString, _settings)
            );

            caseTypeBuilder
                .Property(b => b.Facts)
                .HasConversion(
                    facts => facts.Any() ? JsonConvert.SerializeObject(facts, _settings) : null,
                    factsJsonString => JsonConvert.DeserializeObject<List<Fact>>(factsJsonString, _settings)
                );

            caseTypeBuilder
                .Property(b => b.Notes)
                .HasConversion(
                    notes => notes.Any() ? JsonConvert.SerializeObject(notes, _settings) : null,
                    notesJsonString => JsonConvert.DeserializeObject<List<Note>>(notesJsonString, _settings)
                );

            caseTypeBuilder
                .Property(b => b.Proposals)
                .HasConversion(
                    proposals => proposals.Any() ? JsonConvert.SerializeObject(proposals, _settings) : null,
                    proposalsJsonString => JsonConvert.DeserializeObject<List<Proposal>>(proposalsJsonString, _settings)
                );

            caseTypeBuilder
                .Property(b => b.Stakeholders)
                .HasConversion(
                    stakeholders => stakeholders.Any() ? JsonConvert.SerializeObject(stakeholders, _settings) : null,
                    stakeholdersJsonString => JsonConvert.DeserializeObject<List<Stakeholder>>(stakeholdersJsonString, _settings)
                );

            caseTypeBuilder
                .Property(b => b.BeneficiaryEligibilities)
                .HasConversion(
                    beneficiaryEligibilities => beneficiaryEligibilities.Any() ? JsonConvert.SerializeObject(beneficiaryEligibilities, _settings) : null,
                    beneficiaryEligibilitiesJsonString => JsonConvert.DeserializeObject<List<BeneficiaryEligibility>>(beneficiaryEligibilitiesJsonString, _settings)
                );

            caseTypeBuilder
                .Property(b => b.PaymentInfos)
                .HasConversion(
                    paymentInfos => paymentInfos.Any() ? JsonConvert.SerializeObject(paymentInfos, _settings) : null,
                    paymentInfosJsonString => JsonConvert.DeserializeObject<List<PaymentInfo>>(paymentInfosJsonString, _settings)
                );

            caseTypeBuilder.Ignore(x => x.Fields);
            caseTypeBuilder.Ignore(x => x.IsReadOnly);
            caseTypeBuilder.Ignore(x => x.IssuedPoliciesIds);
            caseTypeBuilder.Ignore(x => x.Handlers);

            #endregion

            #region CaseEvent

            EntityTypeBuilder<CaseEvent> caseEventTypeBuilder = modelBuilder.Entity<CaseEvent>();

            caseEventTypeBuilder.HasKey(p => p.Id);

            caseEventTypeBuilder.Property(p => p.CaseId);

            caseEventTypeBuilder.Property(p => p.Timestamp);

            caseEventTypeBuilder.Property(b => b.Type);

            caseEventTypeBuilder.Property(p => p.Values)
                .HasConversion(
                    values => values.ToString(),
                    dbValue => JToken.Parse(string.IsNullOrEmpty(dbValue) ? "{}" : dbValue));


            #endregion

            #region ReferenceGeneratorConfigs

            EntityTypeBuilder<ReferenceGeneratorConfig> refGenConfigTypeBuilder = modelBuilder.Entity<ReferenceGeneratorConfig>();

            refGenConfigTypeBuilder.HasKey(c => c.Type);
            refGenConfigTypeBuilder.Property(c => c.Format);

            refGenConfigTypeBuilder
                .Property(c => c.Arguments)
                .HasConversion(
                    items => items.Any() ? JsonConvert.SerializeObject(items, _settings) : null,
                    itemsJson => JsonConvert.DeserializeObject<List<FormatArgument>>(itemsJson, _settings)
                );

            refGenConfigTypeBuilder.HasData(SeedGeneratorConfigs);

            #endregion
        }
    }
}
