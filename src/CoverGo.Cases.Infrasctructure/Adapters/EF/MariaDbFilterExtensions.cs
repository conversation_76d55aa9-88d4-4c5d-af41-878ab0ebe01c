using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;

namespace CoverGo.Cases.Infrastructure.Adapters.EF
{
    public static class MariaDbFilterExtensions
    {
        public static List<Case> BuildCasesQueryArguments(
            this CasesDbContext ctx,
            CaseWhere where,
            OrderBy orderBy = null,
            int? skip = null,
            int? first = null)
        {
            List<Case> find = ctx.BuildCasesSearchFilter(where);
            if (orderBy != null)
                find = find.BuildCasesSort(orderBy.FieldName, orderBy.Type == OrderByType.DSC);

            if (skip != null)
                find = find.Skip(skip.GetValueOrDefault()).ToList();
            if (first != null)
                find = find.Take(first.GetValueOrDefault()).ToList();

            return find;
        }

        public static List<Case> BuildCasesSearchFilter(this CasesDbContext ctx, CaseWhere where)
        {
            Expression<Func<Case, bool>> predicate = PredicateBuilder.True<Case>();

            if (where?.Or?.Any(x => x.Proposal == null) == true)
            {
                predicate = PredicateBuilder.False<Case>();
                foreach (CaseWhere or in where.Or.Where(x => x.Proposal == null))
                {
                    predicate = predicate.Or(BuildCaseExpression(or));
                }
            }
            else if (where?.And?.Any(x => x.Proposal == null) == true)
                foreach (CaseWhere and in where.And.Where(x => x.Proposal == null))
                {
                    predicate = predicate.And(BuildCaseExpression(and));
                }                
            else if (where != null)
            {
                predicate = predicate.And(BuildCaseExpression(where));
            }

            var cases = ctx.Cases.Where(predicate).ToList();

            if (where?.Or?.Any(x => x.Proposal != null) == true)
            {
                foreach (CaseWhere or in where.Or.Where(x => x.Proposal != null))
                {
                    cases = cases.Union(ctx.Cases.ToList().BuildProposalExpression(or.Proposal)).ToList();
                }
            }
            else if (where?.And?.Any(x => x.Proposal != null) == true)
                foreach (CaseWhere and in where.And.Where(x => x.Proposal != null))
                {
                    cases = cases.BuildProposalExpression(and.Proposal);
                }
            else if (where.Proposal != null)
            {
                cases = cases.BuildProposalExpression(where.Proposal);
            }

            return cases;
        }

        public static List<Case> BuildCasesSort(this List<Case> find, string orderBy, bool sortDesc) =>
            orderBy switch
            {
                "createdAt" => sortDesc ? find.OrderByDescending(c => c.CreatedAt).ToList() : find.OrderBy(c => c.CreatedAt).ToList(),
                _ => find
            };

        public static Expression<Func<Case, bool>> BuildCaseExpression(CaseWhere where)
        {
            Expression<Func<Case, bool>> predicate = PredicateBuilder.True<Case>();

            if (where == null)
                return predicate;

            predicate = predicate.EnforceAccessPolicyFilter(where);

            if (where?.Or?.Any() == true)
            {
                predicate = PredicateBuilder.False<Case>();
                foreach (CaseWhere or in where.Or)
                    predicate = predicate.Or(BuildCaseExpression(or));
            }

            else if (where?.And?.Any() == true)
                foreach (CaseWhere and in where.And)
                    predicate = predicate.And(BuildCaseExpression(and));

            if (where.Id != null)
                predicate = predicate.And(c => c.Id == where.Id);
            else if (where.Id_in != null)
                predicate = predicate.And(c => where.Id_in.Contains(c.Id));
            else if (where.Name_contains != null)
                predicate = predicate.And(c => c.Name.Contains(where.Name_contains));
            if (where.Status != null)
                predicate = predicate.And(c => c.Status == where.Status);
            if (where.HolderId != null)
                predicate = predicate.And(c => c.HolderId == where.HolderId);
            else if (where.HolderId_in != null)
                predicate = predicate.And(c => where.HolderId_in.Any() && where.HolderId_in.Contains(c.HolderId));
            else if (where.CaseNumber != null)
                predicate = predicate.And(c => c.CaseNumber == where.CaseNumber);
            else if (where.CaseNumber_contains != null)
                predicate = predicate.And(c => c.CaseNumber.Contains(where.CaseNumber_contains));
            else if (!string.IsNullOrEmpty(where.ChannelId))
                predicate = predicate.And(c => c.ChannelId == where.ChannelId);
            else if (where.Proposals_exist == true)
                predicate = predicate.And(c => c.Proposals != null);
            else if (where.Proposals_exist == false)
                predicate = predicate.And(c => c.Proposals == null);
            else if (where.CreatedAt_gt != null)
                predicate = predicate.And(c => c.CreatedAt > where.CreatedAt_gt);
            else if (where.CreatedAt_lt != null)
                predicate = predicate.And(c => c.CreatedAt < where.CreatedAt_lt);
            else if (where.CreatedById != null)
                predicate = predicate.And(c => c.CreatedById == where.CreatedById);
            else if (where.CreatedById_in != null)
                predicate = predicate.And(c => where.CreatedById_in.Contains(c.CreatedById));
            else if (where.LastModifiedById != null)
                predicate = predicate.And(c => c.LastModifiedById == where.LastModifiedById);
            else if (where.LastModifiedById_contains != null)
                predicate = predicate.And(c => where.LastModifiedById_contains.Contains(c.LastModifiedById));

            return predicate;
        }

        private static Expression<Func<Case, bool>> EnforceAccessPolicyFilter(this Expression<Func<Case, bool>> predicate, CaseWhere where)
        {
            if (where.AccessPolicy == AccessPolicy.Standard)
            {
                predicate = predicate.And(c => c.AccessPolicy == null || c.AccessPolicy == AccessPolicy.Standard);
            }
            else if (where.AccessPolicy == AccessPolicy.Restricted)
            {
                predicate = predicate.And(c => c.AccessPolicy == AccessPolicy.Restricted);
            }

            return predicate;
        }

        private static List<Case> BuildProposalExpression(this List<Case> cases, ProposalWhere where)
        {
            if (where.Id != null)
                return cases.Where(c => c.Proposals != null && c.Proposals.Any(x => x.Id == where.Id)).ToList();
            else if (where.Id_in != null)
                return cases.Where(c => c.Proposals != null && c.Proposals.Any(x => where.Id_in.Contains(x.Id))).ToList();
            else if (where.ProposalNumber != null)
                return cases.Where(c => c.Proposals != null && c.Proposals.Any(x => x.ProposalNumber == where.ProposalNumber)).ToList();
            else if (where.ProposalNumber_contains != null)
                return cases.Where(c => c.Proposals != null && c.Proposals.Any(x => x.ProposalNumber.Contains(where.ProposalNumber_contains))).ToList();
            else if (where.IssuedAt_gt != null)
                return cases.Where(c => c.Proposals != null && c.Proposals.Any(x => x.IssuedAt > where.IssuedAt_gt)).ToList();
            else if (where.IssuedAt_lt != null)
                return cases.Where(c => c.Proposals != null && c.Proposals.Any(x => x.IssuedAt < where.IssuedAt_lt)).ToList();
            if (!string.IsNullOrEmpty(where.ChannelId))
                return cases.Where(c => c.Proposals != null && c.ChannelId == where.ChannelId).ToList();

            return cases;
        }
    }

    public static class PredicateBuilder
    {
        public static Expression<Func<T, bool>> True<T>() => f => true;
        public static Expression<Func<T, bool>> False<T>() => f => false;

        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> expr1,
            Expression<Func<T, bool>> expr2)
        {
            InvocationExpression invokedExpr = Expression.Invoke(expr2, expr1.Parameters);
            return Expression.Lambda<Func<T, bool>>
                (Expression.OrElse(expr1.Body, invokedExpr), expr1.Parameters);
        }

        public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> expr1,
            Expression<Func<T, bool>> expr2)
        {
            InvocationExpression invokedExpr = Expression.Invoke(expr2, expr1.Parameters);
            return Expression.Lambda<Func<T, bool>>
                (Expression.AndAlso(expr1.Body, invokedExpr), expr1.Parameters);
        }
    }
}
