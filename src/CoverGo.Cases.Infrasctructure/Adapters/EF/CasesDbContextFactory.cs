using CoverGo.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;
using System;
using System.Linq;

namespace CoverGo.Cases.Infrastructure.Adapters.EF
{
    public class CasesDbContextFactory
    {
        public CasesDbContext CreateDbContext(string tenantId) =>
            new CasesDbContext(BuildContextOptions(DbConfig.GetConfig(tenantId)));

        public CasesDbContext CreateDbContext(DbConfig config) =>
            new CasesDbContext(BuildContextOptions(config));

        public DbContextOptions BuildContextOptions(DbConfig dbConfig)
        {
            string[] mySqlServerVersionSegments = GetMySqlServerVersionSegmentsFromDbConfigOptions();

            DbContextOptionsBuilder builder = new DbContextOptionsBuilder<CasesDbContext>().UseMySql(
                dbConfig.ConnectionString ?? $"server={dbConfig.Endpoint};database={GetDbNameFromDbConfigOptions() ?? "cases"};user={dbConfig.Username};password={dbConfig.Password};",
                new MySqlServerVersion(mySqlServerVersionSegments != null
                    ? new Version(int.Parse(mySqlServerVersionSegments[0]), int.Parse(mySqlServerVersionSegments[1]),
                        int.Parse(mySqlServerVersionSegments[2]))
                    : new Version(10, 3, 24)),
                mySqlOptions => mySqlOptions
                    .EnableRetryOnFailure(10));

            return builder.Options;

            string[] GetMySqlServerVersionSegmentsFromDbConfigOptions()
            {
                string mySqlServerVersion = null;
                dynamic mySqlServerVersionDynamic = null;
                if (dbConfig.Options?.TryGetValue("mySqlServerVersion", out mySqlServerVersionDynamic) == true)
                    mySqlServerVersion = mySqlServerVersionDynamic.ToString();

                return mySqlServerVersion?.Split('.');
            }

            string GetDbNameFromDbConfigOptions() //prefer to use connection string as database should include tenant name and it can be populated there instead of in options
            {
                string dbName = null;
                dynamic dbNameDynamic = null;
                if (dbConfig.Options?.TryGetValue("dbName", out dbNameDynamic) == true)
                    dbName = dbNameDynamic.ToString();

                return dbName;
            }
        }
    }

    public class DesignTimeCaseDbContextFactory : IDesignTimeDbContextFactory<CasesDbContext> //only to run add migration on, either have to register one in startup, implement on configuring in the context or this
    {
        public CasesDbContext CreateDbContext(string[] args) =>
            new CasesDbContext(
                new DbContextOptionsBuilder<CasesDbContext>().UseMySql(
                    DbConfig.GetConfigs().FirstOrDefault()?.ConnectionString ?? $"server=localhost:3306;database=cases;user=root;password=local_dev;",
                    new MySqlServerVersion(new Version(10, 3, 24))).Options
            );
    }
}
