﻿using System;

namespace CoverGo.Cases.Infrastructure
{
    public struct DateTimeWithZone
    {
        private readonly DateTime _utcDateTime;
        private readonly TimeZoneInfo _timeZone;

        public DateTimeWithZone(DateTime dateTime, TimeZoneInfo timeZone)
        {
            DateTime dateTimeUnspec = DateTime.SpecifyKind(dateTime, DateTimeKind.Unspecified);
            _utcDateTime = TimeZoneInfo.ConvertTimeToUtc(dateTimeUnspec, timeZone);
            _timeZone = timeZone;
        }

        public DateTime? UniversalTime => _utcDateTime;

        public TimeZoneInfo TimeZone => _timeZone;

        public DateTime? LocalTime => TimeZoneInfo.ConvertTime(_utcDateTime, _timeZone);
    }
}