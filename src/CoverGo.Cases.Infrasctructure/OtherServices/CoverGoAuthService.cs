﻿using CoverGo.Cases.Domain.OtherServices;
using CoverGo.DomainUtils;
using IdentityModel.Client;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public class CoverGoAuthService : IAuthService
    {
        private readonly HttpClient _client;
        public CoverGoAuthService(HttpClient client)
        {
            _client = client;
        }

        public async Task<Token> GetWholeAccessTokenAsync(string tenantId, string clientId, string clientSecret, string username, string password, CancellationToken cancellationToken)
        {
            TokenResponse response = await _client.RequestPasswordTokenAsync(new PasswordTokenRequest
            {
                Address = $"{tenantId}/connect/token",
                //Address = $"connect/token",
                ClientId = clientId,
                UserName = username,
                Password = password,
                //Scope = "openid profile email",
                Scope = "custom_profile offline_access"
            }, cancellationToken);

            return new Token
            {
                AccessToken = response.AccessToken,
                Error = response.Error,
                ErrorDescription = response.ErrorDescription,
                ExpiresIn = response.ExpiresIn,
                IdentityToken = response.IdentityToken,
                RefreshToken = response.RefreshToken,
                TokenType = response.TokenType
            };
        }

        public async Task<Auth.Client.Login?> GetLoginByEntityId(string tenantId, string entityId, CancellationToken cancellationToken)
        {
            var response = await _client.GenericPostAsync<IEnumerable<Auth.Client.Login>, object>($"{tenantId}/api/v1/auth/logins/filter", new { Where = new { EntityIds = new[] { entityId } }, First = 1 }, cancellationToken);
            return response.FirstOrDefault();
        }

        public async Task<IEnumerable<Auth.Client.Login?>> GetLoginsAsync(string tenantId, QueryArguments<Auth.Client.LoginWhere> queryArguments, CancellationToken cancellationToken = default) =>
           await _client.GenericPostAsync<IEnumerable<Auth.Client.Login>, QueryArguments<Auth.Client.LoginWhere>>($"{tenantId}/api/v1/auth/logins/filter", queryArguments, cancellationToken);
    }
}
