﻿using CoverGo.Cases.Domain.OtherServices;
using GraphQL;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public class CoverGoGatewayService : IGatewayService
    {
        private readonly HttpClient _client;
        private readonly ILogger<CoverGoGatewayService> _logger;

        public CoverGoGatewayService(HttpClient client, ILogger<CoverGoGatewayService> logger)
        {
            _client = client;
            _logger = logger;
        }

        public async Task<JToken> GetContentAsync(string accessToken, string query, JObject variables, CancellationToken cancellationToken)
        {
            var graphQLRequest = new GraphQLRequest
            {
                Query = query,
                Variables = variables
            };

            using var graphQLClient = new GraphQLHttpClient(_client.BaseAddress + "graphql", new NewtonsoftJsonSerializer());
            graphQLClient.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            try
            {
                GraphQLResponse<JToken> result = await graphQLClient.SendQueryAsync<JToken>(graphQLRequest, cancellationToken);
                return result.Data;
            }
            catch (GraphQLHttpRequestException ex)
            {
                _logger.LogError(ex, "GraphQLHttpRequestException occurred while sending query to Gateway. {headers}. {content}.",
                    ex.ResponseHeaders, ex.Content);
                throw;
            }
        }
    }
}
