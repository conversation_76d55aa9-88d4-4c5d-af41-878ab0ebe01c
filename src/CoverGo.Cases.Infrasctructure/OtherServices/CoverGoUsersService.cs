﻿using CoverGo.Cases.Domain.OtherServices;
using CoverGo.Users.Client;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public class CoverGoUsersService : IUsersService
    {
        private readonly IUsersClient _client;

        public CoverGoUsersService(IUsersClient client)
        {
            _client = client;
        }

        public async Task<Company> GetCompanyByIdAsync(string tenantId, string companyId,
            CancellationToken cancellationToken = default)
        {
            var companies = await _client.Companies_QueryAsync(tenantId, new QueryArgumentsOfCompanyWhere { Where = new CompanyWhere { Id = companyId } }, cancellationToken);
            return companies?.FirstOrDefault();
        }
    }
}
