﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public static class HttpClientExtensions
    {
        public static IHttpContextAccessor HttpContextAccessor { get; set; }
        private static readonly JsonSerializer s_serializer;
        private static readonly JsonSerializerSettings s_settings;

        static HttpClientExtensions()
        {
            s_settings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
            };

            s_serializer = JsonSerializer.Create(s_settings);
        }

        public static async Task<T> GenericPostAsync<T, U>(this HttpClient client, string uri, U command, CancellationToken cancellationToken)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Post, uri, command, cancellationToken))
            {
                return await response.Content.ReadAsJsonAsync<T>(cancellationToken);
            }
        }

        public static async Task<T> GenericGetAsync<T>(this HttpClient client, string uri, CancellationToken cancellationToken)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Get, uri, (object)null, cancellationToken))
            {
                return await response.Content.ReadAsJsonAsync<T>(cancellationToken);
            }
        }

        public static async Task<T> GenericDeleteAsync<T>(this HttpClient client, string uri, CancellationToken cancellationToken)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Delete, uri, (object)null, cancellationToken))
            {
                return await response.Content.ReadAsJsonAsync<T>(cancellationToken);
            }
        }

        public static async Task<T> GenericPutAsync<T, U>(this HttpClient client, string uri, U command, CancellationToken cancellationToken)
        {
            using (HttpResponseMessage response = await client.GenericSendAsync(HttpMethod.Put, uri, command, cancellationToken))
            {
                return await response.Content.ReadAsJsonAsync<T>(cancellationToken);
            }
        }

        private static async Task<HttpResponseMessage> GenericSendAsync<T>(this HttpClient httpClient, HttpMethod method, string url, T data, CancellationToken cancellationToken)
        {
            using (var request = new HttpRequestMessage()
            {
                RequestUri = new Uri(httpClient.BaseAddress + url),
                Method = method,
                Content = GetContent(data)
            })
            {
                request.Headers.AcceptLanguage.TryParseAdd(CultureInfo.CurrentCulture.Name);
                if (HttpContextAccessor != null && HttpContextAccessor.HttpContext.Request.Headers.TryGetValue("Authorization", out var authorization))
                {
                    request.Headers.TryAddWithoutValidation("Authorization", (IEnumerable<string>)authorization);
                }
                HttpResponseMessage response = await httpClient.SendAsync(request, cancellationToken);
                return response;
            }
        }

        public static async Task<T> ReadAsJsonAsync<T>(this HttpContent content, CancellationToken cancellationToken)
        {
            using (Stream stream = await content.ReadAsStreamAsync())
                return DeserializeFromStream<T>(stream);
        }

        private static T DeserializeFromStream<T>(Stream stream)
        {
            using (var sr = new StreamReader(stream))
            using (var jsonTextReader = new JsonTextReader(sr))
            {
                return s_serializer.Deserialize<T>(jsonTextReader);
            }
        }

        private static StringContent GetContent<T>(T data)
        {
            string dataAsString = JsonConvert.SerializeObject(data, s_settings);
            var content = new StringContent(dataAsString);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            return content;
        }
    }
}
