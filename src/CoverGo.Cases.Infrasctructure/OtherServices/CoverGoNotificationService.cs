﻿using CoverGo.Cases.Domain.OtherServices;
using CoverGo.DomainUtils;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public class CoverGoNotificationService : INotificationService
    {
        private readonly HttpClient _client;

        public CoverGoNotificationService(HttpClient client)
        {
            _client = client;
        }

        public Task SendAsync(string tenantId, SendNotificationCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, SendNotificationCommand>($"{tenantId}/api/v1/notifications/users", command, cancellationToken);
    }
}
