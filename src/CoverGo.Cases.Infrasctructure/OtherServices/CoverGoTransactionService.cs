﻿using CoverGo.Cases.Domain;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public class CoverGoTransactionService : ITransactionService
    {
        private readonly HttpClient _client;

        public CoverGoTransactionService(HttpClient client)
        {
            _client = client;
        }

        public Task<List<Transaction>> GetAsync(string tenantId, TransactionQueryArguments queryArguments, CancellationToken cancellationToken) =>
           _client.GenericPostAsync<List<Transaction>, TransactionQueryArguments>($"{tenantId}/api/v1/transactions/query", queryArguments, cancellationToken);
    }
}
