﻿using CoverGo.Cases.Domain.OtherServices;

using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    public class CoverGoPricingService : IPricingService
    {
        private readonly HttpClient _client;

        public CoverGoPricingService(HttpClient client)
        {
            _client = client;
        }

        public Task<PriceDto2> CalculateAsync(string tenantId, PriceCalculationFactors factors, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<PriceDto2, PriceCalculationFactors>($"{tenantId}/api/v1/pricing/calculate", factors, cancellationToken);
    }
}
