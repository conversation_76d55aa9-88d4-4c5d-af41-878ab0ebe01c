﻿using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.DomainUtils;

namespace CoverGo.Cases.Infrastructure.OtherServices
{
    [Obsolete("use CoverGo.Policies.Client.IPoliciesClient directly")]
    public class CoverGoPolicyService : IPolicyService
    {
        private readonly HttpClient _client;

        public CoverGoPolicyService(HttpClient client)
        {
            _client = client;
        }

        public async Task<Result<PolicyStatus>> CreatePolicyAsync(string tenantId, CreatePolicyCommand command, string accessToken = null, CancellationToken cancellationToken = default)
            => await _client.GenericPostAsync<Result<PolicyStatus>, CreatePolicyCommand>($"{tenantId}/api/v1/policies?accessToken={accessToken}", command, cancellationToken);

        public Task<Result<string>> AddClauseToPolicyAsync(string tenantId, string policyId, AddClauseCommand command, CancellationToken cancellationToken)
            => _client.GenericPostAsync<Result<string>, AddClauseCommand>($"{tenantId}/api/v1/policies/{policyId}/clauses/add", command, cancellationToken);

        public Task<Result> AddAssociatedContractToPolicyAsync(string tenantId, string policyId, AddAssociatedContractCommand command, CancellationToken cancellationToken)
            => _client.GenericPostAsync<Result, AddAssociatedContractCommand>($"{tenantId}/api/v1/policies/{policyId}/associatedContracts/add", command, cancellationToken);

        public Task<Result<string>> AddStakeholderToPolicyAsync(string tenantId, string policyId, AddStakeholderCommand command, CancellationToken cancellationToken)
           => _client.GenericPostAsync<Result<string>, AddStakeholderCommand>($"{tenantId}/api/v1/policies/{policyId}/stakeholders/add", command, cancellationToken);

        public Task<Result> PolicyFactBatch(string tenantId, string policyId, FactCommandBatch batch, CancellationToken cancellationToken)
            => _client.GenericPostAsync<Result, FactCommandBatch>($"{tenantId}/api/v1/policies/{policyId}/policyFactBatch", batch, cancellationToken);

        public async Task<Result<PolicyStatus>> IssuePolicyAsync(string tenantId, IssuePolicyCommand command, CancellationToken cancellationToken)
            => await _client.GenericPostAsync<Result<PolicyStatus>, IssuePolicyCommand>($"{tenantId}/api/v1/policies/issue", command, cancellationToken);

        public Task<Result> AddCommissionAsync(string tenantId, string policyId, AddCommissionCommand command, CancellationToken cancellationToken)
            => _client.GenericPostAsync<Result, AddCommissionCommand>($"{tenantId}/api/v1/policies/commissions/{policyId}/add", command, cancellationToken);

        public Task<Result> UpdateCommissionAsync(string tenantId, string policyId, UpdateCommissionCommand command, CancellationToken cancellationToken)
            => _client.GenericPostAsync<Result, UpdateCommissionCommand>($"{tenantId}/api/v1/policies/commissions/{policyId}/update", command, cancellationToken);

        public Task<Result> RemoveCommissionAsync(string tenantId, string policyId, RemoveCommissionCommand command, CancellationToken cancellationToken)
            => _client.GenericPostAsync<Result, RemoveCommissionCommand>($"{tenantId}/api/v1/policies/commissions/{policyId}/remove", command, cancellationToken);

        public Task<Result<CreatedStatus>> AddExclusionAsync(string tenantId, string policyId, AddExclusionCommand command, CancellationToken cancellationToken)
        => _client.GenericPostAsync<Result<CreatedStatus>, AddExclusionCommand>($"{tenantId}/api/v1/policies/{policyId}/exclusions/add", command, cancellationToken);
        public Task<Result> RemoveExclusionAsync(string tenantId, string policyId, RemoveCommand command, CancellationToken cancellationToken)
           => _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/{policyId}/exclusions/remove", command, cancellationToken);

        public Task<Result> AddBeneficiaryEligibilityAsync(string tenantId, string policyId, AddBeneficiaryEligibilityCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, AddBeneficiaryEligibilityCommand>($"{tenantId}/api/v1/policies/beneficiaryEligibilities/{policyId}", command, cancellationToken);

        public Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string policyId, UpdateBeneficiaryEligibilityCommand command, CancellationToken cancellationToken) =>
            _client.GenericPutAsync<Result, UpdateBeneficiaryEligibilityCommand>($"{tenantId}/api/v1/policies/beneficiaryEligibilities/{policyId}", command, cancellationToken);

        public Task<Result> RemoveBeneficiaryEligibilityAsync(string tenantId, string policyId, RemoveCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/beneficiaryEligibilities/{policyId}/remove", command, cancellationToken);

        public Task<Result> AddPaymentInfoAsync(string tenantId, string policyId, AddPaymentInfoCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, AddPaymentInfoCommand>($"{tenantId}/api/v1/policies/paymentInfos/{policyId}", command, cancellationToken);

        public Task<Result> UpdatePaymentInfoAsync(string tenantId, string policyId, UpdatePaymentInfoCommand command, CancellationToken cancellationToken) =>
            _client.GenericPutAsync<Result, UpdatePaymentInfoCommand>($"{tenantId}/api/v1/policies/paymentInfos/{policyId}", command, cancellationToken);

        public Task<Result> RemovePaymentInfoAsync(string tenantId, string policyId, RemoveCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, RemoveCommand>($"{tenantId}/api/v1/policies/paymentInfos/{policyId}/remove", command, cancellationToken);
        public Task<Result> AddJacketsToPolicyAsync(string tenantId, string policyId, AddJacketsToPolicyCommand command, CancellationToken cancellationToken) =>
            _client.GenericPostAsync<Result, AddJacketsToPolicyCommand>($"{tenantId}/api/v1/policies/{policyId}/jackets/batch", command, cancellationToken);
    }
}
