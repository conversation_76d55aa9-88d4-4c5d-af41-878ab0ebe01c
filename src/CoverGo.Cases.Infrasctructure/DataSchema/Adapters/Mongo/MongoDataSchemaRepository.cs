﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Cases.Domain.DataSchemas;
using CoverGo.Configuration;
using CoverGo.DomainUtils;
using CoverGo.MongoUtils;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;

namespace CoverGo.Products.Infrastructure.DataSchemas.Adapters.Mongo
{
    public class MongoDataSchemaRepository : IDataSchemaRepository
    {
        const string DbName = "cases";

        public string ProviderId { get; } = "mongoDb";

        public async Task<IReadOnlyCollection<DataSchema>> GetAsync(string tenantId, DataSchemaWhere where, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "cases"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<DataSchema> collection = db.GetCollection<DataSchema>($"{tenantId}-dataSchemas");

            FilterDefinition<DataSchema> idFilter = CreateFilter(where);

            return await collection.Find(idFilter).ToListAsync(cancellationToken);
        }

        FilterDefinition<DataSchema> CreateFilter(DataSchemaWhere where)
        {
            FilterDefinition<DataSchema> result = Builders<DataSchema>.Filter.Empty;

            if (where?.Or?.Any() == true)
                return CreateOrFilter(result, where);

            if (where?.And?.Any() == true)
                return CreateAndFilter(result, where);

            return CreateFieldFilter(result, where);
        }

        FilterDefinition<DataSchema> CreateOrFilter(
            FilterDefinition<DataSchema> filter,
            DataSchemaWhere where)
        {
            foreach (DataSchemaWhere orFilter in where.Or)
                filter = filter != FilterDefinition<DataSchema>.Empty
                    ? filter | CreateFilter(orFilter)
                    : CreateFilter(orFilter);

            return filter;
        }

        FilterDefinition<DataSchema> CreateAndFilter(
            FilterDefinition<DataSchema> filter,
            DataSchemaWhere where)
        {
            foreach (DataSchemaWhere andFilter in where.And)
                filter &= CreateFilter(andFilter);

            return filter;
        }

        FilterDefinition<DataSchema> CreateFieldFilter(
            FilterDefinition<DataSchema> filter,
            DataSchemaWhere where)
        {
            if (where == null)
                return filter;
            if (where.Id != null)
                return Builders<DataSchema>.Filter.Eq(x => x.Id, where.Id);
            if (where.Id_in != null)
                return Builders<DataSchema>.Filter.In(x => x.Id, where.Id_in);
            if (where.Type != null)
                return Builders<DataSchema>.Filter.Eq(x => x.Type, where.Type);
            if (where.Tags_contains != null)
                return Builders<DataSchema>.Filter.AnyIn(x => x.Tags, where.Tags_contains);
            return filter;
        }

        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, string id, CreateDataSchemaCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "cases"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<DataSchema> collection = db.GetCollection<DataSchema>($"{tenantId}-dataSchemas");
            await collection.Indexes.CreateOneAsync(
                new CreateIndexModel<DataSchema>(Builders<DataSchema>.IndexKeys.Ascending("Id")),
                cancellationToken: cancellationToken
            );

            var dataSchema = new DataSchema
            {
                Id = id,
                Name = command.Name,
                Description = command.Description,
                Schema = JToken.Parse(command.Schema),
                Standard = command.Standard,
                Type = command.Type,
                LastModifiedById = command.CreatedById,
                LastModifiedAt = DateTime.UtcNow,
                CreatedAt = DateTime.UtcNow,
                Tags = command.Tags
            };

            await collection.InsertOneAsync(dataSchema, cancellationToken);

            return new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Id = dataSchema.Id } };
        }

        public async Task<Result> UpdateAsync(string tenantId, UpdateDataSchemaCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "cases"));
            IMongoDatabase db = client.GetDatabase(DbName);

            IMongoCollection<DataSchema> collection = db.GetCollection<DataSchema>($"{tenantId}-dataSchemas");

            FilterDefinition<DataSchema> filter = Builders<DataSchema>.Filter.Eq(p => p.Id, command.DataSchemaId);

            UpdateDefinitionBuilder<DataSchema> update = Builders<DataSchema>.Update;

            var updates = new List<UpdateDefinition<DataSchema>>
            {
                update.Set(a => a.LastModifiedAt, DateTime.UtcNow),
                update.Set(a => a.LastModifiedById, command.ModifiedById)
            };
            if (command.Name != null) updates.Add(update.Set(a => a.Name, command.Name));
            if (command.Description != null) updates.Add(update.Set(a => a.Description, command.Description));
            if (command.Schema != null) updates.Add(update.Set(a => a.Schema, JToken.Parse(command.Schema)));
            if (command.Standard != null) updates.Add(update.Set(a => a.Standard, command.Standard));
            if (command.Type != null) updates.Add(update.Set(a => a.Type, command.Type));
            if (command.Tags != null) updates.Add(update.Set(a => a.Tags, command.Tags));

            UpdateResult result = await collection.UpdateOneAsync(filter, update.Combine(updates), cancellationToken: cancellationToken);

            return new Result { Status = result.MatchedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> DeleteAsync(string tenantId, string dataSchemaId, DeleteCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "cases"));
            IMongoDatabase db = client.GetDatabase(DbName);

            DeleteResult result = await db.GetCollection<DataSchema>($"{tenantId}-dataSchemas").DeleteOneAsync(Builders<DataSchema>.Filter.Eq(p => p.Id, dataSchemaId), cancellationToken);

            return result.DeletedCount > 0
                ? new Result { Status = "success" }
                : new Result { Status = "failure", Errors = new List<string> { "DataSchema with such id doesn't exist" } };
        }

        public async Task<Result> AddUiSchemaToDataSchemaAsync(string tenantId, AddUiSchemaToDataSchemaCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "cases"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<DataSchema> collection = db.GetCollection<DataSchema>($"{tenantId}-dataSchemas");

            FilterDefinition<DataSchema> filter = Builders<DataSchema>.Filter.Eq(e => e.Id, command.DataSchemaId);
            DataSchema dataSchema = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (dataSchema == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The dataSchema {command.DataSchemaId} was not found." }
                };

            UpdateDefinition<DataSchema> update = Builders<DataSchema>.Update
                .Set(p => p.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.AddedById)
                .AddToSet(p => p.UiSchemaIds, command.UiSchemaId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken:cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }

        public async Task<Result> RemoveUiSchemaFromDataSchemaAsync(string tenantId, RemoveUiSchemaFromDataSchemaCommand command, CancellationToken cancellationToken)
        {
            MongoClient client = MongoTools.GetOrAddMongoClient(DbConfig.GetConfig(tenantId, "cases"));
            IMongoDatabase db = client.GetDatabase(DbName);
            IMongoCollection<DataSchema> collection = db.GetCollection<DataSchema>($"{tenantId}-dataSchemas");

            FilterDefinition<DataSchema> filter = Builders<DataSchema>.Filter.Eq(e => e.Id, command.DataSchemaId);
            DataSchema dataSchema = await collection.Find(filter).FirstOrDefaultAsync(cancellationToken);

            if (dataSchema == null)
                return new Result
                {
                    Status = "failure",
                    Errors = new List<string> { $"The dataSchema {command.DataSchemaId} was not found." }
                };

            UpdateDefinition<DataSchema> update = Builders<DataSchema>.Update
                .Set(a => a.LastModifiedAt, DateTime.UtcNow)
                .Set(p => p.LastModifiedById, command.RemovedById)
                .Pull(a => a.UiSchemaIds, command.UiSchemaId);

            UpdateResult result = await collection.UpdateOneAsync(filter, update, cancellationToken: cancellationToken);

            return new Result { Status = result.ModifiedCount != 0 ? "success" : "failure" };
        }
    }
}