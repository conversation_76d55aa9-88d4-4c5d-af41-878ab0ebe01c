using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.Billing;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Pricing;
using ProductId = CoverGo.Quotation.Domain.Offers.ProductId;

namespace CoverGo.Quotation.Application.VersionBridge.Mappers;

public class CaseOfferToOfferAggregateMapper
{
    public virtual OfferAggregate? ToOfferAggregate(Offer caseOffer)
    {
        if (string.IsNullOrWhiteSpace(caseOffer.Id)) return null;
        if (string.IsNullOrWhiteSpace(caseOffer.CaseId)) return null;
        if (caseOffer.ProductId is null) return null;

        var productVersionId = new ProductVersionId
        {
            ProductId = new ProductId
            {
                Plan = caseOffer.ProductId.Plan,
                Type = caseOffer.ProductId.Type
            },
            Version = caseOffer.ProductId.Version
        };

        var billingInformation = new BillingInformation
        {
            BillingFrequency = null,
            BillingPricingDateBasis = null,
            BillingYearMode = null,
            PayorId = null
        };

        // Map PolicyDetails if dates are available
        PolicyDetails? policyDetails = null;
        if (caseOffer is { StartDate: not null, EndDate: not null })
        {
            policyDetails = new PolicyDetails(
                DateOnly.FromDateTime(caseOffer.StartDate.Value),
                DateOnly.FromDateTime(caseOffer.EndDate.Value),
                []
            );
        }

        return new OfferAggregate
        {
            Id = new ValueObjectId<OfferAggregate>(caseOffer.Id),
            OpportunityId = new ValueObjectId<OpportunityAggregate>(caseOffer.CaseId),
            BillingInformation = billingInformation,
            ProductVersionId = productVersionId,
            LegacyOfferId = new LegacyOfferId(caseOffer.Id),
            Status = MapStatus(caseOffer.Status),
            PolicyDetails = policyDetails,
            InternalCode = caseOffer.OfferNumber,
            LegacyPolicyId = !string.IsNullOrWhiteSpace(caseOffer.PolicyNumber)
                ? new ValueObjectId<LegacyPolicy>(caseOffer.PolicyNumber)
                : null,
            Pricing = MapPricing(caseOffer.Premium)
        };
    }

    private static OfferStatus MapStatus(string? status)
    {
        return status switch
        {
            // Cases.Domain.Constants.OfferStatus constants
            "Added" => OfferStatus.Draft,
            "Sent" => OfferStatus.Issued,
            "Accepted" => OfferStatus.Accepted,
            "Rejected" => OfferStatus.Rejected,
            "Lapsed" => OfferStatus.Expired,
            "Submitted" => OfferStatus.Draft,
            "UnderwriterRequested" => OfferStatus.Draft,
            "UnderwriterInProgress" => OfferStatus.Draft,
            "NotProceeding" => OfferStatus.Rejected,
            "UnderwriterRejected" => OfferStatus.Rejected,
            "UnderwriterApproved" => OfferStatus.Draft,

            // Additional common status values
            "DRAFT" => OfferStatus.Draft,
            "ISSUED" => OfferStatus.Issued,
            "ACCEPTED" => OfferStatus.Accepted,
            "REJECTED" => OfferStatus.Rejected,
            "EXPIRED" => OfferStatus.Expired,
            "Expired" => OfferStatus.Expired,

            // Default fallback
            _ => OfferStatus.Draft
        };
    }

    private static QuotationPricing? MapPricing(Premium? premium)
    {
        if (premium?.Amount == null) return null;

        // Convert Cases domain CurrencyCode to Quotation domain CurrencyCode
        string currencyCode = premium.CurrencyCode?.ToString() ?? "USD";
        var quotationCurrencyCode = new Domain.Common.CurrencyCode(currencyCode);

        // Calculate totals from premium data
        decimal totalAmount = premium.Amount.Value;
        decimal originalAmount = premium.OriginalPrice ?? totalAmount;

        // Calculate discounts total
        decimal discountsTotal = premium.AppliedDiscounts?.Sum(d => d.NewPrice - d.OriginalPrice) ?? 0m;
        if (discountsTotal > 0) discountsTotal = 0m; // Discounts should be negative or zero

        // Calculate loadings total
        decimal loadingsTotal = premium.Loadings?.Sum(l => (l.NewPrice ?? 0m) - (l.OriginalPrice ?? 0m)) ?? 0m;

        // Calculate taxes total
        decimal taxesTotal = premium.AppliedTaxes?.Sum(t => (t.NewPrice ?? 0m) - (t.OriginalPrice ?? 0m)) ?? 0m;

        // Create Money objects
        var zero = new Money(quotationCurrencyCode);
        var totalMoney = new Money(quotationCurrencyCode, totalAmount);
        var premiumsMoney = new Money(quotationCurrencyCode, originalAmount);
        var discountsMoney = new Money(quotationCurrencyCode, Math.Abs(discountsTotal));
        var loadingsMoney = new Money(quotationCurrencyCode, loadingsTotal);
        var taxesMoney = new Money(quotationCurrencyCode, taxesTotal);

        // Calculate gross amount (total before taxes)
        decimal grossAmount = totalAmount - taxesTotal;
        var grossMoney = new Money(quotationCurrencyCode, grossAmount);

        return new QuotationPricing
        {
            Summary = new QuotationPricingSummary
            {
                Net = premiumsMoney,
                Loading = loadingsMoney,
                Discounts = discountsMoney,
                Gross = grossMoney,
                Taxes = taxesMoney,
                GrossWithTaxes = totalMoney,
                Commissions = zero
            },
            Totals = new QuotationPricingTotals
            {
                Premiums = premiumsMoney,
                Total = totalMoney,
                Factors = zero,
                Taxes = taxesMoney,
                Fees = zero,
                Loadings = loadingsMoney,
                Roundings = zero,
                Discounts = discountsMoney,
                Commissions = zero,
                Limits = zero
            },
            ModalFee = zero,
            ModalFactor = 1.0m,
            Commissions = new Commissions
            {
                PrimaryAgentCommission = zero,
                SecondaryAgentCommission = zero
            }
        };
    }
}
