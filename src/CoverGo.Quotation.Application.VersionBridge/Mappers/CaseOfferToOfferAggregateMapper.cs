using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.Billing;
using CoverGo.Quotation.Domain.Opportunities;
using ProductId = CoverGo.Quotation.Domain.Offers.ProductId;

namespace CoverGo.Quotation.Application.VersionBridge.Mappers;

public class CaseOfferToOfferAggregateMapper
{
    public virtual OfferAggregate? ToOfferAggregate(Offer caseOffer)
    {
        if (string.IsNullOrWhiteSpace(caseOffer.Id)) return null;
        if (string.IsNullOrWhiteSpace(caseOffer.CaseId)) return null;
        if (caseOffer.ProductId is null) return null;

        var productVersionId = new ProductVersionId
        {
            ProductId = new ProductId
            {
                Plan = caseOffer.ProductId.Plan,
                Type = caseOffer.ProductId.Type
            },
            Version = caseOffer.ProductId.Version
        };

        var billingInformation = new BillingInformation
        {
            BillingFrequency = null,
            BillingPricingDateBasis = null,
            BillingYearMode = null,
            PayorId = null
        };

        // Map PolicyDetails if dates are available
        PolicyDetails? policyDetails = null;
        if (caseOffer.StartDate.HasValue && caseOffer.EndDate.HasValue)
        {
            policyDetails = new PolicyDetails(
                DateOnly.FromDateTime(caseOffer.StartDate.Value),
                DateOnly.FromDateTime(caseOffer.EndDate.Value),
                new List<PolicyField>()
            );
        }

        return new OfferAggregate
        {
            Id = new ValueObjectId<OfferAggregate>(caseOffer.Id),
            OpportunityId = new ValueObjectId<OpportunityAggregate>(caseOffer.CaseId),
            BillingInformation = billingInformation,
            ProductVersionId = productVersionId,
            LegacyOfferId = new LegacyOfferId(caseOffer.Id),
            Status = MapStatus(caseOffer.Status),
            PolicyDetails = policyDetails,
            InternalCode = caseOffer.OfferNumber,
            LegacyPolicyId = !string.IsNullOrWhiteSpace(caseOffer.PolicyNumber)
                ? new ValueObjectId<LegacyPolicy>(caseOffer.PolicyNumber)
                : null
        };
    }

    private static OfferStatus MapStatus(string? status)
    {
        return status switch
        {
            // Cases.Domain.Constants.OfferStatus constants
            "Added" => OfferStatus.Draft,
            "Sent" => OfferStatus.Issued,
            "Accepted" => OfferStatus.Accepted,
            "Rejected" => OfferStatus.Rejected,
            "Lapsed" => OfferStatus.Expired,
            "Submitted" => OfferStatus.Draft,
            "UnderwriterRequested" => OfferStatus.Draft,
            "UnderwriterInProgress" => OfferStatus.Draft,
            "NotProceeding" => OfferStatus.Rejected,
            "UnderwriterRejected" => OfferStatus.Rejected,
            "UnderwriterApproved" => OfferStatus.Draft,

            // Additional common status values
            "DRAFT" => OfferStatus.Draft,
            "ISSUED" => OfferStatus.Issued,
            "ACCEPTED" => OfferStatus.Accepted,
            "REJECTED" => OfferStatus.Rejected,
            "EXPIRED" => OfferStatus.Expired,
            "Expired" => OfferStatus.Expired,

            // Default fallback
            _ => OfferStatus.Draft
        };
    }
}
