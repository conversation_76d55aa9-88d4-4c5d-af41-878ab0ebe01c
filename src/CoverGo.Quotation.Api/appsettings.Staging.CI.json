{"serviceUrls": {"auth": "http://covergo-auth:8080/"}, "ConnectionStrings": {"auth": "http://covergo-auth:8080/", "gateway": "http://covergo-gateway:8080/", "policies": "http://covergo-policies:8080/", "products": "http://covergo-products:8080/", "users": "http://covergo-users:8080/", "channelManagement": "http://channel-management:8080/", "reference": "http://covergo-reference:8080/", "templates": "http://covergo-templates:8080/", "premium": "http://covergo-premium:8080/", "redis": "redis:6379,abortConnect=false", "fileSystem": "http://covergo-filesystem:8080/"}, "MongoDatabaseConfiguration": {"ConnectionString": "**************************************?replicaSet=covergo-mongo-set"}, "GraphQL": {"IncludeExceptionDetails": true}, "GraphQLStitching": {"Enabled": true, "SchemaName": "quotation", "Redis": {"Publish": true, "ConfigurationName": "GatewayV2"}}, "CheckDI": false, "UseInMemoryBus": true, "RunJobs": false, "BuyFlowLinkAesEncryptionServiceKey": "qcSRBvH6s0pSZnU4tJxzFnjI5Pz2qdM1", "BuyFlowLinkAesEncryptionServiceIV": "AAAAAAAAAAAAAAAAAAAAAA=="}