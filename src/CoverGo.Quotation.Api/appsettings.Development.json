{"Serilog": {"WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Level:u3}][{TraceId}][{SpanId}][{StatusCode}][{Timestamp:HH:mm:ss.fff}][T{ThreadId:D2}][{Tenant}][{ApplicationName}][{EnvironmentName}][{MachineName}][T{ThreadId:D2}][{SourceContext}] {Message:l}{NewLine}{Exception}"}}]}, "serviceUrls": {"auth": "http://localhost:60000/"}, "ConnectionStrings": {"redis": "localhost:6379,abortConnect=false", "gateway": "http://localhost:60060/", "policies": "http://localhost:60050/", "products": "http://localhost:60020/", "channelManagement": "http://localhost:64483/", "users": "http://localhost:60010/", "reference": "http://localhost:61910/", "templates": "http://localhost:63542/", "auth": "http://covergo-auth:8080", "premium": "http://localhost:50020/", "fileSystem": "http://localhost:61872/"}, "HealthChecksUI": {"HealthChecks": [{"Name": "Quotation Service Health", "Uri": "http://localhost:51504/health-api"}], "Webhooks": [], "EvaluationTimeInSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}, "MongoDatabaseConfiguration": {"ConnectionString": "****************************************?replicaSet=covergo-mongo-set"}, "GraphQL": {"IncludeExceptionDetails": true}, "GraphQLStitching": {"Enabled": true, "SchemaName": "quotation", "Redis": {"Publish": false, "ConfigurationName": "GatewayV2"}}, "CheckDI": true, "FeatureManagement": {"UseEffectiveDateInAddPolicyMember": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_uat", "asia_prod"]}}]}}}