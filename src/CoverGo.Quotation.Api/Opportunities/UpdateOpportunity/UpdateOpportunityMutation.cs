using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Application.Offers.Opportunities;
using CoverGo.Quotation.Application.Opportunities.Commands;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities.Exceptions;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Opportunities.UpdateOpportunity;

[MutationType]
public class UpdateOpportunityMutation
{
    [Authorize]
    [Error(typeof(InputDataValidationError))]
    public Task<Opportunity> UpdateOpportunity(
        [Service] IMediator mediator,
        UpdateOpportunityCommand input,
        CancellationToken cancellationToken
    ) => mediator.Send(input, cancellationToken);

    [Authorize]
    public Task<Opportunity> UpdateOpportunityClient(
        [Service] IMediator mediator,
        UpdateOpportunityClientCommand input,
        CancellationToken cancellationToken
    ) => mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(PrimaryClientCannotBeSupplementaryClientException))]
    [Error(typeof(SupplementaryClientTypeMismatchException))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(SupplementaryClientsNotAllowedException))]
    [Error(typeof(BeneficiaryNotFromPolicyHoldersException))]
    public Task<Opportunity> UpdateOpportunitySupplementaryClients(
        UpdateOpportunitySupplementaryClientsCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken
    ) => mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(MultipleBeneficiariesNotAllowedException))]
    [Error(typeof(BeneficiaryNotFromPolicyHoldersException))]
    public Task<Opportunity> UpdateOpportunityBeneficiaries(
        UpdateOpportunityBeneficiariesCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken
    ) => mediator.Send(input, cancellationToken);
}
