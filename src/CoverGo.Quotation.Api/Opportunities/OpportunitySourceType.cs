using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Api.Opportunities;

public sealed class OpportunitySourceType : ObjectType<OpportunitySource>
{
    protected override void Configure(IObjectTypeDescriptor<OpportunitySource> descriptor)
    {
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
    }
}

public sealed class OpportunitySourceInputType : InputObjectType<OpportunitySource>
{
    protected override void Configure(IInputObjectTypeDescriptor<OpportunitySource> descriptor)
    {
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
    }
}
