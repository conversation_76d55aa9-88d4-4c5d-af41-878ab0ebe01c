using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Opportunities;
using CoverGo.Quotation.Application.Opportunities.Contracts;

using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Opportunities;

[QueryType]
public static class OpportunityQueries
{
    [Authorize]
    public static Task<Opportunity> Opportunity(
        [Service] IMediator mediator,
        Id id,
        CancellationToken cancellationToken = default) =>
        mediator.Send(new OpportunityQuery(id), cancellationToken);

    [Authorize]
    public static Task<Opportunity> OpportunityByLegacyCaseId(
        [Service] IMediator mediator,
        Id legacyCaseId,
        CancellationToken cancellationToken = default) =>
        mediator.Send(new OpportunityByLegacyCaseIdQuery(legacyCaseId), cancellationToken);

    [Authorize]
    public static Task<PagedResult<Opportunity>> Opportunities(
        int? skip,
        int? take,
        OpportunitiesWhere? where,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default
    ) => mediator.Send(new OpportunitiesQuery{Skip = skip, Take = take, Where = where}, cancellationToken);
}
