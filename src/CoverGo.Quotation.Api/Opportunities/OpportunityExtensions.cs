using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Application.Opportunities;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Queries;
using MediatR;

namespace CoverGo.Quotation.Api.Opportunities;

[ExtendObjectType(typeof(Opportunity))]
public class OpportunityExtensions
{
    public async Task<List<Offer>> Offers(
        [Parent] Opportunity opportunity,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default) => await mediator.Send(new OffersByOpportunityIdQuery(opportunity.Id), cancellationToken);

    public async Task<Proposal?> Proposal(
        [Parent] Opportunity opportunity,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default) => await mediator.Send(new ProposalByOpportunityIdQuery(opportunity.Id), cancellationToken);
}
