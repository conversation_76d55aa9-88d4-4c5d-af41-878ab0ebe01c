using CoverGo.Quotation.Domain.Opportunities.Renewal;
using MongoDB.Driver;

using HotChocolate.Authorization;
using HotChocolate.Data;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Api.Opportunities.Renewal;

[QueryType]
public static class RenewalOpportunityQueries
{
    [Authorize]
    [UseSingleOrDefault]
    public static IExecutable<RenewalOpportunity> RenewalOpportunity(
        [Service] IMongoCollection<OpportunityAggregate> renewalOpportunity,
        [GraphQLType<NonNullType<IdType>>] string id)
    {
        return renewalOpportunity.OfType<RenewalOpportunity>().Find(x => x.Id == id).AsExecutable();
    }

    [Authorize]
    [UseOffsetPaging(IncludeTotalCount = true)]
    [UseFiltering]
    public static IExecutable<RenewalOpportunity> RenewalOpportunities(
        [Service] IMongoCollection<OpportunityAggregate> renewalOpportunity) =>
        renewalOpportunity.OfType<RenewalOpportunity>().AsExecutable();
}
