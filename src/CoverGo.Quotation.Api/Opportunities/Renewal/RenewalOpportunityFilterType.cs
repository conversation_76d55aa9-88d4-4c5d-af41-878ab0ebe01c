using CoverGo.Quotation.Api.Configuration.GraphQl.Queries;
using CoverGo.Quotation.Api.Quotes;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using CoverGo.Quotation.Domain.Quotes;
using HotChocolate.Data.Filters;

namespace CoverGo.Quotation.Api.Opportunities.Renewal;

public class RenewalOpportunityFilterType : FilterInputType<RenewalOpportunity>
{
    protected override void Configure(
        IFilterInputTypeDescriptor<RenewalOpportunity> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(y => y.Id).Type<EntityIdOperationFilterInputType>();
    }
}
