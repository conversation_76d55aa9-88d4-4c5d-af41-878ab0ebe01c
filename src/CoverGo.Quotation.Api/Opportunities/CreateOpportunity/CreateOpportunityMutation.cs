using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Api.Offers.Errors;
using CoverGo.Quotation.Application.Opportunities.Commands;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Opportunities.CreateOpportunity;

[MutationType]
public static class CreateOpportunityMutation
{
    [Error(typeof(InputDataValidationError))]
    [UseMutationConvention(PayloadFieldName = "opportunity")]
    [Authorize]
    public static Task<Opportunity> CreateOpportunity(
        CreateOpportunityCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        mediator.Send(input, cancellationToken);

    [Error(typeof(InputDataValidationError))]
    [Error(typeof(InvalidFieldsException))]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(UniqueFieldException))]
    [Error(typeof(OfferBenefitSelectionIncompleteError))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(OfferPolicyDetailsMissingError))]
    [Error(typeof(OfferPremiumNotCalculatedError))]
    [Error(typeof(OfferMembersMissingError))]
    [Error<ProductCantHaveMultiplePrimariesException>]
    [UseMutationConvention(PayloadFieldName = "opportunity")]
    [Authorize]
    public static Task<RenewalOpportunity> CreateRenewalOpportunity(
        CreateRenewalOpportunityCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        mediator.Send(input, cancellationToken);
}
