﻿using CoverGo.Quotation.Application.Opportunities;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using MediatR;

namespace CoverGo.Quotation.Api.Opportunities;

public class OpportunityDataLoader
    : BatchDataLoader<ValueObjectId<OpportunityAggregate>, Opportunity>
{
    readonly IMediator _mediator;

    public OpportunityDataLoader(
        [Service] IMediator mediator,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<ValueObjectId<OpportunityAggregate>, Opportunity>> LoadBatchAsync(IReadOnlyList<ValueObjectId<OpportunityAggregate>> keys, CancellationToken cancellationToken)
    {
        List<Opportunity> results = await _mediator.Send(new OpportunitiesByIdsQuery(keys), cancellationToken);
        return results.ToDictionary(x => x.Id, x => x);
    }
}
