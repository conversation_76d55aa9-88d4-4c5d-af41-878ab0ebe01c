using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using MediatR;

namespace CoverGo.Quotation.Api.ReferenceData;

public abstract class ReferenceDataLoader<TQuery>
    : BatchDataLoader<(string localeId, string mnemonic), string> where TQuery : IQuery<IDictionary<string, string>>
{
    private readonly IMediator _mediator;

    protected ReferenceDataLoader(IMediator mediator, IBatchScheduler batchScheduler, DataLoaderOptions? options = null)
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected abstract TQuery CreateQuery(IEnumerable<string> mnemonics, string localeId);

    protected override async Task<IReadOnlyDictionary<(string localeId, string mnemonic), string>> LoadBatchAsync(
        IReadOnlyList<(string localeId, string mnemonic)> keys,
        CancellationToken cancellationToken)
    {
        if (keys.Select(x => x.localeId).Distinct().Count() > 1)
            throw new ArgumentException("Multiple LocaleId reference data query is not supported.");

        var localeId = keys.Select(x => x.localeId).First();

        var response = await _mediator.Send(CreateQuery(keys.Select(x => x.mnemonic).ToList(), localeId), cancellationToken);

        return response.ToDictionary(x => (localeId, x.Key), x => x.Value).AsReadOnly();
    }
}
