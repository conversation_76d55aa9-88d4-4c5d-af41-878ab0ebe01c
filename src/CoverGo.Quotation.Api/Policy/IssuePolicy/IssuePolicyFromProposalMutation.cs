using CoverGo.Quotation.Application.Policies;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals.Exceptions;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Policy.IssuePolicy;

[MutationType]
public class IssuePolicyFromProposalMutation
{
    [Authorize]
    [Error(typeof(PolicyIssuingAlreadyRequestedException))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(PolicyAlreadyIssuedException))]
    [Error(typeof(PolicyPremiumNotPaidException))]
    [Error(typeof(PolicyBillingChannelMissingException))]
    [Error(typeof(PolicyPayorIdMissingException))]
    [Error(typeof(PolicyPartyIdMissingException))]
    [Error(typeof(ProposalNotApprovedException))]
    public Task<Proposal> IssuePolicyFromProposal(
        IssuePolicyFromProposalCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        mediator.Send(input, cancellationToken);
}
