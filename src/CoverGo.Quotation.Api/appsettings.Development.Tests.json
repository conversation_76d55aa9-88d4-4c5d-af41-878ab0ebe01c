{"MongoDatabaseConfiguration": {"ConnectionString": "****************************************?replicaSet=covergo-mongo-set"}, "serviceUrls": {"auth": "http://localhost:60000/"}, "ConnectionStrings": {"redis": "covergo-redis-master:6379,abortConnect=false", "auth": "http://localhost:60000/", "gateway": "http://localhost:60060/", "policies": "http://localhost:60050/", "products": "http://localhost:60020/", "users": "http://localhost:60010/", "channelManagement": "http://localhost:64483/", "reference": "http://localhost:61910/", "templates": "http://localhost:63542/", "premium": "http://localhost:50020/", "fileSystem": "http://localhost:61872/"}, "GraphQL": {"IncludeExceptionDetails": true}, "GraphQLStitching": {"Redis": {"Publish": false, "ConfigurationName": "GatewayV2"}}, "CheckDI": true, "UseInMemoryBus": true, "RunJobs": false, "BuyFlowLinkAesEncryptionServiceKey": "qcSRBvH6s0pSZnU4tJxzFnjI5Pz2qdM1", "BuyFlowLinkAesEncryptionServiceIV": "AAAAAAAAAAAAAAAAAAAAAA==", "FeatureManagement": {"UseEffectiveDateInAddPolicyMember": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_uat", "asia_prod"]}}]}}}