using CoverGo.Quotation.Application.Opportunities.Commands;

namespace CoverGo.Quotation.Api.OfferMembers;

public class RenewalOfferMemberInputType : InputObjectType<RenewalOfferMemberInput>
{
    protected override void Configure(IInputObjectTypeDescriptor<RenewalOfferMemberInput> descriptor)
    {
        descriptor.Field(x => x.PlanId).Type<NonNullType<IdType>>();
        descriptor.Ignore(it => it.LegacyPolicyId);
        descriptor.Ignore(it => it.LegacyPolicyMemberId);
    }
}
