using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.Queries;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Infrastructure.OfferMembers;

using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.OfferMembers;

[QueryType]
public static class OfferMemberQueries
{
    [Authorize]
    public static async Task<OfferMember> OfferMember(
        [Service] IMediator mediator,
        ValueObjectId<OfferMemberAggregate> offerMemberId,
        CancellationToken cancellationToken = default
    ) => await mediator.Send(new OfferMemberByIdQuery(offerMemberId), cancellationToken);

    [Authorize]
    public static async Task<OfferMember> OfferMemberByLegacyPolicyMemberId(
        [Service] IMediator mediator,
        ValueObjectId<LegacyPolicyMember> legacyPolicyMemberId,
        ValueObjectId<LegacyPolicy> policyId,
        ValueObjectId<OfferAggregate> offerId,
        ValueObjectId<OpportunityAggregate> opportunityId,
        CancellationToken cancellationToken = default
    ) =>
        await mediator.Send(
            new LegacyPolicyMemberQuery(policyId, legacyPolicyMemberId, offerId),
            cancellationToken
        );

    [Authorize]
    public static Task<PagedResult<OfferMember>> OfferMembers(
        [Service] IMediator mediator,
        int skip,
        int take,
        OfferMembersWhere? where,
        CancellationToken cancellationToken = default
    ) =>
        mediator.Send(
            new OfferMembersQuery
            {
                Skip = skip,
                Take = take,
                Where = where
            },
            cancellationToken
        );
}
