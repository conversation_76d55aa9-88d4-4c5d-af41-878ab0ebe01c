using CoverGo.Quotation.Application.OfferMembers.Contracts;

namespace CoverGo.Quotation.Api.OfferMembers;

public class QuoteMemberStateInputType : InputObjectType<OfferMemberInput>
{
    protected override void Configure(IInputObjectTypeDescriptor<OfferMemberInput> descriptor)
    {
        descriptor.Ignore(it => it.LegacyPolicyId);
        descriptor.Ignore(it => it.LegacyPolicyMemberId);
    }
}
