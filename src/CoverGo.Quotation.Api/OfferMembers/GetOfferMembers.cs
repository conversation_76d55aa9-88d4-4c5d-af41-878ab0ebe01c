using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.Queries;
using CoverGo.Quotation.Application.Offers.Contracts;

using MediatR;

namespace CoverGo.Quotation.Api.OfferMembers;

[ExtendObjectType(typeof(Offer))]
public class GetOfferMembers
{
    public async Task<PagedResult<OfferMember>> Members(
        [Parent] Offer offer,
        [Service] IMediator mediator,
        int? skip,
        int? take,
        OfferMembersWhere? where,
        CancellationToken cancellationToken = default
    )
    {
        where ??= new OfferMembersWhere();
        where.OfferId = new IdWhere<Domain.Offers.OfferAggregate>
        {
            Eq = offer.Id.Value,
        };

        return await mediator.Send(
            new OfferMembersQuery
            {
                Skip = skip,
                Take = take,
                Where = where
            },
            cancellationToken
        );
    }
}
