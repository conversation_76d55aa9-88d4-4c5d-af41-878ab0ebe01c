using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.Offers;
using MediatR;

namespace CoverGo.Quotation.Api.OfferMembers;

public class OfferMemberDataLoader
    : BatchDataLoader<Id, OfferMember>
{
    readonly IMediator _mediator;

    public OfferMemberDataLoader(
        [Service] IMediator mediator,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<Id, OfferMember>> LoadBatchAsync(IReadOnlyList<Id> keys,
                                                                                       CancellationToken cancellationToken)
    {
        List<OfferMember> results = await _mediator.Send(new OfferMembersByIdsQuery(keys), cancellationToken);
        return results.ToDictionary(x => x.Id, x => x);
    }
}
