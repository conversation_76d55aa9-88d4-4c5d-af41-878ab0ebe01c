using CoverGo.Quotation.Application.OfferMembers.Commands;
using CoverGo.Quotation.Application.OfferMembers.Commands.Documents;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.Underwriting.Exceptions;
using HotChocolate.Authorization;
using MediatR;
// ReSharper disable UnusedMember.Global

namespace CoverGo.Quotation.Api.OfferMembers;

[MutationType]
public static class OfferMemberMutations
{
    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(UniqueFieldException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    public static async Task<OfferMember> AddOfferMember(
        AddOfferMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(MemberHasDependentsException))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(UniqueFieldException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    public static async Task<OfferMember> UpdateOfferMember(
        UpdateOfferMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(UniqueFieldException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    [UseMutationConvention(PayloadFieldName = "addedOfferMembers")]
    public static async Task<List<OfferMember>> AddOfferMembers(
        AddOfferMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(MemberHasDependentsException))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(UniqueFieldException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    [UseMutationConvention(PayloadFieldName = "updatedOfferMembers")]
    public static async Task<List<OfferMember>> UpdateOfferMembers(
        UpdateOfferMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error<EntityNotFoundException>]
    [UseMutationConvention(PayloadFieldName = "deletedOfferMembers")]
    public static async Task<List<OfferMember>> DeleteOfferMembers(
        DeleteOfferMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error<EntityNotFoundException>]
    public static async Task<OfferMember> AddOfferMemberDocuments(
        AddOfferMemberDocumentsCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error<EntityNotFoundException>]
    public static async Task<OfferMember> RemoveOfferMemberDocument(
        RemoveOfferMemberDocumentCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
