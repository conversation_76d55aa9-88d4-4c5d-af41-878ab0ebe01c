using CoverGo.Quotation.Api.ProposalMembers.Errors;
using CoverGo.Quotation.Application.ProposalMembers.Commands;
using CoverGo.Quotation.Application.ProposalMembers.Commands.Benefits;
using CoverGo.Quotation.Application.ProposalMembers.Commands.Documents;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.ProposalMembers.Exceptions;
using CoverGo.Quotation.Domain.Proposals.Exceptions;
using CoverGo.Quotation.Domain.Underwriting.Benefits.Exceptions;
using CoverGo.Quotation.Domain.Underwriting.Exceptions;

using HotChocolate.Authorization;

using MediatR;
// ReSharper disable UnusedMember.Global

namespace CoverGo.Quotation.Api.ProposalMembers;

[MutationType]
public static class ProposalMemberMutations
{
    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(UniqueFieldException))]
    [Error(typeof(InvalidProposalStatusException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    public static async Task<ProposalMember> AddProposalMember(
        AddProposalMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(MemberHasDependentsException))]
    [Error(typeof(UniqueFieldException))]
    [Error(typeof(InvalidProposalStatusException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    public static async Task<ProposalMember> UpdateProposalMember(
        UpdateProposalMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(UniqueFieldException))]
    [Error(typeof(InvalidProposalStatusException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    [UseMutationConvention(PayloadFieldName = "addedProposalMembers")]
    public static async Task<List<ProposalMember>> AddProposalMembers(
        AddProposalMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(MemberHasDependentsException))]
    [Error(typeof(UniqueFieldException))]
    [Error(typeof(InvalidProposalStatusException))]
    [Error<EntityNotFoundException>]
    [Error<ProductCantHaveMultiplePrimariesException>]
    [UseMutationConvention(PayloadFieldName = "updatedProposalMembers")]
    public static async Task<List<ProposalMember>> UpdateProposalMembers(
        UpdateProposalMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [UseMutationConvention(PayloadFieldName = "deletedProposalMembers")]
    [Error(typeof(InvalidProposalStatusException))]
    [Error<EntityNotFoundException>]
    public static async Task<List<ProposalMember>> DeleteProposalMembers(
        DeleteProposalMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(CantHaveMultipleMemberBenefitLoadingsException))]
    public static async Task<ProposalMember> AddProposalMemberBenefitLoading(
        AddProposalMemberBenefitLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(AutoLoadingCannotBeUpdatedException))]
    [Error(typeof(LoadingTypeCannotBeChangedException))]
    public static async Task<ProposalMember> UpdateProposalMemberBenefitLoading(
        UpdateProposalMemberBenefitLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> DeleteProposalMemberBenefitLoading(
        DeleteProposalMemberBenefitLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> AddProposalMemberDocuments(
        AddProposalMemberDocumentsCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> RemoveProposalMemberDocument(
        RemoveProposalMemberDocumentCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
