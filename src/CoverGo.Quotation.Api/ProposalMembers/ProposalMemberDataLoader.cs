using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Proposals;
using MediatR;

namespace CoverGo.Quotation.Api.ProposalMembers;

public class ProposalMemberByOfferMemberIdDataLoader
    : BatchDataLoader<Id, ProposalMember>
{
    readonly IMediator _mediator;

    public ProposalMemberByOfferMemberIdDataLoader(
        [Service] IMediator mediator,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<Id, ProposalMember>> LoadBatchAsync(IReadOnlyList<Id> keys,
                                                                                          CancellationToken cancellationToken)
    {
        List<ProposalMember> results = await _mediator.Send(new ProposalMembersByOfferMemberIdsQuery(keys), cancellationToken);
        return results.Where(x => x.OfferMemberId is not null)
                    .ToDictionary(x => new Id(x.OfferMemberId!.Value), x => x);
    }
}

