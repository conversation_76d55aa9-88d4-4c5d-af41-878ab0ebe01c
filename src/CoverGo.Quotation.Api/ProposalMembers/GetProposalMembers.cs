using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Queries;
using CoverGo.Quotation.Application.Proposals.Contracts;
using MediatR;

namespace CoverGo.Quotation.Api.ProposalMembers;

[ExtendObjectType(typeof(Proposal))]
public static class GetProposalMembers
{
    public static Task<PagedResult<ProposalMember>> Members(
        [Service] IMediator mediator,
        [Parent] Proposal proposal,
        int? skip,
        int? take,
        ProposalMembersWhere? where,
        ProposalMembersOrder[]? Order,
        CancellationToken cancellationToken
    )
    {
        where ??= new ProposalMembersWhere();
        where.ProposalId = new IdWhere<Domain.Proposals.ProposalAggregate>
        {
            Eq = proposal.Id.Value,
        };

        return mediator.Send(
            new ProposalMembersQuery
            {
                Skip = skip,
                Take = take,
                Where = where,
                Order = Order,
            },
            cancellationToken
        );
    }
}
