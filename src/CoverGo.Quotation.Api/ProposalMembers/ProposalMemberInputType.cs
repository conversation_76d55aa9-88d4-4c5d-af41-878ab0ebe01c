using CoverGo.Quotation.Application.ProposalMembers.Contracts;

namespace CoverGo.Quotation.Api.OfferMembers;

public class ProposalMemberInputType : InputObjectType<ProposalMemberInput>
{
    protected override void Configure(IInputObjectTypeDescriptor<ProposalMemberInput> descriptor)
    {
        descriptor.Ignore(it => it.LegacyPolicyId);
        descriptor.Ignore(it => it.LegacyPolicyMemberId);
    }
}
