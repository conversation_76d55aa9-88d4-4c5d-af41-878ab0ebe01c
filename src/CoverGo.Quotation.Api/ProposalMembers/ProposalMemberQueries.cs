using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Queries;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.ProposalMembers;

using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.ProposalMembers;

[QueryType]
public static class ProposalMemberQueries
{
    [Authorize]
    public static async Task<ProposalMember> ProposalMember(
        [Service] IMediator mediator,
        ValueObjectId<ProposalMemberAggregate> proposalMemberId,
        CancellationToken cancellationToken = default)
        => await mediator.Send(new ProposalMemberByIdQuery(proposalMemberId), cancellationToken);

    [Authorize]
    public static async Task<ProposalMember> ProposalMemberByLegacyPolicyMemberId(
        [Service] IMediator mediator,
        ValueObjectId<LegacyPolicyMember> legacyPolicyMemberId,
        ValueObjectId<LegacyPolicy> policyId,
        CancellationToken cancellationToken = default) =>
        await mediator.Send(
            new ProposalMemberByLegacyPolicyMemberIdQuery(policyId, legacyPolicyMemberId),
            cancellationToken);

    [Authorize]
    public static Task<PagedResult<ProposalMember>> ProposalMembers(
        [Service] IMediator mediator,
        int skip,
        int take,
        ProposalMembersWhere? where,
        ProposalMembersOrder[]? Order,
        CancellationToken cancellationToken)
    {
        return mediator.Send(
            new ProposalMembersQuery
            {
                Skip = skip,
                Take = take,
                Where = where,
                Order = Order,
            },
            cancellationToken
        );
    }
}
