FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS base
RUN apk add --no-cache icu-libs # Without this DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0 will fail
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS sdk-base
RUN apk add findutils && apk cache clean
WORKDIR /app

FROM sdk-base AS restore
ARG GH_ACCOUNT
ARG GH_TOKEN
COPY nuget.config .
COPY Directory.Packages.props .
COPY Directory.Build.props .
COPY Directory.Build.targets .
COPY ["src/CoverGo.Quotation.Api/CoverGo.Quotation.Api.csproj", "src/CoverGo.Quotation.Api/"]
COPY ["src/CoverGo.Quotation.IntegrationEvents/CoverGo.Quotation.IntegrationEvents.csproj", "src/CoverGo.Quotation.IntegrationEvents/"]
COPY ["src/CoverGo.Quotation.Infrastructure/CoverGo.Quotation.Infrastructure.csproj", "src/CoverGo.Quotation.Infrastructure/"]
COPY ["src/CoverGo.Quotation.Infrastructure.Decorators/CoverGo.Quotation.Infrastructure.Decorators.csproj", "src/CoverGo.Quotation.Infrastructure.Decorators/"]
COPY ["src/CoverGo.Quotation.Infrastructure.MongoDbExtensions/CoverGo.Quotation.Infrastructure.MongoDbExtensions.csproj", "src/CoverGo.Quotation.Infrastructure.MongoDbExtensions/"]
COPY ["src/CoverGo.Quotation.Infrastructure.PoliciesClient/CoverGo.Quotation.Infrastructure.PoliciesClient.csproj", "src/CoverGo.Quotation.Infrastructure.PoliciesClient/"]
COPY ["src/CoverGo.Quotation.Infrastructure.ProductsClient/CoverGo.Quotation.Infrastructure.ProductsClient.csproj", "src/CoverGo.Quotation.Infrastructure.ProductsClient/"]
COPY ["src/CoverGo.Quotation.Infrastructure.ChannelManagementClient/CoverGo.Quotation.Infrastructure.ChannelManagementClient.csproj", "src/CoverGo.Quotation.Infrastructure.ChannelManagementClient/"]
COPY ["src/CoverGo.Quotation.Infrastructure.PremiumClient/CoverGo.Quotation.Infrastructure.PremiumClient.csproj", "src/CoverGo.Quotation.Infrastructure.PremiumClient/"]
COPY ["src/CoverGo.Quotation.Application/CoverGo.Quotation.Application.csproj", "src/CoverGo.Quotation.Application/"]
COPY ["src/CoverGo.Quotation.Application.VersionBridge/CoverGo.Quotation.Application.VersionBridge.csproj", "src/CoverGo.Quotation.Application.VersionBridge/"]
COPY ["src/CoverGo.Quotation.Domain/CoverGo.Quotation.Domain.csproj", "src/CoverGo.Quotation.Domain/"]
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN find "./" -type f -name "*.csproj" | xargs -L 1 -d '\n' dotnet restore

FROM restore AS build
ARG BUILDCONFIG=Debug
COPY . .
RUN dotnet build ./src/CoverGo.Quotation.Api/CoverGo.Quotation.Api.csproj -c "$BUILDCONFIG" --no-restore

FROM build AS publish
ARG BUILDCONFIG=Debug
RUN dotnet publish ./src/CoverGo.Quotation.Api/CoverGo.Quotation.Api.csproj -c "$BUILDCONFIG" -o ./out --no-build

FROM base AS runtime
COPY --from=publish /app/out ./

ENV ASPNETCORE_URLS http://*:8080
EXPOSE 8080
ENTRYPOINT ["dotnet", "CoverGo.Quotation.Api.dll"]
