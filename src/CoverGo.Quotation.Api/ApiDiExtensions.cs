using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Text.Json;

using CoverGo.BuildingBlocks.Api.GraphQl.Helpers;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Converters;
using CoverGo.Extensions.DependencyInjection;
using CoverGo.Quotation.Api.Configuration.GraphQl;
using CoverGo.Quotation.Api.Configuration.GraphQl.MongoDb;
using CoverGo.Quotation.Api.Configuration.GraphQl.Queries;
using CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;
using CoverGo.Quotation.Api.Opportunities;
using CoverGo.Quotation.Api.Quotes;
using CoverGo.Quotation.Api.ReferenceData;
using CoverGo.Quotation.Api.Underwriting;
using CoverGo.Quotation.Api.Underwriting.DataLoaders;
using CoverGo.Quotation.Application.BuyFlowLinks.Commands;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers.Commands;
using CoverGo.Quotation.Application.OfferMembers.Commands.Documents;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.BillingInformation;
using CoverGo.Quotation.Application.Offers.Commands;
using CoverGo.Quotation.Application.Offers.Commands.AcceptOffer;
using CoverGo.Quotation.Application.Offers.Commands.CreateOffer;
using CoverGo.Quotation.Application.Offers.Commands.IssueOffer;
using CoverGo.Quotation.Application.Offers.Commands.RejectOffer;
using CoverGo.Quotation.Application.Offers.Commands.UpdateOfferInsuredGroups;
using CoverGo.Quotation.Application.Offers.Download;
using CoverGo.Quotation.Application.Offers.Headcount;
using CoverGo.Quotation.Application.Offers.Opportunities;
using CoverGo.Quotation.Application.Offers.Policies;
using CoverGo.Quotation.Application.Offers.Send;
using CoverGo.Quotation.Application.Opportunities.Commands;
using CoverGo.Quotation.Application.Policies;
using CoverGo.Quotation.Application.ProposalMembers.Commands;
using CoverGo.Quotation.Application.ProposalMembers.Commands.Benefits;
using CoverGo.Quotation.Application.ProposalMembers.Commands.Documents;
using CoverGo.Quotation.Application.Proposals.Applications;
using CoverGo.Quotation.Application.Proposals.BenefitSelection;
using CoverGo.Quotation.Application.Proposals.Commands;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Quotes;

using HotChocolate.Data.Filters;
using HotChocolate.Execution.Configuration;
using HotChocolate.Stitching.SchemaDefinitions;
using HotChocolate.Types.Pagination;
using HotChocolate.Utilities;
using Microsoft.AspNetCore.WebSockets;
using StackExchange.Redis;

namespace CoverGo.Quotation.Api;

public static class ApiDiExtensions
{
    public static IServiceCollection AddApi(
        this IServiceCollection services,
        GraphQLStitchingOptions graphQlStitchingOptions,
        GraphQLOptions graphQlOptions
    )
    {
        services.AddMemoryCache();
        services.AddRouting();
        services.AddWebSockets(op => op.KeepAliveInterval = TimeSpan.FromSeconds(30));

        var hcBuilder = services
            .AddQuotationGraphQlSchema()
            .ModifyRequestOptions(options =>
            {
                if (!Debugger.IsAttached)
                {
                    options.ExecutionTimeout = TimeSpan.FromMinutes(2);
                }
                options.IncludeExceptionDetails = graphQlOptions.IncludeExceptionDetails;
            });

        if (graphQlStitchingOptions.Enabled)
        {
            hcBuilder.PublishSchemaDefinition(
                delegate (IPublishSchemaDefinitionDescriptor schema)
                {
                    schema.SetName(graphQlStitchingOptions.SchemaName!);

                    if (graphQlStitchingOptions.Redis?.Publish == true)
                    {
                        schema.PublishToRedis(
                            // The configuration name under which the schema should be published
                            graphQlStitchingOptions.Redis!.ConfigurationName!,
                            // The connection multiplexer that should be used for publishing
                            sp => sp.GetRequiredService<IConnectionMultiplexer>()
                        );
                    }
                }
            );
        }

        return services;
    }

    public static IRequestExecutorBuilder AddQuotationGraphQlSchema(this IServiceCollection services)
    {
        var hcBuilder = services
            .AddCoverGoGraphQLServer(applyDefaultQuerySettings: false)
            .BindRuntimeType<IDictionary<string, object?>, JsonType>()
            .InitializeOnStartup()
            .AddMongoDbPagingProviders(defaultProvider: true)
            .AddMongoDbFiltering()
            .AddMongoDbProjections()
            .AddMongoDbSorting()
            .SetPagingOptions(new PagingOptions
            {
                MaxPageSize = 100,
                AllowBackwardPagination = true,
                IncludeTotalCount = true,
                RequirePagingBoundaries = true,
            })
            .AddTypes();

        hcBuilder.AddQuoteTypes();

        hcBuilder.AddType<ClientInputType>();
        hcBuilder.AddType<AddUnderwritingCaseMemberDocumentsEntityInputType>();
        hcBuilder.ModifyOptions(o => o.EnableOneOf = true);
        hcBuilder.AddCommandType<CreateOfferCommand>();
        hcBuilder.AddCommandType<AddOfferCommand>();
        hcBuilder.AddCommandType<DuplicateOfferCommand>();
        hcBuilder.AddCommandType<DownloadOfferCommand>();
        hcBuilder.AddCommandType<SendOffersCommand>();
        hcBuilder.AddCommandType<AddOfferMemberCommand>();
        hcBuilder.AddCommandType<UpdateOfferMemberCommand>();
        hcBuilder.AddCommandType<AddOfferMembersCommand>();
        hcBuilder.AddCommandType<UpdateOfferMembersCommand>();
        hcBuilder.AddCommandType<AcceptUnderwritingCaseCommand>();
        hcBuilder.AddCommandType<RejectUnderwritingCaseCommand>();
        hcBuilder.AddCommandType<DeleteOfferMembersCommand>();
        hcBuilder.AddCommandType<CreateOpportunityCommand>();
        hcBuilder.AddCommandType<CreateRenewalOpportunityCommand>();
        hcBuilder.AddCommandType<UpdateOpportunityCommand>();
        hcBuilder.AddCommandType<AddProposalMemberExclusionCommand>();
        hcBuilder.AddCommandType<AddUnderwritingCaseMemberExclusionCommand>();
        hcBuilder.AddCommandType<RemoveProposalMemberExclusionCommand>();
        hcBuilder.AddCommandType<DeleteUnderwritingCaseMemberExclusionCommand>();

        hcBuilder.AddCommandType<RemoveProposalMemberPreExistingConditionCommand>();
        hcBuilder.AddCommandType<AddProposalMemberPreExistingConditionCommand>();

        hcBuilder.AddCommandType<UpdateOfferBillingFrequencyCommand>();
        hcBuilder.AddCommandType<UpdateOfferPolicyDetailsCommand>();
        hcBuilder.AddCommandType<UpdateOfferInsuredGroupsCommand>();
        hcBuilder.AddCommandType<MarkOfferAsIssuedCommand>();
        hcBuilder.AddCommandType<AcceptOfferCommand>();
        hcBuilder.AddCommandType<RejectOfferCommand>();
        hcBuilder.AddCommandType<DefineProposalClassBenefitSelectionCommand>();
        hcBuilder.AddCommandType<DefineOfferClassBenefitSelectionCommand>();
        hcBuilder.AddCommandType<UpdateProposalApplicationFormCommand>();
        hcBuilder.AddCommandType<UpdateProposalBillingFrequencyCommand>();
        hcBuilder.AddCommandType<UpdateProposalTaxCommand>();
        hcBuilder.AddCommandType<ExtendProposalValidityCommand>();
        hcBuilder.AddCommandType<CloseProposalCommand>();
        hcBuilder.AddCommandType<DownloadProposalCommand>();
        hcBuilder.AddCommandType<SendProposalCommand>();

        hcBuilder.AddCommandType<AddProposalLoadingCommand>();
        hcBuilder.AddCommandType<UpdateProposalLoadingCommand>();
        hcBuilder.AddCommandType<DeleteProposalLoadingCommand>();

        hcBuilder.AddCommandType<AddProposalMemberCommand>();
        hcBuilder.AddCommandType<UpdateProposalMemberCommand>();
        hcBuilder.AddCommandType<AddProposalMembersCommand>();
        hcBuilder.AddCommandType<UpdateRenewalOpportunityProposalCommand>();
        hcBuilder.AddCommandType<UpdateProposalMembersCommand>();
        hcBuilder.AddCommandType<RejectUnderwritingCaseMemberCommand>();
        hcBuilder.AddCommandType<DeleteProposalMembersCommand>();
        hcBuilder.AddCommandType<AcceptUnderwritingCaseMemberCommand>();
        hcBuilder.AddCommandType<AddUnderwritingCaseMemberLoadingCommand>();
        hcBuilder.AddCommandType<UpdateUnderwritingCaseMemberLoadingCommand>();
        hcBuilder.AddCommandType<DeleteUnderwritingCaseMemberLoadingCommand>();
        hcBuilder.AddCommandType<AcceptUnderwritingCaseMemberBenefitCommand>();
        hcBuilder.AddCommandType<RejectUnderwritingCaseMemberBenefitCommand>();
        hcBuilder.AddCommandType<AddUnderwritingCaseLoadingCommand>();
        hcBuilder.AddCommandType<UpdateUnderwritingCaseLoadingCommand>();
        hcBuilder.AddCommandType<DeleteUnderwritingCaseLoadingCommand>();
        hcBuilder.AddCommandType<AddUnderwritingCaseMemberPreExistingConditionCommand>();
        hcBuilder.AddCommandType<DeleteUnderwritingCaseMemberPreExistingConditionCommand>();

        hcBuilder.AddCommandType<UpdateOpportunityClientCommand>();
        hcBuilder.AddCommandType<IssuePolicyFromProposalCommand>();

        hcBuilder.AddCommandType<UpdateBuyFlowLinkPasswordCommand>();
        hcBuilder.AddCommandType<UpdateOfferTaxOverridesCommand>();
        hcBuilder.AddCommandType<UpdateProposalTaxOverridesCommand>();

        hcBuilder.AddCommandType<AddOfferMemberDocumentsCommand>();
        hcBuilder.AddCommandType<RemoveOfferMemberDocumentCommand>();
        hcBuilder.AddCommandType<AddProposalMemberDocumentsCommand>();
        hcBuilder.AddCommandType<RemoveProposalMemberDocumentCommand>();
        hcBuilder.AddCommandType<AddUnderwritingCaseMemberDocumentsCommand>();
        hcBuilder.AddCommandType<RemoveUnderwritingCaseMemberDocumentCommand>();
        hcBuilder.AddCommandType<UpdateOpportunitySupplementaryClientsCommand>();
        hcBuilder.AddCommandType<UpdateOpportunityBeneficiariesCommand>();

        hcBuilder.AddType<IndividualClientType>();
        hcBuilder.AddType<GroupClientType>();

        hcBuilder.AddType<PrimaryInsuredGroup>();
        hcBuilder.AddType<DependentInsuredGroup>();
        hcBuilder.AddInterfaceType<InsuredGroup>();

        hcBuilder.AddMoneyType();

        hcBuilder.BindIdDtoType();
        hcBuilder.BindValueObjectIdType();
        hcBuilder.BindGenericValueObjectIdTypeFromAssemblies(
            [Assembly.GetAssembly(typeof(OpportunityAggregate))!]
        );


        hcBuilder.AddTypeConverter<List<FieldInput>, CustomFields<QuoteMemberState>>(
            x => [ ..x.Select(it => new CustomField<QuoteMemberState>(
                it.Key,
                it.Value is JsonElement jsonElement
                    ? JsonElementToObjectConverter.FromJsonElementToObject(it.Value)
                    : it.Value))]);

        hcBuilder
            .BindRuntimeType<ValueObjectId<Domain.Offers.ProductBenefit>, IdType>()
            .AddTypeConverter<string, ValueObjectId<Domain.Offers.ProductBenefit>>(v => new(v))
            .AddTypeConverter<string?, ValueObjectId<Domain.Offers.ProductBenefit>?>(v =>
                v != null ? new(v) : null
            )
            .AddTypeConverter<ValueObjectId<Domain.Offers.ProductBenefit>, string>(v => v.Value)
            .AddTypeConverter<ValueObjectId<Domain.Offers.ProductBenefit>?, string?>(v => v?.Value);

        hcBuilder.AddConvention<IFilterConvention>(
            new FilterConventionExtension(
                convention =>
                {
                    convention.Operation(CustomFilterOperations.Search).Name("search").Description("Searches the following string across pre-defined entity properties.");
                    convention.AddProviderExtension(new MongoDbFilterProviderExtension(y => y
                        .AddFieldHandler<MongoDbSearchOperationHandler>()
                    ));
                    convention.Configure<UnderwritingCaseFilterType>(x => x
                        .Operation(CustomFilterOperations.Search)
                    );
                }));

        services.AddScoped<ReferenceDataLoader<GetExclusionReferenceDataNamesQuery>, ExclusionReferenceDataDataLoader>();
        services.AddScoped<ReferenceDataLoader<GetPreExistingConditionReferenceDataNamesQuery>, PreExistingConditionReferenceDataLoader>();
        services.AddScoped<OpportunityDataLoader>();

        return hcBuilder;
    }

    private static IRequestExecutorBuilder BindIdDtoType(this IRequestExecutorBuilder hcBuilder)
    {
        return hcBuilder
            .BindRuntimeType<Id, IdType>()
            .AddTypeConverter<string?, Id?>(v => v != null ? new(v) : null)
            .AddTypeConverter<Id?, string?>(v => v?.Value);
    }

    private static IRequestExecutorBuilder BindValueObjectIdType(
        this IRequestExecutorBuilder hcBuilder
    )
    {
        return hcBuilder
            .BindRuntimeType<ValueObjectId, IdType>()
            .AddTypeConverter<string?, ValueObjectId?>(v => v != null ? new(v) : null)
            .AddTypeConverter<ValueObjectId?, string?>(v => v?.Value);
    }

    private static IRequestExecutorBuilder BindGenericValueObjectIdTypeFromAssemblies(
        this IRequestExecutorBuilder hcBuilder,
        Assembly[] assemblies
    )
    {
        var entityTypes = assemblies.SelectMany(it =>
            it.DefinedTypes.Where(type =>
            {
                var prop = type.GetProperty(nameof(IEntityId<ValueObjectId>.Id));
                var isGenericValueObjectId =
                    prop is not null
                    && prop.PropertyType.IsGenericType
                    && prop.PropertyType.GetGenericTypeDefinition() == typeof(ValueObjectId<>);
                return isGenericValueObjectId;
            })
        );

        foreach (var entityType in entityTypes)
        {
            hcBuilder.BindRuntimeType(
                typeof(ValueObjectId<>).MakeGenericType(entityType),
                typeof(IdType)
            );
        }

        hcBuilder.AddTypeConverter(
            (Type source, Type target, [NotNullWhen(true)] out ChangeType? converter) =>
            {
                converter = null;
                if (
                    source.IsGenericType
                    && source.GetGenericTypeDefinition() == typeof(ValueObjectId<>)
                    && target == typeof(string)
                )
                {
                    converter = (source) => ((ValueObjectId?)source)?.Value;
                    return true;
                }
                if (
                    target.IsGenericType
                    && target.GetGenericTypeDefinition() == typeof(ValueObjectId<>)
                    && source == typeof(string)
                )
                {
                    var constructor = target
                        .GetConstructors()
                        .Single(it =>
                            it.GetParameters().Length == 1
                            && it.GetParameters()[0].ParameterType == typeof(string)
                        );
                    converter = (source) => source is null ? null : constructor!.Invoke([source]);
                    return true;
                }

                return false;
            }
        );

        return hcBuilder;
    }

    private static IRequestExecutorBuilder AddMoneyType(this IRequestExecutorBuilder hcBuilder)
    {
        return hcBuilder
            .BindRuntimeType<CurrencyCode, StringType>()
            .AddTypeConverter<string, CurrencyCode>(v => new(v))
            .AddTypeConverter<string?, CurrencyCode?>(v => v != null ? new(v) : null)
            .AddTypeConverter<CurrencyCode, string>(v => v.Value)
            .AddTypeConverter<CurrencyCode?, string?>(v => v?.Value);
    }
}
