using CoverGo.Quotation.Api.Offers.Errors;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Offers;

using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.BenefitsSelection;

[MutationType]
public static class DefineOfferClassBenefitSelectionMutation
{
    [Authorize]
    [Error(typeof(OfferClassNotFoundError))]
    [Error(typeof(PlanInvalidFieldsException))]
    public static async Task<Offer> DefineOfferClassBenefitSelection(
        [Service] IMediator mediator,
        DefineOfferClassBenefitSelectionCommand input,
        CancellationToken cancellationToken = default) =>
        await mediator.Send(
            input,
            cancellationToken);
}
