using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.Opportunities;
using CoverGo.Quotation.Domain.Offers.Exceptions;
using CoverGo.Quotation.Domain.Opportunities.Exceptions;
using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.DuplicateOffer;

[MutationType]
public static class DuplicateOfferMutation
{
    [UseMutationConvention(PayloadFieldName = "offer")]
    [Authorize]
    [Error<OfferDuplicationNotAllowedException>()]
    [Error<OpportunityClosedException>()]
    [Error<OpportunityOfferAcceptedException>()]
    [Error<OpportunityInvalidBuyFlowException>()]
    [Error<OfferProductTypeMismatchException>()]
    public static async Task<Offer> DuplicateOffer(
        DuplicateOfferCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
