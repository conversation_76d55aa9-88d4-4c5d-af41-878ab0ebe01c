using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.Exceptions;
using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.AddOffer;

[MutationType]
public static class AddOfferMutation
{
    [Authorize]
    [Error<OpportunityClosedException>()]
    [Error<OpportunityOfferAcceptedException>()]
    [Error<OpportunityInvalidBuyFlowException>()]
    [Error<OfferProductTypeMismatchException>()]
    public static async Task<Offer> AddOffer(
        AddOfferCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
