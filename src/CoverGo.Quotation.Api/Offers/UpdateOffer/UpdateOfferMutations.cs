using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Api.Offers.Errors;
using CoverGo.Quotation.Application.Offers.BillingInformation;
using CoverGo.Quotation.Application.Offers.Commands.UpdateOfferInsuredGroups;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Offers;

using HotChocolate.Authorization;

using MediatR;
using CoverGo.BuildingBlocks.Application.Core.Exceptions;
using CoverGo.Quotation.Domain.Offers.Exceptions;
using CoverGo.Quotation.Domain.Pricing;
using CoverGo.Quotation.Application.Offers.Commands;

namespace CoverGo.Quotation.Api.Offers.UpdateOffer;

[MutationType]
public static class UpdateOfferMutations
{
    [Authorize]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(CalculatingPremiumError))]
    public static async Task<Offer> UpdateOfferBillingFrequency(
        UpdateOfferBillingFrequencyCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(OfferPolicyDetailsInvalidDatesError))]
    [Error(typeof(OfferInImmutableStatusError))]
    public static async Task<Offer> UpdateOfferPolicyDetails(
        Application.Offers.Policies.UpdateOfferPolicyDetailsCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(InvalidFieldsException))]
    public static async Task<Offer> UpdateOfferInsuredGroups(
        UpdateOfferInsuredGroupsCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error<TaxOverrideNotAllowedException>]
    [Error(typeof(InputDataValidationError))]
    [Error<InvalidTaxValueException>]
    public static async Task<Offer> UpdateOfferTaxOverrides(
        UpdateOfferTaxOverridesCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);
}
