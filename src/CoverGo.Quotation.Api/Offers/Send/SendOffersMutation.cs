using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Quotation.Application.Offers.Send;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Offers.Send
{
    [MutationType]
    public class SendOffersMutation
    {
        [Authorize]
        public Task<bool> SendOffers(
            SendOffersCommand input,
            [Service] IMediator mediator,
            CancellationToken cancellationToken
        ) => mediator.Send(input, cancellationToken);
    }
}
