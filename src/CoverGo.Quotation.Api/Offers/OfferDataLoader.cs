﻿using CoverGo.Quotation.Application.Offers;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using MediatR;

namespace CoverGo.Quotation.Api.Offers;

public class OfferDataLoader
    : BatchDataLoader<ValueObjectId<OfferAggregate>, Offer>
{
    readonly IMediator _mediator;

    public OfferDataLoader(
        [Service] IMediator mediator,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<ValueObjectId<OfferAggregate>, Offer>> LoadBatchAsync(IReadOnlyList<ValueObjectId<OfferAggregate>> keys, CancellationToken cancellationToken)
    {
        List<Offer> results = await _mediator.Send(new OffersByIdsQuery(keys), cancellationToken);
        return results.ToDictionary(x => x.Id, x => x);
    }
}
