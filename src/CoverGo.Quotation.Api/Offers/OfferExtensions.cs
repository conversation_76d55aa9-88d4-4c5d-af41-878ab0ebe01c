using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Infrastructure.ProductsClient;
using StrawberryShake;

namespace CoverGo.Quotation.Api.Offers;

[ExtendObjectType<Offer>]
public static class OfferExtensions
{
    public static async Task<CensusSummary?> CensusSummary(
        [Parent] Offer offer,
        [Service] IMemberRepository<OfferMemberAggregate> offerMembersRepository,
        [Service] ProductsClient productsClient,
        CancellationToken cancellationToken)
    {
        var offerMembers =
            (await offerMembersRepository.FindAllByAsync(it => it.OfferId == offer.Id, cancellationToken)).ToList();
        if (offerMembers.Count == 0) return null;

        IOperationResult<IGetProductPlanDetailsResult> productPlanDetails = await productsClient.GetProductPlanDetails.ExecuteAsync(
            offer.ProductVersionId.ProductId.Plan, offer.ProductVersionId.ProductId.Type,
            offer.ProductVersionId.Version,
            cancellationToken);
        productPlanDetails.EnsureNoErrors();
        var planDetails = productPlanDetails.Data?.ProductProductPlanDetails?.PlanDetails;
        bool hasPlansBenefits = planDetails?.Any(p => p.Fields?.Count > 0) ?? false;

        string groupingField = hasPlansBenefits ? "class" : "plan";
        var groupedEntries = offerMembers.GroupBy(member =>
                (groupingField == "class" ? member.Class?.Value : member.PlanId?.Value) ?? string.Empty)
            .Select(group => new GroupedEntry
            {
                GroupKey = groupingField == "class"
                    ? group.Key
                    : planDetails?.FirstOrDefault(p => p.Id == group.Key)?.Name ?? group.Key,
                SubGroupCounts = GetSubGroupCounts(group)
            })
            .ToList();
        IEnumerable<GroupTotal> groupTotals = groupedEntries.Select(entry => new GroupTotal
        {
            GroupKey = entry.GroupKey,
            TotalCount = entry.SubGroupCounts.Sum(count => count.Count)
        });

        return new CensusSummary
        {
            GroupingField = groupingField,
            GroupedEntries = groupedEntries,
            GroupTotals = groupTotals
        };
    }

    private static IEnumerable<SubGroupCount> GetSubGroupCounts(IGrouping<string, OfferMemberAggregate> group)
    {
        int employeeSubGroup = group.Count(member =>
            member.Fields.FirstOrDefault(f => f.Key == "memberType")?.Value?.ToString() == "employee");
        var otherSubGroups = group.Where(member =>
                member.Fields.FirstOrDefault(f => f.Key == "memberType")?.Value?.ToString() == "dependent")
            .GroupBy(member =>
                member.Fields.FirstOrDefault(f => f.Key == "relationshipToEmployee")?.Value?.ToString() ?? string.Empty)
            .Select(subGroup => new SubGroupCount { MemberSubGroup = subGroup.Key, Count = subGroup.Count() }).ToList();

        return new List<SubGroupCount> { new() { MemberSubGroup = "employee", Count = employeeSubGroup } }
            .Concat(otherSubGroups);
    }
}
