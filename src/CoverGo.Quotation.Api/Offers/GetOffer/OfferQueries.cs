using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.GetOffer;

[QueryType]
public class OfferQueries
{
    [Authorize]
    public async Task<Offer> GetOffer(
        [GraphQLDescription("Offer Id")] ValueObjectId<OfferAggregate> id,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(new GetOfferByIdQuery(id), cancellationToken);
}
