using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Api.Offers.Errors;
using CoverGo.Quotation.Application.Offers.Commands.CreateOffer;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Offers;

using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.CreateOffer;

[MutationType]
public static class CreateOfferMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(InvalidFieldsException))]
    [UseMutationConvention(PayloadFieldName = "offer")]
    [Authorize]
    public static async Task<OfferResponse> CreateOffer(
        CreateOfferCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
