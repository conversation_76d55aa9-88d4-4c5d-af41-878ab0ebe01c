using CoverGo.Quotation.Application.Offers;
using CoverGo.Quotation.Application.Offers.Contracts;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.CreateOffer;

[ExtendObjectType<OfferResponse>]
public static class OfferResponseExtensions
{
    public static Task<Offer> Offer(
        [Parent] OfferResponse offerResponse,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return mediator.Send(new GetOfferByIdQuery(offerResponse.Id), cancellationToken);
    }
}
