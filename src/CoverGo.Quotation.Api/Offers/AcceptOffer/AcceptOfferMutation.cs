﻿using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Application.Offers.Commands.AcceptOffer;
using CoverGo.Quotation.Application.Offers.Contracts;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Offers.AcceptOffer;

[MutationType]
public static class AcceptOfferMutation
{
    [Authorize]
    [Error(typeof(InputDataValidationError))]
    public static async Task<Offer> AcceptOffer(AcceptOfferCommand input, [Service] IMediator mediator, CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);
}
