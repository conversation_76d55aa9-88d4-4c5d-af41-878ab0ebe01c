using CoverGo.Quotation.Application.Offers.Download;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Offers.Download;

[MutationType]
public class DownloadOfferMutation
{
    [Authorize]
    public async Task<byte[]> DownloadOffer(
        [Service] IMediator mediator,
        DownloadOfferCommand input,
        CancellationToken cancellationToken
    ) => await mediator.Send(input, cancellationToken);
}
