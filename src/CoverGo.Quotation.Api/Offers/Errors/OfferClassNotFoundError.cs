﻿using CoverGo.Quotation.Domain.Offers.Exceptions;

namespace CoverGo.Quotation.Api.Offers.Errors;

public sealed class OfferClassNotFoundError
{
    private readonly Exception _exception;

    public OfferClassNotFoundError(OfferClassNotFoundException exception)
    {
        _exception = exception;
    }

    public string Message => _exception.Message;
    public string Code => "CLASS_NOT_FOUND";
}
