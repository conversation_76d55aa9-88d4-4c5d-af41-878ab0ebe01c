using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Api.Offers.Errors;
using CoverGo.Quotation.Application.Offers.Commands.IssueOffer;
using CoverGo.Quotation.Application.Offers.Contracts;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Offers.IssueOffer;

[MutationType]
public static class MarkOfferAsIssuedMutation
{
    [Authorize]
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(OfferBenefitSelectionIncompleteError))]
    [Error(typeof(OfferInImmutableStatusError))]
    [Error(typeof(OfferPolicyDetailsMissingError))]
    [Error(typeof(OfferPremiumNotCalculatedError))]
    [Error(typeof(OfferMembersMissingError))]
    public static async Task<Offer> MarkOfferAsIssued(
        MarkOfferAsIssuedCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
