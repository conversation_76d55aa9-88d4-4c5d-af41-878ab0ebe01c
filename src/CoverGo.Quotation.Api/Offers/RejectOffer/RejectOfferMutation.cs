using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Application.Offers.Commands.RejectOffer;
using CoverGo.Quotation.Application.Offers.Contracts;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Offers.RejectOffer;

[MutationType]
public static class RejectOfferMutation
{
    [Authorize]
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(OfferInImmutableStatusError))]
    public static async Task<Offer> RejectOffer(RejectOfferCommand input, [Service] IMediator mediator, CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);
}
