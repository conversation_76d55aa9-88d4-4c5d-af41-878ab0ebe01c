using CoverGo.Quotation.Application.Offers.Contracts;

using MediatR;

namespace CoverGo.Quotation.Api.Offers.OfferBillingInformation;

[ExtendObjectType(typeof(LocalizedString))]
public class BillingFrequencyExtensions
{
    public async Task<string> DisplayValue(
        string locale,
        [Parent] LocalizedString billingFrequency,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
    {
        return $"{locale}+{billingFrequency.Value}";
    }
}
