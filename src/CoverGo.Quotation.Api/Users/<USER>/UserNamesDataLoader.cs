﻿using CoverGo.Quotation.Application.Users.Queries;
using MediatR;

namespace CoverGo.Quotation.Api.Users.DataLoaders;

// ReSharper disable once ClassNeverInstantiated.Global
public sealed class UserNamesDataLoader(IMediator mediator, IBatchScheduler batchScheduler, DataLoaderOptions? options = null)
    : BatchDataLoader<string, string>(batchScheduler, options)
{
    protected override async Task<IReadOnlyDictionary<string, string>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken) =>
        (await mediator.Send(new UserNamesQuery(keys), cancellationToken)).AsReadOnly();
}
