﻿using CoverGo.Quotation.Api.Users.DataLoaders;
using CoverGo.Quotation.Application.Users;

namespace CoverGo.Quotation.Api.Users;

[ExtendObjectType(typeof(QuotationUser))]
public static class UserExtensions
{
    public static async Task<string> Name(
        [Parent] QuotationUser user,
        [Service] UserNamesDataLoader dataLoader,
        CancellationToken cancellationToken = default) =>
        await dataLoader.LoadAsync(user.Id, cancellationToken);
}
