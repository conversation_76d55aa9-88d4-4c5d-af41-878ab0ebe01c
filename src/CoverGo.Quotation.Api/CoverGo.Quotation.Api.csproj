<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <UserSecretsId>************************************</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Applications.HealthCheck" />
    <PackageReference Include="CoverGo.BuildingBlocks.Observability" />
    <PackageReference Include="CoverGo.BuildingBlocks.Audit" />
    <PackageReference Include="CoverGo.BuildingBlocks.Scheduler.Hangfire" />
    <PackageReference Include="HotChocolate.AspNetCore" />
    <PackageReference Include="HotChocolate.AspNetCore.CommandLine" />
    <PackageReference Include="HotChocolate.Data.MongoDb" />
    <PackageReference Include="HotChocolate.Types.Analyzers">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="HotChocolate.Stitching.Redis" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.BuildingBlocks.Api.Graphql" />
    <PackageReference Include="CoverGo.BuildingBlocks.Application.Core" />
    <PackageReference Include="CoverGo.BuildingBlocks.Auth" />
    <PackageReference Include="CoverGo.Multitenancy.AspNetCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Quotation.Infrastructure\CoverGo.Quotation.Infrastructure.csproj" />
    <ProjectReference Include="..\CoverGo.Quotation.IntegrationEvents\CoverGo.Quotation.IntegrationEvents.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\.dockerignore">
      <Link>.dockerignore</Link>
    </Content>
  </ItemGroup>

</Project>
