{"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning"}}, "Enrich": ["FromLogContext", "WithThreadId", "WithMachineName", "WithSpan"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "AllowedHosts": "*", "serviceUrls": {"auth": "http://covergo-auth:8080"}, "ConnectionStrings": {"redis": "covergo-redis-master:6379,abortConnect=false", "gateway": "http://covergo-gateway:8080/", "policies": "http://covergo-policies:8080/", "reference": "http://covergo-reference/", "users": "http://covergo-users:8080/", "products": "http://covergo-products:8080/", "channelManagement": "http://covergo-channel-management/", "templates": "http://covergo-templates:8080/", "auth": "http://covergo-auth:8080", "premium": "http://covergo-premium:8080/", "fileSystem": "http://covergo-filesystem:8080/"}, "ObservabilityConfiguration": {"CollectorUrl": "http://grafana-agent-traces.monitoring:4317", "ServiceName": "quotation-service", "Timeout": 1000}, "MongoDatabaseConfiguration": {"DatabaseName": "quotation", "UseTransactions": true}, "FeatureManagement": {"UseBenefitMirroring": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["coverHealth_dev", "gms_dev", "gms_preprod", "gms", "gms_prod", "gms_qa", "gms_migration", "gms_sandbox", "gms_training", "xn_dev", "xni", "xni_qa", "xni_prod", "xni_preprod", "medihelp_uat", "medihelp_dev", "medihelp_qa", "medihelp", "medisky_dev", "medisky_uat", "medisky_prod", "medisky_qa", "humania_dev", "humania_uat", "santevet_dev", "santevet_uat"]}}]}, "UseOfferIdGeneration": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["gms_dev", "gms_preprod", "gms", "gms_prod", "gms_qa", "gms_migration", "gms_sandbox", "gms_training"]}}]}, "UseLegacyProductLifecycle": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_dev", "asia_preprod", "asia_prod", "xni", "xni_prod", "xni_preprod", "medihelp", "medisky_prod", "gms_dev", "gms", "gms_prod", "gms_qa", "gms_migration", "gms_sandbox", "gms_training"]}}]}, "UseLegacyProposalTaxInput": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["xn_dev", "xni", "xni_qa", "xni_prod", "xni_preprod"]}}]}}, "GraphQL": {"IncludeExceptionDetails": false}, "GraphQLStitching": {"Enabled": true, "SchemaName": "quotation", "Redis": {"Publish": true, "ConfigurationName": "GatewayV2"}}, "PubSubConfiguration": {"Environment": "", "SiloModel": {"Tenants": ["coverHealth_dev", "asia_dev", "asia_preprod", "asia_prod"]}}, "CheckDI": false, "RunJobs": true, "AuditLogging": {"IsDisabled": true, "ExcludedPaths": [], "ExcludedOperations": []}}