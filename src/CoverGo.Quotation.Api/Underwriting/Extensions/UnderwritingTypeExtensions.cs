using CoverGo.Quotation.Api.Underwriting.DataLoaders;
using CoverGo.Quotation.Application.ProposalMembers.Queries;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Underwriting;

using HotChocolate.Authorization;
using HotChocolate.Data;

using MediatR;

using MongoDB.Driver;

namespace CoverGo.Quotation.Api.Underwriting.Extensions;

[ExtendObjectType(typeof(MemberMovementUnderwritingCase))]
public class MemberMovementUnderwritingCaseTypeExtensions
{
    [Authorize]
    [UseOffsetPaging(CollectionSegmentName = "QuoteMembers")]
    [UseSorting]
    [UseFiltering]
    public IExecutable<QuoteMember> Members(
        [Parent] MemberMovementUnderwritingCase uwCase,
        [Service] IMongoCollection<QuoteMember> quoteMembers)
    {
        return quoteMembers.Aggregate()
            .Match(f => f.QuoteId == uwCase.QuoteId && f.NeedsUnderwriting == true)
            .AsExecutable();
    }
}

[ExtendObjectType(typeof(ProposalUnderwritingCase))]
public class ProposalUnderwritingTypeExtensions
{
    public UnderwritingMembers Members(UnderwritingMembersByProposalIdQuery query) =>
        new UnderwritingMembers(query);

    public ProposalUnderwritingMembersStats MembersStats(
        [Parent] ProposalUnderwritingCase underwritingCase
    ) => new ProposalUnderwritingMembersStats(underwritingCase);

    public Task<Proposal> Proposal(
        [Parent] ProposalUnderwritingCase underwritingCase,
        [Service] UnderwritingProposalDataLoader dataLoader,
        CancellationToken cancellationToken
    ) => dataLoader.LoadAsync(underwritingCase.ProposalId, cancellationToken);
}

public class ProposalUnderwritingMembersStats(ProposalUnderwritingCase underwritingCase)
{
    public Task<long> Total([Service] IMediator mediator, CancellationToken cancellationToken) =>
        mediator.Send(
            new ProposalMembersByStatusCountQuery
            {
                ProposalId = underwritingCase.ProposalId,
            },
            cancellationToken
        );

    public Task<long> Approved([Service] IMediator mediator, CancellationToken cancellationToken) =>
        mediator.Send(
            new ProposalMembersByStatusCountQuery
            {
                ProposalId = underwritingCase.ProposalId,
                Status = MemberUnderwritingStatus.Accepted
            },
            cancellationToken
        );

    public Task<long> Rejected([Service] IMediator mediator, CancellationToken cancellationToken) =>
        mediator.Send(
            new ProposalMembersByStatusCountQuery
            {
                ProposalId = underwritingCase.ProposalId,
                Status = MemberUnderwritingStatus.Rejected
            },
            cancellationToken
        );

    public Task<long> Pending([Service] IMediator mediator, CancellationToken cancellationToken) =>
        mediator.Send(
            new ProposalMembersByStatusCountQuery
            {
                ProposalId = underwritingCase.ProposalId,
                Status = MemberUnderwritingStatus.Pending
            },
            cancellationToken
        );
}
