using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Underwriting;

using HotChocolate.Authorization;
using HotChocolate.Data;
using HotChocolate.Data.MongoDb;
using MediatR;

using MongoDB.Driver;

namespace CoverGo.Quotation.Api.Underwriting;

[QueryType]
public static class UnderwritingQuery
{
    [Authorize]
    public static Task<ProposalUnderwritingCase?> UnderwritingCaseByProposal(
        [Service] IMediator mediator,
        UnderwritingByProposalQuery query,
        CancellationToken cancellationToken
    ) => mediator.Send(query, cancellationToken)!;

    /// <summary>
    /// Deprecated. Please, use `underwritingQueue`
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public static UnderwritingCases UnderwritingCases(ProposalUnderwritingsQuery query) =>
        new UnderwritingCases(query);

    /// <summary>
    /// Returns ordered underwriting cases
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [UseOffsetPaging(typeof(NonNullType<UnderwritingCaseType>), CollectionSegmentName = "UnderwritingCases")]
    [UseFiltering]
    public static IExecutable<UnderwritingCase> UnderwritingQueue([Service] IMongoCollection<UnderwritingCase> collection)
    {
        return (IExecutable<UnderwritingCase>)collection
            .AsExecutable()
            .WithSorting(Builders<UnderwritingCase>.Sort.Descending(it => it.EntityAuditInfo.CreatedAt).Wrap());
    }

    /// <summary>
    /// Returns ordered member movement underwriting cases
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [UseOffsetPaging(typeof(NonNullType<MemberMovementUnderwritingCaseType>), CollectionSegmentName = "MemberMovementUnderwritingCases")]
    [UseFiltering]
    public static IExecutable<MemberMovementUnderwritingCase> MemberMovementUnderwritingQueue([Service] IMongoCollection<MemberMovementUnderwritingCase> collection)
    {
        return (IExecutable<MemberMovementUnderwritingCase>)collection
            .AsExecutable()
            .WithSorting(Builders<MemberMovementUnderwritingCase>.Sort.Descending(it => it.EntityAuditInfo.CreatedAt).Wrap());
    }

    [Authorize]
    public static Task<ProposalUnderwritingCase> UnderwritingCase(
        Id id,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default
    ) => mediator.Send(new UnderwritingByIdQuery(id), cancellationToken);
}
