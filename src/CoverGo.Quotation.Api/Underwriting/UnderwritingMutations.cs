using CoverGo.Quotation.Api.ProposalMembers.Errors;
using CoverGo.Quotation.Api.Underwriting.Errors;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Loadings.Exceptions;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;
using CoverGo.Quotation.Domain.Underwriting.Exceptions;
using HotChocolate.Authorization;
using ProposalLoading = CoverGo.Quotation.Application.Loadings.Contracts.ProposalLoading;
using MediatR;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Underwriting.Benefits.Exceptions;

namespace CoverGo.Quotation.Api.Underwriting;

[MutationType]
public static class UnderwritingMutations
{
    [Authorize]
    public static async Task<ProposalUnderwritingCase> RequestUnderwriting(
        [Service] IMediator mediator,
        ValueObjectId<ProposalAggregate> proposalId,
        CancellationToken cancellationToken = default)
        => await mediator.Send(
            new RequestUnderwritingCommand { ProposalId = proposalId },
            cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingCaseNotFoundError))]
    [Error(typeof(InvalidUnderwritingCaseStatusError))]
    [Error(typeof(UnderwritingCaseHasDecisionError))]
    [Error(typeof(UnderwritingCaseHasPendingMembersError))]
    [Error(typeof(UnderwritingCaseNotAcceptedMembersException))]
    public static async Task<UnderwritingCase> AcceptUnderwritingCase(
        [Service] IMediator mediator,
        AcceptUnderwritingCaseCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingCaseNotFoundError))]
    [Error(typeof(InvalidUnderwritingCaseStatusError))]
    [Error(typeof(UnderwritingCaseEmptyRejectReasonError))]
    [Error(typeof(UnderwritingCaseHasDecisionError))]
    public static async Task<UnderwritingCase> RejectUnderwritingCase(
        [Service] IMediator mediator,
        RejectUnderwritingCaseCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> AddProposalMemberExclusion(
        [Service] IMediator mediator,
        AddProposalMemberExclusionCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<IUnderwritableMember> AddUnderwritingCaseMemberExclusion(
        [Service] IMediator mediator,
        AddUnderwritingCaseMemberExclusionCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> RemoveProposalMemberExclusion(
        [Service] IMediator mediator,
        RemoveProposalMemberExclusionCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<IUnderwritableMember> DeleteUnderwritingCaseMemberExclusion(
        [Service] IMediator mediator,
        DeleteUnderwritingCaseMemberExclusionCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> AddProposalMemberPreExistingCondition(
        [Service] IMediator mediator,
        AddProposalMemberPreExistingConditionCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> RemoveProposalMemberPreExistingCondition(
        [Service] IMediator mediator,
        RemoveProposalMemberPreExistingConditionCommand input,
        CancellationToken cancellationToken = default)
        => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(BenefitLevelUnderwritingPendingException))]
    public static async Task<IUnderwritableMember> AcceptUnderwritingCaseMember(
        AcceptUnderwritingCaseMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(ProposalMemberNotFoundError))]
    [Error(typeof(InvalidMemberRejectReasonCodeError))]
    [Error(typeof(BenefitLevelUnderwritingPendingException))]
    public static async Task<IUnderwritableMember> RejectUnderwritingCaseMember([Service] IMediator mediator,
        RejectUnderwritingCaseMemberCommand input,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(QuoteFinalizedException))]
    [Error(typeof(BenefitLoadingAlreadyExistsException))]
    public static async Task<ProposalLoading> AddUnderwritingCaseLoading([Service] IMediator mediator,
        AddUnderwritingCaseLoadingCommand input,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(QuoteFinalizedException))]
    [Error(typeof(LoadingTypeCannotBeChangedException))]
    public static async Task<ProposalLoading> UpdateUnderwritingCaseLoading([Service] IMediator mediator,
        UpdateUnderwritingCaseLoadingCommand input,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<ProposalLoading> DeleteUnderwritingCaseLoading([Service] IMediator mediator,
        DeleteUnderwritingCaseLoadingCommand input,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<MemberLoading> AddUnderwritingCaseMemberLoading(
        AddUnderwritingCaseMemberLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(AutoLoadingCannotBeUpdatedException))]
    [Error(typeof(LoadingNotFoundException))]
    [Error(typeof(LoadingTypeCannotBeChangedException))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<MemberLoading> UpdateUnderwritingCaseMemberLoading(
        UpdateUnderwritingCaseMemberLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(LoadingNotFoundException))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<MemberLoading> DeleteUnderwritingCaseMemberLoading(
        DeleteUnderwritingCaseMemberLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<IUnderwritableMember> AddUnderwritingCaseMemberPreExistingCondition(
        AddUnderwritingCaseMemberPreExistingConditionCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(QuoteFinalizedException))]
    public static async Task<IUnderwritableMember> DeleteUnderwritingCaseMemberPreExistingCondition(
        DeleteUnderwritingCaseMemberPreExistingConditionCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalMemberNotFoundError))]
    [Error(typeof(BenefitNotFoundException))]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<MemberBenefitUnderwriting> AcceptUnderwritingCaseMemberBenefit(
        AcceptUnderwritingCaseMemberBenefitCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalMemberNotFoundError))]
    [Error(typeof(BenefitNotFoundException))]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<MemberBenefitUnderwriting> RejectUnderwritingCaseMemberBenefit(
        RejectUnderwritingCaseMemberBenefitCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(CantHaveMultipleMemberBenefitLoadingsException))]
    public static async Task<IUnderwritableMember> AddUnderwritingCaseMemberBenefitLoading(
        AddUnderwritingCaseMemberBenefitLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    [Error(typeof(AutoLoadingCannotBeUpdatedException))]
    [Error(typeof(LoadingTypeCannotBeChangedException))]
    public static async Task<IUnderwritableMember> UpdateUnderwritingCaseMemberBenefitLoading(
        UpdateUnderwritingCaseMemberBenefitLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<IUnderwritableMember> DeleteUnderwritingCaseMemberBenefitLoading(
        DeleteUnderwritingCaseMemberBenefitLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> AddUnderwritingCaseMemberDocuments(
        AddUnderwritingCaseMemberDocumentsCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(UnderwritingIsInFinalStatusException))]
    [Error(typeof(ProposalMemberCanNotBeChangedError))]
    [Error(typeof(EntityNotFoundException))]
    public static async Task<ProposalMember> RemoveUnderwritingCaseMemberDocument(
        RemoveUnderwritingCaseMemberDocumentCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
