using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Underwriting;
using MediatR;

namespace CoverGo.Quotation.Api.Underwriting;

public class UnderwritingMembers(UnderwritingMembersByProposalIdQuery query)
{
    public Task<long> Total([Service] IMediator mediator, CancellationToken cancellationToken) =>
        mediator.Send(
            new UnderwritingMembersByProposalIdQueryCountQuery
            {
                ProposalId = query.ProposalId,
                Filter = query.Filter
            },
            cancellationToken
        );

    public Task<IEnumerable<ProposalMember>> Items(
        [Service] IMediator mediator,
        CancellationToken cancellationToken
    ) => mediator.Send(query, cancellationToken);
}
