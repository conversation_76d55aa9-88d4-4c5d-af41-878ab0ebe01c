using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Api.Quotes;

namespace CoverGo.Quotation.Api.Underwriting;

public class MemberMovementUnderwritingCaseType : ObjectType<MemberMovementUnderwritingCase>
{
    protected override void Configure(IObjectTypeDescriptor<MemberMovementUnderwritingCase> descriptor)
    {
        descriptor.Ignore(it => it.DomainEvents);
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.QuoteId).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.Policy);
        descriptor.Field("entityAuditInfo").Type<NonNullType<ObjectType<AuditInfo>>>().Resolve(descriptor =>
        {
            var parent = descriptor.Parent<MemberMovementUnderwritingCase>();
            var mapper = descriptor.Service<IMapper>();
            return mapper.Map<AuditInfo>(parent.EntityAuditInfo);
        });
        descriptor.Ignore(it => it.AssignedTo);
        descriptor.Field(x => x.Quote).Resolve(async (ctx, ct) =>
        {
            var quoteId = ctx.Parent<MemberMovementUnderwritingCase>().QuoteId;
            var quoteDataLoader = ctx.Service<QuoteDataLoader>();
            return await quoteDataLoader.LoadAsync(quoteId, ct);
        });
    }
}
