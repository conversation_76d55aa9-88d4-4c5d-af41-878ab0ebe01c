using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Underwriting;

public sealed class UnderwritingDecisionType : InterfaceType<UnderwritingDecisionBase>
{
}

public sealed class UnderwritingAcceptanceDecisionType : ObjectType<UnderwritingAcceptanceDecision>
{
    protected override void Configure(IObjectTypeDescriptor<UnderwritingAcceptanceDecision> descriptor) =>
        descriptor.Implements<UnderwritingDecisionType>();
}

public sealed class UnderwritingRejectionDecisionType : ObjectType<UnderwritingRejectionDecision>
{
    protected override void Configure(IObjectTypeDescriptor<UnderwritingRejectionDecision> descriptor) =>
        descriptor.Implements<UnderwritingDecisionType>();
}
