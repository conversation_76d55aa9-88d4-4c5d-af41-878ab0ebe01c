using CoverGo.Quotation.Api.Configuration.GraphQl.Queries;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Underwriting;
using HotChocolate.Data.Filters;

namespace CoverGo.Quotation.Api.Underwriting;

public class UnderwritingCaseFilterType : FilterInputType<UnderwritingCase>
{
    protected override void Configure(IFilterInputTypeDescriptor<UnderwritingCase> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.Id).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(it => it.Status);
        descriptor.Operation(CustomFilterOperations.Search).Type<StringType>();
    }
}

public class MemberMovementUnderwritingCaseFilterType : FilterInputType<MemberMovementUnderwritingCase>
{
    protected override void Configure(IFilterInputTypeDescriptor<MemberMovementUnderwritingCase> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.QuoteId).Type<EntityIdOperationFilterInputType>();
    }
}
