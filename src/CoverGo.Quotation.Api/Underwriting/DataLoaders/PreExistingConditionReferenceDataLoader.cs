using CoverGo.Quotation.Api.ReferenceData;
using CoverGo.Quotation.Application.Underwriting;
using MediatR;

namespace CoverGo.Quotation.Api.Underwriting.DataLoaders;

public sealed class PreExistingConditionReferenceDataLoader(IMediator mediator, IBatchScheduler batchScheduler, DataLoaderOptions? options = null) : ReferenceDataLoader<GetPreExistingConditionReferenceDataNamesQuery>(mediator, batchScheduler, options)
{
    protected override GetPreExistingConditionReferenceDataNamesQuery CreateQuery(IEnumerable<string> mnemonics, string localeId) => new(mnemonics, localeId);
}
