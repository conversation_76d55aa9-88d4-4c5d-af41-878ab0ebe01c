using CoverGo.Quotation.Api.ReferenceData;
using CoverGo.Quotation.Application.Underwriting;
using MediatR;

namespace CoverGo.Quotation.Api.Underwriting.DataLoaders;

public sealed class ExclusionReferenceDataDataLoader(IMediator mediator, IBatchScheduler batchScheduler, DataLoaderOptions? options = null) : ReferenceDataLoader<GetExclusionReferenceDataNamesQuery>(mediator, batchScheduler, options)
{
    protected override GetExclusionReferenceDataNamesQuery CreateQuery(IEnumerable<string> mnemonics, string localeId) => new(mnemonics, localeId);
}
