using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Infrastructure.Proposals.Queries;

using MediatR;

namespace CoverGo.Quotation.Api.Underwriting.DataLoaders;

public class UnderwritingProposalDataLoader(IMediator mediator, IBatchScheduler batchScheduler, DataLoaderOptions? options = null)
    : BatchDataLoader<ValueObjectId<ProposalAggregate>, Proposal>(batchScheduler, options)
{
    protected override async Task<IReadOnlyDictionary<ValueObjectId<ProposalAggregate>, Proposal>>
        LoadBatchAsync(
            IReadOnlyList<ValueObjectId<ProposalAggregate>> keys,
            CancellationToken cancellationToken) =>
        (await mediator.Send(new ProposalsQuery(keys), cancellationToken)).
        AsReadOnly();
}
