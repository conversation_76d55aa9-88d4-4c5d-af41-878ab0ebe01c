using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using MediatR;

namespace CoverGo.Quotation.Api.Underwriting.DataLoaders;

public sealed class PreExistingConditionDiagnosisLoader
    : BatchDataLoader<string, DiagnosisDetails>
{
    private readonly IMediator _mediator;

    public PreExistingConditionDiagnosisLoader(IMediator mediator, IBatchScheduler batchScheduler, DataLoaderOptions? options = null)
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<string, DiagnosisDetails>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
    {
        var detailsBatch = await _mediator.Send(new GetDiagnosisQuery(keys), cancellationToken);
        return detailsBatch ?? new Dictionary<string, DiagnosisDetails>();
    }
}
