using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Api.Underwriting;

public sealed class UnderwritingCaseDecisionType : InterfaceType<UnderwritingCaseDecision>;

public sealed class UnderwritingApprovedCaseDecisionType : ObjectType<UnderwritingAcceptanceCaseDecision>
{
    protected override void Configure(IObjectTypeDescriptor<UnderwritingAcceptanceCaseDecision> descriptor) =>
        descriptor.Implements<UnderwritingCaseDecisionType>();
}

public sealed class UnderwritingRejectCaseDecisionType : ObjectType<UnderwritingRejectionCaseDecision>
{
    protected override void Configure(IObjectTypeDescriptor<UnderwritingRejectionCaseDecision> descriptor) =>
        descriptor.Implements<UnderwritingCaseDecisionType>();
}
