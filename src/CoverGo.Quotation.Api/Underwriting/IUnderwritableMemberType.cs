using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Api.Underwriting;

public sealed class IUnderwritableMemberType : InterfaceType<IUnderwritableMember>
{
    protected override void Configure(IInterfaceTypeDescriptor<IUnderwritableMember> descriptor)
    {
        descriptor.Name("UnderwritingCaseMember");
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.MemberUnderwriting).Type<NonNullType<MemberUnderwritingType>>();
    }
}