using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Underwriting;

using MediatR;

namespace CoverGo.Quotation.Api.Underwriting;


public class UnderwritingCases(ProposalUnderwritingsQuery query)
{
    public Task<IEnumerable<ProposalUnderwritingCase>> Items([Service] IMediator mediator, CancellationToken cancellationToken) => mediator.Send(query, cancellationToken)!;

    public Task<long> Total([Service] IMediator mediator, CancellationToken cancellationToken) => mediator.Send(new ProposalUnderwritingsCountQuery
    {
        Filter = query.Filter,
        Page = query.Page,
        PageSize = query.PageSize
    }, cancellationToken)!;
}
