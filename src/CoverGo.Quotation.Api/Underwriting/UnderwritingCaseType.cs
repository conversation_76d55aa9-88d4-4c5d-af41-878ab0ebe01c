using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Api.Underwriting;

public class UnderwritingCaseType : InterfaceType<UnderwritingCase>
{
    protected override void Configure(IInterfaceTypeDescriptor<UnderwritingCase> descriptor)
    {
        descriptor.Ignore(it => it.DomainEvents);
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
        descriptor.Field("entityAuditInfo").Type(typeof(NonNullType<ObjectType<AuditInfo>>));
        descriptor.Ignore(it => it.AssignedTo);
        descriptor.Field(it => it.Quote);
    }
}
