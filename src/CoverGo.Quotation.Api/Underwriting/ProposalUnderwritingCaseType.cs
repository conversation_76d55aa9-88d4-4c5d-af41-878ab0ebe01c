using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Api.Underwriting;

public class ProposalUnderwritingCaseType : ObjectType<ProposalUnderwritingCase>
{
    protected override void Configure(IObjectTypeDescriptor<ProposalUnderwritingCase> descriptor)
    {
        descriptor.Ignore(it => it.DomainEvents);
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
        descriptor.Field("entityAuditInfo").Type<NonNullType<ObjectType<AuditInfo>>>().Resolve(descriptor =>
        {
            var parent = descriptor.Parent<ProposalUnderwritingCase>();
            var mapper = descriptor.Service<IMapper>();
            return mapper.Map<AuditInfo>(parent.EntityAuditInfo);
        });
        descriptor.Ignore(it => it.AssignedTo);
    }
}
