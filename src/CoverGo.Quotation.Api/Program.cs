using CoverGo.BuildingBlocks.Application.Core;
using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.BuildingBlocks.Bootstrapper.ApiBootstrapper;
using CoverGo.BuildingBlocks.MessageBus.Dapr;
using CoverGo.BuildingBlocks.Scheduler.Hangfire;
using CoverGo.Quotation.Api;
using CoverGo.Quotation.Api.Configuration.GraphQl;
using CoverGo.Quotation.Application;
using CoverGo.Quotation.Infrastructure;
using StackExchange.Redis;
using CoverGo.Applications.Monitoring;
using CoverGo.BuildingBlocks.Audit;
using CoverGo.Quotation.Application.VersionBridge;
using CoverGo.Quotation.Infrastructure.Decorators;

WebApplicationBuilder webApplicationBuilder = WebApplication.CreateBuilder(args);
var useInMemoryBus = webApplicationBuilder.Configuration.GetValue<bool>("UseInMemoryBus");
var runJobs = webApplicationBuilder.Configuration.GetValue<bool>("RunJobs");
var checkDi = webApplicationBuilder.Configuration.GetValue<bool>("CheckDI");

webApplicationBuilder.Host.UseDefaultServiceProvider(options =>
{
    options.ValidateOnBuild = checkDi;
    options.ValidateScopes = checkDi;
});

var webAppBuilder = ApiServiceBootstrapper
    .Initialize(webApplicationBuilder)
    .WithCoreSetup()
    .WithMultiTenantContext()
    .WithAuthentication()
    .WithLogging()
    .WithMetrics()
    .WithTracing()
    .WithCoreHealthCheck()
    .WithServiceConfiguration(services =>
    {
        services.AddAutoMapper(typeof(ApplicationMappingProfile).Assembly);

        services.AddApplication();

        services.AddVersionBridgeMappers();

        services.AddInfrastructure(webApplicationBuilder.Configuration, useInMemoryBus, useOutbox: runJobs);

        services.AddInfrastructureDecorators(webApplicationBuilder.Configuration);

        services.AddApplicationDecorators();

        if (runJobs)
        {
            services.AddHangfireScheduler(webApplicationBuilder.Configuration);
        }

        services.AddCQRS(
            [
                typeof(ApplicationServiceExtensions).Assembly,
                typeof(InfrastructureServiceExtensions).Assembly,
            ],
            configuration =>
            {
                configuration.AddInfrastructureBehaviorsAndPreprocessors();
            }
        );
        services.AddUserContextProviders();

        var redisConnectionString = webApplicationBuilder.Configuration.GetConnectionString(
            "redis"
        );
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            services.AddSingleton<IConnectionMultiplexer>(sp =>
            {
                return ConnectionMultiplexer.Connect(redisConnectionString);
            });
        }

        var stitchingConfiguration =
            webApplicationBuilder
                .Configuration.GetRequiredSection("GraphQLStitching")
                .Get<GraphQLStitchingOptions>() ?? new();
        var graphqlConfiguration =
            webApplicationBuilder.Configuration.GetRequiredSection("GraphQL").Get<GraphQLOptions>()
            ?? new();

        services
            .AddApi(stitchingConfiguration, graphqlConfiguration)
            .AddCoverGoAuthorization(webApplicationBuilder.Configuration);

        services.AddAuditLogging(webApplicationBuilder.Configuration);
    });

WebApplication app = webAppBuilder.BuildWebApp(app =>
{
    app.MapGraphQL();
    app.UseWebSockets();
    app.MapVersionEndpoint();
    app.UseGraphQLAuditLogging();
    app.UseRestAuditLogging();

    if (!useInMemoryBus)
    {
        app.MessageBus(app.Configuration)
            .SubscribeToIntegrationEvents()
            .SubscribeToV1SyncEvents();

        if (runJobs)
        {
            app.AddHangfireDashboards();
        }
    }

    if (runJobs)
    {
        app.RegisterJobs();
    }
});

try
{
    app.Logger.LogInformation("Starting web host...");
    await app.IndexAsync();
    await app.RunWithGraphQLCommandsAsync(args);
}
catch (Exception ex)
{
    app.Logger.LogCritical(ex, "Host terminated unexpectedly...");
}

// This is to make autogenerated class Program public. It is required to allow it to be used for WebApplicationFactory<Program>.
// https://learn.microsoft.com/en-us/aspnet/core/test/integration-tests?view=aspnetcore-8.0#basic-tests-with-the-default-webapplicationfactory
public partial class Program { }
