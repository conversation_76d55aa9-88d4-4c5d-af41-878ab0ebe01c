using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Queries;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Proposals;

using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.Proposals;

[QueryType]
public static class ProposalQueries
{
    [Authorize]
    public static Task<Proposal> Proposal([GraphQLDescription("Proposal Id")] ValueObjectId<ProposalAggregate> id, [Service] IMediator mediator, CancellationToken cancellationToken = default) =>
        mediator.Send(new ProposalQuery(id), cancellationToken);

    [Authorize]
    public static Task<Proposal?> ProposalByOpportunityId([GraphQLDescription("Opportunity Id")] ValueObjectId<OpportunityAggregate> opportunityId, [Service] IMediator mediator, CancellationToken cancellationToken = default) =>
        mediator.Send(new ProposalByOpportunityIdQuery(opportunityId), cancellationToken);

    [Authorize]
    public static Task<PagedResult<Proposal>> Proposals(
        int? skip,
        int? take,
        ProposalsWhere? where,
        ProposalsOrder? order,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default
    ) => mediator.Send(new ProposalsQuery{
            Skip = skip,
            Take = take,
            Where = where,
            Order = order
        }, cancellationToken);
}
