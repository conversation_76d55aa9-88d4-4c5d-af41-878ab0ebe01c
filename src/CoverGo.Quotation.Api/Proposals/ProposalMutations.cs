using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Api.Proposals.Errors;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Application.Proposals.Applications;
using CoverGo.Quotation.Application.Proposals.BenefitSelection;
using CoverGo.Quotation.Application.Proposals.Commands;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Loadings.Exceptions;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.Exceptions;
using CoverGo.Quotation.Domain.Pricing;
using CoverGo.Quotation.Domain.Proposals.Exceptions;
using CoverGo.Quotation.Infrastructure.Common.SendDocumentsByEmail;
using HotChocolate.Authorization;

using MediatR;
// ReSharper disable UnusedMember.Global

namespace CoverGo.Quotation.Api.Proposals;

[MutationType]
public static class ProposalMutations
{
    [Authorize]
    [Error<ProposalPolicyDetailsInvalidDatesException>]
    [Error(typeof(InvalidProposalStatusException))]
    public static async Task<Proposal> UpdateProposalApplicationForm(
        UpdateProposalApplicationFormCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(InvalidProposalStatusException))]
    [Error(typeof(PolicyInitialPremiumReceivedException))]
    [Error(typeof(PolicyAlreadyIssuedException))]
    public static async Task<Proposal> UpdateProposalBillingFrequency(
        UpdateProposalBillingFrequencyCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(ProposalClassNotFoundError))]
    [Error(typeof(PlanInvalidFieldsException))]
    public static async Task<Proposal> DefineProposalClassBenefitSelection(
        [Service] IMediator mediator,
        DefineProposalClassBenefitSelectionCommand input,
        CancellationToken cancellationToken = default) =>
        await mediator.Send(
            input,
            cancellationToken);

    [Authorize]
    [Error(typeof(BenefitLoadingAlreadyExistsException))]
    public static async Task<ProposalLoading> AddProposalLoading(
        AddProposalLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(LoadingNotFoundException))]
    [Error(typeof(LoadingTypeCannotBeChangedException))]
    public static async Task<ProposalLoading> UpdateProposalLoading(
        UpdateProposalLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(LoadingNotFoundException))]
    public static async Task<ProposalLoading> DeleteProposalLoading(
        DeleteProposalLoadingCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(InvalidTaxRateException))]
    [Error(typeof(CountryReferenceDataNotFoundException))]
    public static async Task<ProposalTax> UpdateProposalTax(
        UpdateProposalTaxCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error<Domain.Common.EntityNotFoundException>]
    [Error(typeof(InvalidProposalStatusException))]
    public static async Task<Proposal> ExtendProposalValidity(
        ExtendProposalValidityCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error<Domain.Common.EntityNotFoundException>]
    [Error(typeof(InvalidProposalStatusException))]
    [Error(typeof(ProposalNotExpiredException))]
    public static async Task<Proposal> CloseProposal(
        CloseProposalCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    public static async Task<byte[]> DownloadProposal(
        DownloadProposalCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(InvalidProposalStatusException))]
    public static async Task<bool> SendProposal(
       SendProposalCommand input,
       [Service] IMediator mediator,
       CancellationToken cancellationToken) =>
       await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error(typeof(MemberNotPrimaryException))]
    [Error(typeof(MemberDependentOfCycleException))]
    [Error(typeof(UniqueFieldException))]
    [Error(typeof(InvalidProposalStatusException))]
    [Error<EntityNotFoundException>]
    [Error(typeof(SendDocumentsByEmailException))]
    [Error<ProductCantHaveMultiplePrimariesException>]
    public static async Task<Proposal> UpdateRenewalOpportunityProposal(
        UpdateRenewalOpportunityProposalCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<TaxOverrideNotAllowedException>]
    [Error<PolicyAlreadyIssuedException>]
    [Error<InvalidTaxValueException>]
    [Error(typeof(InputDataValidationError))]
    public static async Task<Proposal> UpdateProposalTaxOverrides(
     UpdateProposalTaxOverridesCommand input,
     [Service] IMediator mediator,
     CancellationToken cancellationToken) => await mediator.Send(input, cancellationToken);
}
