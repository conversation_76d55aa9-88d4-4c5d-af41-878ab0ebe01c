using CoverGo.Quotation.Domain.Proposals.Exceptions;

namespace CoverGo.Quotation.Api.Proposals.Errors;

public sealed class ProposalClassNotFoundError
{
    private readonly Exception _exception;

    public ProposalClassNotFoundError(ProposalClassNotFoundException exception)
    {
        _exception = exception;
    }

    public string Message => _exception.Message;
    public string Code => "CLASS_NOT_FOUND";
}
