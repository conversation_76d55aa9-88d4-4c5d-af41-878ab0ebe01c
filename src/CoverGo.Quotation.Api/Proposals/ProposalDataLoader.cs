﻿using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Queries;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using MediatR;

namespace CoverGo.Quotation.Api.Proposals;

public class ProposalDataLoader
    : BatchDataLoader<ValueObjectId<ProposalAggregate>, Proposal>
{
    readonly IMediator _mediator;

    public ProposalDataLoader(
        [Service] IMediator mediator,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<ValueObjectId<ProposalAggregate>, Proposal>> LoadBatchAsync(IReadOnlyList<ValueObjectId<ProposalAggregate>> keys, CancellationToken cancellationToken)
    {
        List<Proposal> results = await _mediator.Send(new ProposalsByIdsQuery(keys), cancellationToken);
        return results.ToDictionary(x => x.Id, x => x);
    }
}
