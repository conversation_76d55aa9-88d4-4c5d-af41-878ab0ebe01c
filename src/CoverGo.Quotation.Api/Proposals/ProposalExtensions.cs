using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Infrastructure.ProductsClient;
using StrawberryShake;

namespace CoverGo.Quotation.Api.Proposals;

[ExtendObjectType<Proposal>]
public static class ProposalExtensions
{
    public static async Task<CensusSummary?> CensusSummary(
        [Parent] Proposal proposal,
        [Service] IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
        [Service] IMemberRepository<ProposalMemberAggregate> proposalMembersRepository,
        [Service] ProductsClient productsClient,
        CancellationToken cancellationToken)
    {
        var proposalMembers =
            (await proposalMembersRepository.FindAllByAsync(it => it.ProposalId == proposal.Id, cancellationToken))
            .ToList();
        if (proposalMembers.Count == 0) return null;

        OfferAggregate acceptedOffer = await offerRepository.GetByIdAsync(proposal.OfferId, cancellationToken);

        IOperationResult<IGetProductPlanDetailsResult> productPlanDetails = await productsClient.GetProductPlanDetails.ExecuteAsync(
            acceptedOffer.ProductVersionId.ProductId.Plan, acceptedOffer.ProductVersionId.ProductId.Type,
            acceptedOffer.ProductVersionId.Version,
            cancellationToken);
        productPlanDetails.EnsureNoErrors();
        IReadOnlyList<IGetProductPlanDetails_ProductProductPlanDetails_PlanDetails>? planDetails = productPlanDetails.Data?.ProductProductPlanDetails?.PlanDetails;
        bool hasPlansBenefits = planDetails?.Any(p => p.Fields?.Count > 0) ?? false;

        string groupingField = hasPlansBenefits ? "class" : "plan";
        var groupedEntries = proposalMembers.GroupBy(member =>
                (groupingField == "class" ? member.Class?.Value : member.PlanId?.Value) ?? string.Empty)
            .Select(group => new GroupedEntry
            {
                GroupKey = groupingField == "class"
                    ? group.Key
                    : planDetails?.FirstOrDefault(p => p.Id == group.Key)?.Name ?? group.Key,
                SubGroupCounts = GetSubGroupCounts(group)
            })
            .ToList();
        IEnumerable<GroupTotal> groupTotals = groupedEntries.Select(entry => new GroupTotal
        {
            GroupKey = entry.GroupKey,
            TotalCount = entry.SubGroupCounts.Sum(count => count.Count)
        });

        return new CensusSummary
        {
            GroupingField = groupingField,
            GroupedEntries = groupedEntries,
            GroupTotals = groupTotals
        };
    }

    private static IEnumerable<SubGroupCount> GetSubGroupCounts(IGrouping<string, ProposalMemberAggregate> group)
    {
        int employeeSubGroup = group.Count(member =>
            member.Fields.FirstOrDefault(f => f.Key == "memberType")?.Value?.ToString() == "employee");
        var otherSubGroups = group.Where(member =>
                member.Fields.FirstOrDefault(f => f.Key == "memberType")?.Value?.ToString() == "dependent")
            .GroupBy(member =>
                member.Fields.FirstOrDefault(f => f.Key == "relationshipToEmployee")?.Value?.ToString() ?? string.Empty)
            .Select(subGroup => new SubGroupCount { MemberSubGroup = subGroup.Key, Count = subGroup.Count() }).ToList();

        return new List<SubGroupCount> { new() { MemberSubGroup = "employee", Count = employeeSubGroup } }
            .Concat(otherSubGroups);
    }
}
