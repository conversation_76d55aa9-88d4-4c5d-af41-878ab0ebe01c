using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.Headcount;

namespace CoverGo.Quotation.Api.CustomFields.ImplementationTypes;

public class InsuredGroupFieldType : ObjectType<CustomFieldDto<InsuredGroup>>
{
    protected override void Configure(IObjectTypeDescriptor<CustomFieldDto<InsuredGroup>> descriptor)
    {
        descriptor.Name("InsuredGroupField");
    }
}
