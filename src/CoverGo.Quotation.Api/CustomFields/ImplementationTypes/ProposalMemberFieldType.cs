using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;

namespace CoverGo.Quotation.Api.CustomFields.ImplementationTypes;

public class ProposalMemberFieldType : ObjectType<CustomFieldDto<ProposalMember>>
{
    protected override void Configure(IObjectTypeDescriptor<CustomFieldDto<ProposalMember>> descriptor)
    {
        descriptor.Name("ProposalMemberField");
    }
}
