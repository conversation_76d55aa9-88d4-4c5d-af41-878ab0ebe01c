using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Proposals.Contracts;

namespace CoverGo.Quotation.Api.CustomFields.ImplementationTypes;

public class ProposalPolicyFieldType : ObjectType<CustomFieldDto<ProposalPolicyDetails>>
{
    protected override void Configure(IObjectTypeDescriptor<CustomFieldDto<ProposalPolicyDetails>> descriptor)
    {
        descriptor.Name("ProposalPolicyField");
    }
}
