using CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;
using CoverGo.Quotation.Api.OfferMembers;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.Queries;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using MediatR;

namespace CoverGo.Quotation.Api.CustomFields.ImplementationTypes;

public class ProposalMemberType : ObjectType<ProposalMember>
{
    protected override void Configure(IObjectTypeDescriptor<ProposalMember> descriptor)
    {
        descriptor.Ignore(it => it.InternalMemberUnderwriting);
        descriptor.Ignore(it => it.HealthQuestionnaireResponse);
        descriptor.Field(it => it.Documents)
            .Description("A list of all documents associated with this proposal member and its corresponding offer member.")
            .Type<ListType<MemberDocumentType>>()
            .Resolve(async (context, cancellationToken) =>
            {
                var mediator = context.Service<IMediator>();
                var proposalMember = context.Parent<ProposalMember>();
                var dataLoader = context.Service<OfferMemberDataLoader>();

                OfferMember? offerMember = null;
                if (proposalMember.OfferMemberId is not null)
                    offerMember = await dataLoader.LoadAsync(new Id(proposalMember.OfferMemberId.Value), cancellationToken);

                return await mediator.Send(new MemberDocumentsQuery(offerMember, proposalMember), cancellationToken);
            });
    }
}
