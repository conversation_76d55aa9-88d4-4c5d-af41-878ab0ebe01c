using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers.Contracts;

namespace CoverGo.Quotation.Api.CustomFields.ImplementationTypes;

public class OfferMemberFieldType : ObjectType<CustomFieldDto<OfferMember>>
{
    protected override void Configure(IObjectTypeDescriptor<CustomFieldDto<OfferMember>> descriptor)
    {
        descriptor.Name("OfferMemberField");
    }
}
