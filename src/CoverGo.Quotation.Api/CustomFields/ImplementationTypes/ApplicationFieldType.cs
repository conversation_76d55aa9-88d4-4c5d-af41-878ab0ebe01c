using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Proposals.Contracts;

namespace CoverGo.Quotation.Api.CustomFields.ImplementationTypes;

public class ApplicationFieldType : ObjectType<CustomFieldDto<ApplicationDetails>>
{
    protected override void Configure(IObjectTypeDescriptor<CustomFieldDto<ApplicationDetails>> descriptor)
    {
        descriptor.Name("ApplicationField");
    }
}
