using CoverGo.Quotation.Domain.Pricing;

namespace CoverGo.Quotation.Api.Pricing;

// TODO: Introduce new types with proper names and drop those.

public class MemberTotalsType : ObjectType<MemberPricingTotals>
{
    protected override void Configure(IObjectTypeDescriptor<MemberPricingTotals> descriptor)
    {
        descriptor.Name("MemberTotals");
    }
}

public class MemberSummaryType : ObjectType<MemberPricingSummary>
{
    protected override void Configure(IObjectTypeDescriptor<MemberPricingSummary> descriptor)
    {
        descriptor.Name("MemberSummary");
    }
}
