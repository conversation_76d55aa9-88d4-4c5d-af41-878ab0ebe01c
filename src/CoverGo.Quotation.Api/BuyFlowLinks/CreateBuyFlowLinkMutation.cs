﻿using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Services.UserContext.HttpContext;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.BuyFlowLinks.Commands;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace CoverGo.Quotation.Api.CreateBuyFlowLinks;

[MutationType]
public static class CreateBuyFlowLinkMutation
{
    [Error(typeof(InputDataValidationError))]
    [UseMutationConvention(PayloadFieldName = "buyFlowLink")]
    [Authorize]
    public static Task<BuyFlowLink> CreateBuyFlowLink(
        CreateBuyFlowLinkCommand input,
        [Service] IMediator mediator,
        [Service] IUserContextProvider userContextProvider,
        CancellationToken cancellationToken)
    {
        input.LoginId = userContextProvider.GetUserId()!;
        return mediator.Send(input, cancellationToken);
    }
}
