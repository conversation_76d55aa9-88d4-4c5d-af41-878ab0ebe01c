﻿using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Quotation.Application.BuyFlowLinks.Commands;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using MediatR;
using Microsoft.AspNetCore.Authorization;

namespace CoverGo.Quotation.Api.CreateBuyFlowLinks;

[MutationType]
public static class UpdateBuyFlowLinkPasswordMutation
{
    [Error(typeof(InputDataValidationError))]
    [UseMutationConvention(PayloadFieldName = "buyFlowLink")]
    [Authorize]
    public static Task<BuyFlowLink> UpdateBuyFlowLinkPassword(
        UpdateBuyFlowLinkPasswordCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken) =>
        mediator.Send(input, cancellationToken);
}
