﻿using CoverGo.Quotation.Application.BuyFlowLinks;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using CoverGo.Quotation.Application.Common;
using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Quotation.Api.BuyFlowLinks;

[QueryType]
public static class BuyFlowLinkQueries
{
    [Authorize]
    public static async Task<BuyFlowLink> BuyFlowLink(
       [Service] IMediator mediator,
       Id buyFlowLinkId,
       string password,
       CancellationToken cancellationToken = default
   ) => await mediator.Send(new BuyFlowLinkValidationQuery(buyFlowLinkId, password), cancellationToken);
}
