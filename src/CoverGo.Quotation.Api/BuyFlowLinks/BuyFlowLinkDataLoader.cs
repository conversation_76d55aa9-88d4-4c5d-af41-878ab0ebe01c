﻿using CoverGo.Quotation.Application.BuyFlowLinks;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using CoverGo.Quotation.Application.Common;
using MediatR;

namespace CoverGo.Quotation.Api.Opportunities;

public class BuyFlowLinkFromOpportunityIdDataLoader
    : BatchDataLoader<Id, BuyFlowLink>
{
    readonly IMediator _mediator;

    public BuyFlowLinkFromOpportunityIdDataLoader(
        [Service] IMediator mediator,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _mediator = mediator;
    }

    protected override async Task<IReadOnlyDictionary<Id, BuyFlowLink>> LoadBatchAsync(IReadOnlyList<Id> keys, CancellationToken cancellationToken)
    {
        List<BuyFlowLink> results = await _mediator.Send(new BuyFlowLinksByOpportunityIdsQuery(keys), cancellationToken);
        return results.ToDictionary(x => x.OpportunityId, x => x);
    }
}
