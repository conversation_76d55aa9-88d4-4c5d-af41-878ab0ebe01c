using HotChocolate.Data.Filters;
using HotChocolate.Data.MongoDb.Filters;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.MongoDb;

public class MongoDbFilterProviderExtension : FilterProviderExtensions<MongoDbFilterVisitorContext>
{
    public MongoDbFilterProviderExtension()
    {
    }

    public MongoDbFilterProviderExtension(
        Action<IFilterProviderDescriptor<MongoDbFilterVisitorContext>> configure)
        : base(configure)
    {
    }
}
