using CoverGo.Quotation.Api.Configuration.GraphQl.Queries;
using CoverGo.Quotation.Domain.Underwriting;

using HotChocolate.Configuration;
using HotChocolate.Data.Filters;
using HotChocolate.Data.MongoDb;
using HotChocolate.Data.MongoDb.Filters;
using HotChocolate.Language;

using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.MongoDb;

public class UnderwritingCaseSearchMongoDbFilterDefinition(IBsonSerializer<UnderwritingCase> serializer, string search) : MongoDbFilterDefinition
{
    public override BsonDocument Render(IBsonSerializer documentSerializer, IBsonSerializerRegistry serializerRegistry)
    {
        var definition = FilterBySearch(search);
        return definition.Render(serializer, serializerRegistry);;
    }

    private FilterDefinition<UnderwritingCase> FilterBySearch(string search)
    {
        return Builders<UnderwritingCase>.Filter.Or(
            Builders<UnderwritingCase>.Filter.Regex(
                x => x.<PERSON>,
                new BsonRegularExpression(search, "i")
            ),
            Builders<UnderwritingCase>.Filter.Regex(
                x => x.Agent!.FirstName,
                new BsonRegularExpression(search, "i")
            ),
            Builders<UnderwritingCase>.Filter.Regex(
                x => x.Agent!.LastName,
                new BsonRegularExpression(search, "i")
            ),
            Builders<UnderwritingCase>.Filter.Regex(
                x => x.Client!.Name,
                new BsonRegularExpression(search, "i")
            ),
            Builders<UnderwritingCase>.Filter.Regex(
                "proposalNumber",
                new BsonRegularExpression(search, "i")
            ),
            Builders<UnderwritingCase>.Filter.Regex(
                "policy.policyNumber",
                new BsonRegularExpression(search, "i")
            ));
    }
}

public class MongoDbSearchOperationHandler : MongoDbOperationHandlerBase
{
    // TODO: DI for search of SearchDefinition for entity tyoe. Do it when there will be smth other than UnderwritingCase
    public MongoDbSearchOperationHandler(
        InputParser inputParser) : base(inputParser)
    {
    }

    // This is used to match the handler to all `search` fields
    protected int Operation => CustomFilterOperations.Search;

    public override bool CanHandle(ITypeCompletionContext context, IFilterInputTypeDefinition typeDefinition, IFilterFieldDefinition fieldDefinition)
    {
        return
            fieldDefinition is FilterOperationFieldDefinition operationField &&
            operationField.Id == Operation;
    }

    public override MongoDbFilterDefinition HandleOperation(MongoDbFilterVisitorContext context, IFilterOperationField field, IValueNode value, object? parsedValue)
    {
        var path = context.GetMongoFilterScope().GetPath();
        if (parsedValue is string searchStr && !string.IsNullOrWhiteSpace(searchStr))
        {
            if (field.DeclaringType.EntityType.Type == typeof(UnderwritingCase))
            {
                return new MongoDbFilterOperation(path, new UnderwritingCaseSearchMongoDbFilterDefinition(BsonSerializer.LookupSerializer<UnderwritingCase>(), searchStr));
            }
        }

        return new MongoDbFilterOperation(path, null);
    }
}
