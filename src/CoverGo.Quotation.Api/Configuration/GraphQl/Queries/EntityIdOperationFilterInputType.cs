using HotChocolate.Data.Filters;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.Queries;

/// <summary>
/// This differs from HC one since it doesn't enforce the usage of Global Object Identification
/// </summary>
public class EntityIdOperationFilterInputType : IdOperationFilterInputType
{
    public EntityIdOperationFilterInputType()
    {
    }

    public EntityIdOperationFilterInputType(Action<IFilterInputTypeDescriptor> configure)
        : base(configure)
    {
    }

    protected override void Configure(IFilterInputTypeDescriptor descriptor)
    {
        descriptor.Operation(DefaultFilterOperations.Equals).Type<IdType>();
        descriptor.Operation(DefaultFilterOperations.NotEquals).Type<IdType>();
        descriptor.Operation(DefaultFilterOperations.In).Type<ListType<IdType>>();
        descriptor.Operation(DefaultFilterOperations.NotIn).Type<ListType<IdType>>();
        descriptor.AllowAnd(allow: false).AllowOr(allow: false);
    }
}
