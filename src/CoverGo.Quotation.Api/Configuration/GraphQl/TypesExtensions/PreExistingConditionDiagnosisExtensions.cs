using CoverGo.Quotation.Api.Underwriting.DataLoaders;
using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

[ExtendObjectType(typeof(PreExistingConditionDiagnosis))]
public static class PreExistingConditionDiagnosisExtensions
{
    public static async Task<DiagnosisDetails> Details(
        [Parent] PreExistingConditionDiagnosis diagnosis,
        [Service] PreExistingConditionDiagnosisLoader dataLoader,
        CancellationToken cancellationToken = default) => await dataLoader.LoadAsync(diagnosis.Id.Value, cancellationToken);
}
