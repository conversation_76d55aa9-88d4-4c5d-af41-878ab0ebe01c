using CoverGo.Quotation.Api.ReferenceData;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

[ExtendObjectType(typeof(ExclusionReferenceData))]
public static class ExclusionReferenceDataExtensions
{
    public static async Task<string> DisplayValue(
        [Parent] ExclusionReferenceData referenceData,
        [Service] ReferenceDataLoader<GetExclusionReferenceDataNamesQuery> dataLoader,
        string localeId = "en-US",
        CancellationToken cancellationToken = default) => await dataLoader.LoadAsync((localeId!, referenceData.Mnemonic), cancellationToken);
}
