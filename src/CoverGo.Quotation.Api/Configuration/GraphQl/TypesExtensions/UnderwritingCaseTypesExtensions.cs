using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class AddUnderwritingCaseMemberDocumentsEntityInputType : InputObjectType<AddUnderwritingCaseMemberDocumentsEntityInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<AddUnderwritingCaseMemberDocumentsEntityInput> descriptor) =>
        descriptor.OneOf();
}
