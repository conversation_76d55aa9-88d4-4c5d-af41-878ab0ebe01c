using CoverGo.Quotation.Application.ProposalMembers.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public sealed class RejectReasonBaseType : InterfaceType<RejectReasonBase>;

public sealed class OtherRejectReasonType : ObjectType<OtherRejectReason>
{
    protected override void Configure(IObjectTypeDescriptor<OtherRejectReason> descriptor) =>
        descriptor.Implements<RejectReasonBaseType>();
}

public sealed class RejectReasonType : ObjectType<RejectReason>
{
    protected override void Configure(IObjectTypeDescriptor<RejectReason> descriptor) =>
        descriptor.Implements<RejectReasonBaseType>();
}

public sealed class RejectReasonBaseInputType : InputObjectType<RejectReasonBaseInput>
{
    protected override void Configure(IInputObjectTypeDescriptor<RejectReasonBaseInput> descriptor) =>
        descriptor.OneOf();
}
