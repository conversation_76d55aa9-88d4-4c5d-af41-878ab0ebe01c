using CoverGo.Quotation.Application.Members.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class MemberLoadingInputType : InputObjectType<MemberLoadingInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<MemberLoadingInput> descriptor)
    {
        // TODO: descriptor.OneOf() can be revised after <PERSON><PERSON><PERSON> is updated to Hot Chocolate 13+
    }
}

public class MemberLoadingUpdateInputType : InputObjectType<MemberLoadingUpdateInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<MemberLoadingUpdateInput> descriptor)
    {
        // TODO: descriptor.OneOf() can be revised after <PERSON><PERSON><PERSON> is updated to Hot Chocolate 13+
    }
}
