using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class ProductVersionIdType : ObjectType<ProductVersionId>
{
    protected override void Configure(IObjectTypeDescriptor<ProductVersionId> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(f => f.ProductId);
        descriptor.Field(f => f.Version);
    }
}
