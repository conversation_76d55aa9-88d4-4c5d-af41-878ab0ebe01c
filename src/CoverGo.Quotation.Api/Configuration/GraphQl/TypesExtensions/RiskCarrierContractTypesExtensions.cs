using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public sealed class QuotationRiskCarrierContractInwardCommissionType : UnionType<QuotationRiskCarrierContractInwardCommission>
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<QuotationRiskCarrierContractInwardCommissionPercentageType>();
    }
}

public sealed class QuotationRiskCarrierContractInwardCommissionPercentageType : ObjectType<QuotationRiskCarrierContractInwardCommissionPercentage>
{
}
