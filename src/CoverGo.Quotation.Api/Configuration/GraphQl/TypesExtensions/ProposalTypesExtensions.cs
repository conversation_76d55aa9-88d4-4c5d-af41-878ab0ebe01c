using CoverGo.Quotation.Application.Loadings.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public sealed class ProposalLoadingType : InterfaceType<ProposalLoading>;

public sealed class ProposalPercentageLoadingType : ObjectType<ProposalScaleFactorLoading>
{
    protected override void Configure(IObjectTypeDescriptor<ProposalScaleFactorLoading> descriptor) => descriptor.Implements<ProposalLoadingType>();
}

public sealed class ProposalFixedAmountLoadingType : ObjectType<ProposalFixedAmountLoading>
{
    protected override void Configure(IObjectTypeDescriptor<ProposalFixedAmountLoading> descriptor) => descriptor.Implements<ProposalLoadingType>();
}
