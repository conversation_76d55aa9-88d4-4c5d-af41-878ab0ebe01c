using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.OfferMembers.Contracts;

using MemberDocument = CoverGo.Quotation.Domain.Members.MemberDocument;
using MediatR;
using CoverGo.Quotation.Application.OfferMembers.Queries;
using CoverGo.Quotation.Api.ProposalMembers;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public sealed class MemberLoadingType : InterfaceType<MemberLoading>
{
    protected override void Configure(
        IInterfaceTypeDescriptor<MemberLoading> descriptor) => descriptor.Name("MemberLoading");
}

public sealed class MemberPercentageLoadingType : ObjectType<MemberScaleFactorLoading>
{
    protected override void Configure(
        IObjectTypeDescriptor<MemberScaleFactorLoading> descriptor)
    {
        descriptor.Name("MemberScaleFactorLoading");
        descriptor.Implements<MemberLoadingType>();
    }
}

public sealed class MemberFixedAmountLoadingType : ObjectType<MemberFixedAmountLoading>
{
    protected override void Configure(
               IObjectTypeDescriptor<MemberFixedAmountLoading> descriptor)
    {
        descriptor.Name("MemberFixedAmountLoading");
        descriptor.Implements<MemberLoadingType>();
    }
}

public sealed class MemberDocumentType: ObjectType<MemberDocument>
{
}

public sealed class OfferMemberType : ObjectType<OfferMember>
{
    protected override void Configure(IObjectTypeDescriptor<OfferMember> descriptor)
    {
        descriptor.Field(it => it.Documents)
            .Description("A list of all documents associated with this offer member and its corresponding proposal member.")
            .Type<ListType<MemberDocumentType>>()
            .Resolve(async (context, cancellationToken) => {
                // Get parent OfferMember and its document metadata
                var mediator = context.Service<IMediator>();
                var offerMember = context.Parent<OfferMember>();
                var dataLoader = context.Service<ProposalMemberByOfferMemberIdDataLoader>();

                ProposalMember? proposalMember = await dataLoader.LoadAsync(offerMember.Id, cancellationToken);

                return await mediator.Send(new MemberDocumentsQuery(offerMember, proposalMember),
                                     cancellationToken);
            });
    }
}
