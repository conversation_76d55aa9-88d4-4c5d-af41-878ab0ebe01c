using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public sealed class MemberExclusionInputType : InputObjectType<MemberExclusionInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<MemberExclusionInput> descriptor)
    {
        // TODO: descriptor.OneOf() can be revised after <PERSON><PERSON><PERSON> is updated to Hot Chocolate 13+
    }
}

public sealed class MemberExclusionType : InterfaceType<MemberExclusion>
{
    protected override void Configure(
        IInterfaceTypeDescriptor<MemberExclusion> descriptor)
    {
        descriptor.Name("MemberExclusion");
    }
}

public sealed class ReferenceDataExclusionType : ObjectType<ReferenceDataExclusion>
{
    protected override void Configure(
        IObjectTypeDescriptor<ReferenceDataExclusion> descriptor)
    {
        descriptor.Name("ReferenceDataMemberExclusion");
    }
}

public sealed class CustomExclusionType : ObjectType<CustomExclusion>
{
    protected override void Configure(
        IObjectTypeDescriptor<CustomExclusion> descriptor)
    {
        descriptor.Name("CustomMemberExclusion");
    }
}
