using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public sealed class PreExistingConditionInputType : InputObjectType<PreExistingConditionInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<PreExistingConditionInput> descriptor)
    {
        // TODO: descriptor.OneOf() can be revised after <PERSON><PERSON><PERSON> is updated to Hot Chocolate 13+
    }
}

public sealed class PreExistingConditionInputBaseType : InterfaceType<PreExistingCondition>
{
    protected override void Configure(
        IInterfaceTypeDescriptor<PreExistingCondition> descriptor)
    {
        descriptor.Name("PreExistingCondition");
    }
}

public sealed class ReferenceDataConditionInputType : ObjectType<ReferenceDataCondition>
{
    protected override void Configure(
        IObjectTypeDescriptor<ReferenceDataCondition> descriptor)
    {
        descriptor.Name("ReferenceDataCondition");
    }
}

public sealed class CustomConditionInputType : ObjectType<CustomCondition>
{
    protected override void Configure(
        IObjectTypeDescriptor<CustomCondition> descriptor)
    {
        descriptor.Name("CustomCondition");
    }
}

public sealed class DiagnosisConditionInputType : ObjectType<DiagnosisCondition>
{
    protected override void Configure(
        IObjectTypeDescriptor<DiagnosisCondition> descriptor)
    {
        descriptor.Name("DiagnosisCondition");
    }
}
