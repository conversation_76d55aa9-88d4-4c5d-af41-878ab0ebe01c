using CoverGo.Quotation.Api.Underwriting.DataLoaders;
using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

[ExtendObjectType(typeof(PreExistingConditionReferenceData))]
public static class PreExistingConditionReferenceDataExtensions
{
    public static async Task<string> DisplayValue(
        [Parent] PreExistingConditionReferenceData referenceData,
        [Service] PreExistingConditionReferenceDataLoader dataLoader,
        string localeId = "en-US",
        CancellationToken cancellationToken = default) => await dataLoader.LoadAsync((localeId!, referenceData.Mnemonic), cancellationToken);
}
