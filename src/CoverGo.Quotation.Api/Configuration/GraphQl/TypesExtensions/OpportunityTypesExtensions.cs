using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Api.Opportunities;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Opportunities;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Queries;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using GraphQL.Client.Abstractions.Utilities;
using HotChocolate.Resolvers;
using MediatR;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class OpportunityType : ObjectType<Opportunity>
{
    protected override void Configure(IObjectTypeDescriptor<Opportunity> descriptor)
    {
        descriptor
            .Field(f => f.CloseReason)
            .Type<OpportunityCloseReasonType>();

        descriptor
           .Field("buyFlowLink")
           .Type<BuyFlowLinkType>()
           .Resolve(async (ctx, cancellationToken) =>
           {
               var dataLoader = ctx.Service<BuyFlowLinkFromOpportunityIdDataLoader>();
               var parent = ctx.Parent<Opportunity>();
               return await dataLoader.LoadAsync(parent.Id.Value, cancellationToken);
           });
    }
}

public class RenewalOpportunityType : ObjectType<RenewalOpportunity>
{
    protected override void Configure(IObjectTypeDescriptor<RenewalOpportunity> descriptor)
    {
        descriptor.ExtendsType<OpportunityType>();
        descriptor.BindFieldsExplicitly();
        descriptor.Field(x => x.Id);
        descriptor.Field(x => x.ClientId).Type<IdType>().Resolve(ctx => Resolve<Id>(ctx, x => x.ClientId));
        descriptor.Field(x => x.SalesChannelId).Type<NonNullType<IdType>>().Resolve(ctx => Resolve<Id>(ctx, x => x.SalesChannelId));
        descriptor.Field(x => x.PrimaryAgentId).Type<NonNullType<IdType>>().Resolve(ctx => Resolve<Id>(ctx, x => x.PrimaryAgentId));
        descriptor.Field(x => x.SecondaryAgentId).Type<IdType>().Resolve(ctx => Resolve<Id>(ctx, x => x.SecondaryAgentId));
        descriptor.Field(x => x.ProductVersionId);
        descriptor
            .Field(nameof(Opportunity.AuditInfo).ToCamelCase())
            .Type(typeof(AuditInfo))
            .Resolve(ctx => Resolve<AuditInfo>(ctx, x => x.EntityAuditInfo));
        descriptor.Field(x => x.Status);
        descriptor
            .Field(f => f.CloseReason)
            .Type<OpportunityCloseReasonType>();
        descriptor.Field(x => x.BuyFlowType);
        descriptor.Field(x => x.LegacyCaseId).Type<IdType>().Resolve(ctx => Resolve<Id>(ctx, x => x.LegacyCaseId));
        descriptor
            .Field(x => x.Offers)
            .Type(typeof(List<Offer>))
            .Resolve(async (ctx, cancellationToken) => {
                var parent = ctx.Parent<RenewalOpportunity>();
                if (parent.Offers == null || !parent.Offers.Any())
                {
                    var mediator = ctx.Service<IMediator>();
                    return await mediator.Send(new OffersByOpportunityIdQuery(parent.Id), cancellationToken);
                }
                var mapper = ctx.Service<IMapper>();
                return mapper.Map<List<Offer>>(parent.Offers);
            });
        
        descriptor
            .Field(x => x.Proposal)
            .Type(typeof(Proposal))
            .Resolve(async (ctx, cancellationToken) => {
                var parent = ctx.Parent<RenewalOpportunity>();
                if (parent.Proposal is null)
                {
                    var mediator = ctx.Service<IMediator>();
                    return await mediator.Send(new ProposalByOpportunityIdQuery(parent.Id), cancellationToken);
                }
                var mapper = ctx.Service<IMapper>();
                return mapper.Map<Proposal>(parent.Proposal);
            });
    }

    private static T Resolve<T>(IResolverContext context, Func<RenewalOpportunity, object?> selector)
    {
        var parent = context.Parent<RenewalOpportunity>();
        var mapper = context.Service<IMapper>();
        return mapper.Map<T>(selector(parent));
    }
}

public class OpportunityCloseReasonType : UnionType
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<OpportunityAutoCloseReasonType>();
    }
}

public sealed class OpportunityAutoCloseReasonType : ObjectType<OpportunityAutoCloseReason>
{
}
