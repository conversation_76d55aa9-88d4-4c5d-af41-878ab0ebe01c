using AutoMapper;
using HotChocolate.Resolvers;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public static class Helpers
{
    public static TContract Map<TObject, TContract>(this IResolverContext resolverContext, Func<TObject, object?> fieldSelector)
    {
        var parent = resolverContext.Parent<TObject>();
        var mapper = resolverContext.Service<IMapper>();
        return mapper.Map<TContract>(fieldSelector(parent));
    }
}