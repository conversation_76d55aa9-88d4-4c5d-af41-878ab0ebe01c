using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Pricing;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class TaxFactorType : ObjectType<TaxFactor>
{
    protected override void Configure(IObjectTypeDescriptor<TaxFactor> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(x => x.Factor).Type<DecimalType>();
    }
}

public class TaxAmountType : ObjectType<TaxAmount>
{
    protected override void Configure(IObjectTypeDescriptor<TaxAmount> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(x => x.Amount);
    }
}

public class TaxValueType : UnionType<TaxValue>
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<TaxFactorType>();
        descriptor.Type<TaxAmountType>();
    }
}

public class TaxValueInputType : InputObjectType<TaxValueInput>
{
    protected override void Configure(IInputObjectTypeDescriptor<TaxValueInput> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.OneOf();
        descriptor.Field(t => t.Factor).Type<DecimalType>();
        descriptor.Field(t => t.Amount).Type<DecimalType>();
    }
}
