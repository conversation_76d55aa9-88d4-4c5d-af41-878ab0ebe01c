using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Pricing.Contracts;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class ClientInputType : InputObjectType<ClientInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<ClientInput> descriptor) =>
        descriptor.OneOf();
}

public sealed class ClientType : InterfaceType<Client>
{
}

public sealed class IndividualClientType : ObjectType<IndividualClient>
{
    protected override void Configure(IObjectTypeDescriptor<IndividualClient> descriptor) =>
        descriptor.Implements<ClientType>();
}

public sealed class GroupClientType : ObjectType<GroupClient>
{
    protected override void Configure(IObjectTypeDescriptor<GroupClient> descriptor) =>
        descriptor.Implements<ClientType>();
}

public sealed class PricingFactorType : InterfaceType<PricingFactor>
{
    protected override void Configure(
    IInterfaceTypeDescriptor<PricingFactor> descriptor)
    {
        descriptor.Name("PricingFactor");
    }
}

public sealed class BenefitClassType : InterfaceType<IBenefitClass>
{
    protected override void Configure(
   IInterfaceTypeDescriptor<IBenefitClass> descriptor)
    {
        descriptor.Name("BenefitClass");
    }
}

public sealed class OfferClassType : ObjectType<OfferClass>
{
    protected override void Configure(IObjectTypeDescriptor<OfferClass> descriptor)
    {
        descriptor.Implements<BenefitClassType>();
    }
}

public sealed class ProposalClassType : ObjectType<ProposalClass>
{
    protected override void Configure(IObjectTypeDescriptor<ProposalClass> descriptor)
    {
        descriptor.Implements<BenefitClassType>();
    }
}

public sealed class PercentagePricingFactorType : ObjectType<PercentagePricingFactor>
{
    protected override void Configure(
               IObjectTypeDescriptor<PercentagePricingFactor> descriptor)
    {
        descriptor.Name("PercentagePricingFactor");
        descriptor.Implements<PricingFactorType>();
    }
}

public sealed class FixedAmountPricingFactorType : ObjectType<FixedAmountPricingFactor>
{
    protected override void Configure(
                      IObjectTypeDescriptor<FixedAmountPricingFactor> descriptor)
    {
        descriptor.Name("FixedAmountPricingFactor");
        descriptor.Implements<PricingFactorType>();
    }
}

public sealed class OfferVersionType : ObjectType<Domain.Offers.OfferVersion>
{
    protected override void Configure(IObjectTypeDescriptor<Domain.Offers.OfferVersion> descriptor)
    {
        descriptor.Field(x => x.Increase(null)).Ignore();
    }
}
