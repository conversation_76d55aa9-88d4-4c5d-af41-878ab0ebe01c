﻿using CoverGo.Quotation.Api.Offers;
using CoverGo.Quotation.Api.Opportunities;
using CoverGo.Quotation.Api.Proposals;
using CoverGo.Quotation.Application.BuyFlowLinks.Commands;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Api.Configuration.GraphQl.TypesExtensions;

public class CreateBuyFlowLinkCommandType : InputObjectType<CreateBuyFlowLinkCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<CreateBuyFlowLinkCommand> descriptor)
    {
        descriptor
            .Name("CreateBuyFlowLinkInput")
            .Ignore(b => b.<PERSON>ginId);
    }
}

public class BuyFlowLinkType : ObjectType<BuyFlowLink>
{
    protected override void Configure(IObjectTypeDescriptor<BuyFlowLink> descriptor)
    {
        descriptor
            .Field("opportunity")
            .Type<OpportunityType>()
            .Resolve(async (ctx, cancellationToken) =>
            {
                var dataLoader = ctx.Service<OpportunityDataLoader>();
                var parent = ctx.Parent<BuyFlowLink>();
                return await dataLoader.LoadAsync(new ValueObjectId<OpportunityAggregate>(parent.OpportunityId), cancellationToken);
            });
        descriptor
            .Field("offer")
            .Type(typeof(Offer))
            .Resolve(async (ctx, cancellationToken) => {
                var parent = ctx.Parent<BuyFlowLink>();
                if (parent.OfferId is not null)
                {
                    var dataLoader = ctx.Service<OfferDataLoader>();
                    return await dataLoader.LoadAsync(new ValueObjectId<OfferAggregate>(parent.OfferId), cancellationToken);
                }

                return null;
            });
        descriptor
            .Field("proposal")
            .Type(typeof(Proposal))
            .Resolve(async (ctx, cancellationToken) => {
                var parent = ctx.Parent<BuyFlowLink>();
                if (parent.ProposalId is not null)
                {
                    var dataLoader = ctx.Service<ProposalDataLoader>();
                    return await dataLoader.LoadAsync(new ValueObjectId<ProposalAggregate>(parent.ProposalId), cancellationToken);
                }

                return null;
            });
    }
}
