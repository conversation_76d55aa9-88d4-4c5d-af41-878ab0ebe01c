{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"Local": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "graphql", "applicationUrl": "http://localhost:60001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "BuyFlowLinkAesEncryptionServiceKey": "qcSRBvH6s0pSZnU4tJxzFnjI5Pz2qdM1", "BuyFlowLinkAesEncryptionServiceIV": "AAAAAAAAAAAAAAAAAAAAAA=="}}, "Local with api-gateway": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "graphql", "applicationUrl": "http://localhost:60001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "GraphQLStitching__Redis__Publish": "true", "BuyFlowLinkAesEncryptionServiceKey": "qcSRBvH6s0pSZnU4tJxzFnjI5Pz2qdM1", "BuyFlowLinkAesEncryptionServiceIV": "AAAAAAAAAAAAAAAAAAAAAA=="}}}}