using AutoMapper;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Pricing.Contracts;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Quotes;
using HotChocolate.Resolvers;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteType : ObjectType<Quote>
{
    protected override void Configure(IObjectTypeDescriptor<Quote> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.DistributorId).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.ProductVersionId);
        descriptor.Field(it => it.UnderwritingCaseId).Type<IdType>();
        descriptor.Field(it => it.IsFinalized);
        descriptor
            .Field(it => it.BillingInformation)
            .Type(typeof(BillingInfo))
            .Resolve(context => Map<BillingInfo>(context, quote => quote.BillingInformation));
        descriptor.Field(it => it.PrimaryAgentId).Type<IdType>();
        descriptor.Field(it => it.SecondaryAgentId).Type<IdType>();
        descriptor
            .Field(it => it.ApplicationDetails)
            .Type(typeof(ApplicationDetails))
            .Resolve(context => Map<ApplicationDetails>(context, quote => quote.ApplicationDetails));
        descriptor
            .Field(it => it.Loadings)
            .Type(typeof(IEnumerable<ProposalLoading>))
            .Resolve(context => Map<IEnumerable<ProposalLoading>>(context, quote => quote.Loadings));
        descriptor
            .Field(it => it.Tax)
            .Type(typeof(ProposalTax))
            .Resolve(context => Map<ProposalTax>(context, quote => quote.Tax));
        descriptor.Field(it => it.Pricing);
        descriptor
            .Field(it => it.BillingPlan)
            .Type(typeof(BillingPlan))
            .Resolve(context => Map<BillingPlan>(context, quote => quote.BillingPlan));
    }

    private TContract Map<TContract>(IResolverContext resolverContext, Func<Quote, object?> fieldSelector)
    {
        var parent = resolverContext.Parent<Quote>();
        var mapper = resolverContext.Service<IMapper>();
        return mapper.Map<TContract>(fieldSelector(parent));
    }
}
