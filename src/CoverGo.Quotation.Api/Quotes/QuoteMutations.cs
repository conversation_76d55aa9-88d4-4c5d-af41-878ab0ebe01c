using CoverGo.Quotation.Api.Offers.Errors;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Quotes;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Proposals.Exceptions;
using CoverGo.Quotation.Domain.Quotes;
using HotChocolate.Authorization;

using MediatR;

namespace CoverGo.Quotation.Api.Quotes;

[MutationType]
public static class QuoteMutations
{
    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<QuoteMemberStateInvalidDatesException>]
    [Error<QuoteMembersStateInvalidStartDateException>]
    [Error<QuoteMembersStateInvalidEndDateException>]
    [Error<QuoteMemberStatesDateOverlapException>]
    [Error<QuoteMemberStatesDateNotAscOrderException>]
    [Error<QuoteProductCantHaveMultiplePrimariesException>]
    [Error<MemberDependentOfCycleException>]
    [Error<MemberNotPrimaryException>]
    [Error<MemberHasDependentsException>]
    [Error(typeof(InputValidationError))]
    [Error(typeof(OfferPolicyDetailsInvalidDatesError))]
    [Error<CountryReferenceDataNotFoundException>]
    [Error<InvalidTaxRateException>]
    [Error<InvalidFieldsException>]
    public static async Task<CreateQuotePayload> CreateQuote(
        CreateQuoteCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<QuoteMemberStateInvalidDatesException>]
    [Error<QuoteMembersStateInvalidStartDateException>]
    [Error<QuoteMembersStateInvalidEndDateException>]
    [Error<QuoteMemberStatesDateOverlapException>]
    [Error<QuoteMemberStatesDateNotAscOrderException>]
    [Error<MemberDependentOfCycleException>]
    [Error<MemberNotPrimaryException>]
    [Error<MemberHasDependentsException>]
    [Error<QuoteFinalizedException>]
    [Error<InvalidFieldsException>]
    [Error<QuoteProductCantHaveMultiplePrimariesException>]
    public static async Task<QuoteMember> AddQuoteMember(
        AddQuoteMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<QuoteMemberStateInvalidDatesException>]
    [Error<QuoteMemberStatesDateOverlapException>]
    [Error<QuoteMemberStatesDateNotAscOrderException>]
    [Error<MemberDependentOfCycleException>]
    [Error<MemberNotPrimaryException>]
    [Error<MemberHasDependentsException>]
    [Error<QuoteFinalizedException>]
    [Error<InvalidFieldsException>]
    [Error<QuoteProductCantHaveMultiplePrimariesException>]
    public static async Task<QuoteMember> UpdateQuoteMember(
        UpdateQuoteMemberCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<QuoteFinalizedException>]
    [Error<QuoteProductCantHaveMultiplePrimariesException>]
    [UseMutationConvention(PayloadFieldName = "deletedQuoteMembers")]
    public static async Task<List<QuoteMember>> DeleteQuoteMembers(
        DeleteQuoteMembersCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<QuoteFinalizedException>]
    [Error<QuoteUnderwritingAlreadyRequestedException>]
    public static async Task<Quote> RequestUnderwritingForQuote(
        RequestUnderwritingForQuoteCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }

    [Authorize]
    [Error<EntityNotFoundException>]
    [Error<QuoteFinalizedException>]
    public static async Task<Quote> FinalizeQuote(
        FinalizeQuoteCommand input,
        [Service] IMediator mediator,
        CancellationToken cancellationToken)
    {
        return await mediator.Send(input, cancellationToken);
    }
}
