using CoverGo.Quotation.Domain.Quotes;

using HotChocolate.Data.Sorting;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteMemberSortType : SortInputType<QuoteMember>
{
    protected override void Configure(
        ISortInputTypeDescriptor<QuoteMember> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(y => y.Id);
        descriptor.Field(y => y.MemberId);
    }
}
