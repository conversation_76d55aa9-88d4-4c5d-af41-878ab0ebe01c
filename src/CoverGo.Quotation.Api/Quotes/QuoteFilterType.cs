using CoverGo.Quotation.Api.Configuration.GraphQl.Queries;
using CoverGo.Quotation.Domain.Quotes;

using HotChocolate.Data.Filters;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteFilterType : FilterInputType<Quote>
{
    protected override void Configure(
        IFilterInputTypeDescriptor<Quote> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(y => y.Id).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(y => y.DistributorId).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(y => y.ProductVersionId);
    }
}
