using CoverGo.Quotation.Api.Configuration.GraphQl.Queries;
using CoverGo.Quotation.Domain.Quotes;

using HotChocolate.Data.Filters;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteMemberFilterType : FilterInputType<QuoteMember>
{
    protected override void Configure(
        IFilterInputTypeDescriptor<QuoteMember> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(y => y.Id).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(y => y.QuoteId).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(y => y.IndividualId).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(y => y.MemberId).Type<EntityIdOperationFilterInputType>();
        descriptor.Field(y => y.HealthQuestionnaireResponseId).Type<EntityIdOperationFilterInputType>();

        descriptor.Field(y => y.MemberUnderwriting).Type<MemberUnderwritingFilterType>();
        descriptor.Field(y => y.NeedsUnderwriting);
    }
}
