using CoverGo.Quotation.Domain.Quotes;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteDataLoader : BatchDataLoader<string, Quote>
{
    private readonly IRepository<Quote, string> _quoteRepository;

    public QuoteDataLoader(
        IRepository<Quote, string> quoteRepository,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _quoteRepository = quoteRepository;
    }

    protected override async Task<IReadOnlyDictionary<string, Quote>> LoadBatchAsync(IReadOnlyList<string> keys, CancellationToken cancellationToken)
    {
        var results = await _quoteRepository.FindAllAsync(keys.ToList(), cancellationToken);
        return results.ToDictionary(x => x.Id, x => x);
    }
}
