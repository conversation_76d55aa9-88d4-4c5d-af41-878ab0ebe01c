using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Quotes;

public sealed class MemberBenefitUnderwritingDecisionInputType : InputObjectType<MemberBenefitUnderwritingDecisionInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<MemberBenefitUnderwritingDecisionInput> descriptor)
    {
        // TODO: descriptor.OneOf() can be revised after Policies is updated to Hot Chocolate 13+
    }
}
