using HotChocolate.Data.Filters;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Api.Quotes
{
    public class MemberUnderwritingFilterType : FilterInputType<MemberUnderwriting>
    {
        protected override void Configure(IFilterInputTypeDescriptor<MemberUnderwriting> descriptor)
        {
            descriptor.BindFieldsExplicitly();
            descriptor.Field(x => x.Status).Type<EnumOperationFilterInputType<MemberUnderwritingStatus>>();
        }
    }
}
