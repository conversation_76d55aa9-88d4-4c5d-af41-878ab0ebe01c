using AutoMapper;
using CoverGo.Quotation.Api.CustomFields.ImplementationTypes;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteMemberStateType : ObjectType<QuoteMemberState>
{
    protected override void Configure(IObjectTypeDescriptor<QuoteMemberState> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.PlanId).Type<IdType>();
        descriptor.Field(it => it.Class);
        descriptor.Field(it => it.StartDate);
        descriptor.Field(it => it.EndDate);
        descriptor.Field(it => it.Dependency);
        descriptor
            .Field("fields")
            .Resolve(descriptor =>
            {
                var parent = descriptor.Parent<QuoteMemberState>();
                var mapper = descriptor.Service<IMapper>();
                var result = mapper.Map<List<CustomFieldDto<QuoteMemberState>>>(parent.Fields.Select(it => it).ToList());
                return result;
            })
            .Type<NonNullType<ListType<NonNullType<QuoteMemberStateFieldType>>>>();
    }
}

public class QuoteMemberStateInputType : InputObjectType<QuoteMemberState>
{
    protected override void Configure(IInputObjectTypeDescriptor<QuoteMemberState> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.PlanId).Type<IdType>();
        descriptor.Field(it => it.Class);
        descriptor.Field(it => it.StartDate);
        descriptor.Field(it => it.EndDate);
        descriptor.Field(it => it.Dependency);
        descriptor
            .Field(it => it.Fields)
            .Type<NonNullType<ListType<NonNullType<FieldInputType>>>>();
    }
}
