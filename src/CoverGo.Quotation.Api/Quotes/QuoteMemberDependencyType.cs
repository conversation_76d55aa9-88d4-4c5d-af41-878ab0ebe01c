using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteMemberDependencyType : ObjectType<QuoteMemberDependency>
{
    protected override void Configure(IObjectTypeDescriptor<QuoteMemberDependency> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.RelationshipToPrimary);
        descriptor.Field(it => it.DependentOfId).Type<NonNullType<IdType>>();
    }
}

public class QuoteMemberDependencyInputType : InputObjectType<QuoteMemberDependency>
{
    protected override void Configure(IInputObjectTypeDescriptor<QuoteMemberDependency> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.RelationshipToPrimary);
        descriptor.Field(it => it.DependentOfId).Type<NonNullType<IdType>>();
    }
}
