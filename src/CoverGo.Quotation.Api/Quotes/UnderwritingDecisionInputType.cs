using CoverGo.Quotation.Application.Underwriting.Contracts;

namespace CoverGo.Quotation.Api.Quotes;

public sealed class UnderwritingDecisionInputType : InputObjectType<UnderwritingDecisionInput>
{
    protected override void Configure(
        IInputObjectTypeDescriptor<UnderwritingDecisionInput> descriptor)
    {
        // TODO: descriptor.OneOf() can be revised after <PERSON><PERSON><PERSON> is updated to Hot Chocolate 13+
    }
}
