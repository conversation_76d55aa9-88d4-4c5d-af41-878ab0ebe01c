using CoverGo.Quotation.Domain.Quotes;

using HotChocolate.Authorization;
using HotChocolate.Data;

using MongoDB.Driver;

namespace CoverGo.Quotation.Api.Quotes;

[QueryType]
public static class QuoteQueries
{
    [Authorize]
    [UseOffsetPaging]
    [UseSorting]
    [UseFiltering]
    public static IExecutable<Quote> Quotes(
        [Service] IMongoCollection<Quote> quotes)
    {
        return quotes.AsExecutable();
    }

    [Authorize]
    [UseSingleOrDefault]
    public static IExecutable<Quote> Quote(
        [Service] IMongoCollection<Quote> quotes,
        [GraphQLType<NonNullType<IdType>>] string id)
    {
        return quotes.Find(x => x.Id == id).AsExecutable();
    }

    [Authorize]
    [UseOffsetPaging]
    [UseSorting]
    [UseFiltering]
    public static IExecutable<QuoteMember> QuoteMembers(
        [Service] IMongoCollection<QuoteMember> quoteMembers)
    {
        return quoteMembers.AsExecutable();
    }

    [Authorize]
    [UseSingleOrDefault]
    public static IExecutable<QuoteMember> QuoteMember(
        [Service] IMongoCollection<QuoteMember> quoteMembers,
        [GraphQLType<NonNullType<IdType>>] string id)
    {
        return quoteMembers.Find(x => x.Id == id).AsExecutable();
    }
}

[ExtendObjectType<Quote>]
public static class QuoteExtensions
{
    [Authorize]
    [UseOffsetPaging(CollectionSegmentName = "QuoteMembers")]
    [UseSorting]
    [UseFiltering]
    public static IExecutable<QuoteMember> Members(
        [Parent] Quote quote,
        [Service] IMongoCollection<QuoteMember> quoteMembers)
    {
        return quoteMembers.Aggregate().Match(f => f.QuoteId == quote.Id).AsExecutable();
    }
}
