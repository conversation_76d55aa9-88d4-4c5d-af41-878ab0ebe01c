using CoverGo.BuildingBlocks.Api.GraphQl.Helpers;
using CoverGo.Quotation.Application.Quotes;

using HotChocolate.Execution.Configuration;

namespace CoverGo.Quotation.Api.Quotes;

public static class QuotesExtensions
{
    public static IRequestExecutorBuilder AddQuoteTypes(this IRequestExecutorBuilder builder)
    {
        builder.AddCommandType<CreateQuoteCommand>();
        builder.AddCommandType<AddQuoteMemberCommand>();
        builder.AddCommandType<UpdateQuoteMemberCommand>();
        builder.AddCommandType<DeleteQuoteMembersCommand>();
        builder.AddCommandType<RequestUnderwritingForQuoteCommand>();
        builder.AddCommandType<FinalizeQuoteCommand>();
        return builder;
    }
}
