using AutoMapper;
using CoverGo.Quotation.Api.Underwriting;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteMemberType : ObjectType<QuoteMember>
{
    protected override void Configure(IObjectTypeDescriptor<QuoteMember> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.Id).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.QuoteId).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.MemberId).Type<IdType>();
        descriptor.Field(it => it.IndividualId).Type<IdType>();
        descriptor.Field(it => it.States);
        descriptor.Field(it => it.DependentStates);
        descriptor.Field(it => it.NeedsUnderwriting);
        descriptor
            .Field("fields")
            .Resolve(descriptor =>
            {
                var parent = descriptor.Parent<QuoteMember>();
                var mapper = descriptor.Service<IMapper>();
                return mapper.Map<List<CustomFieldDto<QuoteMember>>>(parent.Fields.Select(it => it).ToList());
            })
            .Type<NonNullType<ListType<NonNullType<QuoteMemberFieldType>>>>();
        descriptor.Field(it => it.HealthQuestionnaireResponseId).Type<IdType>();
        descriptor
            .Field("memberUnderwriting")
            .Resolve(descriptor =>
            {
                var parent = descriptor.Parent<QuoteMember>();
                var mapper = descriptor.Service<IMapper>();
                return mapper.Map<Application.Underwriting.Contracts.MemberUnderwriting>(parent.MemberUnderwriting);
            })
            .Type<NonNullType<MemberUnderwritingType>>();
        descriptor.Field(it => it.Pricing);
    }
}
