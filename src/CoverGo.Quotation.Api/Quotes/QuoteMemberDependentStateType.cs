using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Api.Quotes;

public class QuoteMemberDependentStateType : ObjectType<QuoteMemberDependentState>
{
    protected override void Configure(IObjectTypeDescriptor<QuoteMemberDependentState> descriptor)
    {
        descriptor.BindFieldsExplicitly();
        descriptor.Field(it => it.QuoteMemberId).Type<NonNullType<IdType>>();
        descriptor.Field(it => it.StartDate);
        descriptor.Field(it => it.EndDate);
    }
}
