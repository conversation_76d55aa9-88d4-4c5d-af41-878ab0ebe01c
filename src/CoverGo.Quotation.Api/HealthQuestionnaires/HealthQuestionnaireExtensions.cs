using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.HealthQuestionnaires;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;

namespace CoverGo.Quotation.Api.HealthQuestionnaires;

[ExtendObjectType(typeof(OfferMember))]
public static class OfferMemberExtensions
{
    public static async Task<HealthQuestionnaireResponse?> HealthQuestionnaireResponse(
        [Parent] OfferMember offerMember,
        [Service] HealthQuestionnaireDataLoader batchLoader,
        CancellationToken cancellationToken = default) => offerMember.HealthQuestionnaireResponseId == null
            ? null
            : await batchLoader.LoadAsync(new Id(offerMember.HealthQuestionnaireResponseId!.Value), cancellationToken);
}

[ExtendObjectType(typeof(ProposalMember))]
public static class ProposalMemberExtensions
{
    public static async Task<HealthQuestionnaireResponse?> HealthQuestionnaireResponse(
        [Parent] ProposalMember proposalMember,
        [Service] HealthQuestionnaireDataLoader batchLoader,
        CancellationToken cancellationToken = default) => proposalMember.HealthQuestionnaireResponseId == null
            ? null
            : await batchLoader.LoadAsync(new Id(proposalMember.HealthQuestionnaireResponseId!.Value), cancellationToken);
}
