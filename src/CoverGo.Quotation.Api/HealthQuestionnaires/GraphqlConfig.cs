using CoverGo.Quotation.Application.HealthQuestionnaires;

namespace CoverGo.Quotation.Api.HealthQuestionnaires;

public sealed class QuestionAnswerType : InterfaceType<QuestionAnswer>
{
    protected override void Configure(IInterfaceTypeDescriptor<QuestionAnswer> descriptor)
    {
    }
}

public sealed class BooleanQuestionAnswerType : ObjectType<BooleanQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<BooleanQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public sealed class TextQuestionAnswerType : ObjectType<TextQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<TextQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public sealed class OptionsQuestionAnswerType : ObjectType<OptionsQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<OptionsQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public sealed class MultiOptionsQuestionAnswerType : ObjectType<MultiOptionsQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<MultiOptionsQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public sealed class NumericQuestionAnswerType : ObjectType<NumericQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<NumericQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public sealed class DateQuestionAnswerType : ObjectType<DateQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<DateQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public sealed class FileQuestionAnswerType : ObjectType<FileQuestionAnswer>
{
    protected override void Configure(IObjectTypeDescriptor<FileQuestionAnswer> descriptor) =>
        descriptor.Implements<QuestionAnswerType>();
}

public class ResponseContextType : UnionType
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Name("ResponseContext");
        descriptor.Type<InsuredResponseContextType>();
        descriptor.Type<OfferMemberResponseContextType>();
        descriptor.Type<ProposalMemberResponseContextType>();
    }
}

public class InsuredResponseContextType : ObjectType<InsuredResponseContext>
{
}

public class OfferMemberResponseContextType : ObjectType<OfferMemberResponseContext>
{
}

public class ProposalMemberResponseContextType : ObjectType<ProposalMemberResponseContext>
{
}

public class HealthQuestionnaireResponseType : ObjectType<HealthQuestionnaireResponse>
{
    protected override void Configure(IObjectTypeDescriptor<HealthQuestionnaireResponse> descriptor)
    {
        descriptor
            .Field(f => f.Context)
            .Type<ResponseContextType>();
    }
}

public class QuestionType : InterfaceType<Question>
{
    protected override void Configure(IInterfaceTypeDescriptor<Question> descriptor)
    {
    }
}

public class BooleanQuestionType: ObjectType<BooleanQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<BooleanQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}

public class TextQuestionType : ObjectType<TextQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<TextQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}


public class OptionsQuestionType : ObjectType<OptionsQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<OptionsQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}

public class MultiOptionsQuestionType : ObjectType<MultiOptionsQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<MultiOptionsQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}

public class NumericQuestionType : ObjectType<NumericQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<NumericQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}

public class DateQuestionType : ObjectType<DateQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<DateQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}

public class FileQuestionType : ObjectType<FileQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<FileQuestion> descriptor) =>
        descriptor.Implements<QuestionType>();
}

public class OptionType : ObjectType<Option>
{
    protected override void Configure(IObjectTypeDescriptor<Option> descriptor)
    {
    }
}


public class EnablingQuestionType : InterfaceType<EnablingQuestion>
{
    protected override void Configure(IInterfaceTypeDescriptor<EnablingQuestion> descriptor)
    {
    }
}

public class EnablingMemberDataConditionType : InterfaceType<EnablingMemberDataCondition>
{
    protected override void Configure(IInterfaceTypeDescriptor<EnablingMemberDataCondition> descriptor)
    {
    }
}

public class OptionsEnablingMemberDataConditionType : ObjectType<OptionsEnablingMemberDataCondition>
{
    protected override void Configure(IObjectTypeDescriptor<OptionsEnablingMemberDataCondition> descriptor)
    {
        descriptor.Implements<EnablingMemberDataConditionType>();
    }
}

public class NumericEnablingMemberDataConditionType : ObjectType<NumericEnablingMemberDataCondition>
{
    protected override void Configure(IObjectTypeDescriptor<NumericEnablingMemberDataCondition> descriptor)
    {
        descriptor.Implements<EnablingMemberDataConditionType>();
    }
}

public class MultiOptionsEnablingQuestionType : ObjectType<MultiOptionsEnablingQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<MultiOptionsEnablingQuestion> descriptor)
    {
        descriptor.Implements<EnablingQuestionType>();
    }
}

public class BooleanEnablingQuestionType : ObjectType<BooleanEnablingQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<BooleanEnablingQuestion> descriptor) =>
        descriptor.Implements<EnablingQuestionType>();
}

public class NumericEnablingQuestionType : ObjectType<NumericEnablingQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<NumericEnablingQuestion> descriptor) =>
        descriptor.Implements<EnablingQuestionType>();
}

public class TextEnablingQuestionType : ObjectType<TextEnablingQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<TextEnablingQuestion> descriptor) =>
    descriptor.Implements<EnablingQuestionType>();
}


public class OptionEnablingQuestionType : ObjectType<OptionEnablingQuestion>
{
    protected override void Configure(IObjectTypeDescriptor<OptionEnablingQuestion> descriptor) =>
        descriptor.Implements<EnablingQuestionType>();
}

public class QuestionEnablerType : UnionType<QuestionEnabler>
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        // The object types that belong to this union
        descriptor.Type<OptionsEnablingMemberDataConditionType>();
        descriptor.Type<NumericEnablingMemberDataConditionType>();
        descriptor.Type<MultiOptionsEnablingQuestionType>();
        descriptor.Type<BooleanEnablingQuestionType>();
        descriptor.Type<TextEnablingQuestionType>();
        descriptor.Type<NumericEnablingQuestionType>();
        descriptor.Type<OptionEnablingQuestionType>();
    }
}

