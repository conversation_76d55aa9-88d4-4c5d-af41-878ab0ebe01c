using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.HealthQuestionnaires;

namespace CoverGo.Quotation.Api.HealthQuestionnaires;

public class HealthQuestionnaireDataLoader
    : BatchDataLoader<Id, HealthQuestionnaireResponse>
{
    private readonly IHealthQuestionnaireResponseRepository _healthQuestionnaireResponseRepository;

    public HealthQuestionnaireDataLoader(
        IHealthQuestionnaireResponseRepository healthQuestionnaireResponseRepository,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null
    )
        : base(batchScheduler, options)
    {
        _healthQuestionnaireResponseRepository = healthQuestionnaireResponseRepository;
    }

    protected override async Task<IReadOnlyDictionary<Id, HealthQuestionnaireResponse>> LoadBatchAsync(IReadOnlyList<Id> keys, CancellationToken cancellationToken)
    {
        var results = await _healthQuestionnaireResponseRepository.GetByIdsAsync(keys.Select(it => it.Value).ToList(), cancellationToken);

        return results.ToDictionary(x => new Id(x.Id), x => x);
    }
}
