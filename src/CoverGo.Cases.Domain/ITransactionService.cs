﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain
{
    public interface ITransactionService
    {
        Task<List<Transaction>> GetAsync(string tenantId, TransactionQueryArguments queryArguments, CancellationToken cancellationToken);
    }
    public class Transaction : SystemObject
    {
        public string Id { get; set; }
        public string PolicyId { get; set; }
        public DateTime DateTime { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public decimal Amount { get; set; }
        public string Remark { get; set; }
        public TransactionStatus Status { get; set; }
        public PaymentMethod Method { get; set; }

        public string ProviderId { get; set; }

        public string ProviderTransactionId { get; set; }
        public dynamic Data { get; set; }

        public IEnumerable<Note> Notes { get; set; } = Enumerable.Empty<Note>();
        public IEnumerable<Fact> Facts { get; set; } = Enumerable.Empty<Fact>();
    }

    public enum TransactionStatus
    {
        NotStarted,
        Pending,
        Approved,
        Rejected,
        Timeout,
        CancelledByUser,
        Refunded
    }

    public enum PaymentMethod
    {
        Cash,
        CreditCard,
        Giro,
        Atm,
        SelfServicePaymentMachine,
        BankTransfer,
        Cheque,
    }

    public class TransactionWhere : Where
    {
        public List<TransactionWhere> Or { get; set; }
        public List<TransactionWhere> And { get; set; }

        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string TransactionNumber { get; set; }
        public string TransactionNumber_contains { get; set; }
        public TransactionStatus? Status { get; set; }
        public string PolicyId { get; set; }
        public List<string> PolicyId_in { get; set; }
        public string EndorsementId { get; set; }
        public List<string> EndorsementId_in { get; set; }
        public string ClaimId { get; set; }
        public List<string> ClaimId_in { get; set; }
        public string ProposalId { get; set; }
        public List<string> ProposalId_in { get; set; }
        public DateTime? DateTime_gt { get; set; }
        public DateTime? DateTime_lt { get; set; }
        public string PaymentProviderId { get; set; }
        public string PaymentId { get; set; }
    }

    public class TransactionQueryArguments
    {
        public TransactionWhere Where { get; set; }
        public DateTime? AsOf { get; set; }
        public OrderBy OrderBy { get; set; }
        public int? First { get; set; }
        public int? Skip { get; set; }
        public bool IncludeEvents { get; set; }
    }
}
