﻿using CoverGo.DomainUtils;

namespace CoverGo.Cases.Domain
{
    public class Note : SystemObject
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
    }

    public class AddNoteCommand
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateNoteCommand
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveNoteCommand
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
    }
}
