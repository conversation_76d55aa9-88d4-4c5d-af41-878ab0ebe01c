﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;

namespace CoverGo.Cases.Domain
{
    public class Product2 : SystemObject
    {
        public ProductId Id { get; set; }
        public string TenantId { get; set; }
        public string InsurerId { get; set; }
        public string IssuerProductId { get; set; }
        public string IssuerId { get; set; }

        public IEnumerable<Benefit> Benefits { get; set; }
    }

    public class ProductId
    {
        public string Plan { get; set; }
        public string Version { get; set; }
        public string Type { get; set; }

        public override bool Equals(object obj) => obj is ProductId id &&
            Plan == id.Plan &&
            Version == id.Version &&
            Type == id.Type;

        public override int GetHashCode() => HashCode.Combine(Plan, Version, Type);
        public override string ToString() => $"{Plan}|{Version}|{Type}";
    }

    public class ProductIdToUpdate
    {
        public string Plan { get; set; }
        public bool IsPlanChanged { get; set; }
        public string Version { get; set; }
        public bool IsVersionChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
    }

    public class Benefit
    {
        public string TypeId { get; set; }
        public string ParentTypeId { get; set; }
        public object Value { get; set; }
        public string ParentOptionKey { get; set; }
        public string OptionKey { get; set; }
        public IEnumerable<BenefitOption> Options { get; set; }
        public bool IsOptional { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
    }

    public class ProductQuery
    {
        public ProductWhere Where { get; set; }
        public JToken Factors { get; set; }
    }
    public class ProductWhere
    {
        public IEnumerable<ProductWhere> Or { get; set; }
        public IEnumerable<ProductWhere> And { get; set; }

        public List<ProductId> Id_in { get; set; }
        public string TenantId { get; set; }
        public List<string> TenantId_in { get; set; }
        public InsurerWhere Insurer { get; set; }
        public BenefitWhere Benefits_some { get; set; }
    }

    public class BenefitWhere
    {
        public IEnumerable<BenefitWhere> Or { get; set; }
        public IEnumerable<BenefitWhere> And { get; set; }

        public string TypeId { get; set; }
        public List<string> TypeId_in { get; set; }
        public string RawData_gt { get; set; }
        public string RawData_lt { get; set; }
    }

    public class ProductIdWhere
    {
        public string Type { get; set; }
        public IEnumerable<string> Type_in { get; set; }

        public string Plan { get; set; }
        public IEnumerable<string> Plan_in { get; set; }
        public string Plan_contains { get; set; }

        public string Version { get; set; }
        public IEnumerable<string> Version_in { get; set; }
        public string Version_contains { get; set; }
    }

    public class InsurerWhere
    {
        public string Id { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string Id_contains { get; set; }
    }
}
