<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CoverGo.Applications.Domain" />
		<PackageReference Include="CoverGo.BuildingBlocks.Auth" />
		<PackageReference Include="CoverGo.FeatureManagement" />
		<PackageReference Include="CoverGo.JsonUtils" />
		<PackageReference Include="CoverGo.Threading.Tasks" />
    	<PackageReference Include="CoverGo.Applications.AttachedRules" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="CoverGo.Templates.Client.Rest" />
		<PackageReference Include="CoverGo.Products.Client" />
		<PackageReference Include="CoverGo.Users.Client" />
		<PackageReference Include="CoverGo.ChannelManagement.Client" />
		<PackageReference Include="CoverGo.Policies.Client" />
	</ItemGroup>

</Project>
