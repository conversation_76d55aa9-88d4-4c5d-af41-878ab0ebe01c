﻿using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.OtherServices
{
    public interface IPricingService
    {
        Task<PriceDto2> CalculateAsync(string tenantId, PriceCalculationFactors factors, CancellationToken cancellationToken);
    }

    public class PriceFilter
    {
        public IEnumerable<ProductId> ProductIds { get; set; }
        public IEnumerable<string> DiscountCodes { get; set; }
        public JToken Factors { get; set; }
        public DateTime PricingDate { get; set; }
    }

    public class PriceCalculationFactors
    {
        public PriceFilter Filter { get; set; }
        public decimal? OriginalPrice { get; set; }
        public IEnumerable<Discount> ManualDiscounts { get; set; }
        public IEnumerable<Loading> Loadings { get; set; }
    }

    public class PriceDto2
    {
        public ProductId ProductId { get; set; }
        public decimal? Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public PaymentFrequency PaymentFrequency { get; set; }
        [Obsolete]
        public IEnumerable<Discount> AppliedDiscounts { get; set; }
        public IEnumerable<Discount> Discounts { get; set; }
        public IEnumerable<Tax> AppliedTaxes { get; set; }
        public IEnumerable<Loading> Loadings { get; set; }
        public decimal? OriginalPrice { get; set; }
    }

    public static class PricingExtensions
    {
        public static Premium ToDomain(this PriceDto2 dto, bool? isPricedAtStartDate) => new Premium
        {
            Amount = dto.Amount,
            CurrencyCode = dto.CurrencyCode,
            PaymentFrequency = dto.PaymentFrequency,
            AppliedDiscounts = dto.AppliedDiscounts?.ToList(),
            DiscountCodes = dto.Discounts?.Select(d => d.Code)?.Where(c => c != null)?.ToList(),
            Discounts = dto.Discounts?.ToList(),
            AppliedTaxes = dto.AppliedTaxes?.ToList(),
            Loadings = dto.Loadings?.ToList(),
            OriginalPrice = dto.OriginalPrice,
            IsPricedAtStartDate = isPricedAtStartDate ?? false
        };

        public static Premium ToPrice(PremiumInput input) => new Premium
        {
            Amount = input.Amount,
            DiscountCodes = input.DiscountCodes?.ToList(),
            CurrencyCode = input.CurrencyCode,
            OriginalPrice = input.GrossAmount,
            IsPricedAtStartDate = input.IsPricedAtStartDate
        };
    }
}
