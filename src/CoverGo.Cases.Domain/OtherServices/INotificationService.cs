﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.OtherServices
{
    public interface INotificationService
    {
        Task SendAsync(string tenantId, SendNotificationCommand command, CancellationToken cancellationToken);
    }

    public class SendNotificationCommand
    {
        public string Type { get; set; }
        public string FromEntityId { get; set; }
        public string ToEntityId { get; set; }
        public string PolicyId { get; set; }
        public string OfferId { get; set; }
        public PushMessage PushMessage { get; set; }
        public EmailMessage EmailMessage { get; set; }
        public SmsMessage SmsMessage { get; set; }
        public bool SaveRecord { get; set; } = true;
        public bool UseConfig { get; set; }
    }

    public class PushMessage
    {
        public string Token { get; set; }
        public string Topic { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public Dictionary<string, string> Data { get; set; }
    }

    public class EmailMessage
    {
        public string From { get; set; }
        public string FromName { get; set; }
        public string To { get; set; }
        public List<string> Tos { get; set; }
        public List<string> Ccs { get; set; }
        public List<string> Bccs { get; set; }
        public string Subject { get; set; }
        public string HtmlContent { get; set; }
        public List<PdfAttachment> PdfAttachments { get; set; }
        public TemplateRendering TemplateRendering { get; set; }
    }

    public class TemplateRendering
    {
        public string TemplateId { get; set; }
        public RenderParameters Input { get; set; }
    }

    public class PdfAttachment
    {
        public string FileName { get; set; }
        public string HtmlContent { get; set; }
        public byte[] Bytes { get; set; }
        public string Password { get; set; }
        public MarginSettings MarginSettings { get; set; }
        public HeaderFooterSettings HeaderSettings { get; set; }
        public HeaderFooterSettings FooterSettings { get; set; }
    }

    public class MarginSettings
    {
        public double? Top { get; set; }
        public double? Bottom { get; set; }
        public double? Left { get; set; }
        public double? Right { get; set; }
    }

    public class HeaderFooterSettings
    {
        public int? FontSize { get; set; }
        public string FontName { get; set; }
        public string Left { get; set; }
        public string Center { get; set; }
        public string Right { get; set; }
        public bool? Line { get; set; }
        public double? Spacing { get; set; }
        public string HtmUrl { get; set; }
    }

    public class SmsMessage
    {
        public string From { get; set; }
        public string To { get; set; }
        public string Body { get; set; }
    }
}
