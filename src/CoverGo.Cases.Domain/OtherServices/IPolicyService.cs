﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;

namespace CoverGo.Cases.Domain.OtherServices
{
    [Obsolete("use CoverGo.Policies.Client.IPoliciesClient directly")]
    public interface IPolicyService
    {
        Task<Result<PolicyStatus>> CreatePolicyAsync(string tenantId, CreatePolicyCommand command, string accessToken = null, CancellationToken cancellationToken = default);
        Task<Result<PolicyStatus>> IssuePolicyAsync(string tenantId, IssuePolicyCommand command, CancellationToken cancellationToken);
        Task<Result<string>> AddClauseToPolicyAsync(string tenantId, string policyId, AddClauseCommand command, CancellationToken cancellationToken);
        Task<Result> AddAssociatedContractToPolicyAsync(string tenantId, string policyId, AddAssociatedContractCommand command, CancellationToken cancellationToken);
        Task<Result<string>> AddStakeholderToPolicyAsync(string tenantId, string policyId, AddStakeholderCommand command, CancellationToken cancellationToken);
        Task<Result> PolicyFactBatch(string tenantId, string policyId, FactCommandBatch batch, CancellationToken cancellationToken);
        Task<Result> AddCommissionAsync(string tenantId, string policyId, AddCommissionCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateCommissionAsync(string tenantId, string policyId, UpdateCommissionCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveCommissionAsync(string tenantId, string policyId, RemoveCommissionCommand command, CancellationToken cancellationToken);
        Task<Result<CreatedStatus>> AddExclusionAsync(string tenantId, string policyId, AddExclusionCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveExclusionAsync(string tenantId, string policyId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddBeneficiaryEligibilityAsync(string tenantId, string policyId, AddBeneficiaryEligibilityCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateBeneficiaryEligibilityAsync(string tenantId, string policyId, UpdateBeneficiaryEligibilityCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveBeneficiaryEligibilityAsync(string tenantId, string policyId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddPaymentInfoAsync(string tenantId, string policyId, AddPaymentInfoCommand command, CancellationToken cancellationToken);
        Task<Result> UpdatePaymentInfoAsync(string tenantId, string policyId, UpdatePaymentInfoCommand command, CancellationToken cancellationToken);
        Task<Result> RemovePaymentInfoAsync(string tenantId, string policyId, RemoveCommand command, CancellationToken cancellationToken);
        Task<Result> AddJacketsToPolicyAsync(string tenantId, string policyId, AddJacketsToPolicyCommand command, CancellationToken cancellationToken);
    }

    public class PolicyStatus
    {
        public string Id { get; set; }
        public string Status { get; set; }
    }

    public class CreatePolicyCommand
    {
        public string IssuerNumber { get; set; }
        public string IssuerId { get; set; }
        public GeneratedFrom GeneratedFrom { get; set; }
        public Entity ContractHolder { get; set; }
        public List<Entity> OtherContractHolders { get; set; }
        public List<Entity> ContractInsured { get; set; }
        public DateTime? IssueDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Description { get; set; }
        public string ReferralCode { get; set; }
        public JToken Values { get; set; }
        public string Source { get; set; }
        public string ClientId { get; set; }
        public string Status { get; set; }
        public string CreatedById { get; set; }
        public Premium Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public ProductId ProductId { get; set; }
        public List<BenefitOption> BenefitOptions { get; set; }
        public string Fields { get; set; }
        public string ExtraFields { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }
        public PolicyCommission PolicyCommission { get; set; }
    }
    public record PolicyCommission(string DistributorID, List<string> CampaignCodes);

    public class Entity : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string InternalCode { get; set; }
        public string PhotoPath { get; set; }

        public IEnumerable<Contact> Contacts { get; set; }
        public IEnumerable<Identity> Identities { get; set; }
        public IEnumerable<Address> Addresses { get; set; }
        public IEnumerable<Fact> Facts { get; set; }
        public IEnumerable<Note> Notes { get; set; }

        public string CreatedBy { get; set; }
        public string LastModifiedBy { get; set; }

        public IEnumerable<EventLog> Events { get; set; }

        public string Source { get; set; }
    }

    public class GeneratedFrom
    {
        public string CaseId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }

    public class Contact
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class Identity
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public string Value { get; set; }
    }

    public class Address
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public Dictionary<string, string> Fields { get; set; }
    }

    public class ConvertOfferCommand
    {
        public string OfferId { get; set; }
        public string ConvertedById { get; set; }
    }

    public class IssuePolicyCommand
    {
        public string IssuerNumber { get; set; }
        public string PolicyId { get; set; }
        public bool IsManual { get; set; }
        public string IssuedById { get; set; }
    }
}
