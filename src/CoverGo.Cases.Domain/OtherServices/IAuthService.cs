﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Auth.Client;
using CoverGo.DomainUtils;

namespace CoverGo.Cases.Domain.OtherServices
{
    public interface IAuthService
    {
        Task<Token> GetWholeAccessTokenAsync(string tenantId, string clientId, string clientSecret, string username, string password, CancellationToken cancellationToken);
        Task<Login?> GetLoginByEntityId(string tenantId, string entityId, CancellationToken cancellationToken);
        Task<IEnumerable<Login?>> GetLoginsAsync(string tenantId, QueryArguments<LoginWhere> queryArguments, CancellationToken cancellationToken);
    }

    public class Token
    {
        public string AccessToken { get; set; }
        public string IdentityToken { get; set; }
        public string TokenType { get; set; }
        public string RefreshToken { get; set; }
        public string ErrorDescription { get; set; }
        public int ExpiresIn { get; set; }
        public string Error { get; set; }
    }
}
