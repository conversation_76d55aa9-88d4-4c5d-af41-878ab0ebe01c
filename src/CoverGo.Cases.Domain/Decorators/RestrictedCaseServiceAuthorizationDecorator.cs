﻿using System.Collections.Generic;
using System.Linq;
using CoverGo.BuildingBlocks.Auth.Decorators;

namespace CoverGo.Cases.Domain.Decorators;

public class RestrictedCaseServiceAuthorizationDecorator : RestrictedContentAuthorizationDecoratorBase<ICaseService>
{
    protected override IEnumerable<string> ConvertToObjectIdsArray(object command)
    {
        IEnumerable<string> emptyArr = Enumerable.Empty<string>();
        if (command == null) return emptyArr;

        return command switch
        {
            ConvertOfferToApplicationCommand convertOfferToApplicationCommand => new[]
            {
                convertOfferToApplicationCommand.CaseId
            },
            _ => Enumerable.Empty<string>()
        };
    }
}