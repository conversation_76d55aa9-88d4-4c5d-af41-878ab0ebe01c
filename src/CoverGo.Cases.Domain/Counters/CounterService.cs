﻿using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;

namespace CoverGo.Cases.Domain.Counters
{
    public class CounterService : CoverGoGenericDataServiceBase<Counter, CounterUpsert, CounterFilter, ICounterRepository>, ICounterService
    {
        private readonly ICounterRepository _repository;

        public CounterService(ICounterRepository repository) : base(repository)
        {
            _repository = repository;
        }

        public Task<long> GetNextAsync(string tenantId, CounterUpsert upsert,
            CancellationToken cancellationToken) => _repository.GetNextAsync(tenantId, upsert, cancellationToken);
    }
}