using System.Collections.Generic;
using CoverGo.DomainUtils;

namespace CoverGo.Cases.Domain.Counters
{
    public class Counter : SystemObject, IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Scope { get; set; }
        public Dictionary<string, long> Counters { get; set; }
    }

#nullable enable
    public class IncreaseCounterCommand
    {
        public string Scope { get; set; } = string.Empty;
        public string CounterKey { get; set; } = string.Empty;
    }
#nullable restore

    public class CounterUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Scope { get; set; }
        public string CounterKey { get; set; }
        public string ById { get; set; }
    }

    public class CounterFilter
    {
        [FilterCondition(FilterCondition.Eq)]
        public string Scope { get; set; }
    }
}