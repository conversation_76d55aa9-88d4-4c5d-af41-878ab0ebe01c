﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Attributes;
using CoverGo.Cases.Domain.AgentAssignment;
using CoverGo.Cases.Domain.HandlerAssignment;

namespace CoverGo.Cases.Domain;

public interface ICaseService
{
    Task ReplayAllAsync(string tenantId, CancellationToken cancellationToken);

    Task ReplayAsync(string tenantId, IEnumerable<string> ids, CancellationToken cancellationToken);

    Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, QueryArguments<CaseWhere> queryArguments,
        CancellationToken cancellationToken);

    Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, QueryArguments<CaseWhere> queryArguments,
        CancellationToken cancellationToken);

    Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments<CaseWhere> queryArguments,
        CancellationToken cancellationToken);

    IEnumerable<string> GetIds(string tenantId, QueryArguments<CaseWhere> queryArguments);

    Task<IEnumerable<Case>> GetAsync(string tenantId, QueryArguments<CaseWhere> queryArguments,
        CancellationToken cancellationToken);

    Task<IEnumerable<DetailedEventLog>> GetEventLogsAsync(string tenantId, EventQuery query,
        CancellationToken cancellationToken);

    Task<IEnumerable<CasesReport>> GetReportAsync(string tenantId, QueryArguments<CaseWhere> queryArguments,
        CancellationToken cancellationToken);

    Task<long> GetTotalCountAsync(string tenantId, CaseWhere where, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> GeneratePoliciesAsync(string tenantId, string caseId,
        GeneratePoliciesFromProposalCommand command, string accessToken = null,
        CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> AddIssuedPolicyAsync(string tenantId, string caseId, AddIssuedPolicyCommand command,
        CancellationToken cancellationToken);

    Task<Result<string>> CreateAsync(string tenantId, CreateCaseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateAsync(string tenantId, string caseId, UpdateCaseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> DeleteAsync(string tenantId, string caseId, DeleteCaseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> AddBeneficiaryEligibilityToCaseAsync(string tenantId, string caseId,
        AddBeneficiaryEligibilityCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateBeneficiaryEligibilityOfCaseAsync(string tenantId, string caseId,
        UpdateBeneficiaryEligibilityCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveBeneficiaryEligibilityFromCaseAsync(string tenantId, string caseId, RemoveCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> AddPaymentInfoToCaseAsync(string tenantId, string caseId,
        AddPaymentInfoCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemovePaymentInfoFromCaseAsync(string tenantId, string caseId, RemoveCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdatePaymentInfoOfCaseAsync(string tenantId, string caseId, UpdatePaymentInfoCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddStakeholderToCaseAsync(string tenantId, string caseId, AddStakeholderCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateStakeholderOfCaseAsync(string tenantId, string caseId, UpdateStakeholderCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveStakeHolderFromCaseAsync(string tenantId, string caseId, RemoveStakeholderCommand command,
        CancellationToken cancellationToken);
    
    Task<Result> ProcessCommandToEventSourcing(string tenantId, string caseId, CaseEventType eventType,
        object command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateOfferAsync(string tenantId, string caseId, UpdateOfferCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddProposalAsync(string tenantId, string caseId, AddProposalCommand command,
        string accessToken = null, CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> CopyProposalAsync(string tenantId, string caseId, CopyProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> RenewProposalAsync(string tenantId, string caseId, RenewProposalCommand command,
        string accessToken = null, CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> IssueProposalAsync(string tenantId, string caseId, IssueProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RejectProposalAsync(string tenantId, string caseId, RejectProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateProposalAsync(string tenantId, string caseId, UpdateProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveProposalAsync(string tenantId, string caseId, RemoveCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddStakeholderToProposalAsync(string tenantId, string caseId,
        AddStakeholderCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateStakeholderOfProposalAsync(string tenantId, string caseId, UpdateStakeholderCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveStakeHolderFromProposalAsync(string tenantId, string caseId,
        RemoveStakeholderCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddOfferAsync(string tenantId, string caseId, AddOfferCommand command,
        string accessToken = null, CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveOfferAsync(string tenantId, string caseId, RemoveOfferFromProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddDiscountToOfferAsync(string tenantId, string caseId, AddDiscountToOfferCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateDiscountOfOfferAsync(string tenantId, string caseId, UpdateDiscountOfOfferCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveDiscountFromOfferAsync(string tenantId, string caseId,
        RemoveDiscountFromOfferCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddLoadingToOfferAsync(string tenantId, string caseId, AddLoadingCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateLoadingOfOfferAsync(string tenantId, string caseId, UpdateLoadingCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveLoadingFromOfferAsync(string tenantId, string caseId, RemoveLoadingCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> AddExclusionToOfferAsync(string tenantId, string caseId,
        AddExclusionCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveExclusionFromOfferAsync(string tenantId, string caseId, RemoveExclusionCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddClauseToOfferAsync(string tenantId, string caseId, AddClauseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateClauseOfOfferAsync(string tenantId, string caseId, UpdateClauseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveClauseFromOfferAsync(string tenantId, string caseId, RemoveClauseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> OfferClauseBatchAsync(string tenantId, string caseId, ClauseCommandBatch batch,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> OfferJacketBatchAsync(string tenantId, string caseId, JacketCommandBatch batch,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveJacketFromOfferAsync(string tenantId, string caseId, RemoveJacketInstanceCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> OfferFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddFactToOfferAsync(string tenantId, string caseId, AddFactCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateFactOfOfferAsync(string tenantId, string caseId, UpdateFactCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveFactFromOfferAsync(string tenantId, string caseId, RemoveFactCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpsertBenefitOptionOfOfferAsync(string tenantId, string caseId, UpsertBenefitOptionCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveBenefitOptionFromOfferAsync(string tenantId, string caseId,
        RemoveBenefitOptionCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AddStakeholderToProposalOfferAsync(string tenantId, string caseId,
        AddStakeholderCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateStakeholderOfProposalOfferAsync(string tenantId, string caseId,
        UpdateStakeholderCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveStakeHolderFromProposalOfferAsync(string tenantId, string caseId,
        RemoveStakeholderCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> AddAssociatedContractToProposalOfferAsync(string tenantId, string caseId,
        AddAssociatedContractCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveAssociatedContractFromProposalOfferAsync(string tenantId, string caseId,
        RemoveAssociatedContractCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> AddCommissionToOfferAsync(string tenantId, string caseId, AddCommissionCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateCommissionOfOfferAsync(string tenantId, string caseId, UpdateCommissionCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveCommissionFromOfferAsync(string tenantId, string caseId, RemoveCommissionCommand command,
        CancellationToken cancellationToken);
    
    [AuthorizeRestrictedContent("caseId")]
    Task<Result> AddNoteToCaseAsync(string tenantId, string caseId, AddNoteCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateNoteOfCaseAsync(string tenantId, string caseId, UpdateNoteCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveNoteFromCaseAsync(string tenantId, string caseId, RemoveNoteCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> AddFactToCaseAsync(string tenantId, string caseId, AddFactCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UpdateFactOfCaseAsync(string tenantId, string caseId, UpdateFactCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RemoveFactFromCaseAsync(string tenantId, string caseId, RemoveFactCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> CaseFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> SetSystemDatesAsync(string tenantId, string caseId, AdminSetSystemDatesCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> SetReadOnlyAsync(string tenantId, string caseId, AdminSetReadOnlyCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> AgentCreateAsync(string tenantId, AgentCreateCaseCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> SendAgentProposalToClientAsync(string tenantId, string caseId,
        SendAgentProposalToClientCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> AcceptAgentProposalAsync(string tenantId, string caseId, AcceptAgentProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> RejectAgentProposalAsync(string tenantId, string caseId, RejectAgentProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<ProposalAndOfferIds>> AddAgentProposalAsync(string tenantId, string caseId,
        AddAgentProposalCommand command, string accessToken = null, CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<string>> UpdateAgentProposalAsync(string tenantId, string caseId,
        UpdateAgentProposalCommand command, CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> SendAgentProposalForApprovalAsync(string tenantId, string caseId,
        SendAgentProposalForApprovalCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> ApproveAgentProposalAsync(string tenantId, string caseId, ApproveAgentProposalCommand command,
        CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> DisapproveAgentProposalAsync(string tenantId, string caseId,
        DisapproveAgentProposalCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> SendAgentProposalToAgentAsync(string tenantId, string caseId,
        SendAgentProposalToAgentCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<ProposalAndOfferIds>> DuplicateProposalAsync(string tenantId, string caseId,
        DuplicateProposalCommand command, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent]
    Task<Result<CreatedStatus>> ConvertOfferToApplication(string tenantId, string loginId,
        ConvertOfferToApplicationCommand command, string accessToken = null,
        CancellationToken cancellationToken = default);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> AssignAgentsAsync(string tenantId, string caseId, List<AssignAgentCommand> commands, CancellationToken cancellationToken);

    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UnassignAgentsAsync(string tenantId, UnassignAgentsCommand command, CancellationToken cancellationToken);
    [AuthorizeRestrictedContent("caseId")]
    Task<Result<CreatedStatus>> AssignHandlersAsync(string tenantId, AssignHandlersCommand command, CancellationToken cancellationToken);
    [AuthorizeRestrictedContent("caseId")]
    Task<Result> UnassignHandlersAsync(string tenantId, UnassignHandlersCommand command, CancellationToken cancellationToken);
}