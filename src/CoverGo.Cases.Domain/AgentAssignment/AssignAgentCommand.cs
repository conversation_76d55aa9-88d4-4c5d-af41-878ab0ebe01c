﻿using HotChocolate;

namespace CoverGo.Cases.Domain.AgentAssignment;

public record AssignAgentCommand(string AgentId, AgentRoleType RoleType)
{
    [GraphQLIgnore]
    public string StakeholderId { get; set; }
    [GraphQLIgnore]
    public string EntityId { get; set; }
    [GraphQLIgnore]
    public string PortalUserId { get; set; }
    [GraphQLIgnore]
    public string AssignedById { get; set; }
}