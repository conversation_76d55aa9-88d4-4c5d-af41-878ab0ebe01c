using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain.Brokers
{
    public class Broker : SystemObject, IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string NormalizedCode { get; set; }
        public string Name { get; set; }
        public string NormalizedName { get; set; }
        public string Channel { get; set; }
        public string Group { get; set; }
        public string Description { get; set; }
        public string ContactPerson { get; set; }
        public string ContactPersonTelNo { get; set; }
        public string ContactPersonFaxNo { get; set; }
        public string ContactPersonEmail { get; set; }
        public JToken Fields { get; set; }
    }

    public class BrokerUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Channel { get; set; }
        public string Group { get; set; }
        public string ContactPerson { get; set; }
        public string Description { get; set; }
        public string ContactPersonTelNo { get; set; }
        public string ContactPersonFaxNo { get; set; }
        public string ContactPersonEmail { get; set; }
        public string Fields { get; set; }
        public string ById { get; set; }
    }

    public class BrokerFilter
    {
        [FilterCondition(FilterCondition.Eq)]
        public string Id { get; set; }
        [FilterCondition(FilterCondition.Eq, nameof(Broker.Id), flag: false)]
        public string Id_neq { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.Id))]
        public List<string> Id_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(Broker.Id))]
        public string Id_contains { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.Code))]
        public List<string> Code_in { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.NormalizedCode))]
        public List<string> NormalizedCode_in { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string Name { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.Name))]
        public List<string> Name_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(Broker.Name))]
        public string Name_contains { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string NormalizedName { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.NormalizedName))]
        public string NormalizedName_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(Broker.NormalizedName))]
        public string NormalizedName_contains { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.Channel))]
        public List<string> Channel_in { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.Group))]
        public List<string> Group_in { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.ContactPerson))]
        public List<string> ContactPerson_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(Broker.ContactPerson))]
        public string ContactPerson_contains { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.ContactPersonTelNo))]
        public List<string> ContactPersonTelNo_in { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.ContactPersonFaxNo))]
        public List<string> ContactPersonFaxNo_in { get; set; }

        [FilterCondition(FilterCondition.In, nameof(Broker.ContactPersonEmail))]
        public List<string> ContactPersonEmail_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(Broker.ContactPersonEmail))]
        public string ContactPersonEmail_contains { get; set; }

        [FilterCondition(FilterCondition.Fields)]
        public FieldsWhere Fields { get; set; }
    }
}