using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Security.Claims;
using CoverGo.Applications.AttachedRules;
using CoverGo.Applications.AttachedRules.Attributes;
using CoverGo.Applications.Domain;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.Cases.Domain.Decorators;
using CoverGo.Cases.Domain.Extensions;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Cases.Domain;

public class CaseServiceFactory : ICaseServiceFactory
{
    private readonly ICaseService _caseService;
    private readonly ICurrentUser _currentUser;
    private readonly IEnumerable<IAttachedRulesExecutor> _attachedRulesExecutors;
    private readonly IPermissionValidator _permissionValidator;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IRestrictedCaseService _restrictedCaseService;

    public CaseServiceFactory(ICurrentUser currentUser, 
        ICaseService caseService,
        IEnumerable<IAttachedRulesExecutor> attachedRulesExecutors,
        IPermissionValidator permissionValidator, 
        IHttpContextAccessor httpContextAccessor, 
        IRestrictedCaseService restrictedCaseService)
    {
        _attachedRulesExecutors = attachedRulesExecutors;
        _permissionValidator = permissionValidator;
        _httpContextAccessor = httpContextAccessor;
        _restrictedCaseService = restrictedCaseService;
        _caseService = caseService;
        _currentUser = currentUser;
    }

    public ICaseService Build()
    {
        ICaseService decoratedService = DecorateAttachedRules(_caseService);
        return DecorateRestrictedContentAuthorization(decoratedService);
    }

    private ICaseService DecorateAttachedRules(ICaseService service) =>
        AttachedRulesDecorator<ICaseService, Case>.Decorate(
            service,
            async caseId =>
            {
                if (string.IsNullOrEmpty(_currentUser.Authorization)) return null;

                IEnumerable<Case> cases = await _caseService.GetAsync(_currentUser.TenantId,
                    new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } },
                    _currentUser.CancellationToken);
                return cases.FirstOrDefault();
            },
            _attachedRulesExecutors);

    private ICaseService DecorateRestrictedContentAuthorization(ICaseService service) =>
        RestrictedCaseServiceAuthorizationDecorator
            .Decorate<RestrictedCaseServiceAuthorizationDecorator>(
                service,
                IsAccessRestricted,
                UserHasFullAccessRestrictedContentPermission);

    private bool IsAccessRestricted(List<string> casesIds) =>
        _restrictedCaseService.IsAccessRestricted(_currentUser.TenantId, new ReadOnlyCollection<string>(casesIds), _currentUser.Authorization);

    private bool UserHasFullAccessRestrictedContentPermission() =>
        PermissionValidatorExtension.AuthorizeWith(_permissionValidator, _httpContextAccessor.HttpContext?.User.Identity as ClaimsIdentity,
            UserClaim.AccessRestrictedContent.ToString(), UserClaimValues.AccessRestrictedContent.Full);
}