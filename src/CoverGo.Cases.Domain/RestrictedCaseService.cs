﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Caching.Memory;

namespace CoverGo.Cases.Domain;

public class RestrictedCaseService : IRestrictedCaseService
{
    private readonly IMemoryCache _restrictedCasesIdsCache;
    private readonly ICaseService _caseService;

    public RestrictedCaseService(IMemoryCache restrictedCasesIdsCache, ICaseService caseService)
    {
        _restrictedCasesIdsCache = restrictedCasesIdsCache;
        _caseService = caseService;
    }

    public bool IsAccessRestricted(string tenantId, ReadOnlyCollection<string> casesIds, string accessToken)
    {
        if (string.IsNullOrEmpty(accessToken))
        {
            return false;
        }

        var uncachedCasesIds = new List<string>();
        foreach (string caseId in casesIds)
        {
            string cacheKey = BuildCacheKey(caseId);
            if (!_restrictedCasesIdsCache.TryGetValue(cacheKey, out bool isRestricted))
            {
                uncachedCasesIds.Add(caseId);
                continue;
            }

            if (isRestricted) return true;
        }

        List<string> accessRestrictedCasesIds = GetAccessRestrictedCasesIds(tenantId, uncachedCasesIds);

        UpdateCache(uncachedCasesIds, accessRestrictedCasesIds);

        return accessRestrictedCasesIds.Any();
    }

    private void UpdateCache(IEnumerable<string> uncachedCasesIds,
        IReadOnlyCollection<string> restrictedCasesIds)
    {
        foreach (string caseId in uncachedCasesIds)
        {
            string cacheKey = BuildCacheKey(caseId);
            bool isRestricted = restrictedCasesIds.Contains(caseId);

            _restrictedCasesIdsCache.Set(cacheKey, isRestricted, TimeSpan.FromDays(1));
        }
    }

    private static string BuildCacheKey(string caseId) =>
        $"{nameof(CaseServiceFactory)}_{nameof(AccessPolicy)}_{caseId}";

    private List<string> GetAccessRestrictedCasesIds(string tenantId,
        IEnumerable<string> caseIds)
    {
        IEnumerable<string> restrictedCasesIds = _caseService.GetIds(tenantId,
            new QueryArguments<CaseWhere>
            {
                Where = new CaseWhere { AccessPolicy = AccessPolicy.Restricted, Id_in = caseIds }
            });

        return restrictedCasesIds.ToList();
    }
}