﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Common;
using CoverGo.BuildingBlocks.Auth.Contracts;
using CoverGo.Cases.Domain.AgentAssignment;
using CoverGo.Cases.Domain.HandlerAssignment;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{

    public interface ICaseRepository
    {
        string ProviderId { get; }
        Task<IEnumerable<string>> GetIdsAsync(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken);
        IEnumerable<string> GetIds(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first);
        Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken);
        Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first, CancellationToken cancellationToken);
        Task<IEnumerable<Case>> GetAsync(string tenantId, CaseWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<JToken>> GetReportAsync(string tenantId, CaseWhere where, OrderBy orderBy = null, int? skip = null, int? first = null, CancellationToken cancellationToken = default);
        Task<long> GetTotalCountAsync(string tenantId, CaseWhere where, CancellationToken cancellationToken);
        Task UpsertAsync(string tenantId, string id, Case @case, CancellationToken cancellationToken);
        Task DeleteAsync(string tenantId, string id, CancellationToken cancellationToken);
    }
    public class CasesReport
    {
        public string ReportFields { get; set; }
    }
    public class BaseReport
    {
        public DateTime? CreatedDate { get; set; }
        public int NumberOfCases { get; set; }
        public int NumberOfCompletedCases { get; set; }
    }
    public class TcbReport : BaseReport
    {
        public string Source { get; set; }
        public int LeadGcm { get; set; }
        public int TotalConversationAPE { get; set; }
        public double Premium { get; set; }
    }

    public class Case : SystemObject, IRestrictedContent
    {
        public string Id { get; set; }
        public string CaseNumber { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Source { get; set; }
        public IEnumerable<Fact> Facts { get; set; } = Enumerable.Empty<Fact>();
        public IEnumerable<Note> Notes { get; set; }
        public string HolderId { get; set; }
        public List<string> OtherHolderIds { get; set; }
        public List<string> InsuredIds { get; set; }
        public string Status { get; set; }
        public IEnumerable<Proposal> Proposals { get; set; } = Enumerable.Empty<Proposal>();
        public IEnumerable<Stakeholder> Stakeholders { get; set; }
        public List<CaseAgent> Agents { get; set; } = new List<CaseAgent>();
        public List<CaseHandler> Handlers { get; set; } = new List<CaseHandler>();
        public IEnumerable<BeneficiaryEligibility> BeneficiaryEligibilities { get; set; }
        public IEnumerable<PaymentInfo> PaymentInfos { get; set; }
        public string ComponentId { get; set; }
        public JToken Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string WorkflowSchemaId { get; set; }
        public List<string> IssuedPoliciesIds { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }
        public bool IsReadOnly { get; set; } = false;

        public void InjectMetaFieldsToOfferFields2()
        {
            if (Proposals == null) return;

            JObject metaFieldsToBeInjected = new();
            foreach (Proposal p in Proposals)
            {
                JProperty proposalIssuedAtProp = p.BuildProposalIssuedAtJProperty();
                if (proposalIssuedAtProp != null) metaFieldsToBeInjected.Add(proposalIssuedAtProp);

                // Note: Should complete building 'metaFieldsToBeInjected' content
                // by adding JProperty before calling offer.InjectMetaFieldsToFields2()
                foreach (Offer o in p.Basket)
                    o.InjectMetaFieldsToFields2(JToken.FromObject(metaFieldsToBeInjected));
            }
        }
    }

    public class CaseWhere : Where, IRestrictedContentFilter
    {
        public List<CaseWhere> Or { get; set; }
        public List<CaseWhere> And { get; set; }
        public string Id { get; set; }
        public string Name_contains { get; set; }
        public IEnumerable<string> Id_in { get; set; }
        public string CaseNumber { get; set; }
        public string Source { get; set; }
        public string Source_contains { get; set; }
        public string Status { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<string> HolderId_in { get; set; }
        public ProposalWhere Proposals_contains { get; set; }
        [Obsolete("use `Proposals_contains` instead")]
        public ProposalWhere Proposal { get; set; }
        public StakeholderWhere Stakeholders_contains { get; set; }
        public FactWhere Facts_contains { get; set; }
        public ProposalWhere Proposals_every { get; set; }
        public bool? Proposals_exist { get; set; }
        public string CaseNumber_contains { get; set; }
        public FieldsWhere FieldsWhere { get; set; }
        public bool? HavingIssuedPolicies { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }

        /// <summary>
        /// Get all AccessPolicy filters
        /// </summary>
        public IEnumerable<AccessPolicy?> GetAccessPolicyFilters()
        {
            var accessPolicyFilters = new List<AccessPolicy?>();

            if (AccessPolicy != null)
                accessPolicyFilters.Add(AccessPolicy);

            if (And != null)
                foreach (var filter in And.Where(f => f != null))
                    accessPolicyFilters.AddRange(filter.GetAccessPolicyFilters());
            if (Or != null)
                foreach (var filter in Or.Where(f => f != null))
                    accessPolicyFilters.AddRange(filter.GetAccessPolicyFilters());

            return accessPolicyFilters.Distinct();
        }
    }

    public class StakeholderWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Type { get; set; }
        public List<string> Type_in { get; set; }
        public string EntityId { get; set; }
        public List<string> EntityId_in { get; set; }
        public string Name_contains { get; set; }
        public string Code_contains { get; set; }
    }

    public class ProposalWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string ProposalNumber { get; set; }
        public string ProposalNumber_contains { get; set; }
        public string Name { get; set; }
        public string Name_contains { get; set; }
        public string PolicyId_contains { get; set; }
        public List<string> PolicyId_contains_every { get; set; }
        public List<string> PolicyId_contains_some { get; set; }
        public bool? PolicyIds_contains_any { get; set; }
        public OfferWhere Offers_contains { get; set; }
        public List<OfferWhere> Offer_contains_every { get; set; }
        public List<OfferWhere> Offer_contains_some { get; set; }
        public RenewalHistoryWhere RenewalHistory { get; set; }
        public DateTime? ExpiryDate_lt { get; set; }
        public DateTime? ExpiryDate_gt { get; set; }
        public DateTime? IssuedAt_lt { get; set; }
        public DateTime? IssuedAt_gt { get; set; }
        public string Status { get; set; }
        public bool? IsIssued { get; set; }
        public bool? IsApprovalNeeded { get; set; }
        public bool? Offers_exist { get; set; }
        public OfferWhere Offers_every { get; set; }
        public string ReferralCode { get; set; }
        public string ChannelId { get; set; }
    }

    public class FactWhere : Where
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public JToken Value { get; set; }
    }
    public class RenewalHistoryWhere
    {
        public int? RenewalCount_gt { get; set; }
        public string RenewedFromId { get; set; }
        public string RenewedById { get; set; }
    }

    public class OfferWhere : Where
    {
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string OfferNumber { get; set; }
        public List<string> OfferNumber_in { get; set; }
        public string Status { get; set; }
        public DateTime? StartDate_gte { get; set; }
        public DateTime? StartDate_lte { get; set; }
        public List<ProductId> ProductId_in { get; set; }
    }

    public class CreateCaseCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string CaseNumber { get; set; }
        public string Source { get; set; }
        public string Status { get; set; }
        public string HolderId { get; set; }
        public List<string> OtherHolderIds { get; set; }
        public List<string> InsuredIds { get; set; }
        public string ComponentId { get; set; }

        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string WorkflowSchemaId { get; set; }
        public AccessPolicy? AccessPolicy { get; set; }
        public string ChannelId { get; set; }

        public string AgentId { get; set; }

        public string CreatedById { get; set; }
    }

    public class AgentCreateCaseCommand : CreateCaseCommand
    {
        public List<Stakeholder> Stakeholders { get; set; }
    }

    public class UpdateCaseCommand
    {
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string Source { get; set; }
        public bool IsSourceChanged { get; set; }
        public string CaseNumber { get; set; }
        public bool IsCaseNumberChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string HolderId { get; set; }
        public bool IsHolderIdChanged { get; set; }
        public List<string> OtherHolderIds { get; set; }
        public bool IsOtherHolderIdsChanged { get; set; }
        public List<string> InsuredIds { get; set; }
        public bool IsInsuredIdsChanged { get; set; }
        public string ComponentId { get; set; }
        public bool IsComponentIdChanged { get; set; }
        public string FieldsPatch { get; set; }
        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string WorkflowSchemaId { get; set; }
        public bool IsWorkflowSchemaIdChanged { get; set; }
        public string ChannelId { get; set; }
        public bool IsChannelIdChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class AddIssuedPolicyCommand
    {
        public string PolicyId { get; set; }
    }

    public class DeleteCaseCommand
    {
        public string DeletedById { get; set; }
    }

    public class AddProposalCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Status { get; set; }
        public string ProposalNumber { get; set; }
        public string ProposalNumberType { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public PremiumInput TotalPrice { get; set; }
        public RenewalHistory RenewalHistory { get; set; }
        public string ReferralCode { get; set; }
        public string AddedById { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool IsApprovalNeeded { get; set; }
    }

    public class RenewProposalCommand
    {
        public string ProposalNumber { get; set; }
        public bool OverrideStartDateAndEndDateAutomatically { get; set; }
        public string RenewedFromId { get; set; }
        public string RenewedById { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class CopyProposalCommand
    {
        public string CopiedFromId { get; set; }
        public string CopiedById { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class IssueProposalCommand
    {
        public string ProposalId { get; set; }
        public string IssuedById { get; set; }
    }


    public class AdminSetSystemDatesCommand
    {
        public DateTime? CreatedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }
    }

    public class AdminSetReadOnlyCommand
    {
        public bool? ReadOnly { get; set; }
    }


    public class UpdateProposalCommand
    {
        public string ProposalId { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string Description { get; set; }
        public bool IsDescriptionChanged { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string ProposalNumber { get; set; }
        public bool IsProposalNumberChanged { get; set; }
        public string ReferralCode { get; set; }
        public bool IsReferralCodeChanged { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool IsExpiryDateChanged { get; set; }
        public PremiumToUpdate TotalPrice { get; set; }
        public bool IsTotalPriceChanged { get; set; }
        public RenewalHistory RenewalHistory { get; set; }
        public bool IsRenewalHistoryChanged { get; set; }
        public string ModifiedById { get; set; }
        public bool IsApprovalNeeded { get; set; }
    }

    public class AddAgentProposalCommand
    {
        public AddProposalCommand ProposalCommand { get; set; }
        public AddOfferCommand OfferCommand { get; set; }
    }

    public class UpdateAgentProposalCommand
    {
        public UpdateProposalCommand ProposalCommand { get; set; }
        public UpdateOfferCommand OfferCommand { get; set; }
    }

    public class RemoveOfferFromProposalCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class GeneratePoliciesFromProposalCommand
    {
        public string ProposalId { get; set; }
        public string ClientId { get; set; }
        public IEnumerable<string> PolicyIds { get; set; }
        public string GeneratedById { get; set; }
        public bool CopyCaseFieldsToExtraFields { get; set; }
        public bool StoreClausesByValue { get; set; }
        public bool StoreJacketsByValue { get; set; }
        public string PolicyStatus { get; set; }
        public IEnumerable<string> OffersToAccept { get; set; }
    }

    public class RejectProposalCommand
    {
        public string ProposalId { get; set; }
        public IEnumerable<string> Codes { get; set; }
        public string Remarks { get; set; }
        public string RejectedById { get; set; }
    }

    public class AcceptAgentProposalCommand
    {
        public string ProposalId { get; set; }
        public string AcceptedById { get; set; }
    }

    public class ApproveAgentProposalCommand
    {
        public string ProposalId { get; set; }
        public string ApprovedById { get; set; }
    }

    public class DisapproveAgentProposalCommand
    {
        public string ProposalId { get; set; }
        public string DissaprovedById { get; set; }
    }

    public class SendAgentProposalForApprovalCommand
    {
        public string ProposalId { get; set; }
        public string SentById { get; set; }
    }

    public class SendAgentProposalToAgentCommand
    {
        public string ProposalId { get; set; }
        public string SentById { get; set; }
    }

    public class DuplicateProposalCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public Proposal Proposal { get; set; }
        public ProductId NewProductId { get; set; }
        public string DuplicatedFromProposalId { get; set; }
        public string DuplicatedById { get; set; }
    }

    public class SendAgentProposalToClientCommand
    {
        public string ProposalId { get; set; }
        public string SentById { get; set; }
    }

    public class RejectAgentProposalCommand
    {
        public string ProposalId { get; set; }
        public IEnumerable<string> Codes { get; set; }
        public string Remarks { get; set; }
        public string RejectedById { get; set; }
    }

    public class AddAssociatedContractCommand
    {
        public string Id { get; set; }
        public string ContractId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveAssociatedContractCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class BeneficiaryEligibility : SystemObject
    {
        public string Id { get; set; }
        public Entity ContractEntity { get; set; }
        public string BenefitTypeId { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }
    }

    public class AddBeneficiaryEligibilityCommand
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string BenefitTypeId { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }
        public string AddedById { get; set; }
        public decimal Flat { get; set; }  //Note: not accepted in input, not in output object
        public CurrencyCode CurrencyCode { get; set; } //Note: not accepted in input, not in output object
    }

    public class UpdateBeneficiaryEligibilityCommand
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string BenefitTypeId { get; set; }
        public decimal Ratio { get; set; }
        public string Notes { get; set; }
        public bool IsRevocable { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public bool IsBenefitTypeIdChanged { get; set; }
        public bool IsRatioChanged { get; set; }
        public bool IsNotesChanged { get; set; }
        public bool IsIsRevocableChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class PaymentInfo : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string PayorId { get; set; }
        public PaymentFrequency Frequency { get; set; }
        public PaymentMethod Method { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Comment { get; set; }
    }

    public class AddPaymentInfoCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string PayorId { get; set; }
        public PaymentFrequency Frequency { get; set; }
        public PaymentMethod Method { get; set; }
        public decimal Amount { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Comment { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdatePaymentInfoCommand
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public string PayorId { get; set; }
        public bool IsPayorIdChanged { get; set; }
        public PaymentFrequency Frequency { get; set; }
        public bool IsFrequencyChanged { get; set; }
        public PaymentMethod Method { get; set; }
        public bool IsMethodChanged { get; set; }
        public decimal Amount { get; set; }
        public bool IsAmountChanged { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string Comment { get; set; }
        public bool IsCommentChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class ConvertOfferToApplicationCommand
    {
        public string CaseId { get; set; }
        public string ProposalId { get; set; }
        public string ClientId { get; set; }
        public string AcceptedById { get; set; }
    }

    public enum PaymentFrequency
    {
        OneTime,
        Monthly,
        Quarterly,
        SemiAnnually,
        Annually
    }

    public static class CaseStatus
    {
        public const string Submitted = "REQUEST_SUBMITTED";
        public const string InProgress = "IN_PROGRESS";
        public const string OfferGenerated = "OFFER_GENERATED";
        public const string AwaitingClientFeedback = "AWAITING_CLIENT_FEEDBACK";
        public const string OfferAccepted = "OFFER_ACCEPTED";
        public const string OfferRejected = "OFFER_REJECTED";

        public const string Issued = "ISSUED";
    }

    public static class ProposalStatus
    {
        public const string InProgress = "IN_PROGRESS";
        public const string OfferForReview = "OFFER_FOR_REVIEW";
        public const string OfferApproved = "OFFER_APPROVED";
        public const string OfferGenerated = "OFFER_GENERATED";
        public const string AwaitingClientFeedback = "AWAITING_CLIENT_FEEDBACK";
        public const string OfferAccepted = "OFFER_ACCEPTED";
        public const string OfferRejected = "OFFER_REJECTED";
    }

}
