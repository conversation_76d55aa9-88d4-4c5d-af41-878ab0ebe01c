using System.Collections.Generic;
using System.Linq;

namespace CoverGo.Cases.Domain.Custom;

public class EventCustomRules : IEventCustomRules
{
    private readonly IEnumerable<IEventCustomProcessingRule> _rules;

    public EventCustomRules(IEnumerable<IEventCustomProcessingRule> rules)
    {
        _rules = rules;
    }

    public void Apply(string tenantId, Case @case, CaseEvent evt)
    {
        var rulesToApply = _rules.Where(x => x.ShouldApply(tenantId, @case, evt));

        foreach (var rule in rulesToApply)
        {
            rule.Apply(@case, evt);
        }
    }
}