using System.Linq;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain.Custom.Bupa;

public class BupaProposalStatusUpdateWhenOfferAccepted : IEventCustomProcessingRule
{
    public void Apply(Case @case, CaseEvent evt)
    {
        var command = evt.Values.ToObject<UpdateOfferCommand>();

        var proposal = @case.Proposals.FirstOrDefault(x => x.Id == command.ProposalId);
        if (proposal == null) return;

        var offer = proposal.Basket.FirstOrDefault(x => x.Id == command.OfferId);
        if (offer == null) return;

        if (offer.Status == "Accepted")
        {
            @case.Fields ??= JObject.Parse("{}");
            @case.Fields["quotationStatus"] = "Accepted";
            proposal.Status = "Accepted";
        }
    }
    public bool ShouldApply(string tenantId, Case @case, CaseEvent evt)
    {
        if (tenantId.ToLower().IndexOf("bupa") < 0) return false;
        return evt.Type == CaseEventType.updateOffer;
    }
}