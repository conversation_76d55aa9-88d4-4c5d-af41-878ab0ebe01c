﻿namespace CoverGo.Cases.Domain
{
    public class Stakeholder
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string Type { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
    }

    public class AddStakeholderCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string Type { get; set; }
        public string AddedById { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
    }

    public class UpdateStakeholderCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string EntityId { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveStakeholderCommand
    {
        public string Id { get; set; }
        public string RemovedById { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }
}
