﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.OfferStatusPermissions
{
    public record TransitionValidationResult(bool IsValid, string Message = null)
    {
        public static TransitionValidationResult Valid() => new(true);
        public static TransitionValidationResult Invalid(string message) => new(false, message);
    }
}
