﻿using System.Security.Claims;

namespace CoverGo.Cases.Domain.OfferStatusPermissions.Bupa
{
    public class BupaOfferStatusTransitionPermissionValidator : IOfferStatusTransitionPermissionValidator
    {
        private const string AdminRoleName = "admin";
        private const string PresalesUserRoleName = "BD/Pre-Sales";
        private const string SalesUserRoleName = "Sales";
        private const string UnderwriterRoleName = "Underwriting/Pricing";

        public TransitionValidationResult IsValidStatusTransitionForOffer(string currentStatus, string nextStatus, ClaimsIdentity identity)
        {

            if (string.IsNullOrWhiteSpace(nextStatus))
                return TransitionValidationResult.Valid();
            TransitionValidationResult transitionValidationResult = IsValidTransition(currentStatus, nextStatus, identity);
            if (transitionValidationResult != null)
                return transitionValidationResult;
            return TransitionValidationResult.Invalid($"Change of offer status from {currentStatus} to {nextStatus} is not allowed by your user role.");
        }

        private static TransitionValidationResult IsValidTransition(string currentStatus, string nextStatus, ClaimsIdentity identity)
        {
            bool isSalesSuperUser = identity.HasClaim(identity.RoleClaimType, PresalesUserRoleName) || identity.HasClaim(identity.RoleClaimType, AdminRoleName);
            bool isUnderwriter = identity.HasClaim(identity.RoleClaimType, UnderwriterRoleName) || identity.HasClaim(identity.RoleClaimType, AdminRoleName);
            bool isSalesUser = isSalesSuperUser || identity.HasClaim(identity.RoleClaimType, SalesUserRoleName) || identity.HasClaim(identity.RoleClaimType, AdminRoleName);
            return (currentStatus, nextStatus, isSalesUser, isUnderwriter) switch
            {
                (BupaOfferStatus.Added, BupaOfferStatus.Added, true, _) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.Added, BupaOfferStatus.UnderwriterRequested, true, _) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterRequested, true, _) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.Added, true, _) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterRequested, BupaOfferStatus.UnderwriterInProgress, _, true) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.UnderwriterInProgress, _, true) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.UnderwriterApproved, _, true) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterInProgress, BupaOfferStatus.Added, _, true) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.UnderwriterApproved, _, true) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.Sent, _, true) => TransitionValidationResult.Valid(),
                (BupaOfferStatus.UnderwriterApproved, BupaOfferStatus.UnderwriterInProgress, _, true) => TransitionValidationResult.Valid(),
                _ => TransitionValidationResult.Invalid($"Change of offer status from {currentStatus} to {nextStatus} is not allowed by your user role."),
            };
        }
    }
}
