﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.OfferStatusPermissions.Bupa
{
    public static class BupaOfferStatus
    {
        public const string Added = nameof(Added);
        public const string UnderwriterRequested = nameof(UnderwriterRequested);
        public const string UnderwriterInProgress = nameof(UnderwriterInProgress);
        public const string UnderwriterApproved = nameof(UnderwriterApproved);
        public const string Sent = nameof(Sent);
    }
}
