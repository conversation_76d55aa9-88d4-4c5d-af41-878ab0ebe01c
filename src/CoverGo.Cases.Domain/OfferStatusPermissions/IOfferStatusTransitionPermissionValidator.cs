﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.OfferStatusPermissions
{
    public interface IOfferStatusTransitionPermissionValidator
    {
        TransitionValidationResult IsValidStatusTransitionForOffer(string currentStatus, string nextStatus, ClaimsIdentity identity);
    }
}
