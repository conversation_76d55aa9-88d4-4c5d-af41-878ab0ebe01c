﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;

namespace CoverGo.Cases.Domain
{
    public class Offer : SystemObject
    {
        public const string MetaFieldsKey = "__metaFields";

        public string Id { get; set; }
        public string CaseId { get; set; }
        public string ProposalId { get; set; }
        public string Status { get; set; }
        public string OfferNumber { get; set; }
        public string PolicyNumber { get; set; } // used when generating policies
        public JToken Values { get; set; }
        public ProductId ProductId { get; set; }
        public List<BenefitOption> BenefitOptions { get; set; }
        public Premium Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public List<Exclusion> Exclusions { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<Clause> Clauses { get; set; } = new List<Clause>();
        public List<JacketInstance> Jackets { get; set; } = new List<JacketInstance>();
        public List<Fact> Facts { get; set; } = new List<Fact>();
        public List<Stakeholder> Stakeholders { get; set; }
        public List<DetailedEventLog> Events { get; set; }
        public List<AssociatedContract> AssociatedContracts { get; set; }
        public List<Commission> Commissions { get; set; } = new List<Commission>();

        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public JToken Fields2 { get; set; }
        public string FieldsSchemaId { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; } = new();
        public string DistributorID { get; set; }
        public List<string> CampaignCodes { get; set; } = new();

        public void InjectMetaFieldsToFields2(JToken metaFields)
        {
            if (Fields2 == null) return;

            // Remove everything inside existing metaFields (if any)
            JObject obj = Fields2.ToObject<JObject>();
            obj?.Property(MetaFieldsKey)?.Remove();
            if (obj != null) Fields2 = JToken.FromObject(obj);

            // Override metaFields with incoming metaFields in form of JToken
            if (metaFields != null && metaFields.HasValues)
                Fields2[MetaFieldsKey] = metaFields;
        }
    }
    public class OfferEventWhere : Where
    {
        public string Id { get; set; }
        public string CaseId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }

    public class AssociatedContract
    {
        public string Id { get; set; }
        public string ContractId { get; set; }
    }

    public class BenefitOption : SystemObject
    {
        public string TypeId { get; set; }
        public string Key { get; set; }
        public JToken Value { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string InsuredId { get; set; }
    }

    public class AddOfferCommand
    {
        public string OfferId { get; set; }
        public string ProposalId { get; set; }
        public string OfferNumber { get; set; }
        public string OfferNumberType { get; set; }
        public string PolicyNumber { get; set; } // used when generating policies
        public string Status { get; set; }
        public JToken Values { get; set; }
        public ProductId ProductId { get; set; }
        public List<BenefitOption> BenefitOptions { get; set; }
        public PremiumInput Premium { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public string AddedById { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
        public string DistributorID { get; set; }
        public List<string> CampaignCodes { get; set; }
    }

    public class UpdateOfferCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Status { get; set; }
        public bool IsStatusChanged { get; set; }
        public string OfferNumber { get; set; }
        public bool IsOfferNumberChanged { get; set; }
        public string PolicyNumber { get; set; } // used when generating policies
        public bool IsPolicyNumberChanged { get; set; }
        public JToken Values { get; set; }
        public bool IsValuesChanged { get; set; }
        public bool IsProductIdChanged { get; set; }
        public ProductIdToUpdate ProductId { get; set; }
        public bool IsPremiumChanged { get; set; }
        public PremiumToUpdate Premium { get; set; }
        public bool? IsPremiumOverridden { get; set; }
        public string ModifiedById { get; set; }
        public DateTime? StartDate { get; set; }
        public bool IsStartDateChanged { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsEndDateChanged { get; set; }
        public string Pricing { get; set; }
        public bool IsPricingChanged { get; set; }
        public string Underwriting { get; set; }
        public bool IsUnderwritingChanged { get; set; }
        public string Fields { get; set; }
        public bool IsFieldsChanged { get; set; }
        public string FieldsSchemaId { get; set; }
        public bool IsFieldsSchemaIdChanged { get; set; }
        public string ProductTreeId { get; set; }
        public bool IsProductTreeIdChanged { get; set; }
        public string DistributorID { get; set; }
        public bool IsDistributorIDChanged { get; set; }
        public List<string> CampaignCodes { get; set; }
        public bool IsCampaignCodesChanged { get; set; }
    }

    public class PremiumToUpdate : PremiumInput
    {
        public bool IsAmountChanged { get; set; }
        public bool IsGrossAmountChanged { get; set; }
        public bool IsCurrencyCodeChanged { get; set; }
        public bool IsDiscountCodesChanged { get; set; }
        public bool IsIsPricedAtStartDateChanged { get; set; }
    }

    public class AddDiscountToOfferCommand
    {
        public string DiscountId { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Name { get; set; }
        public int Order { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public CurrencyCode CurrencyCode { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateDiscountOfOfferCommand
    {
        public string ProposalId { get; set; }
        public string DiscountId { get; set; }
        public string OfferId { get; set; }
        public string Name { get; set; }
        public bool IsNameChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public bool IsCalculationJsonLogicChanged { get; set; }

        public string ModifiedById { get; set; }
    }

    public class RemoveDiscountFromOfferCommand
    {
        public string ProposalId { get; set; }
        public string DiscountId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddLoadingCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Code { get; set; }
        public decimal? Ratio { get; set; }
        public decimal? Flat { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public int Order { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateLoadingCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Code { get; set; }
        public bool IsCodeChanged { get; set; }
        public decimal? Ratio { get; set; }
        public bool IsRatioChanged { get; set; }
        public decimal? Flat { get; set; }
        public bool IsFlatChanged { get; set; }
        public JObject CalculationJsonLogic { get; set; }
        public bool IsCalculationJsonLogicChanged { get; set; }
        public int Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveLoadingCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string RemovedById { get; set; }
    }

    public class AddExclusionCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Code { get; set; }
        public string BenefitParentTypeId { get; set; }
        public string BenefitTypeId { get; set; }
        public string BenefitOptionKey { get; set; }
        public string Remark { get; set; }
        public string AddedById { get; set; }
    }

    public class RemoveExclusionCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string RemovedById { get; set; }
    }

    public class Commission : SystemObject
    {
        public string Id { get; set; }
        public string EntityId { get; set; }
        public string JsonRule { get; set; }
        public string Remark { get; set; }
    }

    public class AddCommissionCommand
    {
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string AddedById { get; set; }
        public string EntityId { get; set; }
        public string JsonRule { get; set; }
        public string Remark { get; set; }
    }

    public class UpdateCommissionCommand
    {
        public string ModifiedById { get; set; }
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string JsonRule { get; set; }
        public bool IsJsonRuleChanged { get; set; }
        public string EntityId { get; set; }
        public bool IsEntityIdChanged { get; set; }
        public string Remark { get; set; }
        public bool IsRemarkChanged { get; set; }
    }

    public class RemoveCommissionCommand
    {
        public string RemovedById { get; set; }
        public string Id { get; set; }
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
    }

    public class UpsertBenefitOptionCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string TypeId { get; set; } //Note: Should be benefitTypeId
        public string Key { get; set; }
        public JToken Value { get; set; }
        public string InsuredId { get; set; }

        public string UpsertedById { get; set; }
    }

    public class RemoveBenefitOptionCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string TypeId { get; set; } //Note: Should be benefitTypeId
        public string RemovedById { get; set; }
    }
}
