using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain;

public class Proposal : SystemObject
{
    public const string MetaFieldsIssuedAtKey = "proposalIssuedAt";

    public string Id { get; set; }
    public string CaseId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Status { get; set; }
    public string ProposalNumber { get; set; }
    public string ReferralCode { get; set; }
    public bool IsIssued { get; set; }
    public string IssuedById { get; set; }
    public DateTime? IssuedAt { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public IEnumerable<Note> Notes { get; set; } = Enumerable.Empty<Note>();
    public IEnumerable<Offer> Basket { get; set; } = Enumerable.Empty<Offer>();
    public Premium TotalPrice { get; set; }
    public IEnumerable<string> PolicyIds { get; set; } = Enumerable.Empty<string>();
    public IEnumerable<Stakeholder> Stakeholders { get; set; }
    public RenewalHistory RenewalHistory { get; set; }
    public bool IsRejected { get; set; }
    public IEnumerable<string> RejectionCodes { get; set; }
    public string RejectionRemarks { get; set; }
    public string RejectedById { get; set; }
    public bool IsAccepted { get; set; }
    public string AcceptedById { get; set; }
    public bool IsApprovalNeeded { get; set; }
    public string DuplicatedFromProposalId { get; set; }

    public JProperty BuildProposalIssuedAtJProperty()
    {
        if (!IsIssued) return null;

        string issueTimeStr = IssuedAt!.Value.ToString("yyyy/MM/dd HH:mm:ss", CultureInfo.InvariantCulture);
        JProperty metaFields = new(MetaFieldsIssuedAtKey, issueTimeStr);

        return metaFields;
    }
}

public class RenewalHistory
{
    public string RenewedFromId { get; set; }
    public string RenewedToId { get; set; }
    public int RenewalCount { get; set; }
}
