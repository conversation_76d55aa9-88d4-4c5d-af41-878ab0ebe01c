﻿using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace CoverGo.Cases.Domain
{
    public class Fact
    {
        public string Id { get; set; }
        public string Type { get; set; }
        public JToken Value { get; set; }
    }

    public class AddFactCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }
        public JToken Value { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateFactCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string Type { get; set; }
        public bool IsTypeChanged { get; set; }
        public JToken Value { get; set; }
        public bool IsValueChanged { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveFactCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string Id { get; set; }
        public string RemovedById { get; set; }
    }

    public class FactCommandBatch
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public List<AddFactCommand> AddFactCommands { get; set; }
        public List<UpdateFactCommand> UpdateFactCommands { get; set; }
    }
}
