﻿using System;
using System.Collections.Generic;
using CoverGo.Templates.Client;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public class Clause
    {
        public string Id { get; set; }
        public int Order { get; set; }
        public string TemplateId { get; set; }
        public bool StoreTemplateByValue { get; set; }
        public Template Template { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public string HtmlOverride { get; set; }
        public string  Type { get; set; }
    }

    public class AddClauseCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string ClauseId { get; set; }
        public int Order { get; set; }
        public string TemplateId { get; set; }
        public Template Template { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public string HtmlOverride { get; set; }
        public string Type { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateClauseCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string ClauseId { get; set; }
        public int? Order { get; set; }
        public bool IsOrderChanged { get; set; }
        public string TemplateId { get; set; }
        public bool IsTemplateIdChanged { get; set; }
        public Template Template { get; set; }
        public bool IsTemplateChanged { get; set; }
        public RenderParameters RenderParameters { get; set; }
        public bool IsRenderParametersChanged { get; set; }
        public string HtmlOverride { get; set; }
        public bool IsHtmlOverrideChanged { get; set; }
        public bool IsTypeChanged { get; set; }
        public string Type { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveClauseCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string ClauseId { get; set; }
        public string RemovedById { get; set; }
    }

    public class ClauseCommandBatch
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public List<AddClauseCommand> AddClauseCommands { get; set; }
        public List<UpdateClauseCommand> UpdateClauseCommands { get; set; }
    }

    public class RenderParameters
    {
        public string Name { get; set; }
        public JToken Content { get; set; }
        public string Url { get; set; }
        public string AccessToken { get; set; }
        public JObject Variables { get; set; }
    }
}
