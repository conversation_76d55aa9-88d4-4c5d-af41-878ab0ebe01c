﻿using Newtonsoft.Json.Linq;

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain
{
    public interface IReferenceGenerator
    {
        string ProviderId { get; }

        Task<string> GenerateAsync(string tenantId, string type, JToken input, string accessToken = null, GraphQlVariables variables = null, CancellationToken cancellationToken = default);
    }

    public class ReferenceGeneratorConfig
    {
        public string Type { get; set; }
        public string Format { get; set; }
        public List<FormatArgument> Arguments { get; set; } = new List<FormatArgument>();
    }

    public class FormatArgument
    {
        public int Order { get; set; }
        public string Format { get; set; } = string.Empty;
        public Dictionary<string, string> Dictionary { get; set; }
        public string Type { get; set; }
        public string SharedCounterKey { get; set; }
        public string SharedCounterScope { get; set; }
        public string GraphQlQuery { get; set; } // only used for graphql
        public string JsonPath { get; set; } // only used for graphql, input
        public string ExpectedType { get; set; } // only used for graphql, input
        public int AddOpOnResult { get; set; } // only used for graphql, input
        public string SplitOn { get; set; } // only used for graphql, input
        public int SplitIndex { get; set; } // only used for graphql, input
        public string DateTimeOffset { get; set; } // only used for input

        public int CurrentIncrement { get; set; } // only used for incrementor
    }

    public class GraphQlVariables
    {
        public CaseWhere CaseWhere1 { get; set; }
        public ProposalWhere ProposalWhere1 { get; set; }
        public ProposalWhere IsRenewalProposalWhere1 { get; set; }
        public ProposalWhere RenewFromProposalWhere { get; set; }
        public OfferWhere OfferWhere1 { get; set; }
        public string TypeId { get; set; }
    }
}
