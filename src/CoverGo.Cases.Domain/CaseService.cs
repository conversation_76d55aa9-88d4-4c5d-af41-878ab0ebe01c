using CoverGo.Applications.AttachedRules;
using CoverGo.Cases.Domain.AgentAssignment;
using CoverGo.Cases.Domain.Custom;
using CoverGo.Cases.Domain.HandlerAssignment;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.ChannelManagement.Client;
using CoverGo.DomainUtils;
using CoverGo.JsonUtils;
using CoverGo.Threading.Tasks;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.FeatureManagement;
using Entity = CoverGo.Cases.Domain.OtherServices.Entity;
using EventQuery = CoverGo.DomainUtils.EventQuery;
using IChannelManagementClient = CoverGo.ChannelManagement.Client.IChannelManagementClient;
using IPoliciesClient = CoverGo.Policies.Client.IPoliciesClient;

namespace CoverGo.Cases.Domain
{
    public class CaseService : ICaseService
    {
        private readonly EventStoreResolver _eventStoreResolver;
        private readonly CaseRepositoryResolver _caseRepositoryResolver;
        private readonly ReferenceGeneratorResolver _referenceGeneratorResolver;
        private readonly IPricingService _pricingService;
        [Obsolete("use CoverGo.Policies.Client.IPoliciesClient directly")]
        private readonly IPolicyService _policyService;
        private readonly IPoliciesClient _policyClient;
        private readonly INotificationService _notificationService;
        private readonly IAuthService _authService;
        private readonly ITransactionService _transactionService;
        private readonly IJsonProjector _jsonProjector;
        private readonly IEventCustomRules _eventCustomRules;
        private readonly JsonSerializer _jsonSerializer;
        private readonly IEnumerable<IAttachedRulesExecutor> _attachedRulesExecutors;
        private readonly IMessageBrokerClient _messageBrokerClient;
        private readonly IChannelManagementClient _channelManagmentClient;
        private readonly ILogger<CaseService> _logger;
        private readonly IMultiTenantFeatureManager _featureManager;

        private readonly List<CaseEventType> _addEvents = new List<CaseEventType>
        {
            CaseEventType.addProposal,
            CaseEventType.addOffer,
            CaseEventType.addFactToCase,
            CaseEventType.addNoteToCase
        };

        private readonly List<CaseEventType> _removeEvents = new List<CaseEventType>
        {
            CaseEventType.removeProposal,
            CaseEventType.removeOffer,
            CaseEventType.removeFactFromCase,
            CaseEventType.removeNoteFromCase
        };

        public CaseService(
            EventStoreResolver eventStoreResolver,
            CaseRepositoryResolver caseRepositoryResolver,
            ReferenceGeneratorResolver referenceGeneratorResolver,
            IPricingService pricingService,
            IPolicyService policyService,
            IPoliciesClient policyClient,
            INotificationService notificationService,
            IAuthService authService,
            ITransactionService transactionService,
            IJsonProjector jsonProjector,
            IEventCustomRules eventCustomRules,
            IEnumerable<IAttachedRulesExecutor> attachedRulesExecutors,
            IMessageBrokerClient messageBrokerClient,
            IChannelManagementClient channelManagementClient,
            ILogger<CaseService> logger,
            IMultiTenantFeatureManager featureManager)
        {
            _eventStoreResolver = eventStoreResolver;
            _caseRepositoryResolver = caseRepositoryResolver;
            _referenceGeneratorResolver = referenceGeneratorResolver;
            _pricingService = pricingService;
            _policyService = policyService;
            _policyClient = policyClient;
            _notificationService = notificationService;
            _authService = authService;
            _transactionService = transactionService;
            _jsonProjector = jsonProjector;
            _eventCustomRules = eventCustomRules;
            _messageBrokerClient = messageBrokerClient;
            _channelManagmentClient = channelManagementClient;
            _logger = logger;
            _featureManager = featureManager;

            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                NullValueHandling = NullValueHandling.Ignore,
            };
            settings.Converters.Add(new StringEnumConverter());
            _jsonSerializer = JsonSerializer.Create(settings);
            _attachedRulesExecutors = attachedRulesExecutors;
        }

        public async Task<IEnumerable<Case>> GetAsync(string tenantId, QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            if (queryArguments.AsOf == null)
            {
                List<Case> casesList = (await repository.GetAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken)).ToList();
                return casesList;
            }

            IEnumerable<Case> cases = await repository.GetAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);

            IEventStore eventStore = _eventStoreResolver(tenantId);
            IEnumerable<CaseEvent> events = await eventStore.GetEventsAsync(tenantId, null, cases.Select(p => p.Id), null, queryArguments.AsOf, cancellationToken);
            (IEnumerable<Case> handledCases, IEnumerable<string> deletedCaseIds) = await HandleCases(tenantId, events, cancellationToken);

            List<Case> handledCasesList = handledCases.ToList();
            if (!handledCasesList.Any())
                return handledCasesList;

            handledCasesList.ForEach(c => c.InjectMetaFieldsToOfferFields2());

            return handledCasesList;
        }

        public async Task<IEnumerable<CasesReport>> GetReportAsync(string tenantId, QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            IEnumerable<JToken> jTokens = await repository.GetReportAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);

            return jTokens.Select(e => e.ToObject<CasesReport>(_jsonSerializer));
        }

        public async Task<IEnumerable<string>> GetIdsAsync(string tenantId, QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            IEnumerable<string> ids = await repository.GetIdsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
            return ids;
        }

        public IEnumerable<string> GetIds(string tenantId, QueryArguments<CaseWhere> queryArguments)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            IEnumerable<string> ids = repository.GetIds(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First);
            return ids;
        }

        public async Task<IEnumerable<string>> GetProposalIdsAsync(string tenantId, QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            IEnumerable<string> ids = await repository.GetProposalIdsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
            return ids;
        }

        public async Task<IEnumerable<string>> GetOfferIdsAsync(string tenantId, QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            IEnumerable<string> ids = await repository.GetOfferIdsAsync(tenantId, queryArguments.Where, queryArguments.OrderBy, queryArguments.Skip, queryArguments.First, cancellationToken);
            return ids;
        }

        public async Task<IEnumerable<DetailedEventLog>> GetEventLogsAsync(string tenantId, EventQuery query, CancellationToken cancellationToken)
        {
            IEventStore eventStore = _eventStoreResolver(tenantId);
            IEnumerable<CaseEvent> events = await eventStore.GetEventsAsync(tenantId, null, query.Ids, cancellationToken: cancellationToken);

            IEnumerable<DetailedEventLog> eventLogs = HandleLogs(events);
            return eventLogs;
        }

        public async Task<long> GetTotalCountAsync(string tenantId, CaseWhere where, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            return await repository.GetTotalCountAsync(tenantId, where, cancellationToken);
        }

        private async Task<Result> AddEventAndReplayAsync(string tenantId, CaseEvent caseEvent, CancellationToken cancellationToken)
        {
            IEnumerable<Case> cases = await _caseRepositoryResolver(tenantId).GetAsync(tenantId, new CaseWhere { Id = caseEvent.CaseId }, null, null, null, cancellationToken);
            Case state = cases.FirstOrDefault();
            EnsureReadOnly();

            IEventStore eventStore = _eventStoreResolver(tenantId);
            Result result = await eventStore.AddEventAsync(tenantId, caseEvent, cancellationToken);

            try
            {
                if (result.Status == "success")
                    await ReplayAsync(tenantId, new[] { caseEvent.CaseId }, cancellationToken);

                if (tenantId.StartsWith("walaa")) return result; // Not for Walaa, please. The guy below spends ~150ms on something meaningless to Walaa.

                var rulesExecutor = _attachedRulesExecutors?.FirstOrDefault(x => x.Type == AttachedRuleType.OnEventApiCall) as IAttachedRulesExecutor<Result>;

                if (rulesExecutor is null) return result;

                Case @case = (await GetAsync(tenantId, new() { Where = { Id = caseEvent.CaseId } }, cancellationToken)).FirstOrDefault();
                Result rulesExecutionResult = await rulesExecutor.Execute(@case, caseEvent);

                if (rulesExecutionResult.Errors?.Any() ?? false)
                {
                    var errors = rulesExecutionResult.Errors.ToList();
                    if (result.Errors?.Any() ?? false) errors.AddRange(result.Errors);
                    result.Errors = errors;
                }
                return result;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error adding event and replaying");
                await eventStore.DeleteInvalidEventAsync(tenantId, caseEvent.Id, cancellationToken);
                return Result.Failure(e.Message);
            }

            void EnsureReadOnly()
            {
                if (state == null) return;
                if (caseEvent.Type == CaseEventType.setReadOnly) return;
                if (state.IsReadOnly) throw new InvalidOperationException("Unable to modify read only case");
            }
        }

        public async Task ReplayAllAsync(string tenantId, CancellationToken cancellationToken)
        {
            IEnumerable<string> startsWiths = Enumerable.Range(0, 256).Select(i => i.ToString("x2"));
            IEventStore eventStore = _eventStoreResolver(tenantId);
            foreach (string startsWith in startsWiths)
            {
                IEnumerable<CaseEvent> events = await eventStore.GetCaseEventsStartingWith(tenantId, startsWith, cancellationToken);
                await HandleAndUpsertAsync(tenantId, events, cancellationToken);
            }
        }

        public async Task ReplayAsync(string tenantId, IEnumerable<string> ids, CancellationToken cancellationToken)
        {
            IEventStore eventStore = _eventStoreResolver(tenantId);
            IEnumerable<CaseEvent> events = await eventStore.GetEventsAsync(tenantId, null, ids, cancellationToken: cancellationToken);
            await HandleAndUpsertAsync(tenantId, events, cancellationToken);
        }

        private async Task HandleAndUpsertAsync(string tenantId, IEnumerable<CaseEvent> events, CancellationToken cancellationToken)
        {
            (IEnumerable<Case> cases, IEnumerable<string> deletedIds) = await HandleCases(tenantId, events, cancellationToken);
            ICaseRepository repository = _caseRepositoryResolver(tenantId);

            var parallelOptions = new ParallelRunOptions()
            {
                MaxDegreeOfParallelism = 10,
                CancellationToken = cancellationToken
            };

            await deletedIds.ParallelForEachAsync(async (deletedTransactionId, ct) =>
                await repository.DeleteAsync(tenantId, deletedTransactionId, ct), parallelOptions);

            await cases.ParallelForEachAsync(async (@case, ct) => await repository.UpsertAsync(tenantId, @case.Id, @case, ct),
                parallelOptions);
        }

        #region handlers

        private async Task<(IEnumerable<Case>, IEnumerable<string>)> HandleCases(string tenantId, IEnumerable<CaseEvent> events, CancellationToken cancellationToken)
        {
            var cases = new List<Case>();
            var deletedIds = new List<string>();

            foreach (IGrouping<string, CaseEvent> eventsByCaseId in events.GroupBy(e => e.CaseId))
            {
                if (eventsByCaseId.Any(e => e.Type == CaseEventType.delete))
                {
                    deletedIds.Add(eventsByCaseId.Key);
                    continue;
                }

                Case @case = HandleCreateEvents(eventsByCaseId);
                if (@case == null)
                    continue;

                @case.LastModifiedAt = eventsByCaseId.Max(e => e.Timestamp);
                @case.LastModifiedById = GetById(eventsByCaseId.FirstOrDefault(e => e.Timestamp == eventsByCaseId.Max(e => e.Timestamp)));

                foreach (CaseEvent e in eventsByCaseId)
                {
                    if (e.Type == CaseEventType.update || e.Type == CaseEventType.addIssuedPolicy || e.Type == CaseEventType.setSystemDates || e.Type == CaseEventType.setReadOnly)
                        HandleUpdateEvent(@case, e);
                    if (e.Type == CaseEventType.addProposal || e.Type == CaseEventType.issueProposal || e.Type == CaseEventType.rejectProposal || e.Type == CaseEventType.updateProposal || e.Type == CaseEventType.removeProposal || e.Type == CaseEventType.singleOfferProposalAcceptance)
                        HandleProposalEvent(@case, e);
                    if (e.Type == CaseEventType.addStakeholderToProposal || e.Type == CaseEventType.updateStakeholderOfProposal || e.Type == CaseEventType.removeStakeholderFromProposal)
                        HandleProposalStakeholderEvent(@case.Proposals, e);
                    if (e.Type == CaseEventType.addOffer || e.Type == CaseEventType.updateOffer || e.Type == CaseEventType.removeOffer || e.Type == CaseEventType.recordQuote)
                        HandleOfferEvent(@case, e);
                    if (e.Type == CaseEventType.createAgentProposal || e.Type == CaseEventType.updateAgentProposal || e.Type == CaseEventType.sendAgentProposalForApproval || e.Type == CaseEventType.acceptAgentProposal || e.Type == CaseEventType.approveAgentProposal || e.Type == CaseEventType.sendAgentProposalToAgent || e.Type == CaseEventType.sendAgentProposalToClient || e.Type == CaseEventType.disapproveAgentProposal || e.Type == CaseEventType.rejectAgentProposal || e.Type == CaseEventType.duplicateProposal)
                        HandleAgentProposalEvent(@case, e);
                }
                HandleOfferStakeholderEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addStakeholderToOffer || e.Type == CaseEventType.updateStakeholderOfOffer || e.Type == CaseEventType.removeStakeholderFromOffer));
                HandleOfferDiscountEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addDiscountToOffer || e.Type == CaseEventType.updateDiscountOfOffer || e.Type == CaseEventType.removeDiscountFromOffer));
                HandleOfferLoadingEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addLoadingToOffer || e.Type == CaseEventType.updateLoadingOfOffer || e.Type == CaseEventType.removeLoadingFromOffer));
                HandleOfferExclusionEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addExclusionToOffer || e.Type == CaseEventType.removeExclusionFromOffer));
                HandleOfferBenefitOptionEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.upsertBenefitOptionOfOffer || e.Type == CaseEventType.removeBenefitOptionFromOffer));
                HandleOfferClauseEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addClauseToOffer || e.Type == CaseEventType.updateClauseOfOffer || e.Type == CaseEventType.removeClauseFromOffer || e.Type == CaseEventType.offerClauseBatch));
                HandleOfferJacketEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.offerJacketBatch || e.Type == CaseEventType.removeJacketFromOffer));
                HandleOfferFactEvents(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.offerFactBatch || e.Type == CaseEventType.addFactToOffer || e.Type == CaseEventType.updateFactOfOffer || e.Type == CaseEventType.removeFactFromOffer));
                HandleAssociatedContracts(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addAssociatedContractToOffer || e.Type == CaseEventType.removeAssociatedContractFromOffer));
                HandleCommissions(@case.Proposals, eventsByCaseId.Where(e => e.Type == CaseEventType.addCommissonToOffer || e.Type == CaseEventType.updateCommissionOfOffer || e.Type == CaseEventType.removeCommissionFromOffer));
                @case.Facts = HandleFactEvents(eventsByCaseId.Where(e => e.Type == CaseEventType.addFactToCase || e.Type == CaseEventType.updateFactOfCase || e.Type == CaseEventType.removeFactFromCase || e.Type == CaseEventType.caseFactBatch));
                @case.Notes = HandleNoteEvents(eventsByCaseId.Where(e => e.Type == CaseEventType.addNoteToCase || e.Type == CaseEventType.updateNoteOfCase || e.Type == CaseEventType.removeNoteFromCase));
                @case.Stakeholders = HandleStakeholderEvents(@case, eventsByCaseId.Where(e => e.Type == CaseEventType.addStakeholderToCase || e.Type == CaseEventType.updateStakeholderOfCase || e.Type == CaseEventType.removeStakeholderFromCase));
                @case.Agents = HandleAgentAssignmentEvents(@case, eventsByCaseId.Where(e => e.Type == CaseEventType.assignAgent || e.Type == CaseEventType.unassignAgents));
                @case.Handlers = HandleHandlerAssignmentEvents(@case, eventsByCaseId.Where(e => e.Type == CaseEventType.assignHandler || e.Type == CaseEventType.unassignHandlers));
                @case.BeneficiaryEligibilities = HandleBeneficiaryEligibilities(eventsByCaseId.Where(e => e.Type == CaseEventType.addBeneficiaryEligibilityToCase || e.Type == CaseEventType.updateBeneficiaryEligibilityOfCase || e.Type == CaseEventType.removeBeneficiaryEligibilityFromCase));
                @case.PaymentInfos = HandlePaymentInfos(eventsByCaseId.Where(e => e.Type == CaseEventType.addPaymentInfoToCase || e.Type == CaseEventType.updatePaymentInfoOfCase || e.Type == CaseEventType.removePaymentInfoFromCase));


                PopulateProposalRenewalHistoryCount(@case.Proposals.ToList());

                IEnumerable<CaseEvent> generatePoliciesEvents = eventsByCaseId.Where(e => e.Type == CaseEventType.generatePolicies);
                foreach (CaseEvent generatePoliciesEvent in generatePoliciesEvents)
                {
                    GeneratePoliciesFromProposalCommand generatePoliciesCommand = generatePoliciesEvent.Values?.ToObject<GeneratePoliciesFromProposalCommand>(_jsonSerializer);
                    Proposal proposal = @case.Proposals.FirstOrDefault(p => p.Id == generatePoliciesCommand.ProposalId);

                    if (proposal == null)
                        continue;

                    proposal.PolicyIds = proposal.PolicyIds.Concat(generatePoliciesCommand.PolicyIds);

                    if (generatePoliciesCommand.OffersToAccept != null)
                    {
                        foreach (Offer offer in proposal.Basket.Where(o => generatePoliciesCommand.OffersToAccept.Contains(o.Id)))
                        {
                            offer.Status = "Accepted";
                        }
                    }
                }

                foreach (var evt in eventsByCaseId) _eventCustomRules.Apply(tenantId, @case, evt);

                cases.Add(@case);

                if (tenantId.Contains("walaa", StringComparison.InvariantCultureIgnoreCase)) continue; // Do not recalculate the premium. This is not needed for Walaa.

                if (Environment.GetEnvironmentVariable("datacenterId") != "dbs-hk")
                    await RecalculatePremia(tenantId, @case, cancellationToken);
            }

            return (cases, deletedIds);

            void PopulateProposalRenewalHistoryCount(List<Proposal> proposals)
            {
                foreach (Proposal proposal in proposals)
                {
                    proposal.RenewalHistory.RenewalCount = IncrementRenewalCount(0, proposals, proposal.Id);
                }

                int IncrementRenewalCount(int currentCount, IEnumerable<Proposal> proposals, string proposalId)
                {
                    Proposal proposal = proposals.FirstOrDefault(p => p.Id == proposalId);
                    return proposal?.RenewalHistory?.RenewedFromId == null
                        ? currentCount
                        : IncrementRenewalCount(currentCount + 1, proposals, proposal.RenewalHistory?.RenewedFromId);
                }
            }
        }

        private Case HandleCreateEvents(IEnumerable<CaseEvent> caseEvents)
        {
            Dictionary<CaseEventType, Func<CaseEvent, Case>> eventHandlerDictionary = new Dictionary<CaseEventType, Func<CaseEvent, Case>>()
            {
                { CaseEventType.creation, HandleCreation },
                { CaseEventType.agentCreateCase, HandleAgentCreate },
            };
            CaseEvent createEvent = caseEvents.FirstOrDefault(e => e.Type == CaseEventType.creation || e.Type == CaseEventType.agentCreateCase);
            if (createEvent == null)
                return null;
            if (!eventHandlerDictionary.ContainsKey(createEvent.Type))
                return null;
            return eventHandlerDictionary[createEvent.Type](createEvent);

            Case HandleCreation(CaseEvent @event)
            {
                CreateCaseCommand createCommand = @event.Values?.ToObject<CreateCaseCommand>(_jsonSerializer);
                return MapCaseFromcommand(createCommand, @event.CaseId, @event.Timestamp);
            }

            Case HandleAgentCreate(CaseEvent @event)
            {
                AgentCreateCaseCommand createCommand = @event.Values?.ToObject<AgentCreateCaseCommand>(_jsonSerializer);
                Case @case = MapCaseFromcommand(createCommand, @event.CaseId, @event.Timestamp);
                @case.Stakeholders = createCommand.Stakeholders;

                return @case;
            }

            Case MapCaseFromcommand(CreateCaseCommand command, string caseId, DateTime timestamp) => new()
            {
                Id = caseId,
                Name = command?.Name,
                Description = command?.Description,
                Source = command?.Source,
                CaseNumber = command?.CaseNumber,
                Status = command?.Status,
                HolderId = command?.HolderId,
                OtherHolderIds = command?.OtherHolderIds,
                InsuredIds = command?.InsuredIds,
                ComponentId = command?.ComponentId,
                Fields = command?.Fields == null ? null : JToken.Parse(command?.Fields),
                FieldsSchemaId = command?.FieldsSchemaId,
                WorkflowSchemaId = command?.WorkflowSchemaId,
                AccessPolicy = command?.AccessPolicy,
                ChannelId = command?.ChannelId,
                Stakeholders = string.IsNullOrEmpty(command?.AgentId) ? null : new[]
                {
                    new Stakeholder
                    {
                        Id = Guid.NewGuid().ToString(),
                        EntityId = command.AgentId,
                        Type = "Agent"
                    }
                },
                CreatedAt = timestamp,
                CreatedById = command?.CreatedById
            };
        }

        private void HandleUpdateEvent(Case @case, CaseEvent @event)
        {
            Dictionary<CaseEventType, Action<Case, CaseEvent>> eventHandlerDictionary = new()
            {
                { CaseEventType.update, HandleCaseUpdate },
                { CaseEventType.addIssuedPolicy, HandleAddIssuedPolicy },
                { CaseEventType.setSystemDates, HandleSetSystemDate },
                { CaseEventType.setReadOnly, HandleSetReadOnly }
            };

            if (eventHandlerDictionary.ContainsKey(@event.Type))
                eventHandlerDictionary[@event.Type](@case, @event);

            void HandleCaseUpdate(Case @case, CaseEvent @event)
            {
                UpdateCaseCommand command = @event.Values?.ToObject<UpdateCaseCommand>(_jsonSerializer);

                if (command.IsNameChanged) @case.Name = command?.Name;
                if (command.IsDescriptionChanged) @case.Description = command?.Description;
                if (command.IsSourceChanged) @case.Source = command?.Source;
                if (command.IsCaseNumberChanged) @case.CaseNumber = command?.CaseNumber;
                if (command.IsStatusChanged) @case.Status = command?.Status;
                if (command.IsHolderIdChanged) @case.HolderId = command?.HolderId;
                if (command.IsOtherHolderIdsChanged) @case.OtherHolderIds = command?.OtherHolderIds;
                if (command.IsInsuredIdsChanged) @case.InsuredIds = command?.InsuredIds;
                if (command.IsComponentIdChanged) @case.ComponentId = command?.ComponentId;
                if (command.IsFieldsChanged) @case.Fields = string.IsNullOrEmpty(command?.Fields) ? null : JToken.Parse(command?.Fields);

                if (!string.IsNullOrEmpty(command.FieldsPatch))
                {
                    @case.Fields = @case.Fields == null ? null : _jsonProjector.Write(@case.Fields, command.FieldsPatch);
                }

                if (command.IsFieldsSchemaIdChanged) @case.FieldsSchemaId = command.FieldsSchemaId;
                if (command.IsWorkflowSchemaIdChanged) @case.WorkflowSchemaId = command.WorkflowSchemaId;
                if (command.IsChannelIdChanged) @case.ChannelId = command.ChannelId;
            }

            void HandleAddIssuedPolicy(Case @case, CaseEvent @event)
            {
                AddIssuedPolicyCommand command = @event.Values?.ToObject<AddIssuedPolicyCommand>(_jsonSerializer);
                if (@case.IssuedPoliciesIds == null) @case.IssuedPoliciesIds = new List<string>();
                if (@case.IssuedPoliciesIds.Contains(command.PolicyId)) return;

                @case.IssuedPoliciesIds.Add(command.PolicyId);
                @case.Status = CaseStatus.Issued;
            }

            void HandleSetSystemDate(Case @case, CaseEvent @event)
            {
                AdminSetSystemDatesCommand command = @event.Values.ToObject<AdminSetSystemDatesCommand>();

                if (command.CreatedAt.HasValue) @case.CreatedAt = command.CreatedAt.Value;
                if (command.LastModifiedAt.HasValue) @case.LastModifiedAt = command.LastModifiedAt.Value;
            }

            void HandleSetReadOnly(Case @case, CaseEvent @event)
            {
                AdminSetReadOnlyCommand command = @event.Values.ToObject<AdminSetReadOnlyCommand>();
                if (command.ReadOnly.HasValue) @case.IsReadOnly = command.ReadOnly.Value;
            }
        }

        private void HandleProposalEvent(Case @case, CaseEvent @event)
        {
            Dictionary<CaseEventType, Action<Case, CaseEvent>> eventHandlerDictionary = new()
            {
                { CaseEventType.addProposal, HandleAddProposal },
                { CaseEventType.issueProposal, HandleIssueProposal },
                { CaseEventType.rejectProposal, HandleRejectProposal },
                { CaseEventType.updateProposal, HandleUpdateProposal },
                { CaseEventType.removeProposal, HandleRemoveProposal },
                { CaseEventType.singleOfferProposalAcceptance, HandleSingleOfferProposalAcceptance }
            };

            if (eventHandlerDictionary.ContainsKey(@event.Type))
                eventHandlerDictionary[@event.Type](@case, @event);

            void HandleAddProposal(Case @case, CaseEvent @event)
            {
                AddProposalCommand command = @event.Values?.ToObject<AddProposalCommand>(_jsonSerializer);
                Proposal proposal = CreateProposalFromCommand(@event, command);

                @case.Proposals = @case.Proposals.Append(proposal);
            }

            void HandleIssueProposal(Case @case, CaseEvent @event)
            {
                IssueProposalCommand command = @event.Values?.ToObject<IssueProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;

                proposalToUpdate.IsIssued = true;
                proposalToUpdate.Status = "ISSUED";
                proposalToUpdate.IssuedById = command.IssuedById;
                proposalToUpdate.IssuedAt = @event.Timestamp;
            }

            void HandleRejectProposal(Case @case, CaseEvent @event)
            {
                RejectProposalCommand command = @event.Values?.ToObject<RejectProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;

                proposalToUpdate.IsRejected = true;
                proposalToUpdate.Status = "REJECTED";
                proposalToUpdate.RejectionCodes = command.Codes;
                proposalToUpdate.RejectionRemarks = command.Remarks;
                proposalToUpdate.RejectedById = command.RejectedById;
            }

            void HandleUpdateProposal(Case @case, CaseEvent @event)
            {
                UpdateProposalCommand command = @event.Values?.ToObject<UpdateProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;

                UpdateProposalFromCommand(@event, command, proposalToUpdate);
            }

            void HandleRemoveProposal(Case @case, CaseEvent @event)
            {
                RemoveCommand command = @event.Values?.ToObject<RemoveCommand>(_jsonSerializer);
                @case.Proposals = @case.Proposals.Where(c => c.Id != command.Id);
            }

            void HandleSingleOfferProposalAcceptance(Case @case, CaseEvent @event)
            {
                {
                    ConvertOfferToApplicationCommand command = @event.Values?.ToObject<ConvertOfferToApplicationCommand>(_jsonSerializer);
                    Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                    if (proposalToUpdate == null) return;
                    proposalToUpdate.IsAccepted = true;
                    proposalToUpdate.Status = Constants.ProposalStatus.OFFER_ACCEPTED;
                    proposalToUpdate.AcceptedById = command.AcceptedById;
                    Offer offer = proposalToUpdate.Basket.First();
                    offer.Status = Constants.OfferStatus.ACCEPTED;
                }

                {
                    ConvertOfferToApplicationCommand command = @event.Values?.ToObject<ConvertOfferToApplicationCommand>(_jsonSerializer);
                    IEnumerable<Proposal> proposalsToUpdate = @case.Proposals.Where(p => p.Id != command.ProposalId);
                    foreach (Proposal proposalToUpdate in proposalsToUpdate)
                    {
                        proposalToUpdate.IsRejected = true;
                        proposalToUpdate.Status = Constants.ProposalStatus.OFFER_REJECTED;
                        proposalToUpdate.RejectionCodes = new List<string>();
                        proposalToUpdate.RejectionRemarks = "";
                        proposalToUpdate.RejectedById = command.AcceptedById;
                        Offer offer = proposalToUpdate.Basket.FirstOrDefault();
                        if (offer != null)
                            offer.Status = Constants.OfferStatus.REJECTED;
                    }
                }

                @case.Status = CaseStatus.OfferAccepted;
            }
        }

        private static void UpdateProposalFromCommand(CaseEvent @event, UpdateProposalCommand command, Proposal proposalToUpdate)
        {
            proposalToUpdate.LastModifiedById = command.ModifiedById;
            proposalToUpdate.LastModifiedAt = @event.Timestamp;
            if (command.IsNameChanged) proposalToUpdate.Name = command.Name;
            if (command.IsStatusChanged) proposalToUpdate.Status = command.Status;
            if (command.IsDescriptionChanged) proposalToUpdate.Description = command.Description;
            if (command.IsProposalNumberChanged) proposalToUpdate.ProposalNumber = command.ProposalNumber;
            if (command.IsReferralCodeChanged) proposalToUpdate.ReferralCode = command.ReferralCode;
            if (command.IsExpiryDateChanged) proposalToUpdate.ExpiryDate = command.ExpiryDate;
            if (command.IsTotalPriceChanged)
            {
                if (proposalToUpdate.TotalPrice == null) proposalToUpdate.TotalPrice = new Premium { };
                if (command.TotalPrice.IsAmountChanged) proposalToUpdate.TotalPrice.Amount = command.TotalPrice.Amount;
                if (command.TotalPrice.IsCurrencyCodeChanged) proposalToUpdate.TotalPrice.CurrencyCode = command.TotalPrice.CurrencyCode;
                //if (command.TotalPrice.IsDiscountCodesChanged) proposalToUpdate.TotalPrice.AppliedDiscounts = command.TotalPrice.DiscountCodes; //Note: figure this out
                if (command.TotalPrice.IsGrossAmountChanged) proposalToUpdate.TotalPrice.OriginalPrice = command.TotalPrice.GrossAmount;
            }
            if (command.IsRenewalHistoryChanged) proposalToUpdate.RenewalHistory = command.RenewalHistory;
            proposalToUpdate.IsApprovalNeeded = command.IsApprovalNeeded;
        }

        private static Proposal CreateProposalFromCommand(CaseEvent @event, AddProposalCommand command) => new Proposal
        {
            Id = command.Id,
            Name = command.Name,
            Description = command.Description,
            IsApprovalNeeded = command.IsApprovalNeeded,
            CaseId = @event.CaseId,
            Status = command.Status,
            ProposalNumber = command.ProposalNumber,
            ExpiryDate = command.ExpiryDate,
            TotalPrice = command.TotalPrice != null ? PricingExtensions.ToPrice(command.TotalPrice) : null,
            Basket = new List<Offer>(),
            RenewalHistory = command.RenewalHistory ?? new RenewalHistory(),
            ReferralCode = command.ReferralCode,

            CreatedById = command.AddedById,
            LastModifiedById = command.AddedById,
            CreatedAt = @event.Timestamp,
            LastModifiedAt = @event.Timestamp
        };

        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        private void HandleAgentProposalEvent(Case @case, CaseEvent @event)
        {
            Dictionary<CaseEventType, Action<Case, CaseEvent>> eventHandlerDictionary = new()
            {
                { CaseEventType.createAgentProposal, HandleCreateAgentProposal },
                { CaseEventType.sendAgentProposalForApproval, HandleSendAgentProposalForApproval },
                { CaseEventType.approveAgentProposal, HandleApproveAgentProposal },
                { CaseEventType.disapproveAgentProposal, HandleDisapproveAgentProposal },
                { CaseEventType.sendAgentProposalToAgent, HandleSendAgentProposalToAgent },
                { CaseEventType.sendAgentProposalToClient, HandleSendAgentProposalToClient },
                { CaseEventType.acceptAgentProposal, HandleAcceptAgentProposal  },
                { CaseEventType.rejectAgentProposal, HandleRejectAgentProposal },
                { CaseEventType.updateAgentProposal, HandleUpdateAgentProposal },
                { CaseEventType.duplicateProposal, HandleDuplicateProposal },
            };

            if (eventHandlerDictionary.ContainsKey(@event.Type))
                eventHandlerDictionary[@event.Type](@case, @event);

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleUpdateAgentProposal(Case @case, CaseEvent @event)
            {
                UpdateAgentProposalCommand command = @event.Values?.ToObject<UpdateAgentProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalCommand.ProposalId);
                if (proposalToUpdate == null) return;
                UpdateProposalFromCommand(@event, command.ProposalCommand, proposalToUpdate);
                Offer offerToUpdate = proposalToUpdate.Basket.FirstOrDefault(); // there should be only one offer in the basket
                if (offerToUpdate != null)
                {
                    UpdateOfferFromCommand(command.OfferCommand, offerToUpdate);
                }
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleCreateAgentProposal(Case @case, CaseEvent @event)
            {
                AddAgentProposalCommand command = @event.Values?.ToObject<AddAgentProposalCommand>(_jsonSerializer);
                if (command == null) return;
                Proposal proposal = CreateProposalFromCommand(@event, command.ProposalCommand);
                proposal.Basket = proposal.Basket.Append(CreateOfferFromCommand(@event, command.OfferCommand));
                @case.Proposals = @case.Proposals.Append(proposal);
                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleSendAgentProposalForApproval(Case @case, CaseEvent @event)
            {
                SendAgentProposalForApprovalCommand command = @event.Values?.ToObject<SendAgentProposalForApprovalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                proposalToUpdate.Status = ProposalStatus.OfferForReview;
                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleApproveAgentProposal(Case @case, CaseEvent @event)
            {
                ApproveAgentProposalCommand command = @event.Values?.ToObject<ApproveAgentProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                proposalToUpdate.Status = ProposalStatus.OfferApproved;
                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleDisapproveAgentProposal(Case @case, CaseEvent @event)
            {
                DisapproveAgentProposalCommand command = @event.Values?.ToObject<DisapproveAgentProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                proposalToUpdate.Status = ProposalStatus.InProgress;
                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleSendAgentProposalToAgent(Case @case, CaseEvent @event)
            {
                SendAgentProposalToAgentCommand command = @event.Values?.ToObject<SendAgentProposalToAgentCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                proposalToUpdate.Status = ProposalStatus.OfferGenerated;
                Offer offerToUpdate = proposalToUpdate.Basket?.FirstOrDefault();
                if (offerToUpdate == null || offerToUpdate.Fields2 == null) return;
                (offerToUpdate.Fields2 as JObject).Add("offerGeneratedDate", @event.Timestamp);
                offerToUpdate.Fields = offerToUpdate.Fields2.ToString(Formatting.None);

                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleDuplicateProposal(Case @case, CaseEvent @event)
            {
                DuplicateProposalCommand command = @event.Values?.ToObject<DuplicateProposalCommand>(_jsonSerializer);
                if (command == null) return;

                Proposal newInstanceOfProposal = command.Proposal;
                newInstanceOfProposal.Id = command.ProposalId;
                newInstanceOfProposal.Status = ProposalStatus.InProgress;
                newInstanceOfProposal.DuplicatedFromProposalId = command.DuplicatedFromProposalId;
                Offer offer = newInstanceOfProposal.Basket.FirstOrDefault();
                if (offer != null)
                {
                    offer.Id = command.OfferId;
                    if (command.NewProductId != null)
                        offer.ProductId = command.NewProductId;

                    offer.CreatedAt = @event.Timestamp;
                    offer.LastModifiedAt = @event.Timestamp;
                    offer.CreatedById = command.DuplicatedById;
                    offer.LastModifiedById = command.DuplicatedById;
                }
                newInstanceOfProposal.CreatedAt = @event.Timestamp;
                newInstanceOfProposal.LastModifiedAt = @event.Timestamp;
                newInstanceOfProposal.CreatedById = command.DuplicatedById;
                newInstanceOfProposal.LastModifiedById = command.DuplicatedById;


                @case.Proposals = @case.Proposals.Append(newInstanceOfProposal);

                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleSendAgentProposalToClient(Case @case, CaseEvent @event)
            {
                SendAgentProposalToClientCommand command = @event.Values?.ToObject<SendAgentProposalToClientCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                proposalToUpdate.Status = ProposalStatus.AwaitingClientFeedback;
                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleRejectAgentProposal(Case @case, CaseEvent @event)
            {
                RejectAgentProposalCommand command = @event.Values?.ToObject<RejectAgentProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;

                proposalToUpdate.IsRejected = true;
                proposalToUpdate.Status = ProposalStatus.OfferRejected;
                proposalToUpdate.RejectionCodes = command.Codes;
                proposalToUpdate.RejectionRemarks = command.Remarks;
                proposalToUpdate.RejectedById = command.RejectedById;
                UpdateCaseStatusFromProposals(@case);
            }

            [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
            void HandleAcceptAgentProposal(Case @case, CaseEvent @event)
            {
                AcceptAgentProposalCommand command = @event.Values?.ToObject<AcceptAgentProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                proposalToUpdate.IsAccepted = true;
                proposalToUpdate.Status = ProposalStatus.OfferAccepted;
                proposalToUpdate.AcceptedById = command.AcceptedById;
                UpdateCaseStatusFromProposals(@case);
            }
        }

        static void UpdateCaseStatusFromProposals(Case @case)
        {
            @case.Status = @case switch
            {
                var cs when cs.Proposals.All((proposal) => proposal.Status == ProposalStatus.OfferRejected) => CaseStatus.OfferRejected,
                var cs when cs.Proposals.Any((proposal) => proposal.Status == ProposalStatus.OfferAccepted) => CaseStatus.OfferAccepted,
                var cs when cs.Proposals.Any((proposal) => proposal.Status == ProposalStatus.AwaitingClientFeedback) => CaseStatus.AwaitingClientFeedback,
                var cs when cs.Proposals.Any((proposal) => proposal.Status == ProposalStatus.OfferGenerated) => CaseStatus.OfferGenerated,
                var cs when cs.Proposals.Any((proposal) => proposal.Status == ProposalStatus.InProgress || proposal.Status == ProposalStatus.OfferForReview || proposal.Status == ProposalStatus.OfferApproved) => CaseStatus.InProgress,
                _ => CaseStatus.Submitted
            };
            if (@case.Status == CaseStatus.OfferAccepted)
            {
                foreach (Proposal proposal in @case.Proposals)
                {
                    if (proposal.Status != ProposalStatus.OfferAccepted)
                    {
                        proposal.Status = ProposalStatus.OfferRejected;
                    }
                }
                @case.Status = CaseStatus.OfferAccepted;
            }
        }

        private void HandleProposalStakeholderEvent(IEnumerable<Proposal> proposals, CaseEvent @event)
        {
            Dictionary<CaseEventType, Action<IEnumerable<Proposal>, CaseEvent>> eventHandlerDictionary = new()
            {
                { CaseEventType.addStakeholderToProposal, HandleAddStakeholder },
                { CaseEventType.updateStakeholderOfProposal, HandleUpdateStakeholder },
                { CaseEventType.removeStakeholderFromProposal, HandleRemoveStakeholder }
            };

            if (eventHandlerDictionary.ContainsKey(@event.Type))
                eventHandlerDictionary[@event.Type](proposals, @event);

            void HandleAddStakeholder(IEnumerable<Proposal> proposals, CaseEvent caseEvent)
            {
                AddStakeholderCommand command = @event.Values.ToObject<AddStakeholderCommand>(_jsonSerializer);
                Proposal proposalToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null)
                    return;

                var stakeholder = new Stakeholder
                {
                    Id = command.Id,
                    EntityId = command.EntityId,
                    Type = command.Type,
                    Name = command.Name,
                    Code = command.Code
                };

                if (!proposalToUpdate.Stakeholders?.Any() ?? true)
                    proposalToUpdate.Stakeholders = new List<Stakeholder> { stakeholder };
                else
                    proposalToUpdate.Stakeholders = proposalToUpdate.Stakeholders.Append(stakeholder);
            }

            void HandleUpdateStakeholder(IEnumerable<Proposal> proposals, CaseEvent caseEvent)
            {
                UpdateStakeholderCommand command = @event.Values.ToObject<UpdateStakeholderCommand>(_jsonSerializer);

                Stakeholder stakeholderToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Stakeholders?.FirstOrDefault(s => s.Id == command.Id);
                if (stakeholderToUpdate == null) return;

                if (command.IsEntityIdChanged) stakeholderToUpdate.EntityId = command.EntityId;
                if (command.IsTypeChanged) stakeholderToUpdate.Type = command.Type;
            }

            void HandleRemoveStakeholder(IEnumerable<Proposal> proposals, CaseEvent caseEvent)
            {
                RemoveStakeholderCommand command = @event.Values.ToObject<RemoveStakeholderCommand>(_jsonSerializer);

                Proposal proposalToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId);

                if (!proposalToUpdate?.Stakeholders?.Any() ?? true)
                    return;

                proposalToUpdate.Stakeholders = proposalToUpdate.Stakeholders?.Except(proposalToUpdate.Stakeholders?.Where(f => f.Id == command.Id));
            }
        }

        private void HandleOfferEvent(Case @case, CaseEvent @event)
        {
            Dictionary<CaseEventType, Action<Case, CaseEvent>> eventHandlerDictionary = new()
            {
                { CaseEventType.addOffer, HandleAddOffer },
                { CaseEventType.updateOffer, HandleUpdateOffer },
                { CaseEventType.removeOffer, HandleRemoveOffer },
                { CaseEventType.recordQuote, HandleRecordQuote },
                { CaseEventType.singleOfferProposalAcceptance, HandleSingleOfferProposalAcceptance }
            };

            if (eventHandlerDictionary.ContainsKey(@event.Type))
                eventHandlerDictionary[@event.Type](@case, @event);

            void HandleAddOffer(Case @case, CaseEvent @event)
            {
                AddOfferCommand command = @event.Values?.ToObject<AddOfferCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                Offer offer = CreateOfferFromCommand(@event, command);

                proposalToUpdate.Basket = proposalToUpdate.Basket.Append(offer);
            };

            void HandleUpdateOffer(Case @case, CaseEvent @event)
            {
                UpdateOfferCommand command = @event.Values?.ToObject<UpdateOfferCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                Offer offerToUpdate = proposalToUpdate.Basket.FirstOrDefault(o => o.Id == command.OfferId);
                if (offerToUpdate == null) return;
                UpdateOfferFromCommand(command, offerToUpdate);
            };

            void HandleRemoveOffer(Case @case, CaseEvent @event)
            {
                RemoveOfferFromProposalCommand command = @event.Values?.ToObject<RemoveOfferFromProposalCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null)
                    return;

                proposalToUpdate.Basket = proposalToUpdate.Basket.Where(c => c.Id != command.OfferId);
            }

            void HandleRecordQuote(Case @case, CaseEvent @event)
            {
                RecordQuoteCommand command = @event.Values!.ToObject<RecordQuoteCommand>(_jsonSerializer);
                Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposalToUpdate == null) return;
                Offer offerToUpdate = proposalToUpdate.Basket.FirstOrDefault(o => o.Id == command.OfferId);
                if (offerToUpdate == null) return;
                offerToUpdate.Fields = command.Fields;
                offerToUpdate.ProductTreeRecords = command.ProductTreeRecords;
            }

            void HandleSingleOfferProposalAcceptance(Case @case, CaseEvent @event)
            {
                ConvertOfferToApplicationCommand command = @event.Values?.ToObject<ConvertOfferToApplicationCommand>(_jsonSerializer);
                AcceptOffer();
                RejectOtherOffers();
                @case.Status = CaseStatus.OfferAccepted;

                void AcceptOffer()
                {
                    Proposal proposalToUpdate = @case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId);
                    if (proposalToUpdate == null) return;
                    proposalToUpdate.IsAccepted = true;
                    proposalToUpdate.Status = ProposalStatus.OfferAccepted;
                    proposalToUpdate.AcceptedById = command.AcceptedById;
                    Offer offer = proposalToUpdate.Basket.First();
                    offer.Status = ProposalStatus.OfferAccepted;
                }

                void RejectOtherOffers()
                {
                    IEnumerable<Proposal> proposalsToUpdate = @case.Proposals.Where(p => p.Id != command.ProposalId);
                    foreach (Proposal proposalToUpdate in proposalsToUpdate)
                    {
                        proposalToUpdate.IsRejected = true;
                        proposalToUpdate.Status = ProposalStatus.OfferRejected;
                        proposalToUpdate.RejectionCodes = new List<string>();
                        proposalToUpdate.RejectionRemarks = "";
                        proposalToUpdate.RejectedById = command.AcceptedById;
                        Offer offer = proposalToUpdate.Basket.FirstOrDefault();
                        if (offer != null)
                            offer.Status = ProposalStatus.OfferRejected;
                    }
                }
            }
        }

        private static void UpdateOfferFromCommand(UpdateOfferCommand command, Offer offerToUpdate)
        {
            if (command.IsStatusChanged) offerToUpdate.Status = command.Status;
            if (command.IsOfferNumberChanged) offerToUpdate.OfferNumber = command.OfferNumber;
            if (command.IsPolicyNumberChanged) offerToUpdate.PolicyNumber = command.PolicyNumber;
            if (command.IsValuesChanged) offerToUpdate.Values = command.Values;
            if (command.IsStartDateChanged) offerToUpdate.StartDate = command.StartDate;
            if (command.IsEndDateChanged) offerToUpdate.EndDate = command.EndDate;
            UpdateOfferPremiumValues(command, offerToUpdate);
            if (command.IsProductIdChanged)
            {
                UpdateOfferProductFromCommand(command, offerToUpdate);
            }
            if (command.IsPricingChanged) offerToUpdate.Pricing = command.Pricing;
            if (command.IsUnderwritingChanged) offerToUpdate.Underwriting = command.Underwriting;
            if (command.IsFieldsChanged) offerToUpdate.Fields2 = command.Fields == null ? null : JToken.Parse(command.Fields);
            if (command.IsFieldsSchemaIdChanged) offerToUpdate.FieldsSchemaId = command.FieldsSchemaId;
            if (command.IsProductTreeIdChanged) offerToUpdate.ProductTreeId = command.ProductTreeId;
            if (command.IsDistributorIDChanged) offerToUpdate.DistributorID = command.DistributorID;
            if (command.IsCampaignCodesChanged) offerToUpdate.CampaignCodes = command.CampaignCodes;
        }

        private static void UpdateOfferPremiumValues(UpdateOfferCommand command, Offer offerToUpdate)
        {
            if (command.IsPremiumChanged)
            {
                UpdateOfferPremiumFromCommand(command, offerToUpdate);
            }
            if (command.IsPremiumOverridden != null)
                offerToUpdate.IsPremiumOverridden = command.IsPremiumOverridden.GetValueOrDefault();
        }

        private static void UpdateOfferProductFromCommand(UpdateOfferCommand command, Offer offerToUpdate)
        {
            if (command.ProductId == null) offerToUpdate.ProductId = null;

            else
            {
                if (offerToUpdate.ProductId == null) offerToUpdate.ProductId = new ProductId { };
                if (command.ProductId.IsPlanChanged) offerToUpdate.ProductId.Plan = command.ProductId.Plan;
                if (command.ProductId.IsVersionChanged) offerToUpdate.ProductId.Version = command.ProductId.Version;
                if (command.ProductId.IsTypeChanged) offerToUpdate.ProductId.Type = command.ProductId.Type;
            }
        }

        private static void UpdateOfferPremiumFromCommand(UpdateOfferCommand command, Offer offerToUpdate)
        {

            if (command.Premium == null) offerToUpdate.Premium = null;

            else
            {
                if (offerToUpdate.Premium == null) offerToUpdate.Premium = new Premium { };
                if (command.Premium.IsAmountChanged) offerToUpdate.Premium.Amount = command.Premium.Amount;
                if (command.Premium.IsCurrencyCodeChanged) offerToUpdate.Premium.CurrencyCode = command.Premium.CurrencyCode;
                if (command.Premium.IsDiscountCodesChanged) offerToUpdate.Premium.DiscountCodes = command.Premium.DiscountCodes?.ToList();
                if (command.Premium.IsGrossAmountChanged) offerToUpdate.Premium.OriginalPrice = command.Premium.GrossAmount;
            }
        }

        private static Offer CreateOfferFromCommand(CaseEvent @event, AddOfferCommand command) => new Offer
        {
            Id = command.OfferId,
            Status = command.Status,
            OfferNumber = command.OfferNumber,
            PolicyNumber = command.PolicyNumber,
            BenefitOptions = command.BenefitOptions,
            ProductId = command.ProductId,
            Premium = command.Premium != null ? PricingExtensions.ToPrice(command.Premium) : null,
            IsPremiumOverridden = command.IsPremiumOverridden,
            Values = command.Values,
            StartDate = command.StartDate,
            EndDate = command.EndDate,

            Pricing = command.Pricing,
            Underwriting = command.Underwriting,
            Fields2 = command.Fields == null ? null : JToken.Parse(command.Fields),
            FieldsSchemaId = command.FieldsSchemaId,
            ProductTreeId = command.ProductTreeId,
            ProductTreeRecords = command.ProductTreeRecords,
            DistributorID = command.DistributorID,
            CampaignCodes = command.CampaignCodes,

            CreatedAt = @event.Timestamp,
            LastModifiedAt = @event.Timestamp,
            CreatedById = command.AddedById,
            LastModifiedById = command.AddedById,
        };

        private void HandleOfferDiscountEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> discountEvents)
        {
            foreach (CaseEvent @event in discountEvents)
            {
                if (@event.Type == CaseEventType.addDiscountToOffer)
                {
                    AddDiscountToOfferCommand command = @event.Values?.ToObject<AddDiscountToOfferCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    if (offerToUpdate.Premium == null)
                        offerToUpdate.Premium = new Premium { };

                    var discount = new Discount
                    {
                        Id = command.DiscountId,
                        Name = command.Name,
                        CalculationJsonLogic = command.CalculationJsonLogic,
                        Order = command.Order,
                        CurrencyCode = command.CurrencyCode,
                        IsManual = true,
                        CreatedAt = @event.Timestamp,
                        LastModifiedAt = @event.Timestamp,
                        CreatedById = command.AddedById,
                        LastModifiedById = command.AddedById
                    };

                    if (!offerToUpdate.Premium.Discounts?.Any() ?? true)
                        offerToUpdate.Premium.Discounts = new List<Discount> { discount };
                    else
                        offerToUpdate.Premium.Discounts.Add(discount);
                }

                else if (@event.Type == CaseEventType.updateDiscountOfOffer)
                {
                    UpdateDiscountOfOfferCommand command = @event.Values?.ToObject<UpdateDiscountOfOfferCommand>(_jsonSerializer);
                    Discount discountToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId)?.Premium?.Discounts?.Find(p => p.Id == command.DiscountId);

                    if (discountToUpdate == null)
                        continue;

                    if (command.IsNameChanged)
                        discountToUpdate.Name = command.Name;
                    if (command.IsCalculationJsonLogicChanged)
                        discountToUpdate.CalculationJsonLogic = command.CalculationJsonLogic;
                    if (command.IsOrderChanged)
                        discountToUpdate.Order = command.Order;

                    discountToUpdate.LastModifiedAt = @event.Timestamp;
                    discountToUpdate.LastModifiedById = command.ModifiedById;
                }

                else if (@event.Type == CaseEventType.removeDiscountFromOffer)
                {
                    RemoveDiscountFromOfferCommand command = @event.Values?.ToObject<RemoveDiscountFromOfferCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    if (!offerToUpdate.Premium?.Discounts?.Any() ?? true)
                        continue;

                    offerToUpdate.Premium.Discounts.Remove(offerToUpdate.Premium.Discounts.Find(d => d.Id == command.DiscountId));
                }
            }
        }

        private void HandleOfferLoadingEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> loadingEvents)
        {
            foreach (CaseEvent @event in loadingEvents)
            {
                if (@event.Type == CaseEventType.addLoadingToOffer)
                {
                    AddLoadingCommand command = @event.Values?.ToObject<AddLoadingCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    if (offerToUpdate.Premium == null)
                        offerToUpdate.Premium = new Premium { };

                    var loading = new Loading
                    {
                        Id = command.Id,
                        CalculationJsonLogic = command.CalculationJsonLogic,
                        Code = command.Code,
                        Order = command.Order,
                        Flat = command.Flat,
                        Ratio = command.Ratio,
                        CreatedAt = @event.Timestamp,
                        LastModifiedAt = @event.Timestamp,
                        CreatedById = command.AddedById,
                        LastModifiedById = command.AddedById
                    };

                    if (!offerToUpdate.Premium.Loadings?.Any() ?? true)
                        offerToUpdate.Premium.Loadings = new List<Loading> { loading };
                    else
                        offerToUpdate.Premium.Loadings.Add(loading);
                }

                else if (@event.Type == CaseEventType.updateLoadingOfOffer)
                {
                    UpdateLoadingCommand command = @event.Values?.ToObject<UpdateLoadingCommand>(_jsonSerializer);
                    Loading loadingToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId)?.Premium?.Loadings?.Find(p => p.Id == command.Id);

                    if (loadingToUpdate == null)
                        continue;

                    if (command.IsCodeChanged)
                        loadingToUpdate.Code = command.Code;
                    if (command.IsFlatChanged)
                        loadingToUpdate.Flat = command.Flat;
                    if (command.IsRatioChanged)
                        loadingToUpdate.Ratio = command.Ratio;
                    if (command.IsCalculationJsonLogicChanged)
                        loadingToUpdate.CalculationJsonLogic = command.CalculationJsonLogic;
                    if (command.IsOrderChanged)
                        loadingToUpdate.Order = command.Order;

                    loadingToUpdate.LastModifiedAt = @event.Timestamp;
                    loadingToUpdate.LastModifiedById = command.ModifiedById;
                }

                else if (@event.Type == CaseEventType.removeLoadingFromOffer)
                {
                    RemoveLoadingCommand command = @event.Values?.ToObject<RemoveLoadingCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (!offerToUpdate.Premium?.Loadings?.Any() ?? true)
                        continue;

                    offerToUpdate.Premium.Loadings.Remove(offerToUpdate.Premium.Loadings.Find(d => d.Id == command.Id));
                }
            }
        }

        private void HandleOfferExclusionEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> exclusionEvents)
        {
            foreach (CaseEvent @event in exclusionEvents)
            {
                if (@event.Type == CaseEventType.addExclusionToOffer)
                {
                    AddExclusionCommand command = @event.Values?.ToObject<AddExclusionCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    var exclusion = new Exclusion
                    {
                        Id = command.Id,
                        Code = command.Code,
                        BenefitTypeId = command.BenefitTypeId,
                        BenefitParentTypeId = command.BenefitParentTypeId,
                        BenefitOptionKey = command.BenefitOptionKey,
                        Remark = command.Remark,
                        CreatedAt = @event.Timestamp,
                        LastModifiedAt = @event.Timestamp,
                        CreatedById = command.AddedById,
                        LastModifiedById = command.AddedById
                    };

                    if (offerToUpdate.Exclusions == null)
                        offerToUpdate.Exclusions = new List<Exclusion> { };

                    offerToUpdate?.Exclusions.Add(exclusion);
                }

                else if (@event.Type == CaseEventType.removeExclusionFromOffer)
                {
                    RemoveExclusionCommand command = @event.Values?.ToObject<RemoveExclusionCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (!offerToUpdate?.Exclusions?.Any() ?? true)
                        continue;

                    offerToUpdate.Exclusions.Remove(offerToUpdate.Exclusions.Find(d => d.Id == command.Id));
                }
            }
        }

        private void HandleOfferBenefitOptionEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> benefitOptionEvents)
        {
            foreach (CaseEvent @event in benefitOptionEvents)
            {
                if (@event.Type == CaseEventType.upsertBenefitOptionOfOffer)
                {
                    UpsertBenefitOptionCommand command = @event.Values?.ToObject<UpsertBenefitOptionCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    if (offerToUpdate.BenefitOptions == null)
                        offerToUpdate.BenefitOptions = new List<BenefitOption> { };

                    var benefitOption = new BenefitOption
                    {
                        TypeId = command.TypeId,
                        InsuredId = command.InsuredId,
                        Key = command.Key,
                        Value = command.Value,

                        CreatedAt = @event.Timestamp,
                        LastModifiedAt = @event.Timestamp,
                        CreatedById = command.UpsertedById,
                        LastModifiedById = command.UpsertedById
                    };

                    var existingBenefitOption = offerToUpdate.BenefitOptions.Find(b => b.TypeId == command.TypeId);

                    if (existingBenefitOption != null)
                    {
                        existingBenefitOption.Key = command.Key;
                        existingBenefitOption.Value = command.Value;
                        existingBenefitOption.InsuredId = command.InsuredId;
                        existingBenefitOption.LastModifiedAt = @event.Timestamp;
                        existingBenefitOption.LastModifiedById = command.UpsertedById;
                    }
                    else
                        offerToUpdate.BenefitOptions.Add(benefitOption);
                }

                else if (@event.Type == CaseEventType.removeBenefitOptionFromOffer)
                {
                    RemoveBenefitOptionCommand command = @event.Values?.ToObject<RemoveBenefitOptionCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (!offerToUpdate?.BenefitOptions?.Any() ?? true)
                        continue;

                    offerToUpdate.BenefitOptions.Remove(offerToUpdate.BenefitOptions.Find(d => d.TypeId == command.TypeId));
                }
            }
        }

        private void HandleOfferClauseEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> clauseEvents)
        {
            foreach (CaseEvent @event in clauseEvents)
            {
                if (@event.Type == CaseEventType.addClauseToOffer)
                {
                    AddClauseCommand command = @event.Values?.ToObject<AddClauseCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    var clause = new Clause
                    {
                        Id = command.ClauseId,
                        Order = command.Order,
                        TemplateId = command.TemplateId,
                        Template = command.Template,
                        RenderParameters = command.RenderParameters,
                        HtmlOverride = command.HtmlOverride,
                        Type = command.Type
                    };

                    if (!offerToUpdate.Clauses?.Any() ?? true)
                        offerToUpdate.Clauses = new List<Clause> { clause };
                    else
                        offerToUpdate.Clauses.Add(clause);
                }

                else if (@event.Type == CaseEventType.updateClauseOfOffer)
                {
                    UpdateClauseCommand command = @event.Values?.ToObject<UpdateClauseCommand>(_jsonSerializer);
                    Clause clauseToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId)?.Clauses?.Find(p => p.Id == command.ClauseId);

                    if (clauseToUpdate == null)
                        continue;

                    if (command.IsOrderChanged)
                        clauseToUpdate.Order = command.Order.Value;
                    if (command.IsTemplateIdChanged)
                        clauseToUpdate.TemplateId = command.TemplateId;
                    if (command.IsRenderParametersChanged)
                        clauseToUpdate.RenderParameters = command.RenderParameters;
                    if (command.IsHtmlOverrideChanged)
                        clauseToUpdate.HtmlOverride = command.HtmlOverride;
                    if (command.IsTemplateChanged)
                        clauseToUpdate.Template = command.Template;
                    if (command.IsTypeChanged)
                        clauseToUpdate.Type = command.Type;
                }

                else if (@event.Type == CaseEventType.removeClauseFromOffer)
                {
                    RemoveClauseCommand command = @event.Values?.ToObject<RemoveClauseCommand>(_jsonSerializer);
                    Proposal proposal = proposals?.FirstOrDefault(p => p.Id == command.ProposalId);
                    if (proposal == null)
                        continue;

                    Offer offerToUpdate = proposal.Basket?.FirstOrDefault(o => o.Id == command.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    if (!offerToUpdate.Clauses?.Any() ?? true)
                        continue;

                    offerToUpdate.Clauses.Remove(offerToUpdate.Clauses.Find(d => d.Id == command.ClauseId));
                }

                else if (@event.Type == CaseEventType.offerClauseBatch)
                {
                    ClauseCommandBatch batch = @event.Values.ToObject<ClauseCommandBatch>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == batch.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == batch.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    IEnumerable<Clause> clausesToAdd = batch.AddClauseCommands?.Select(command => new Clause
                    {
                        Id = command.ClauseId,
                        Order = command.Order,
                        TemplateId = command.TemplateId,
                        RenderParameters = command.RenderParameters,
                        HtmlOverride = command.HtmlOverride,
                        Template = command.Template,
                        Type = command.Type
                    });

                    offerToUpdate.Clauses ??= new List<Clause>();

                    offerToUpdate.Clauses.AddRange(clausesToAdd ?? new List<Clause>());

                    foreach (UpdateClauseCommand updateClauseCommand in batch.UpdateClauseCommands ?? new List<UpdateClauseCommand>())
                    {
                        Clause clauseToUpdate = offerToUpdate.Clauses?.Find(f => f.Id == updateClauseCommand.ClauseId);
                        if (clauseToUpdate == null)
                            continue;

                        if (updateClauseCommand.IsOrderChanged)
                        {
                            clauseToUpdate.Order = updateClauseCommand.Order.Value;
                        }
                        if (updateClauseCommand.IsTemplateIdChanged)
                        {
                            clauseToUpdate.TemplateId = updateClauseCommand.TemplateId;
                        }
                        if (updateClauseCommand.IsRenderParametersChanged)
                        {
                            clauseToUpdate.RenderParameters = updateClauseCommand.RenderParameters;
                        }
                        if (updateClauseCommand.IsHtmlOverrideChanged)
                        {
                            clauseToUpdate.HtmlOverride = updateClauseCommand.HtmlOverride;
                        }
                        if (updateClauseCommand.IsTemplateChanged)
                        {
                            clauseToUpdate.Template = updateClauseCommand.Template;
                        }
                        if (updateClauseCommand.IsTypeChanged)
                        {
                            clauseToUpdate.Type = updateClauseCommand.Type;
                        }
                    }
                }
            }
        }

        private void HandleOfferJacketEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> clauseEvents)
        {
            foreach (CaseEvent @event in clauseEvents)
            {
                if (@event.Type == CaseEventType.removeJacketFromOffer)
                {
                    RemoveJacketInstanceCommand command = @event.Values?.ToObject<RemoveJacketInstanceCommand>(_jsonSerializer);
                    Proposal proposal = proposals?.FirstOrDefault(p => p.Id == command.ProposalId);
                    if (proposal == null)
                        continue;

                    Offer offerToUpdate = proposal.Basket?.FirstOrDefault(o => o.Id == command.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    if (!offerToUpdate.Jackets?.Any() ?? true)
                        continue;

                    offerToUpdate.Jackets.Remove(offerToUpdate.Jackets.Find(d => d.Id == command.InstanceId));
                }

                else if (@event.Type == CaseEventType.offerJacketBatch)
                {
                    JacketCommandBatch batch = @event.Values.ToObject<JacketCommandBatch>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == batch.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == batch.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    IEnumerable<JacketInstance> clausesToAdd = batch.AddJacketInstanceCommands?.Select(command => new JacketInstance()
                    {
                        Id = command.InstanceId,
                        Order = command.Order,
                        JacketId = command.JacketId,
                        StoreJacketByValue = command.StoreJacketByValue,
                        Jacket = command.Jacket
                    });

                    offerToUpdate.Jackets ??= new List<JacketInstance>();

                    offerToUpdate.Jackets.AddRange(clausesToAdd ?? new List<JacketInstance>());

                    foreach (UpdateJacketInstanceCommand updateJacketCommand in batch.UpdateJacketInstanceCommands ?? new List<UpdateJacketInstanceCommand>())
                    {
                        JacketInstance jacketInstanceToUpdate = offerToUpdate.Jackets?.Find(f => f.Id == updateJacketCommand.InstanceId);
                        if (jacketInstanceToUpdate == null)
                            continue;

                        jacketInstanceToUpdate.Order = updateJacketCommand.Order;
                    }
                }
            }
        }

        private void HandleOfferFactEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> factEvents)
        {
            foreach (CaseEvent @event in factEvents)
            {
                if (@event.Type == CaseEventType.offerFactBatch)
                {
                    FactCommandBatch batch = @event.Values.ToObject<FactCommandBatch>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == batch.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == batch.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    IEnumerable<Fact> factsToAdd = batch.AddFactCommands?.Select(c => new Fact
                    {
                        Id = c.Id,
                        Type = c.Type,
                        Value = c.Value
                    });

                    if (!offerToUpdate.Facts?.Any() ?? true)
                        offerToUpdate.Facts = new List<Fact> { };

                    offerToUpdate.Facts.AddRange(factsToAdd ?? new List<Fact> { });

                    foreach (UpdateFactCommand updateFactCommand in batch.UpdateFactCommands ?? new List<UpdateFactCommand> { })
                    {
                        Fact factToUpdate = offerToUpdate.Facts?.Find(f => f.Id == updateFactCommand.Id);
                        if (factToUpdate == null)
                            continue;

                        factToUpdate.Type = updateFactCommand.Type;
                        factToUpdate.Value = updateFactCommand.Value;
                    }
                }

                if (@event.Type == CaseEventType.addFactToOffer)
                {
                    AddFactCommand command = @event.Values?.ToObject<AddFactCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    var fact = new Fact
                    {
                        Id = command.Id,
                        Type = command.Type,
                        Value = command.Value
                    };

                    if (!offerToUpdate.Facts?.Any() ?? true)
                        offerToUpdate.Facts = new List<Fact> { fact };
                    else
                        offerToUpdate.Facts.Add(fact);
                }

                else if (@event.Type == CaseEventType.updateFactOfOffer)
                {
                    UpdateFactCommand command = @event.Values?.ToObject<UpdateFactCommand>(_jsonSerializer);
                    Fact factToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId)?.Facts?.Find(f => f.Id == command.Id);

                    if (factToUpdate == null)
                        continue;

                    factToUpdate.Type = command.Type;
                    factToUpdate.Value = command.Value;
                }

                else if (@event.Type == CaseEventType.removeFactFromOffer)
                {
                    RemoveFactCommand command = @event.Values.ToObject<RemoveFactCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (!offerToUpdate?.Facts?.Any() ?? true)
                        continue;

                    offerToUpdate.Facts.Remove(offerToUpdate.Facts.Find(f => f.Id == command.Id));
                }
            }
        }

        private void HandleOfferStakeholderEvents(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> events)
        {
            foreach (CaseEvent @event in events)
            {
                if (@event.Type == CaseEventType.addStakeholderToOffer)
                {
                    AddStakeholderCommand command = @event.Values.ToObject<AddStakeholderCommand>(_jsonSerializer);
                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    var stakeholder = new Stakeholder
                    {
                        Id = command.Id,
                        EntityId = command.EntityId,
                        Type = command.Type
                    };

                    if (!offerToUpdate.Stakeholders?.Any() ?? true)
                        offerToUpdate.Stakeholders = new List<Stakeholder> { stakeholder };
                    else
                        offerToUpdate.Stakeholders.Add(stakeholder);
                }

                else if (@event.Type == CaseEventType.updateStakeholderOfOffer)
                {
                    UpdateStakeholderCommand command = @event.Values.ToObject<UpdateStakeholderCommand>(_jsonSerializer);

                    Stakeholder stakeholderToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId)?.Stakeholders.Find(s => s.Id == command.Id);
                    if (stakeholderToUpdate == null) continue;

                    if (command.IsEntityIdChanged) stakeholderToUpdate.EntityId = command.EntityId;
                    if (command.IsTypeChanged) stakeholderToUpdate.Type = command.Type;
                }

                else if (@event.Type == CaseEventType.removeStakeholderFromOffer)
                {
                    RemoveStakeholderCommand command = @event.Values.ToObject<RemoveStakeholderCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(o => o.Id == command.OfferId);

                    if (!offerToUpdate.Stakeholders?.Any() ?? true)
                        continue;

                    offerToUpdate.Stakeholders.Remove(offerToUpdate.Stakeholders.Find(f => f.Id == command.Id));
                }
            }
        }

        private List<CaseAgent> HandleAgentAssignmentEvents(Case @case, IEnumerable<CaseEvent> events)
        {
            var agents = new List<CaseAgent>{};
            foreach (CaseEvent @event in events)
            {
                if (@event.Type == CaseEventType.assignAgent)
                {
                    AssignAgentCommand command = @event.Values.ToObject<AssignAgentCommand>(_jsonSerializer);

                    var agent = new CaseAgent
                    {
                        Id = command.AgentId,
                        StakeholderId = command.StakeholderId,
                        PortalUserId = command.PortalUserId,
                        EntityId = command.EntityId,
                        RoleType = command.RoleType
                    };
                    agents.Add(agent);
                }

                else if (@event.Type == CaseEventType.unassignAgents)
                {
                    UnassignAgentsCommand command = @event.Values.ToObject<UnassignAgentsCommand>(_jsonSerializer);
                    agents.RemoveAll(a => command.AgentIds.Contains(a.Id));
                }
            }

            return agents;
        }

        private List<CaseHandler> HandleHandlerAssignmentEvents(Case @case, IEnumerable<CaseEvent> events)
        {
            var handlers = new List<CaseHandler> { };
            foreach (CaseEvent @event in events)
            {
                if (@event.Type == CaseEventType.assignHandler)
                {
                    AssignHandlerCommand command = @event.Values.ToObject<AssignHandlerCommand>(_jsonSerializer);

                    var agent = new CaseHandler
                    {
                        StakeholderId = command.StakeholderId,
                        PortalUserId = command.PortalUserId,
                        EntityId = command.EntityId,
                    };
                    handlers.Add(agent);
                }

                else if (@event.Type == CaseEventType.unassignHandlers)
                {
                    UnassignHandlersCommand command = @event.Values.ToObject<UnassignHandlersCommand>(_jsonSerializer);
                    handlers.RemoveAll(a => command.PortalUserIds.Contains(a.PortalUserId));
                }
            }

            return handlers;
        }

        private IEnumerable<Fact> HandleFactEvents(IEnumerable<CaseEvent> factEvents)
        {
            var facts = new List<Fact> { };
            foreach (CaseEvent @event in factEvents)
            {
                if (@event.Type == CaseEventType.addFactToCase)
                {
                    AddFactCommand command = @event.Values.ToObject<AddFactCommand>(_jsonSerializer);
                    var fact = new Fact
                    {
                        Id = command.Id,
                        Type = command.Type,
                        Value = command.Value
                    };

                    facts.Add(fact);
                }

                else if (@event.Type == CaseEventType.updateFactOfCase)
                {
                    UpdateFactCommand command = @event.Values.ToObject<UpdateFactCommand>(_jsonSerializer);
                    Fact factToUpdate = facts.Find(f => f.Id == command.Id);
                    if (factToUpdate == null) continue;

                    factToUpdate.Type = command.Type;
                    factToUpdate.Value = command.Value;
                }

                else if (@event.Type == CaseEventType.removeFactFromCase)
                {
                    RemoveFactCommand command = @event.Values.ToObject<RemoveFactCommand>(_jsonSerializer);
                    facts.Remove(facts.Find(f => f.Id == command.Id));
                }

                else if (@event.Type == CaseEventType.caseFactBatch)
                {
                    FactCommandBatch batch = @event.Values.ToObject<FactCommandBatch>(_jsonSerializer);

                    IEnumerable<Fact> factsToAdd = batch.AddFactCommands?.Select(c => new Fact
                    {
                        Id = c.Id,
                        Type = c.Type,
                        Value = c.Value
                    });

                    facts.AddRange(factsToAdd ?? new List<Fact> { });

                    foreach (UpdateFactCommand updateFactCommand in batch.UpdateFactCommands ?? new List<UpdateFactCommand> { })
                    {
                        Fact factToUpdate = facts.Find(f => f.Id == updateFactCommand.Id);
                        if (factToUpdate == null)
                            continue;

                        factToUpdate.Type = updateFactCommand.Type;
                        factToUpdate.Value = updateFactCommand.Value;
                    }
                }
            }

            return facts;
        }

        private IEnumerable<Note> HandleNoteEvents(IEnumerable<CaseEvent> noteEvents)
        {
            var notes = new List<Note> { };
            foreach (CaseEvent @event in noteEvents)
            {
                if (@event.Type == CaseEventType.addNoteToCase)
                {
                    AddNoteCommand command = @event.Values.ToObject<AddNoteCommand>(_jsonSerializer);
                    var note = new Note
                    {
                        Id = command.Id,
                        Title = command.Title,
                        Content = command.Content,

                        CreatedAt = @event.Timestamp,
                        LastModifiedAt = @event.Timestamp,
                        CreatedById = command.AddedById,
                        LastModifiedById = command.AddedById
                    };

                    notes.Add(note);
                }

                else if (@event.Type == CaseEventType.updateNoteOfCase)
                {
                    UpdateNoteCommand command = @event.Values.ToObject<UpdateNoteCommand>(_jsonSerializer);
                    Note noteToUpdate = notes.Find(f => f.Id == command.Id);
                    if (noteToUpdate == null) continue;

                    noteToUpdate.Title = command.Title;
                    noteToUpdate.Content = command.Content;
                    noteToUpdate.LastModifiedAt = @event.Timestamp;
                    noteToUpdate.LastModifiedById = command.ModifiedById;
                }

                else if (@event.Type == CaseEventType.removeNoteFromCase)
                {
                    RemoveNoteCommand command = @event.Values.ToObject<RemoveNoteCommand>(_jsonSerializer);
                    notes.Remove(notes.Find(f => f.Id == command.Id));
                }
            }

            return notes;
        }

        private IEnumerable<Stakeholder> HandleStakeholderEvents(Case @case, IEnumerable<CaseEvent> events)
        {
            var stakeholders = @case.Stakeholders == null ? new List<Stakeholder>() : new List<Stakeholder>(@case.Stakeholders);

            foreach (CaseEvent @event in events)
            {
                if (@event.Type == CaseEventType.addStakeholderToCase)
                {
                    AddStakeholderCommand command = @event.Values.ToObject<AddStakeholderCommand>(_jsonSerializer);
                    var stakeholder = new Stakeholder
                    {
                        Id = command.Id,
                        EntityId = command.EntityId,
                        Type = command.Type,
                        Name = command.Name,
                        Code = command.Code
                    };

                    stakeholders.Add(stakeholder);
                }

                else if (@event.Type == CaseEventType.updateStakeholderOfCase)
                {
                    UpdateStakeholderCommand command = @event.Values.ToObject<UpdateStakeholderCommand>(_jsonSerializer);
                    Stakeholder stakeholderToUpdate = stakeholders.Find(s => s.Id == command.Id);
                    if (stakeholderToUpdate == null) continue;

                    if (command.IsEntityIdChanged) stakeholderToUpdate.EntityId = command.EntityId;
                    if (command.IsTypeChanged) stakeholderToUpdate.Type = command.Type;
                }

                else if (@event.Type == CaseEventType.removeStakeholderFromCase)
                {
                    RemoveStakeholderCommand command = @event.Values.ToObject<RemoveStakeholderCommand>(_jsonSerializer);
                    stakeholders.Remove(stakeholders.Find(f => f.Id == command.Id));
                }
            }

            return stakeholders;
        }

        private IEnumerable<DetailedEventLog> HandleLogs(IEnumerable<CaseEvent> caseEvents) => //Reminder: add new command to here
           caseEvents.Select(e =>
               new DetailedEventLog
               {
                   RelatedId = e.CaseId,
                   Type = e.Type.ToString(),
                   Timestamp = e.Timestamp,
                   ById = GetById(e),
                   Value = e.Values,
               })?.ToList();

        private void HandleAssociatedContracts(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> associatedContractEvents)
        {
            foreach (CaseEvent @event in associatedContractEvents)
            {
                if (@event.Type == CaseEventType.addAssociatedContractToOffer)
                {
                    AddAssociatedContractCommand command = @event.Values.ToObject<AddAssociatedContractCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(b => b.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    if (offerToUpdate.AssociatedContracts == null)
                        offerToUpdate.AssociatedContracts = new List<AssociatedContract> { };

                    offerToUpdate.AssociatedContracts.Add(new AssociatedContract { Id = command.Id, ContractId = command.ContractId });
                }

                else if (@event.Type == CaseEventType.removeAssociatedContractFromOffer)
                {
                    RemoveAssociatedContractCommand command = @event.Values.ToObject<RemoveAssociatedContractCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(b => b.Id == command.OfferId);

                    if (offerToUpdate == null)
                        continue;

                    offerToUpdate.AssociatedContracts?.Remove(offerToUpdate.AssociatedContracts.Find(a => a.Id == command.Id));
                }
            }
        }

        private void HandleCommissions(IEnumerable<Proposal> proposals, IEnumerable<CaseEvent> events)
        {
            foreach (CaseEvent @event in events)
            {
                if (@event.Type == CaseEventType.addCommissonToOffer)
                {
                    AddCommissionCommand command = @event.Values.ToObject<AddCommissionCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(b => b.Id == command.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    if (offerToUpdate.Commissions == null)
                        offerToUpdate.Commissions = new List<Commission>();

                    var commission = new Commission();

                    commission.Id = command.Id;
                    commission.EntityId = command.EntityId;
                    commission.JsonRule = command.JsonRule;
                    commission.Remark = command.Remark;
                    commission.CreatedById = command.AddedById;
                    commission.LastModifiedById = command.AddedById;
                    commission.CreatedAt = @event.Timestamp;
                    commission.LastModifiedAt = @event.Timestamp;

                    offerToUpdate.Commissions.Add(commission);
                }

                if (@event.Type == CaseEventType.updateCommissionOfOffer)
                {
                    UpdateCommissionCommand command = @event.Values.ToObject<UpdateCommissionCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(b => b.Id == command.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    Commission commissionToUpdate = offerToUpdate.Commissions.Find(p => p.Id == command.Id);
                    if (commissionToUpdate == null)
                        continue;

                    if (command.IsEntityIdChanged)
                        commissionToUpdate.EntityId = command.EntityId;
                    if (command.IsJsonRuleChanged)
                        commissionToUpdate.JsonRule = command.JsonRule;
                    if (command.IsRemarkChanged)
                        commissionToUpdate.Remark = command.Remark;

                    commissionToUpdate.LastModifiedById = command.ModifiedById;
                    commissionToUpdate.LastModifiedAt = @event.Timestamp;
                }

                if (@event.Type == CaseEventType.removeCommissionFromOffer)
                {
                    RemoveCommissionCommand command = @event.Values.ToObject<RemoveCommissionCommand>(_jsonSerializer);

                    Offer offerToUpdate = proposals?.FirstOrDefault(p => p.Id == command.ProposalId)?.Basket?.FirstOrDefault(b => b.Id == command.OfferId);
                    if (offerToUpdate == null)
                        continue;

                    offerToUpdate.Commissions?.Remove(offerToUpdate.Commissions.Find(a => a.Id == command.Id));
                }
            }
        }

        private IEnumerable<BeneficiaryEligibility> HandleBeneficiaryEligibilities(IEnumerable<CaseEvent> benefitEligibilityEvents)
        {
            var beneficiaryEligibilities = new List<BeneficiaryEligibility>();

            foreach (CaseEvent benEliEvent in benefitEligibilityEvents)
            {
                if (benEliEvent.Type == CaseEventType.addBeneficiaryEligibilityToCase)
                {
                    AddBeneficiaryEligibilityCommand command = benEliEvent.Values.ToObject<AddBeneficiaryEligibilityCommand>(_jsonSerializer);
                    var beneficiaryEligibility = new BeneficiaryEligibility { };

                    beneficiaryEligibility.Id = command.Id;
                    beneficiaryEligibility.BenefitTypeId = command.BenefitTypeId;
                    beneficiaryEligibility.IsRevocable = command.IsRevocable;
                    beneficiaryEligibility.Notes = command.Notes;
                    beneficiaryEligibility.Ratio = command.Ratio;
                    beneficiaryEligibility.CreatedById = command.AddedById;
                    beneficiaryEligibility.LastModifiedById = command.AddedById;
                    beneficiaryEligibility.CreatedAt = benEliEvent.Timestamp;
                    beneficiaryEligibility.LastModifiedAt = benEliEvent.Timestamp;
                    beneficiaryEligibility.ContractEntity = new Entity { Id = command.EntityId };

                    beneficiaryEligibilities.Add(beneficiaryEligibility);
                    continue;
                };

                if (benEliEvent.Type == CaseEventType.updateBeneficiaryEligibilityOfCase)
                {
                    UpdateBeneficiaryEligibilityCommand command = benEliEvent.Values.ToObject<UpdateBeneficiaryEligibilityCommand>(_jsonSerializer);

                    BeneficiaryEligibility beneficiaryEligibilityToUpdate = beneficiaryEligibilities.Find(b => b.Id == command.Id);
                    if (beneficiaryEligibilityToUpdate != null)
                    {
                        if (command.IsBenefitTypeIdChanged)
                            beneficiaryEligibilityToUpdate.BenefitTypeId = command.BenefitTypeId;
                        if (command.IsEntityIdChanged)
                            beneficiaryEligibilityToUpdate.ContractEntity.Id = command.EntityId;
                        if (command.IsIsRevocableChanged)
                            beneficiaryEligibilityToUpdate.IsRevocable = command.IsRevocable;
                        if (command.IsNotesChanged)
                            beneficiaryEligibilityToUpdate.Notes = command.Notes;
                        if (command.IsRatioChanged)
                            beneficiaryEligibilityToUpdate.Ratio = command.Ratio;

                        beneficiaryEligibilityToUpdate.LastModifiedAt = benEliEvent.Timestamp;
                        beneficiaryEligibilityToUpdate.LastModifiedById = command.ModifiedById;
                    }

                    continue;
                }

                if (benEliEvent.Type == CaseEventType.removeBeneficiaryEligibilityFromCase)
                {
                    RemoveCommand command = benEliEvent.Values.ToObject<RemoveCommand>(_jsonSerializer);
                    beneficiaryEligibilities.Remove(beneficiaryEligibilities.Find(b => b.Id == command.Id));

                    continue;
                }
            }

            return beneficiaryEligibilities;
        }

        private IEnumerable<PaymentInfo> HandlePaymentInfos(IEnumerable<CaseEvent> policyEvents)
        {
            var paymentInfos = new List<PaymentInfo>();
            foreach (CaseEvent paymentInfoEvent in policyEvents)
            {
                if (paymentInfoEvent.Type == CaseEventType.addPaymentInfoToCase)
                {
                    AddPaymentInfoCommand command = paymentInfoEvent.Values.ToObject<AddPaymentInfoCommand>(_jsonSerializer);
                    var paymentInfo = new PaymentInfo { };

                    paymentInfo.Id = command.Id;
                    paymentInfo.Name = command.Name;
                    paymentInfo.PayorId = command.PayorId;
                    paymentInfo.Amount = command.Amount;
                    paymentInfo.CurrencyCode = command.CurrencyCode;
                    paymentInfo.Comment = command.Comment;
                    paymentInfo.StartDate = command.StartDate;
                    paymentInfo.EndDate = command.EndDate;
                    paymentInfo.Frequency = command.Frequency;
                    paymentInfo.Method = command.Method;
                    paymentInfo.CreatedById = command.AddedById;
                    paymentInfo.LastModifiedById = command.AddedById;
                    paymentInfo.CreatedAt = paymentInfoEvent.Timestamp;
                    paymentInfo.LastModifiedAt = paymentInfoEvent.Timestamp;

                    paymentInfos.Add(paymentInfo);

                    continue;
                }

                if (paymentInfoEvent.Type == CaseEventType.updatePaymentInfoOfCase)
                {
                    UpdatePaymentInfoCommand command = paymentInfoEvent.Values.ToObject<UpdatePaymentInfoCommand>(_jsonSerializer);
                    PaymentInfo paymentInfoToUpdate = paymentInfos.Find(p => p.Id == command.Id);
                    if (paymentInfoToUpdate != null)
                    {
                        if (command.IsNameChanged)
                            paymentInfoToUpdate.Name = command.Name;
                        if (command.IsFrequencyChanged)
                            paymentInfoToUpdate.Frequency = command.Frequency;
                        if (command.IsMethodChanged)
                            paymentInfoToUpdate.Method = command.Method;
                        if (command.IsAmountChanged)
                            paymentInfoToUpdate.Amount = command.Amount;
                        if (command.IsCurrencyCodeChanged)
                            paymentInfoToUpdate.CurrencyCode = command.CurrencyCode;
                        if (command.IsStartDateChanged)
                            paymentInfoToUpdate.StartDate = command.StartDate.GetValueOrDefault();
                        if (command.IsEndDateChanged)
                            paymentInfoToUpdate.EndDate = command.EndDate.GetValueOrDefault();
                        if (command.IsCommentChanged)
                            paymentInfoToUpdate.Comment = command.Comment;
                        if (command.IsPayorIdChanged)
                            paymentInfoToUpdate.PayorId = command.PayorId;

                        paymentInfoToUpdate.LastModifiedById = command.ModifiedById;
                        paymentInfoToUpdate.LastModifiedAt = paymentInfoEvent.Timestamp;
                    }

                    continue;
                }

                if (paymentInfoEvent.Type == CaseEventType.removePaymentInfoFromCase)
                {
                    RemoveCommand command = paymentInfoEvent.Values.ToObject<RemoveCommand>(_jsonSerializer);
                    paymentInfos.Remove(paymentInfos.Find(p => p.Id == command.Id));

                    continue;
                }
            }

            return paymentInfos;
        }

        private async Task RecalculatePremia(string tenantId, Case @case, CancellationToken cancellationToken)
        {
            IEnumerable<Offer> offers = @case.Proposals?.SelectMany(p => p.Basket)
                .Where(o => o.Premium?.Amount == null && o.ProductId != null);

            (string offerId, PriceDto2 price)[] results = await offers.ParallelSelectAsync(async (o, ct) =>
                (o.Id, await _pricingService.CalculateAsync(tenantId, GetPriceCalculationFactors(o), ct)),
                new ParallelRunOptions() { MaxDegreeOfParallelism = 10, CancellationToken = cancellationToken });

            foreach (Proposal proposal in @case.Proposals)
            {
                foreach (Offer offer in proposal.Basket)
                {
                    if (offer.Premium?.Amount != null || offer.ProductId == null) //Complete override of amount
                        continue;

                    offer.Premium = Array.Find(results, t => t.offerId == offer.Id).price?.ToDomain(offer.Premium?.IsPricedAtStartDate);
                }

                if (proposal.TotalPrice == null)
                    proposal.TotalPrice = new Premium { Amount = proposal.Basket.Sum(b => b.Premium?.Amount ?? 0) };

                //else if (proposal.TotalPrice.Discounts?.Any() ?? false) //Note: implement this when they use overrides of proposal
                //    proposal.TotalPrice = (await _pricingService.CalculateAsync(tenantId,
                //        new PriceCalculationFactors
                //        {
                //            Filter = new PriceFilter
                //            {
                //                DiscountCodes = proposal.TotalPrice?.AppliedDiscounts?.Select(d => d.Code), // Need to add DiscounCodes in offer
                //                Discounts = proposal.TotalPrice?.Discounts
                //            },
                //            OriginalPrice = proposal.TotalPrice?.OriginalPrice,
                //            Loadings = proposal.TotalPrice?.Loadings
                //        }))?.ToDomain();
            }
        }

        private static PriceCalculationFactors GetPriceCalculationFactors(Offer offer)
        {
            DateTime GetPricingDate() =>
                offer.Premium?.IsPricedAtStartDate == true
                    ? offer.StartDate ?? offer.CreatedAt
                    : offer.CreatedAt;

            return new PriceCalculationFactors
            {
                Filter = new PriceFilter
                {
                    ProductIds = new List<ProductId> { offer.ProductId },
                    Factors = offer.Values,
                    DiscountCodes = offer.Premium?.DiscountCodes,
                    PricingDate = GetPricingDate()
                },
                OriginalPrice = offer.Premium?.OriginalPrice,
                ManualDiscounts = offer.Premium?.Discounts?.Where(d => d.IsManual),
                Loadings = offer.Premium?.Loadings
            };
        }

        private string GetById(CaseEvent @event) //Reminder: check if new eventTypes need to be added here
        {
            if (@event.Type == CaseEventType.creation) return @event.Values.Value<string>("createdById");
            else if (_addEvents.Contains(@event.Type)) return @event.Values.Value<string>("addedById");
            else if (_removeEvents.Contains(@event.Type)) return @event.Values.Value<string>("removedById");
            else if (@event.Type == CaseEventType.issueProposal) return @event.Values.Value<string>("issuedById");
            else return @event.Values.Value<string>("modifiedById");
        }
        #endregion

        public async Task<Result<string>> CreateAsync(string tenantId, CreateCaseCommand command, CancellationToken cancellationToken) =>
            await CreateInternalAsync(tenantId, command, CaseEventType.creation, cancellationToken);

        public async Task<Result<string>> AgentCreateAsync(string tenantId, AgentCreateCaseCommand command, CancellationToken cancellationToken) =>
            await CreateInternalAsync(tenantId, command, CaseEventType.agentCreateCase, cancellationToken);

        private async Task<Result<string>> CreateInternalAsync(string tenantId, CreateCaseCommand command, CaseEventType caseEventType, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            IReferenceGenerator refGen = _referenceGeneratorResolver(tenantId);
            command.CaseNumber ??= await refGen.GenerateAsync(tenantId, "createCase", JToken.FromObject(command, _jsonSerializer), cancellationToken: cancellationToken);

            // Generate a sequential number if the feature is enabled for this tenant
            if (await _featureManager.IsEnabled("GENERATE_SEQUENTIAL_NUMBER_IN_CASE_FIELDS", tenantId))
            {
                string sequentialNumber = await refGen.GenerateAsync(tenantId, "createCaseSequentialNumber", JToken.FromObject(command, _jsonSerializer), cancellationToken: cancellationToken);

                // Parse existing Fields JSON or create new JObject if Fields is null or empty or whitespace
                JObject fieldsObj = string.IsNullOrWhiteSpace(command.Fields)
                    ? new JObject()
                    : JObject.Parse(command.Fields);

                // Add sequential number to Fields
                fieldsObj["sequentialNumber"] = sequentialNumber;

                // Update command.Fields with the new JSON string
                command.Fields = fieldsObj.ToString(Formatting.None);
            }

            var caseEvent = new CaseEvent(id, caseEventType, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            if (!tenantId.StartsWith("walaa")) // Not for Walaa, please. The guy below spends ~400-500ms on something meaningless to Walaa.
            {
                await _messageBrokerClient.SendMessageAsync(command, tenantId, $"case_{caseEventType}");
            }

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateAsync(string tenantId, string caseId, UpdateCaseCommand command, CancellationToken cancellationToken)
        {
            if (command.FieldsPatch != null)
            {
                bool isValid = ValidateFieldsPatchDocument(command.FieldsPatch);
                if (!isValid)
                    return Result.Failure($"{nameof(UpdateCaseCommand.FieldsPatch)}: `{command.FieldsPatch}` is not a valid JsonPatchDocument.");
            }

            var caseEvent = new CaseEvent(caseId, CaseEventType.update, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        private static bool ValidateFieldsPatchDocument(string fieldsPatch)
        {
            try
            {
                JsonPatchDocument jsonPatchDocument = JsonConvert.DeserializeObject<JsonPatchDocument>(fieldsPatch);
                return jsonPatchDocument != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<Result> DeleteAsync(string tenantId, string caseId, DeleteCaseCommand command, CancellationToken cancellationToken)
        {
            ICaseRepository repository = _caseRepositoryResolver(tenantId);
            Case @case = (await repository.GetAsync(tenantId, new CaseWhere { Id = caseId }, cancellationToken: cancellationToken)).FirstOrDefault();
            if (@case == null)
                return Result.Failure($"{tenantId}: The case '{caseId}' doesn't exist and can't be deleted");

            var caseEvent = new CaseEvent(caseId, CaseEventType.delete, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> AddIssuedPolicyAsync(string tenantId, string caseId, AddIssuedPolicyCommand command, CancellationToken cancellationToken)
        {
            CaseEvent caseEvent = new CaseEvent(caseId, CaseEventType.addIssuedPolicy, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddProposalAsync(string tenantId, string caseId, AddProposalCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            IReferenceGenerator refGen = _referenceGeneratorResolver(tenantId);
            command.ProposalNumber ??= await refGen.GenerateAsync(tenantId, command.ProposalNumberType ?? "addProposal", JToken.FromObject(command, _jsonSerializer), accessToken, new GraphQlVariables
                {
                    CaseWhere1 = new CaseWhere { Id = caseId },
                    IsRenewalProposalWhere1 = new ProposalWhere { Id = command.RenewalHistory?.RenewedFromId ?? "none if null" }
                }, cancellationToken);

            var caseEvent = new CaseEvent(caseId, CaseEventType.addProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result<ProposalAndOfferIds>> AddAgentProposalAsync(string tenantId, string caseId, AddAgentProposalCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            string id = Guid.NewGuid().ToString();
            command.ProposalCommand.Id = id;

            string offerId = Guid.NewGuid().ToString();
            command.OfferCommand.OfferId = offerId;

            IReferenceGenerator refGen = _referenceGeneratorResolver(tenantId);
            command.ProposalCommand.ProposalNumber ??= await refGen.GenerateAsync(tenantId, $"addProposal_{command.OfferCommand.ProductId.Type}", JToken.FromObject(command, _jsonSerializer), accessToken,
                new GraphQlVariables
                {
                    CaseWhere1 = new CaseWhere { Id = caseId },
                    IsRenewalProposalWhere1 = new ProposalWhere { Id = command.ProposalCommand.RenewalHistory?.RenewedFromId ?? "none if null" }
                }, cancellationToken);

            var caseEvent = new CaseEvent(caseId, CaseEventType.createAgentProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<ProposalAndOfferIds>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = new ProposalAndOfferIds
                {
                    ProposalId = id,
                    OfferId = offerId
                }
            };
        }

        public async Task<Result<string>> UpdateAgentProposalAsync(string tenantId, string caseId, UpdateAgentProposalCommand command, CancellationToken cancellationToken = default)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateAgentProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };
            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = command.ProposalCommand.ProposalId
            };
        }

        public async Task<Result<string>> CopyProposalAsync(string tenantId, string caseId, CopyProposalCommand command, CancellationToken cancellationToken)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, cancellationToken)).FirstOrDefault();
            if (@case == null)
                return new Result<string> { Status = "failure", Errors = new List<string> { $"The case '{caseId}' doesn't exist." } };

            Proposal proposalToCopy = @case.Proposals?.FirstOrDefault(p => p.Id == command.CopiedFromId);
            if (proposalToCopy == null)
                return new Result<string> { Status = "failure", Errors = new List<string> { $"The proposal '{command.CopiedFromId}' doesn't exist." } };

            // add new proposal
            var addProposalCommand = new AddProposalCommand
            {
                AddedById = command.CopiedById,
                Name = proposalToCopy.Name
            };

            Result<string> proposalResult = await AddProposalAsync(tenantId, caseId, addProposalCommand, cancellationToken: cancellationToken);
            if (proposalResult.Status != "success")
                return proposalResult;

            // add offers
            foreach (Offer offerToCopy in proposalToCopy.Basket)
            {
                var addOfferCommand = new AddOfferCommand
                {
                    AddedById = command.CopiedById,
                    BenefitOptions = offerToCopy.BenefitOptions,
                    ProductId = offerToCopy.ProductId,
                    ProposalId = proposalResult.Value,
                    Values = offerToCopy.Values,
                    Fields = offerToCopy.Fields2?.ToString(Formatting.None) ?? offerToCopy.Fields,
                    StartDate = offerToCopy.StartDate,
                    EndDate = offerToCopy.EndDate
                };

                Result<string> offerResult = await AddOfferAsync(tenantId, caseId, addOfferCommand, cancellationToken: cancellationToken);
                if (offerResult.Status != "success")
                    return offerResult;

                foreach (Fact fact in offerToCopy.Facts)
                {
                    string offerFactId = Guid.NewGuid().ToString();
                    Result<string> addOfferFactResult = await AddFactToOfferAsync(tenantId, caseId, new AddFactCommand
                    {
                        AddedById = command.CopiedById,
                        Id = offerFactId,
                        OfferId = offerResult.Value,
                        ProposalId = proposalResult.Value,
                        Type = fact.Type,
                        Value = fact.Value,
                    }, cancellationToken);

                    if (addOfferFactResult.Status != "success")
                        return new Result<string>
                        {
                            Status = addOfferFactResult.Status,
                            Errors = addOfferFactResult.Errors
                        };
                }
            }

            return Result<string>.Success(proposalResult.Value);
        }

        public async Task<Result<string>> RenewProposalAsync(string tenantId, string caseId, RenewProposalCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, cancellationToken)).FirstOrDefault();
            if (@case == null)
                return new Result<string> { Status = "failure", Errors = new List<string> { $"The case '{caseId}' doesn't exist." } };

            Proposal proposalToRenew = @case.Proposals?.FirstOrDefault(p => p.Id == command.RenewedFromId);
            if (proposalToRenew == null)
                return new Result<string> { Status = "failure", Errors = new List<string> { $"The proposal '{command.RenewedFromId}' doesn't exist." } };

            IReferenceGenerator refGen = _referenceGeneratorResolver(tenantId);
            // add new proposal
            var addProposalCommand = new AddProposalCommand
            {
                AddedById = command.RenewedById,
                Name = proposalToRenew.Name,
                RenewalHistory = new RenewalHistory { RenewedFromId = command.RenewedFromId },
                ProposalNumber = command.ProposalNumber ?? await refGen.GenerateAsync(tenantId, "renewProposal", JToken.FromObject(command, _jsonSerializer), accessToken,
                    new GraphQlVariables
                    {
                        CaseWhere1 = new CaseWhere { Id = caseId },
                        RenewFromProposalWhere = new ProposalWhere { Id = command.RenewedFromId }
                    }, cancellationToken)
            };

            Result<string> proposalResult = await AddProposalAsync(tenantId, caseId, addProposalCommand, accessToken, cancellationToken);
            if (proposalResult.Status != "success")
                return proposalResult;

            // add offers
            foreach (Offer offerToRenew in proposalToRenew.Basket)
            {
                var addOfferCommand = new AddOfferCommand
                {
                    AddedById = command.RenewedById,
                    BenefitOptions = offerToRenew.BenefitOptions,
                    ProductId = offerToRenew.ProductId,
                    ProposalId = proposalResult.Value,
                    Values = offerToRenew.Values,
                };

                if (command.OverrideStartDateAndEndDateAutomatically && offerToRenew.EndDate.HasValue && offerToRenew.StartDate.HasValue)
                {
                    DateTime renewalStartDate = offerToRenew.EndDate.Value.AddDays(1);
                    addOfferCommand.StartDate = renewalStartDate;
                    addOfferCommand.EndDate = renewalStartDate.Add(offerToRenew.EndDate.Value - offerToRenew.StartDate.Value);
                }

                Result<string> offerResult = await AddOfferAsync(tenantId, caseId, addOfferCommand, accessToken, cancellationToken);
                if (offerResult.Status != "success")
                    return offerResult;

                foreach (Fact fact in offerToRenew.Facts)
                {
                    string offerFactId = Guid.NewGuid().ToString();
                    Result<string> addOfferFactResult = await AddFactToOfferAsync(tenantId, caseId, new AddFactCommand
                    {
                        AddedById = command.RenewedById,
                        Id = offerFactId,
                        OfferId = offerResult.Value,
                        ProposalId = proposalResult.Value,
                        Type = fact.Type,
                        Value = fact.Value,
                    }, cancellationToken);

                    if (addOfferFactResult.Status != "success")
                        return new Result<string>
                        {
                            Status = addOfferFactResult.Status,
                            Errors = addOfferFactResult.Errors
                        };
                }
            }

            // update renewal history of previous proposal
            var updateProposalCommand = new UpdateProposalCommand
            {
                ProposalId = proposalToRenew.Id,
                IsRenewalHistoryChanged = true,
                RenewalHistory = new RenewalHistory
                {
                    RenewedFromId = proposalToRenew.RenewalHistory.RenewedFromId,
                    RenewedToId = proposalResult.Value
                }
            };

            Result updateResult = await UpdateProposalAsync(tenantId, caseId, updateProposalCommand, cancellationToken);
            return updateResult.Status != null
                ? new Result<string> { Status = updateResult.Status, Errors = updateResult.Errors, Value = proposalResult.Value }
                : new Result<string>
                {
                    Status = "success",
                    Value = proposalResult.Value
                };
        }

        public async Task<Result> IssueProposalAsync(string tenantId, string caseId, IssueProposalCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.issueProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            if (tenantId == "companyCover_uat" || tenantId == "digitalBroker")
            {
                Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, cancellationToken)).FirstOrDefault();
                if (@case == null)
                    return new Result { Status = "failure", Errors = new List<string> { $"The case '{caseId}' doesn't exist." } };

                Proposal proposal = @case.Proposals?.FirstOrDefault(p => p.Id == command.ProposalId);
                if (proposal == null)
                    return new Result { Status = "failure", Errors = new List<string> { $"The case '{caseId}' doesn't have a proposal '{command.ProposalId}'." } };

                Offer offer = proposal.Basket?.FirstOrDefault();
                if (offer == null)
                    return new Result { Status = "failure", Errors = new List<string> { $"The proposal '{command.ProposalId}' does not have any offers." } };

                int? numberOfEmployees = offer?.Values?.Value<JArray>("insureds")?.Children().Count();
                if (numberOfEmployees == null)
                    return new Result { Status = "failure", Errors = new List<string> { $"The offer of the proposal to issue is missing the value `insureds`" } };

                List<Transaction> proposalTransactions = await _transactionService.GetAsync(tenantId, new TransactionQueryArguments { Where = new TransactionWhere { ProposalId = command.ProposalId } }, cancellationToken);
                string templateId = null;

                if (tenantId == "companyCover_uat") //need to add settings for companyCover prod
                {
                    if (proposalTransactions?.Exists(t => t.Status == TransactionStatus.Approved) ?? false)
                        templateId = numberOfEmployees >= 4 ? "62ea0505-b719-4022-b03c-258178843dec" : "e3799431-5c53-42c1-be36-4e34ce635e71";
                    else
                        templateId = numberOfEmployees >= 4 ? "47aff027-70de-4a49-bfd9-6c2d62a5df33" : "1d9c135b-b266-4504-a822-46412e262767";
                }
                else if (tenantId == "digitalBroker")
                {
                    if (proposalTransactions?.Exists(t => t.Status == TransactionStatus.Approved) ?? false)
                        templateId = numberOfEmployees >= 4 ? "65e55b81-f892-4c44-bc62-a171bbc59f04" : "43584b37-e8da-4b4d-9880-5894ceb9c6c7";
                    else
                        templateId = numberOfEmployees >= 4 ? "fe9b2553-4668-494c-ba8e-92faf731c4b7" : "8c917c5d-369f-4d5a-aa9b-c423d26c10bf";
                }

                Token token = await _authService.GetWholeAccessTokenAsync(tenantId, "covergo_crm", "",
                    tenantId == "companyCover_uat" ? "<EMAIL>" : "<EMAIL>",
                    tenantId == "companyCover_uat" ? "adminadmin" : "jXw^;5>jE;_[3M5_",
                    cancellationToken);

                await _notificationService.SendAsync(tenantId, new SendNotificationCommand
                {
                    ToEntityId = @case.HolderId,
                    EmailMessage = new EmailMessage
                    {
                        Subject = "CompanyCover application",
                        TemplateRendering = new TemplateRendering
                        {
                            TemplateId = templateId,
                            Input = new RenderParameters
                            {
                                AccessToken = token.AccessToken,
                                Name = "data",
                                Variables = JObject.Parse($"{{\"caseWhere1\":{{\"id\":\"{caseId}\"}},\"proposalWhere1\":{{\"id\":\"{command.ProposalId}\"}},\"offerWhere1\":{{\"id\":\"{@case.Proposals.FirstOrDefault(p => p.Id == command.ProposalId).Basket.FirstOrDefault().Id}\"}}}}")
                            }
                        },
                    },
                    UseConfig = true
                }, cancellationToken);
            }

            return result;
        }

        public async Task<Result> SetSystemDatesAsync(string tenantId, string caseId, AdminSetSystemDatesCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.setSystemDates, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> SetReadOnlyAsync(string tenantId, string caseId, AdminSetReadOnlyCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.setReadOnly, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> UpdateProposalAsync(string tenantId, string caseId, UpdateProposalCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveProposalAsync(string tenantId, string caseId, RemoveCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddOfferAsync(string tenantId, string caseId, AddOfferCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            IReferenceGenerator refGen = _referenceGeneratorResolver(tenantId);

            string id = Guid.NewGuid().ToString();
            command.OfferId = id;
            command.OfferNumber ??= await refGen.GenerateAsync(tenantId, command.OfferNumberType ?? "addOffer", JToken.FromObject(command, _jsonSerializer), accessToken,
                new GraphQlVariables
                {
                    CaseWhere1 = new CaseWhere { Id = caseId },
                    ProposalWhere1 = new ProposalWhere { Id = command.ProposalId },
                    TypeId = command.ProductId?.Type
                }, cancellationToken);

            var caseEvent = new CaseEvent(caseId, CaseEventType.addOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateOfferAsync(string tenantId, string caseId, UpdateOfferCommand command, CancellationToken cancellationToken)
        {
            Case existingCase = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, cancellationToken)).FirstOrDefault();

            if (existingCase == null)
                return Result.Failure($"The case '{caseId}' does not exist.");

            Proposal existingProposal = existingCase.Proposals?.FirstOrDefault(p => p.Id == command.ProposalId);
            if (existingProposal == null)
                return Result.Failure($"The case '{caseId}' does not have a proposal with id '{command.ProposalId}'.");

            if (existingProposal.Basket.FirstOrDefault(b => b.Id == command.OfferId) == null)
                return Result.Failure($"The proposal '{command.ProposalId}' for case '{caseId}' does not have an offer with id '{command.OfferId}'.");

            var caseEvent = new CaseEvent(caseId, CaseEventType.updateOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveOfferAsync(string tenantId, string caseId, RemoveOfferFromProposalCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddFactToCaseAsync(string tenantId, string caseId, AddFactCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addFactToCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
            return new Result<CreatedStatus> { Status = result.Status, Errors = result.Errors, Value = result.Status != "success" ? null : new CreatedStatus { Id = command.Id } };
        }

        public async Task<Result> UpdateFactOfCaseAsync(string tenantId, string caseId, UpdateFactCommand command, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(command.Id))
                return Result.Failure($"Missing id when updating fact {command.Type}");

            var caseEvent = new CaseEvent(caseId, CaseEventType.updateFactOfCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> CaseFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch, CancellationToken cancellationToken)
        {
            foreach (AddFactCommand command in batch.AddFactCommands ?? new List<AddFactCommand> { })
            {
                string factId = Guid.NewGuid().ToString();
                command.Id = factId;
            }

            if (batch.UpdateFactCommands != null)
            {
                var missingIds = batch.UpdateFactCommands.Where(f => string.IsNullOrEmpty(f.Id));
                if (missingIds.Any())
                    return Result.Failure($"Missing id when updating fact {string.Join(", ", missingIds.Select(x => x.Type))}");
            }

            var caseEvent = new CaseEvent(caseId, CaseEventType.caseFactBatch, DateTime.UtcNow)
            {
                Values = JObject.FromObject(batch, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveFactFromCaseAsync(string tenantId, string caseId, RemoveFactCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeFactFromCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> AddNoteToCaseAsync(string tenantId, string caseId, AddNoteCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addNoteToCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> UpdateNoteOfCaseAsync(string tenantId, string caseId, UpdateNoteCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateNoteOfCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveNoteFromCaseAsync(string tenantId, string caseId, RemoveNoteCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeNoteFromCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> GeneratePoliciesAsync(string tenantId, string caseId, GeneratePoliciesFromProposalCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, cancellationToken)).FirstOrDefault();
            if (@case == null)
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The case '{caseId}' doesn't exist." } };

            return await SolvePolicyCreationAsync(tenantId, @case, command, accessToken, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> ConvertOfferToApplication(string tenantId, string loginId, ConvertOfferToApplicationCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            command.AcceptedById = loginId;
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = command.CaseId } }, cancellationToken)).FirstOrDefault();
            if (@case == null)
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The case '{command.CaseId}' doesn't exist." } };


            var result = await SolvePolicyCreationAsync(tenantId, @case, new GeneratePoliciesFromProposalCommand
            {
                ClientId = command.ClientId,
                ProposalId = command.ProposalId,
            }, accessToken, cancellationToken);

            if (result.IsSuccess)
            {
                var policyId = result.Value.Ids.Single();
                foreach (var stakeholder in @case.Stakeholders)
                {
                    await _policyService.AddStakeholderToPolicyAsync(tenantId,
                                        policyId,
                                        new AddStakeholderCommand
                                        {
                                            EntityId = stakeholder.EntityId,
                                            Type = stakeholder.Type,
                                            AddedById = loginId
                                        }, cancellationToken);
                }


                await HandleSingleOfferProposalAcceptanceAsync(tenantId, command.CaseId, command, cancellationToken);
            }

            return result;
        }

        public async Task<Result<CreatedStatus>> SolvePolicyCreationAsync(string tenantId, Case @case, GeneratePoliciesFromProposalCommand command, string accessToken = null, CancellationToken cancellationToken = default)
        {
            Proposal proposal = @case.Proposals?.FirstOrDefault(p => p.Id == command.ProposalId);
            if (proposal == null)
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The case '{@case.Id}' doesn't have the specified proposal." } };

            if (!proposal.Basket?.Any() ?? true)
                return new Result<CreatedStatus> { Status = "failure", Errors = new List<string> { $"The proposal '{proposal.Id}' doesn't have any offers in the basket." } };

            var errors = new List<string> { };
            var policyIds = new List<string> { };

            string policyExtraFields = command.CopyCaseFieldsToExtraFields ? @case.Fields?.ToString(Formatting.None) : null;
            foreach (Offer offer in proposal.Basket)
            {
                var fields2 = offer.Fields2;
                if (fields2 != null && fields2["insuredGroups"] != null)
                {
                    fields2["insuredGroups"].Parent.Remove();
                }
                #region Create Policy
                var createPolicyCommand = new CreatePolicyCommand
                {
                    IssuerNumber = offer.PolicyNumber,
                    GeneratedFrom = new GeneratedFrom
                    {
                        CaseId = @case.Id,
                        ProposalId = proposal.Id,
                        OfferId = offer.Id
                    },
                    ContractHolder = new Entity { Id = @case.HolderId },
                    OtherContractHolders = @case.OtherHolderIds?.Select(i => new Entity { Id = i })?.ToList(),
                    ContractInsured = @case.InsuredIds?.Select(i => new Entity { Id = i })?.ToList(),
                    Source = @case.Source,
                    StartDate = offer.StartDate,
                    EndDate = offer.EndDate,
                    Values = offer.Values,
                    CreatedById = command.GeneratedById,
                    ClientId = command.ClientId,
                    ProductId = offer.ProductId,
                    BenefitOptions = offer.BenefitOptions,
                    ReferralCode = proposal.ReferralCode,
                    Fields = fields2 != null ? fields2.ToString(Formatting.None) : offer.Fields,
                    ExtraFields = policyExtraFields,
                    Status = command.PolicyStatus,
                    ProductTreeId = offer.ProductTreeId,
                    ProductTreeRecords = offer.ProductTreeRecords,
                    AccessPolicy = @case?.AccessPolicy,
                    ChannelId = @case?.ChannelId,
                    PolicyCommission = new PolicyCommission(offer.DistributorID, offer.CampaignCodes),
                };

                if (offer.IsPremiumOverridden)
                {
                    createPolicyCommand.Premium = offer.Premium;
                    createPolicyCommand.IsPremiumOverridden = offer.IsPremiumOverridden;
                }
                else
                    createPolicyCommand.Premium = offer.Premium != null
                        ? new Premium
                        {
                            AppliedDiscounts = offer.Premium.AppliedDiscounts,
                            DiscountCodes = offer.Premium.DiscountCodes,
                            Discounts = offer.Premium.Discounts,
                            Loadings = offer.Premium.Loadings,
                            AppliedTaxes = offer.Premium.AppliedTaxes,
                            IsPricedAtStartDate = offer.Premium.IsPricedAtStartDate
                        } : null;

                Result<PolicyStatus> createResult = await _policyService.CreatePolicyAsync(tenantId, createPolicyCommand, accessToken);
                if (createResult?.Status != "success")
                {
                    errors.Concat(createResult.Errors);
                    continue;
                }
                var policyId = createResult.Value.Id;
                #endregion

                var parallelRunOptions = new ParallelRunOptions()
                {
                    CancellationToken = cancellationToken,
                    MaxDegreeOfParallelism = 10
                };

                if (offer?.AssociatedContracts?.Any() ?? false)
                {
                    await offer?.AssociatedContracts.Where(c => c != null)?.ParallelForEachAsync((a, ct) =>
                        _policyService?.AddAssociatedContractToPolicyAsync(tenantId, policyId,
                            new AddAssociatedContractCommand
                            {
                                ContractId = a?.ContractId,
                                AddedById = command?.GeneratedById
                            }, ct), parallelRunOptions);
                }
                if (offer?.Clauses?.Any() ?? false)
                {
                    await offer?.Clauses?.ParallelForEachAsync((c, ct) =>
                        _policyService.AddClauseToPolicyAsync(tenantId,
                            policyId,
                            new AddClauseCommand
                            {
                                Order = c?.Order ?? 0,
                                TemplateId = c?.TemplateId,
                                Template = c?.Template,
                                RenderParameters = c?.RenderParameters,
                                HtmlOverride = c?.HtmlOverride,
                                AddedById = command?.GeneratedById
                            }, ct), parallelRunOptions);
                }
                if (offer.Stakeholders?.Any() ?? false)
                {
                    await offer.Stakeholders?.ParallelForEachAsync((o, ct) =>
                        _policyService.AddStakeholderToPolicyAsync(tenantId,
                            policyId,
                            new AddStakeholderCommand
                            {
                                EntityId = o.EntityId,
                                Type = o.Type,
                                AddedById = command.GeneratedById
                            }, ct), parallelRunOptions);
                }
                if (offer.Exclusions?.Any() ?? false)
                {
                    await offer.Exclusions?.ParallelForEachAsync((o, ct) => _policyService.AddExclusionAsync(tenantId,
                        policyId,
                        new AddExclusionCommand
                        {
                            Code = o.Code,
                            Remark = o.Remark,
                            BenefitParentTypeId = o.BenefitParentTypeId,
                            BenefitTypeId = o.BenefitTypeId,
                            BenefitOptionKey = o.BenefitOptionKey,
                            AddedById = command.GeneratedById
                        }, ct), parallelRunOptions);
                }

                if (offer.Jackets?.Any() ?? false)
                {
                    var addJacketsToPolicyCommand = new AddJacketsToPolicyCommand
                    {
                        AddJacketInstanceCommands = offer.Jackets.Select(x => new AddJacketInstanceCommand
                        {
                            JacketId = x.JacketId,
                            AddedById = command.GeneratedById,
                            InstanceId = Guid.NewGuid().ToString(),
                            Order = x.Order,
                            StoreJacketByValue = x.Jacket != null,
                            Jacket = x.Jacket
                        }).ToList()
                    };

                    await _policyService.AddJacketsToPolicyAsync(tenantId, policyId,
                        addJacketsToPolicyCommand, cancellationToken);
                }

                var factsToInput = @case.Facts.Concat(offer.Facts).ToList();
                if (factsToInput?.Any() ?? false)
                    await _policyService.PolicyFactBatch(tenantId, policyId, new FactCommandBatch
                    {
                        AddFactCommands = factsToInput.Select(f => new AddFactCommand
                        {
                            Type = f.Type,
                            Value = f.Value,
                            AddedById = command.GeneratedById
                        })?.ToList()
                    }, cancellationToken);

                foreach (Commission commission in offer.Commissions)
                {
                    var addCommissionCommand = new AddCommissionCommand
                    {
                        AddedById = command.GeneratedById,
                        JsonRule = commission.JsonRule,
                        EntityId = commission.EntityId,
                        Remark = commission.Remark
                    };

                    Result addCommissionResult = await _policyService.AddCommissionAsync(tenantId, policyId, addCommissionCommand, cancellationToken);
                    if (addCommissionResult.Status != "success")
                    {
                        errors.Concat(addCommissionResult.Errors);
                        continue;
                    }
                }

                #region update policy.PolicyCommission
                {
                    var agentEntityId = @case.Stakeholders.FirstOrDefault(h => h.Type == "Agent")?.EntityId;
                    if (!string.IsNullOrWhiteSpace(agentEntityId))
                    {
                        var login = await _authService.GetLoginByEntityId(tenantId, agentEntityId, cancellationToken);
                        if (login is not null)
                        {
                            var resAgent = await _channelManagmentClient.Agents.ExecuteAsync(skip: 0, take: 1, filter: new() { PortalUserId = login.Id }, null, cancellationToken);
                            var distributorID = resAgent.Data?.Agents?.Items?.FirstOrDefault(a => a.PortalUserId == login.Id)?.DistributorId;
                            if (!string.IsNullOrEmpty(distributorID))
                            {
                                DateTimeOffset? startDate = offer.StartDate is null ? null : new DateTimeOffset(offer.StartDate.Value);
                                var resCommission = startDate is null ? null : await _channelManagmentClient.CommissionCandidates.ExecuteAsync(new() { DistributorID = distributorID, ProductID = new() { Type = offer.ProductId.Type, Plan = offer.ProductId.Plan, Version = offer.ProductId.Version }, Date = startDate.Value }, cancellationToken);
                                var commissionID = resCommission?.Data.CommissionCandidates.FirstOrDefault()?.CommissionID;
                                var res = await _policyClient.Policy_UpdatePolicyPUTAsync(tenantId, policyId, new() { PolicyCommission = new() { DistributorID = distributorID, CommissionID = commissionID } }, cancellationToken);
                                if (!res.IsSuccess) _logger.LogError($"Failed to update policy commission for policy {policyId} with error: {res.Errors.FirstOrDefault()}");
                            }
                        }
                    }
                }
                #endregion

                if (@case.BeneficiaryEligibilities?.Any() ?? false)
                {
                    await @case.BeneficiaryEligibilities?.ParallelForEachAsync((b, ct) =>
                        _policyService.AddBeneficiaryEligibilityAsync(tenantId, policyId,
                            new AddBeneficiaryEligibilityCommand
                            {
                                EntityId = b.ContractEntity?.Id,
                                BenefitTypeId = b.BenefitTypeId,
                                Ratio = b.Ratio,
                                Notes = b.Notes,
                                IsRevocable = b.IsRevocable,
                                AddedById = command.GeneratedById
                            }, ct), parallelRunOptions);
                }

                if (@case.PaymentInfos?.Any() ?? false)
                {
                    await @case.PaymentInfos?.ParallelForEachAsync((p, ct) =>
                        _policyService.AddPaymentInfoAsync(tenantId, policyId,
                            new AddPaymentInfoCommand
                            {
                                Name = p.Name,
                                PayorId = p.PayorId,
                                Frequency = p.Frequency,
                                Method = p.Method,
                                Amount = p.Amount,
                                CurrencyCode = p.CurrencyCode,
                                StartDate = p.StartDate,
                                EndDate = p.EndDate,
                                Comment = p.Comment,
                                AddedById = command.GeneratedById
                            }, ct), parallelRunOptions);
                }

                policyIds.Add(policyId);
            }

            command.PolicyIds = policyIds;
            var caseEvent = new CaseEvent(@case.Id, CaseEventType.generatePolicies, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return errors.Any() ?
                new Result<CreatedStatus> { Status = "failure", Errors = errors } :
                new Result<CreatedStatus> { Status = "success", Value = new CreatedStatus { Ids = policyIds }, Errors = null };
        }

        public async Task<Result<string>> AddStakeholderToCaseAsync(string tenantId, string caseId, AddStakeholderCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addStakeholderToCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateStakeholderOfCaseAsync(string tenantId, string caseId, UpdateStakeholderCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateStakeholderOfCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveStakeHolderFromCaseAsync(string tenantId, string caseId, RemoveStakeholderCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeStakeholderFromCase, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddStakeholderToProposalAsync(string tenantId, string caseId, AddStakeholderCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addStakeholderToProposal, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateStakeholderOfProposalAsync(string tenantId, string caseId, UpdateStakeholderCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateStakeholderOfProposal, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveStakeHolderFromProposalAsync(string tenantId, string caseId, RemoveStakeholderCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeStakeholderFromProposal, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddDiscountToOfferAsync(string tenantId, string caseId, AddDiscountToOfferCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.DiscountId = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addDiscountToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateDiscountOfOfferAsync(string tenantId, string caseId, UpdateDiscountOfOfferCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateDiscountOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveDiscountFromOfferAsync(string tenantId, string caseId, RemoveDiscountFromOfferCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeDiscountFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddLoadingToOfferAsync(string tenantId, string caseId, AddLoadingCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addLoadingToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateLoadingOfOfferAsync(string tenantId, string caseId, UpdateLoadingCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateLoadingOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveLoadingFromOfferAsync(string tenantId, string caseId, RemoveLoadingCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeLoadingFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddExclusionToOfferAsync(string tenantId, string caseId, AddExclusionCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addExclusionToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<CreatedStatus>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = new CreatedStatus { Id = id }
            };
        }

        public async Task<Result> RemoveExclusionFromOfferAsync(string tenantId, string caseId, RemoveExclusionCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeExclusionFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddClauseToOfferAsync(string tenantId, string caseId, AddClauseCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.ClauseId = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addClauseToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateClauseOfOfferAsync(string tenantId, string caseId, UpdateClauseCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateClauseOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveClauseFromOfferAsync(string tenantId, string caseId, RemoveClauseCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeClauseFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> OfferClauseBatchAsync(string tenantId, string caseId, ClauseCommandBatch batch, CancellationToken cancellationToken)
        {
            foreach (AddClauseCommand command in batch.AddClauseCommands ?? new List<AddClauseCommand> { })
            {
                command.ClauseId = Guid.NewGuid().ToString();
            }

            var caseEvent = new CaseEvent(caseId, CaseEventType.offerClauseBatch, DateTime.UtcNow)
            {
                Values = JObject.FromObject(batch, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> OfferJacketBatchAsync(string tenantId, string caseId, JacketCommandBatch batch, CancellationToken cancellationToken)
        {
            foreach (AddJacketInstanceCommand command in batch.AddJacketInstanceCommands ?? new List<AddJacketInstanceCommand> { })
            {
                command.InstanceId = Guid.NewGuid().ToString();
            }

            var caseEvent = new CaseEvent(caseId, CaseEventType.offerJacketBatch, DateTime.UtcNow)
            {
                Values = JObject.FromObject(batch, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveJacketFromOfferAsync(string tenantId, string caseId, RemoveJacketInstanceCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeJacketFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> OfferFactBatchAsync(string tenantId, string caseId, FactCommandBatch batch, CancellationToken cancellationToken)
        {
            foreach (AddFactCommand command in batch.AddFactCommands ?? new List<AddFactCommand> { })
            {
                string factId = Guid.NewGuid().ToString();
                command.Id = factId;
            }

            var caseEvent = new CaseEvent(caseId, CaseEventType.offerFactBatch, DateTime.UtcNow)
            {
                Values = JObject.FromObject(batch, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddFactToOfferAsync(string tenantId, string caseId, AddFactCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addFactToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateFactOfOfferAsync(string tenantId, string caseId, UpdateFactCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateFactOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveFactFromOfferAsync(string tenantId, string caseId, RemoveFactCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeFactFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> UpsertBenefitOptionOfOfferAsync(string tenantId, string caseId, UpsertBenefitOptionCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.upsertBenefitOptionOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveBenefitOptionFromOfferAsync(string tenantId, string caseId, RemoveBenefitOptionCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeBenefitOptionFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<string>> AddStakeholderToProposalOfferAsync(string tenantId, string caseId, AddStakeholderCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addStakeholderToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<string>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = id
            };
        }

        public async Task<Result> UpdateStakeholderOfProposalOfferAsync(string tenantId, string caseId, UpdateStakeholderCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateStakeholderOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveStakeHolderFromProposalOfferAsync(string tenantId, string caseId, RemoveStakeholderCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeStakeholderFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> AddAssociatedContractToProposalOfferAsync(string tenantId, string caseId, AddAssociatedContractCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;
            var caseEvent = new CaseEvent(caseId, CaseEventType.addAssociatedContractToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveAssociatedContractFromProposalOfferAsync(string tenantId, string caseId, RemoveAssociatedContractCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeAssociatedContractFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> AddCommissionToOfferAsync(string tenantId, string caseId, AddCommissionCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;
            var caseEvent = new CaseEvent(caseId, CaseEventType.addCommissonToOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> UpdateCommissionOfOfferAsync(string tenantId, string caseId, UpdateCommissionCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateCommissionOfOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveCommissionFromOfferAsync(string tenantId, string caseId, RemoveCommissionCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeCommissionFromOffer, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddBeneficiaryEligibilityToCaseAsync(string tenantId, string caseId, AddBeneficiaryEligibilityCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addBeneficiaryEligibilityToCase, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<CreatedStatus>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = new CreatedStatus { Id = id }
            };
        }

        public async Task<Result> UpdateBeneficiaryEligibilityOfCaseAsync(string tenantId, string caseId, UpdateBeneficiaryEligibilityCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updateBeneficiaryEligibilityOfCase, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemoveBeneficiaryEligibilityFromCaseAsync(string tenantId, string caseId, RemoveCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removeBeneficiaryEligibilityFromCase, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result<CreatedStatus>> AddPaymentInfoToCaseAsync(string tenantId, string caseId, AddPaymentInfoCommand command, CancellationToken cancellationToken)
        {
            string id = Guid.NewGuid().ToString();
            command.Id = id;

            var caseEvent = new CaseEvent(caseId, CaseEventType.addPaymentInfoToCase, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            Result result = await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);

            return new Result<CreatedStatus>
            {
                Status = result.Status,
                Errors = result.Errors,
                Value = new CreatedStatus { Id = id }
            };
        }

        public async Task<Result> UpdatePaymentInfoOfCaseAsync(string tenantId, string caseId, UpdatePaymentInfoCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.updatePaymentInfoOfCase, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RemovePaymentInfoFromCaseAsync(string tenantId, string caseId, RemoveCommand command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, CaseEventType.removePaymentInfoFromCase, DateTime.Now)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            return await AddEventAndReplayAsync(tenantId, caseEvent, cancellationToken);
        }

        public async Task<Result> RejectProposalAsync(string tenantId, string caseId, RejectProposalCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.rejectProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> SendAgentProposalForApprovalAsync(string tenantId, string caseId, SendAgentProposalForApprovalCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.sendAgentProposalForApproval, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> ApproveAgentProposalAsync(string tenantId, string caseId, ApproveAgentProposalCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.approveAgentProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> DisapproveAgentProposalAsync(string tenantId, string caseId, DisapproveAgentProposalCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.disapproveAgentProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> SendAgentProposalToAgentAsync(string tenantId, string caseId, SendAgentProposalToAgentCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.sendAgentProposalToAgent, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result<ProposalAndOfferIds>> DuplicateProposalAsync(string tenantId, string caseId, DuplicateProposalCommand command, CancellationToken cancellationToken)
        {
            string proposalId = Guid.NewGuid().ToString();
            command.ProposalId = proposalId;

            string offerId = Guid.NewGuid().ToString();
            command.OfferId = offerId;

            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.duplicateProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result.IsSuccess
                ? Result<ProposalAndOfferIds>.Success(new ProposalAndOfferIds { ProposalId = proposalId, OfferId = offerId })
                : Result<ProposalAndOfferIds>.Failure(result.Errors);
        }

        public async Task<Result> SendAgentProposalToClientAsync(string tenantId, string caseId, SendAgentProposalToClientCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.sendAgentProposalToClient, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> AcceptAgentProposalAsync(string tenantId, string caseId, AcceptAgentProposalCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.acceptAgentProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> HandleSingleOfferProposalAcceptanceAsync(string tenantId, string caseId, ConvertOfferToApplicationCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.singleOfferProposalAcceptance, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> RejectAgentProposalAsync(string tenantId, string caseId, RejectAgentProposalCommand command, CancellationToken cancellationToken)
        {
            Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.rejectAgentProposal, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            }, cancellationToken);

            return result;
        }

        public async Task<Result> ProcessCommandToEventSourcing(string tenantId, string caseId, CaseEventType eventType, object command, CancellationToken cancellationToken)
        {
            var caseEvent = new CaseEvent(caseId, eventType, DateTime.UtcNow)
            {
                Values = JObject.FromObject(command, _jsonSerializer)
            };

            IEventStore eventStore = _eventStoreResolver(tenantId);
            Result result = await eventStore.AddEventAsync(tenantId, caseEvent, cancellationToken);

            try
            {
                if (result.Status == "success")
                    await ReplayAsync(tenantId, new[] { caseEvent.CaseId }, cancellationToken);

                return result;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error processing command to event sourcing");
                await eventStore.DeleteInvalidEventAsync(tenantId, caseEvent.Id, cancellationToken);
                return Result.Failure(e.Message);
            }
        }

        public async Task<Result<CreatedStatus>> AssignAgentsAsync(string tenantId, string caseId, List<AssignAgentCommand> commands, CancellationToken cancellationToken)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, cancellationToken)).FirstOrDefault();
            if (@case is null)
                return Result<CreatedStatus>.Failure($"CaseId `{caseId}` was not found.");


            var errors = new List<string> { };
            var addedIds = new List<string> { };

            foreach (var command in commands)
            {
                if ((@case.Agents?.Any(a => a.RoleType == AgentRoleType.Primary) ?? false) && command.RoleType == AgentRoleType.Primary)
                {
                    errors.Add($"Case Agent with role `{AgentRoleType.Primary}` already assigned.");
                    continue;
                }

                if (@case.Agents?.Any(a => a.Id == command.AgentId) ?? false)
                {
                    errors.Add($"Case Agent `{command.AgentId}` already assigned.");
                    continue;
                }

                IAgentById_AgentById agent = (await _channelManagmentClient.AgentById.ExecuteAsync(command.AgentId, cancellationToken))?.Data?.AgentById;
                if (agent is null)
                {
                    errors.Add($"Agent `{command.AgentId}` was not found.");
                    continue;
                }

                Result<string> addStakeholderResult = await AddStakeholderToCaseAsync(tenantId, caseId,
                    new AddStakeholderCommand
                    {
                        EntityId = agent.AssociatedLoginEntityId,
                        Type = $"{command.RoleType} Agent",
                        AddedById = command.AssignedById
                    }, cancellationToken);

                command.StakeholderId = addStakeholderResult.Value;
                command.PortalUserId = agent.PortalUserId;
                command.EntityId = agent.AssociatedLoginEntityId;
                Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(caseId, CaseEventType.assignAgent, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(command, _jsonSerializer)
                }, cancellationToken);

                addedIds.Add(addStakeholderResult.Value);
            }

            return errors.Any()
                ? new Result<CreatedStatus> { Status = "failure", Errors = errors, Value = new CreatedStatus { Ids = addedIds } }
                : Result<CreatedStatus>.Success(new CreatedStatus { Ids = addedIds});
        }

        public async Task<Result> UnassignAgentsAsync(string tenantId, UnassignAgentsCommand command, CancellationToken cancellationToken)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = command.CaseId } }, cancellationToken)).FirstOrDefault();
            if (@case is null)
                return Result.Failure($"CaseId `{command.CaseId}` was not found.");

            var errors = new List<string>();
            var idsFound = new List<string>();
            foreach (string agentId in command.AgentIds)
            {
                CaseAgent agent = @case.Agents.FirstOrDefault(a => a.Id == agentId);
                if (agent is null)
                {
                    errors.Add($"AgentId `{agentId}` was not found on the case.");
                    continue;
                }

                idsFound.Add(agentId);
                await RemoveStakeHolderFromCaseAsync(tenantId, command.CaseId, new RemoveStakeholderCommand { Id = agent.StakeholderId, RemovedById = command.UnassignedById }, cancellationToken);
            }

           if(idsFound.Any())
                await AddEventAndReplayAsync(tenantId, new CaseEvent(command.CaseId, CaseEventType.unassignAgents, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(new UnassignAgentsCommand(command.CaseId, idsFound) {UnassignedById = command.UnassignedById }, _jsonSerializer)
                }, cancellationToken);

            return errors.Any()
                ? Result.Failure(errors)
                : Result.Success();
        }

        public async Task<Result<CreatedStatus>> AssignHandlersAsync(string tenantId, AssignHandlersCommand command, CancellationToken cancellationToken)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = command.CaseId } }, cancellationToken)).FirstOrDefault();
            if (@case is null)
                return Result<CreatedStatus>.Failure($"CaseId `{command.CaseId}` was not found.");

            IEnumerable<Auth.Client.Login> logins = await _authService.GetLoginsAsync(tenantId, new QueryArguments<Auth.Client.LoginWhere> { Where = new Auth.Client.LoginWhere { Ids = command.PortalUserIds, ExcludePermissions = true } }, cancellationToken);
            Dictionary<string, string> loginsDict = logins.ToDictionary(obj => obj.Id, obj => obj.EntityId);

            var errors = new List<string> { };
            var addedIds = new List<string> { }; ;
            foreach (string loginId in command.PortalUserIds)
            {
                if (@case.Handlers?.Any(h => h.PortalUserId == loginId) ?? false)
                {
                    errors.Add($"Handler `{loginId}` already assigned to case.");
                    continue;
                }

                if (!loginsDict.ContainsKey(loginId))
                {
                    errors.Add($"Handler login Id `{loginId}` was not found.");
                    continue;
                }

                Result<string> addStakeholderResult = await AddStakeholderToCaseAsync(tenantId, command.CaseId,
                    new AddStakeholderCommand
                    {
                        EntityId = loginsDict[loginId],
                        Type = "Handler",
                        AddedById = command.AssignedById
                    }, cancellationToken);

                Result result = await AddEventAndReplayAsync(tenantId, new CaseEvent(command.CaseId, CaseEventType.assignHandler, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(new AssignHandlerCommand(addStakeholderResult.Value, loginsDict[loginId], loginId, command.AssignedById), _jsonSerializer)
                }, cancellationToken);
                addedIds.Add(addStakeholderResult.Value);
            }

            return errors.Any()
                ? new Result<CreatedStatus> { Status = "failure", Errors = errors, Value = new CreatedStatus { Ids = addedIds } }
                : Result<CreatedStatus>.Success(new CreatedStatus { Ids = addedIds });
        }

        public async Task<Result> UnassignHandlersAsync(string tenantId, UnassignHandlersCommand command, CancellationToken cancellationToken)
        {
            Case @case = (await GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = command.CaseId } }, cancellationToken)).FirstOrDefault();
            if (@case is null)
                return Result.Failure($"CaseId {command.CaseId} was not found.");

            var errors = new List<string>();
            var idsFound = new List<string>();
            foreach (string handlerLoginId in command.PortalUserIds)
            {
                CaseHandler handler = @case.Handlers.FirstOrDefault(a => a.PortalUserId == handlerLoginId);
                if (handler is null)
                {
                    errors.Add($"HandlerId {handlerLoginId} was not found on the case.");
                    continue;
                }
                idsFound.Add(handlerLoginId);
                await RemoveStakeHolderFromCaseAsync(tenantId, command.CaseId, new RemoveStakeholderCommand { Id = handler.StakeholderId, RemovedById = command.UnassignedById }, cancellationToken);
            }

            if (idsFound.Any())
                await AddEventAndReplayAsync(tenantId, new CaseEvent(command.CaseId, CaseEventType.unassignHandlers, DateTime.UtcNow)
                {
                    Values = JObject.FromObject(new UnassignHandlersCommand(command.CaseId, idsFound) { UnassignedById = command.UnassignedById }, _jsonSerializer)
                }, cancellationToken);

            return errors.Any()
                ? Result.Failure(errors)
                : Result.Success();
        }
    }
}
