﻿using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.DataSchemas
{
    public interface IDataSchemaRepository
    {
        public string ProviderId { get; }
        Task<IReadOnlyCollection<DataSchema>> GetAsync(string tenantId, DataSchemaWhere filter, CancellationToken cancellationToken);
        Task<Result<CreatedStatus>> CreateAsync(string tenantId, string id, CreateDataSchemaCommand command, CancellationToken cancellationToken);
        Task<Result> UpdateAsync(string tenantId, UpdateDataSchemaCommand command, CancellationToken cancellationToken);
        Task<Result> DeleteAsync(string tenantId, string dataSchemaId, DeleteCommand command, CancellationToken cancellationToken);
        Task<Result> AddUiSchemaToDataSchemaAsync(string tenantId, AddUiSchemaToDataSchemaCommand command, CancellationToken cancellationToken);
        Task<Result> RemoveUiSchemaFromDataSchemaAsync(string tenantId, RemoveUiSchemaFromDataSchemaCommand command, CancellationToken cancellationToken);
    }

    public class CreateDataSchemaCommand
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Schema { get; set; }
        public DataSchemaStandard Standard { get; set; }
        public string Type { get; set; }
        public string CreatedById { get; set; }
        public string[] Tags { get; set; }
    }

    public class UpdateDataSchemaCommand
    {
        public string DataSchemaId { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Schema { get; set; }
        public DataSchemaStandard? Standard { get; set; }
        public string Type { get; set; }
        public string ModifiedById { get; set; }
        public string[] Tags { get; set; }
    }
    public class AddUiSchemaToDataSchemaCommand
    {
        public string DataSchemaId { get; set; }
        public string UiSchemaId { get; set; }
        public string AddedById { get; set; }
    }
    public class RemoveUiSchemaFromDataSchemaCommand
    {
        public string DataSchemaId { get; set; }
        public string UiSchemaId { get; set; }
        public string RemovedById { get; set; }
    }
}