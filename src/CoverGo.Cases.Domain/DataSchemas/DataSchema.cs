﻿using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain.DataSchemas
{
    public class DataSchema : SystemObject
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public JToken Schema { get; set; }
        public DataSchemaStandard Standard { get; set; }
        public string Type { get; set; }
        public string[] Tags { get; set; }
        public string[] UiSchemaIds { get; set; }
    }

    public class DataSchemaStandard
    {
        public DataSchemaStandardTypeEnum Type { get; set; }
        public string Version { get; set; }
    }

    public enum DataSchemaStandardTypeEnum
    {
        JSON_SCHEMA = 0,
        STATE_CHART = 1
    }
}