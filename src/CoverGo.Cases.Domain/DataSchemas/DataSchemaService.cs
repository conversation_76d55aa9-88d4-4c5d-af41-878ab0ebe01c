﻿using CoverGo.DomainUtils;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Domain.DataSchemas
{
    public class DataSchemaService
    {
        readonly IDataSchemaRepository _dataSchemaRepository;

        public DataSchemaService(IDataSchemaRepository dataSchemaRepository) =>
            _dataSchemaRepository = dataSchemaRepository;

        public async Task<IEnumerable<DataSchema>> GetAsync(string tenantId, DataSchemaWhere where, CancellationToken cancellationToken) =>
            await _dataSchemaRepository.GetAsync(tenantId, where, cancellationToken);

        public Task<Result<CreatedStatus>> CreateAsync(string tenantId, CreateDataSchemaCommand command, CancellationToken cancellationToken) =>
            _dataSchemaRepository.CreateAsync(tenantId, Guid.NewGuid().ToString(), command, cancellationToken);

        public Task<Result> UpdateAsync(string tenantId, UpdateDataSchemaCommand command, CancellationToken cancellationToken) =>
            _dataSchemaRepository.UpdateAsync(tenantId, command, cancellationToken);

        public Task<Result> DeleteAsync(string tenantId, string dataSchemaId, DeleteCommand command, CancellationToken cancellationToken) =>
            _dataSchemaRepository.DeleteAsync(tenantId, dataSchemaId, command, cancellationToken);
        public Task<Result> AddUiSchemaToDataSchema(string tenantId, AddUiSchemaToDataSchemaCommand command, CancellationToken cancellationToken) =>
            _dataSchemaRepository.AddUiSchemaToDataSchemaAsync(tenantId, command, cancellationToken);
        public Task<Result> RemoveUiSchemaFromDataSchema(string tenantId, RemoveUiSchemaFromDataSchemaCommand command, CancellationToken cancellationToken) =>
            _dataSchemaRepository.RemoveUiSchemaFromDataSchemaAsync(tenantId, command, cancellationToken);
    }
}