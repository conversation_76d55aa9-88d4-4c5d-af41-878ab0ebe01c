﻿using System.Collections.Generic;

namespace CoverGo.Cases.Domain.DataSchemas
{
    public class DataSchemaWhere
    {
        public List<DataSchemaWhere> Or { get; set; }
        public List<DataSchemaWhere> And { get; set; }
        public string Id { get; set; }
        public List<string> Id_in { get; set; }
        public string Type { get; set; }
        public List<string> Tags_contains { get; set; }
    }
}