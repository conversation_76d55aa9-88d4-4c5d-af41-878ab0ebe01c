﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using MongoDB.Bson;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public interface IEventStore
    {
        string ProviderId { get; }

        Task<Result> AddEventAsync(string tenantId, CaseEvent claimEvent, CancellationToken cancellationToken);
        Task<IEnumerable<CaseEvent>> GetEventsAsync(string tenantId, IEnumerable<CaseEventType> types, IEnumerable<string> transactionIds, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
        Task<Result> DeleteInvalidEventAsync(string tenantId, string eventId, CancellationToken cancellationToken);
        Task<IEnumerable<CaseEvent>> GetCaseEventsStartingWith(string tenantId, string startsWith, CancellationToken cancellationToken);
    }
    public class CaseEvent
    {
        public CaseEvent() { } //parameterless constructor for EF MariaDb to work

        public CaseEvent(string objectId, CaseEventType type, DateTime timestamp)
        {
            Id = ObjectId.GenerateNewId().ToString();
            CaseId = objectId;
            Type = type;
            Timestamp = timestamp;
        }

        public string Id { get; set; }
        public string CaseId { get; set; }
        public CaseEventType Type { get; set; }
        public JToken Values { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public enum CaseEventType
    {
        creation,
        update,
        delete,

        addProposal,
        issueProposal,
        rejectProposal,
        updateProposal,
        removeProposal,
        setSystemDates,
        setReadOnly,


        addStakeholderToProposal,
        updateStakeholderOfProposal,
        removeStakeholderFromProposal,

        addOffer,
        updateOffer,
        removeOffer,

        addDiscountToOffer,
        updateDiscountOfOffer,
        removeDiscountFromOffer,

        addLoadingToOffer,
        updateLoadingOfOffer,
        removeLoadingFromOffer,

        addExclusionToOffer,
        removeExclusionFromOffer,

        addClauseToOffer,
        updateClauseOfOffer,
        removeClauseFromOffer,
        offerClauseBatch,

        offerJacketBatch,
        removeJacketFromOffer,

        addFactToOffer,
        updateFactOfOffer,
        offerFactBatch,
        removeFactFromOffer,

        upsertBenefitOptionOfOffer,
        removeBenefitOptionFromOffer,

        addStakeholderToOffer,
        updateStakeholderOfOffer,
        removeStakeholderFromOffer,

        addAssociatedContractToOffer,
        removeAssociatedContractFromOffer,

        addFactToCase,
        updateFactOfCase,
        caseFactBatch,
        removeFactFromCase,

        addNoteToCase,
        updateNoteOfCase,
        removeNoteFromCase,

        addStakeholderToCase,
        updateStakeholderOfCase,
        removeStakeholderFromCase,

        generatePolicies,
        removeCommissionFromOffer,
        updateCommissionOfOffer,
        addCommissonToOffer,

        addBeneficiaryEligibilityToCase,
        updateBeneficiaryEligibilityOfCase,
        removeBeneficiaryEligibilityFromCase,

        addPaymentInfoToCase,
        updatePaymentInfoOfCase,
        removePaymentInfoFromCase,

        addIssuedPolicy,

        recordQuote,

        agentCreateCase,

        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        createAgentProposal,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        sendAgentProposalToAgent,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        duplicateProposal,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        sendAgentProposalForApproval,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        approveAgentProposal,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        disapproveAgentProposal,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        sendAgentProposalToClient,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        acceptAgentProposal,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        rejectAgentProposal,
        [Obsolete("Only used by Asia tenant old policies on tactical flow.")]
        updateAgentProposal,

        singleOfferProposalAcceptance,

        assignAgent,
        unassignAgents,
        assignHandler,
        unassignHandlers
    }
}
