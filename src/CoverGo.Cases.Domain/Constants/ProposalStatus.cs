﻿namespace CoverGo.Cases.Domain.Constants
{
    public static class ProposalStatus
    {
        public const string OFFER_DRAFT = "Offer Draft";
        public const string OFFER_SENT = "Offer Sent";
        public const string OFFER_REJECTED = "Offer Rejected";
        public const string OFFER_ACCEPTED = "Offer Accepted";
        public const string PENDING_FOR_UNDERWRITER = "Pending for Underwriter";
        public const string UNDERWRITER_IN_PROGRESS = "Underwriter in-progress";
        public const string NOT_PROCEEDING = "Not Proceeding";
        public const string UNDERWRITER_APPROVED = "Underwriter Approved";
        public const string UNDERWRITER_REJECTED = "Underwriter Rejected";
        public const string ISSUED = "ISSUED";
    }
}
