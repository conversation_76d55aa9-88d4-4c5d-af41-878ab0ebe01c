﻿namespace CoverGo.Cases.Domain.Constants
{
    //Below statuses are derived from FE core constants  
    public static class OfferStatus
    {

        public const string DRAFT = "Added";
        public const string SENT = "Sent";
        public const string ACCEPTED = "Accepted";
        public const string LAPSED = "Lapsed";
        public const string REJECTED = "Rejected";
        public const string SUBMITTED = "Submitted";
        public const string UNDERWRITER_REQUESTED = "UnderwriterRequested";
        public const string UNDERWRITER_IN_PROGRESS = "UnderwriterInProgress";
        public const string NOT_PROCEEDING = "NotProceeding";
        public const string UNDERWRITER_REJECTED = "UnderwriterRejected";
        public const string UNDERWRITER_APPROVED = "UnderwriterApproved";
    }
}
