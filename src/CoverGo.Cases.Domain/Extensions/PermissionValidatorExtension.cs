﻿using System.Security.Claims;
using System.Security.Principal;
using CoverGo.BuildingBlocks.Auth.Permissions;

namespace CoverGo.Cases.Domain.Extensions
{
    public static class PermissionValidatorExtension
    {
        public static bool AuthorizeWith(this IPermissionValidator validator, IIdentity identity, string permissionType,
            string permissionId)
        {
            if (identity is not ClaimsIdentity claimsIdentity)
                return false;

            try
            {
                validator.Authorize(claimsIdentity, new PermissionRequest(permissionType).WithTargetIds(permissionId));
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}