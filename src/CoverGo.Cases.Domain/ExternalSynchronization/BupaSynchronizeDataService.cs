using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public class BupaSynchronizeDataService : BaseSynchronizeDataService
    {
        private const string BUPA_LOGICAL_ID = "bupa";

        private readonly ICaseServiceFactory _caseServiceFactory;
        private readonly IProductsClient _productsClient;
        private readonly IExternalSynchronizationTargetService _externalSyncTargetService;

        public override string LogicalId => BUPA_LOGICAL_ID;

        public BupaSynchronizeDataService(
            ICaseServiceFactory caseServiceFactory,
            IProductsClient productsClient,
            IExternalSynchronizationTargetService externalSyncTargetService,
            IExternalSynchronizationService externalSyncService,
            ILogger<BupaSynchronizeDataService> logger)
            : base(logger, externalSyncService)
        {
            _caseServiceFactory = caseServiceFactory;
            _productsClient = productsClient;
            _externalSyncTargetService = externalSyncTargetService;
        }

        protected override async Task<Result<JToken>> CollectDataAsync(string tenantId, string caseId, string offerId, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("[{ServiceName}] Collecting case {CaseId} and offer {OfferId}",
                    GetType().Name, caseId, offerId);

                var caseService = _caseServiceFactory.Build();

                // Get case data
                var cases = await caseService.GetAsync(tenantId,
                    new QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } },
                    cancellationToken);

                var caseData = cases?.FirstOrDefault();
                if (caseData == null)
                {
                    return Result<JToken>.Failure($"Case with ID '{caseId}' not found");
                }

                // Find the offer in the case
                var offer = caseData.Proposals?
                    .SelectMany(p => p.Basket ?? Enumerable.Empty<Offer>())
                    .FirstOrDefault(o => o.Id == offerId);

                if (offer == null)
                {
                    return Result<JToken>.Failure($"Offer with ID '{offerId}' not found in case");
                }

                // Get product data if offer has a product ID
                CoverGo.Products.Client.Product product = null;
                if (offer?.ProductId != null && !string.IsNullOrEmpty(offer.ProductId.Plan))
                {
                    try
                    {
                        var products = await _productsClient.Products_GetAllAsync(
                            tenantId,
                            new CoverGo.Products.Client.ProductQuery
                            {
                                Where = new CoverGo.Products.Client.ProductWhere
                                {
                                    ProductId = new CoverGo.Products.Client.ProductIdWhere
                                    {
                                        Plan = offer.ProductId.Plan,
                                        Type = offer.ProductId.Type,
                                        Version = offer.ProductId.Version
                                    }
                                }
                            },
                            null);

                        product = products?.FirstOrDefault();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[{ServiceName}] Failed to retrieve product data for offer {OfferId}, continuing without product data",
                            GetType().Name, offer.Id);
                        // Continue without product data - not a critical failure
                    }
                }

                _logger.LogInformation("[{ServiceName}] Successfully collected case, offer and product data", GetType().Name);

                // Return collected data as JToken including product data
                var collectedData = new JObject
                {
                    ["Case"] = JToken.FromObject(caseData),
                    ["Offer"] = JToken.FromObject(offer),
                    ["Product"] = JToken.FromObject(product ?? new object())
                };

                return Result<JToken>.Success(collectedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ServiceName}] Failed to collect case {CaseId} and offer {OfferId}",
                    GetType().Name, caseId, offerId);
                return Result<JToken>.Failure($"Failed to collect case and offer data: {ex.Message}");
            }
        }

        protected override Task<Result<JToken>> TransformAsync(string tenantId, JToken collectedData, CancellationToken cancellationToken)
        {
            try
            {
                var caseData = collectedData["Case"].ToObject<Case>();
                var offerData = collectedData["Offer"].ToObject<Offer>();
                var productData = collectedData["Product"]?.Type == JTokenType.Null ? null : collectedData["Product"]?.ToObject<CoverGo.Products.Client.Product>();

                _logger.LogInformation("[{ServiceName}] Transforming data for case {CaseId} and offer {OfferId}",
                    GetType().Name, caseData?.Id, offerData?.Id);

                // Create the base transformation object using collected data
                var transformedData = new JObject
                {
                    ["case"] = JToken.FromObject(caseData),
                    ["offer"] = JToken.FromObject(offerData),
                    ["product"] = productData != null ? JToken.FromObject(productData) : null
                };

                // Add BUPA-specific transformation logic
                transformedData["bupaMetadata"] = JToken.FromObject(new
                {
                    transformedAt = DateTime.UtcNow,
                    targetSystem = "BUPA",
                    version = "1.0",
                    tenantId = tenantId
                });

                // BUPA might require specific field mappings or additional data
                if (transformedData["case"] != null)
                {
                    var caseObj = (JObject)transformedData["case"];
                    caseObj["bupaReferenceNumber"] = $"BUPA-{caseData?.Id}-{DateTime.UtcNow:yyyyMMddHHmmss}";
                }

                if (transformedData["offer"] != null)
                {
                    var offerObj = (JObject)transformedData["offer"];
                    offerObj["bupaOfferReference"] = $"BUPA-OFFER-{offerData?.Id}";
                }

                _logger.LogInformation("[{ServiceName}] Successfully transformed data", GetType().Name);

                return Task.FromResult(Result<JToken>.Success(transformedData));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ServiceName}] Failed to transform data", GetType().Name);
                return Task.FromResult(Result<JToken>.Failure($"Failed to transform data: {ex.Message}"));
            }
        }

        protected override async Task<Result<ExternalSynchronization>> SendAsync(string tenantId, string caseId, string offerId, string targetLogicalId, JToken transformedData, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("[BupaSynchronizeDataService] Sending data to BUPA external system for case {CaseId}, offer {OfferId}", caseId, offerId);

                // Get ExternalSynchronizationTarget by logicalId
                var targetQuery = new QueryArguments<Filter<ExternalSynchronizationTargetFilter>>
                {
                    Where = new Filter<ExternalSynchronizationTargetFilter>
                    {
                        Where = new ExternalSynchronizationTargetFilter { LogicalId = targetLogicalId }
                    }
                };

                var targets = await _externalSyncTargetService.QueryAsync(tenantId, targetQuery, cancellationToken);
                var target = targets?.FirstOrDefault();

                if (target == null)
                {
                    return Result<ExternalSynchronization>.Failure($"ExternalSynchronizationTarget with LogicalId '{targetLogicalId}' not found");
                }

                // Extract URLs from the target
                var urls = target.Urls;
                var authUrl = urls?["authorization"]?.ToString();
                var validationUrl = urls?["validation"]?.ToString();
                var confirmationUrl = urls?["confirmation"]?.ToString();

                _logger.LogInformation("[BupaSynchronizeDataService] Using BUPA URLs - Authorization: {AuthUrl}, Validation: {ValidationUrl}, Confirmation: {ConfirmationUrl}",
                    authUrl, validationUrl, confirmationUrl);

                // Create initial authorization payload
                var authPayload = CreateAuthorizationPayload(transformedData, tenantId, caseId, offerId);

                // Note: Validation and confirmation payloads will be created dynamically
                // after getting responses from previous steps, as they depend on data from previous responses

                // Simulate API calls to the different BUPA endpoints
                // In a real implementation, this would make actual HTTP calls
                // We'll store partial results in case of failures

                var combinedPayload = new JObject();
                var combinedResponse = new JObject();
                var completedSteps = 0;
                var errorDetails = new List<string>();

                JObject authResponse = null;
                JObject validationResponse = null;
                JObject confirmationResponse = null;

                try
                {
                    // 1. Authorization API call
                    _logger.LogInformation("[BupaSynchronizeDataService] Calling BUPA Authorization API");
                    await Task.Delay(50, cancellationToken); // Simulate network call
                    authResponse = SimulateAuthorizationResponse(authPayload);

                    // Store successful authorization
                    combinedPayload["authorization"] = authPayload;
                    combinedResponse["authorization"] = authResponse;
                    completedSteps++;

                    _logger.LogInformation("[BupaSynchronizeDataService] Authorization API completed successfully");

                    // 2. Validation API call (depends on authorization response)
                    _logger.LogInformation("[BupaSynchronizeDataService] Calling BUPA Validation API");
                    var validationPayload = CreateValidationPayload(transformedData, tenantId, caseId, offerId, authResponse);
                    await Task.Delay(50, cancellationToken); // Simulate network call
                    validationResponse = SimulateValidationResponse(validationPayload, authResponse);

                    // Store successful validation
                    combinedPayload["validation"] = validationPayload;
                    combinedResponse["validation"] = validationResponse;
                    completedSteps++;

                    _logger.LogInformation("[BupaSynchronizeDataService] Validation API completed successfully");

                    // 3. Confirmation API call (depends on both authorization and validation responses)
                    _logger.LogInformation("[BupaSynchronizeDataService] Calling BUPA Confirmation API");
                    var confirmationPayload = CreateConfirmationPayload(transformedData, tenantId, caseId, offerId, authResponse, validationResponse);
                    await Task.Delay(50, cancellationToken); // Simulate network call
                    confirmationResponse = SimulateConfirmationResponse(confirmationPayload, authResponse, validationResponse);

                    // Store successful confirmation
                    combinedPayload["confirmation"] = confirmationPayload;
                    combinedResponse["confirmation"] = confirmationResponse;
                    completedSteps++;

                    _logger.LogInformation("[BupaSynchronizeDataService] Confirmation API completed successfully");
                }
                catch (Exception stepEx)
                {
                    _logger.LogError(stepEx, "[BupaSynchronizeDataService] Failed at step {CompletedSteps}/3", completedSteps);
                    errorDetails.Add($"Failed at step {completedSteps + 1}/3: {stepEx.Message}");

                    // Add error information to response
                    combinedResponse["error"] = new JObject
                    {
                        ["occurred"] = true,
                        ["step"] = completedSteps + 1,
                        ["message"] = stepEx.Message,
                        ["timestamp"] = DateTime.UtcNow
                    };
                }

                // Add summary with partial completion information
                combinedResponse["summary"] = new JObject
                {
                    ["status"] = completedSteps == 3 ? "success" : "partial_success",
                    ["message"] = completedSteps == 3
                        ? "Data synchronized successfully with BUPA using multiple APIs"
                        : $"Partial synchronization completed: {completedSteps}/3 steps successful",
                    ["externalId"] = $"bupa-ext-{Guid.NewGuid():N}",
                    ["processedAt"] = DateTime.UtcNow,
                    ["completedSteps"] = completedSteps,
                    ["totalSteps"] = 3,
                    ["urlsUsed"] = new JObject
                    {
                        ["authorization"] = authUrl,
                        ["validation"] = validationUrl,
                        ["confirmation"] = confirmationUrl
                    }
                };

                if (errorDetails.Count > 0)
                {
                    combinedResponse["summary"]["errors"] = new JArray(errorDetails.ToArray());
                }

                if (completedSteps == 3)
                {
                    _logger.LogInformation("[BupaSynchronizeDataService] Successfully completed all BUPA API calls");
                }
                else
                {
                    _logger.LogWarning("[BupaSynchronizeDataService] Partially completed BUPA API calls: {CompletedSteps}/3 steps successful", completedSteps);
                }

                // Return result with both payload and response (including partial results)
                var result = new ExternalSynchronization
                {
                    Payload = combinedPayload,
                    Response = combinedResponse
                };

                // Return success even for partial completion so that partial data is stored
                // The status in the response will indicate if it was partial or complete
                return Result<ExternalSynchronization>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[BupaSynchronizeDataService] Failed to send data to BUPA external system for case {CaseId}, offer {OfferId}", caseId, offerId);
                return Result<ExternalSynchronization>.Failure($"Failed to send data to BUPA: {ex.Message}");
            }
        }

        private JObject CreateAuthorizationPayload(JToken transformedData, string tenantId, string caseId, string offerId)
        {
            return new JObject
            {
                ["requestType"] = "authorization",
                ["timestamp"] = DateTime.UtcNow,
                ["tenantId"] = tenantId,
                ["caseId"] = caseId,
                ["offerId"] = offerId,
                ["credentials"] = new JObject
                {
                    ["clientId"] = "bupa-client-001",
                    ["scope"] = "case-synchronization",
                    ["version"] = "v1.0"
                },
                ["metadata"] = new JObject
                {
                    ["source"] = "CoverGo",
                    ["targetSystem"] = "BUPA",
                    ["dataClassification"] = "confidential"
                }
            };
        }

        private JObject CreateValidationPayload(JToken transformedData, string tenantId, string caseId, string offerId, JObject authResponse)
        {
            return new JObject
            {
                ["requestType"] = "validation",
                ["timestamp"] = DateTime.UtcNow,
                ["tenantId"] = tenantId,
                ["caseId"] = caseId,
                ["offerId"] = offerId,
                ["data"] = transformedData,
                ["authContext"] = new JObject
                {
                    ["authToken"] = authResponse["authToken"],
                    ["sessionId"] = authResponse["sessionId"],
                    ["scope"] = authResponse["scope"]
                },
                ["validationRules"] = new JArray
                {
                    "mandatory-fields",
                    "data-format",
                    "business-rules",
                    "bupa-compliance"
                },
                ["checksum"] = $"chk_{Guid.NewGuid():N}"
            };
        }

        private JObject CreateConfirmationPayload(JToken transformedData, string tenantId, string caseId, string offerId, JObject authResponse, JObject validationResponse)
        {
            return new JObject
            {
                ["requestType"] = "confirmation",
                ["timestamp"] = DateTime.UtcNow,
                ["tenantId"] = tenantId,
                ["caseId"] = caseId,
                ["offerId"] = offerId,
                ["finalData"] = transformedData,
                ["authContext"] = new JObject
                {
                    ["authToken"] = authResponse["authToken"],
                    ["sessionId"] = authResponse["sessionId"]
                },
                ["validationContext"] = new JObject
                {
                    ["validationId"] = validationResponse["validationId"],
                    ["validationScore"] = validationResponse["validationResults"]?["score"],
                    ["validationPassed"] = validationResponse["validationResults"]?["passed"]
                },
                ["processingOptions"] = new JObject
                {
                    ["async"] = false,
                    ["priority"] = "normal",
                    ["retryPolicy"] = "standard"
                },
                ["acknowledgment"] = new JObject
                {
                    ["required"] = true,
                    ["format"] = "detailed"
                }
            };
        }

        private JObject SimulateAuthorizationResponse(JObject authPayload)
        {
            return new JObject
            {
                ["status"] = "success",
                ["responseTime"] = DateTime.UtcNow,
                ["authToken"] = $"Bearer bupa-token-{Guid.NewGuid():N}",
                ["expiresIn"] = 3600,
                ["scope"] = "case-synchronization",
                ["sessionId"] = $"sess-{Guid.NewGuid():N}",
                ["message"] = "Authorization successful"
            };
        }

        private JObject SimulateValidationResponse(JObject validationPayload, JObject authResponse)
        {
            return new JObject
            {
                ["status"] = "success",
                ["responseTime"] = DateTime.UtcNow,
                ["validationId"] = $"bupa-val-{Guid.NewGuid():N}",
                ["sessionId"] = authResponse["sessionId"],
                ["validationResults"] = new JObject
                {
                    ["passed"] = true,
                    ["score"] = 100,
                    ["checks"] = new JArray
                    {
                        new JObject { ["rule"] = "mandatory-fields", ["status"] = "passed" },
                        new JObject { ["rule"] = "data-format", ["status"] = "passed" },
                        new JObject { ["rule"] = "business-rules", ["status"] = "passed" },
                        new JObject { ["rule"] = "bupa-compliance", ["status"] = "passed" }
                    }
                },
                ["message"] = "Data validation successful"
            };
        }

        private JObject SimulateConfirmationResponse(JObject confirmationPayload, JObject authResponse, JObject validationResponse)
        {
            return new JObject
            {
                ["status"] = "success",
                ["responseTime"] = DateTime.UtcNow,
                ["confirmationId"] = $"bupa-conf-{Guid.NewGuid():N}",
                ["sessionId"] = authResponse["sessionId"],
                ["validationId"] = validationResponse["validationId"],
                ["processingResults"] = new JObject
                {
                    ["accepted"] = true,
                    ["externalReferenceId"] = $"BUPA-REF-{Guid.NewGuid():N}",
                    ["processingTime"] = "150ms",
                    ["nextSteps"] = new JArray
                    {
                        "Data stored in BUPA system",
                        "Confirmation sent to stakeholders",
                        "Tracking reference generated"
                    }
                },
                ["acknowledgment"] = new JObject
                {
                    ["received"] = true,
                    ["timestamp"] = DateTime.UtcNow,
                    ["trackingNumber"] = $"TRK-{Guid.NewGuid():N}"
                },
                ["message"] = "Data synchronization confirmed and completed"
            };
        }
    }
}