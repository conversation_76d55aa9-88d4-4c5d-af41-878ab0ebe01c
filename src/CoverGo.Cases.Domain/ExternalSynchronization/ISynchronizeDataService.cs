using System.Threading;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public interface ISynchronizeDataService
    {
        /// <summary>
        /// The logical identifier for this synchronization service implementation
        /// </summary>
        string LogicalId { get; }

        /// <summary>
        /// Processes the entire synchronization workflow: CollectData -> Transform -> Send
        /// </summary>
        /// <param name="request">The synchronization request containing all necessary data</param>
        /// <returns>Result containing the external synchronization record</returns>
        Task<Result<ExternalSynchronization>> ProcessAsync(SynchronizeDataRequest request);
    }
}