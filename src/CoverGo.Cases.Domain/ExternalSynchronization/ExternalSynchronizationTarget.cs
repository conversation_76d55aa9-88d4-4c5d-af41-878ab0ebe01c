using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public class ExternalSynchronizationTarget : SystemObject, IUniqueSystemObject
    {
        public string Id { get; set; }
        public string LogicalId { get; set; }
        public JToken Credentials { get; set; }
        public JToken Urls { get; set; }
    }

    public class ExternalSynchronizationTargetUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string LogicalId { get; set; }
        public string Credentials { get; set; }
        public JToken Urls { get; set; }
        public string ById { get; set; }
    }

    public class ExternalSynchronizationTargetFilter
    {
        [FilterCondition(FilterCondition.Eq)]
        public string Id { get; set; }

        [FilterCondition(FilterCondition.Eq, nameof(ExternalSynchronizationTarget.Id), flag: false)]
        public string Id_neq { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronizationTarget.Id))]
        public List<string> Id_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronizationTarget.Id))]
        public string Id_contains { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string LogicalId { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronizationTarget.LogicalId))]
        public List<string> LogicalId_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronizationTarget.LogicalId))]
        public string LogicalId_contains { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronizationTarget.Urls))]
        public string Urls_contains { get; set; }
    }
}