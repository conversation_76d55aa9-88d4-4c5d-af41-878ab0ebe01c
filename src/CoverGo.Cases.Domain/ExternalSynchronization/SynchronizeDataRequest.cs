using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public class SynchronizeDataRequest
    {
        public string TenantId { get; set; }
        public string CaseId { get; set; }
        public string OfferId { get; set; }
        public string TargetLogicalId { get; set; }
        public JToken TransformedData { get; set; }
        public System.Threading.CancellationToken CancellationToken { get; set; }
    }
}