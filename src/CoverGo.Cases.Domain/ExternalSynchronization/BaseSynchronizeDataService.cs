using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public abstract class BaseSynchronizeDataService : ISynchronizeDataService
    {
        protected readonly ILogger _logger;
        protected readonly IExternalSynchronizationService _externalSyncService;

        protected BaseSynchronizeDataService(ILogger logger, IExternalSynchronizationService externalSyncService)
        {
            _logger = logger;
            _externalSyncService = externalSyncService;
        }

        public abstract string LogicalId { get; }

        public virtual async Task<Result<ExternalSynchronization>> ProcessAsync(SynchronizeDataRequest request)
        {
            // Input validation
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsSuccess)
                return Result<ExternalSynchronization>.Failure(validationResult.Errors);

            ExternalSynchronization syncRecord = null;

            try
            {
                _logger.LogInformation("[{ServiceName}] Starting synchronization for case {CaseId}, offer {OfferId}",
                    GetType().Name, request.CaseId, request.OfferId);

                // Initialize the synchronization record with status "Initialized"
                var createResult = await CreateSyncRecordAsync(request.TenantId, request.CaseId, request.OfferId, request.TargetLogicalId, "Initialized", request.CancellationToken);
                if (!createResult.IsSuccess)
                    return Result<ExternalSynchronization>.Failure(createResult.Errors);

                syncRecord = createResult.Value;

                // Step 1: Collect Data
                var collectResult = await CollectDataAsync(request.TenantId, request.CaseId, request.OfferId, request.CancellationToken);
                if (!collectResult.IsSuccess)
                {
                    await UpdateSyncRecordStatusAsync(request.TenantId, syncRecord.Id, "Failed", collectResult.Errors.ToArray(), request.CancellationToken);
                    return Result<ExternalSynchronization>.Failure(collectResult.Errors);
                }

                // Update status to "DataCollected"
                await UpdateSyncRecordStatusAsync(request.TenantId, syncRecord.Id, "DataCollected", null, request.CancellationToken);

                // Step 2: Transform Data
                var transformResult = await TransformAsync(request.TenantId, collectResult.Value, request.CancellationToken);
                if (!transformResult.IsSuccess)
                {
                    await UpdateSyncRecordStatusAsync(request.TenantId, syncRecord.Id, "Failed", transformResult.Errors.ToArray(), request.CancellationToken);
                    return Result<ExternalSynchronization>.Failure(transformResult.Errors);
                }

                // Update status to "Transformed"
                await UpdateSyncRecordStatusAsync(request.TenantId, syncRecord.Id, "Transformed", null, request.CancellationToken);

                // Step 3: Send Data
                var sendResult = await SendAsync(request.TenantId, request.CaseId, request.OfferId, request.TargetLogicalId, transformResult.Value, request.CancellationToken);
                if (!sendResult.IsSuccess)
                {
                    await UpdateSyncRecordStatusAsync(request.TenantId, syncRecord.Id, "Failed", sendResult.Errors.ToArray(), request.CancellationToken);
                    return Result<ExternalSynchronization>.Failure(sendResult.Errors);
                }

                // Update final status to "Sent" with payload and response
                // Use the payload from SendAsync if available, otherwise use the transformed data
                var payloadToStore = sendResult.Value.Payload ?? transformResult.Value;
                var finalResult = await UpdateSyncRecordFinalAsync(request.TenantId, syncRecord.Id, "Sent", payloadToStore, sendResult.Value.Response, request.CancellationToken);
                if (!finalResult.IsSuccess)
                    return Result<ExternalSynchronization>.Failure(finalResult.Errors);

                _logger.LogInformation("[{ServiceName}] Synchronization completed successfully for case {CaseId}, offer {OfferId}",
                    GetType().Name, request.CaseId, request.OfferId);

                // Return the final record with all the context preserved from the original sync record
                var completeFinalRecord = finalResult.Value;
                completeFinalRecord.CaseId = syncRecord.CaseId;
                completeFinalRecord.OfferId = syncRecord.OfferId;
                completeFinalRecord.TargetLogicalId = syncRecord.TargetLogicalId;

                return Result<ExternalSynchronization>.Success(completeFinalRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ServiceName}] ProcessAsync failed for case {CaseId}, offer {OfferId}",
                    GetType().Name, request.CaseId, request.OfferId);

                // Update sync record with error if we have one
                if (syncRecord != null)
                {
                    try
                    {
                        await UpdateSyncRecordStatusAsync(request.TenantId, syncRecord.Id, "Failed", new[] { $"Unexpected error: {ex.Message}" }, request.CancellationToken);
                    }
                    catch (Exception updateEx)
                    {
                        _logger.LogError(updateEx, "[{ServiceName}] Failed to update sync record with error status", GetType().Name);
                    }
                }

                return Result<ExternalSynchronization>.Failure($"Unexpected error: {ex.Message}");
            }
        }

        /// <summary>
        /// Collects case and offer data from the data store. Each service implements its own data collection logic.
        /// </summary>
        protected abstract Task<Result<JToken>> CollectDataAsync(string tenantId, string caseId, string offerId, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Transforms the collected data into the format expected by the external system. Each service implements its own transformation logic.
        /// </summary>
        protected abstract Task<Result<JToken>> TransformAsync(string tenantId, JToken collectedData, System.Threading.CancellationToken cancellationToken);

        /// <summary>
        /// Sends the transformed data to the external system. Each service implements its own sending logic.
        /// </summary>
        protected abstract Task<Result<ExternalSynchronization>> SendAsync(string tenantId, string caseId, string offerId, string targetLogicalId, JToken transformedData, System.Threading.CancellationToken cancellationToken);

        protected virtual Result ValidateRequest(SynchronizeDataRequest request)
        {
            if (request == null)
                return Result.Failure("Request is required");

            if (string.IsNullOrEmpty(request.TenantId))
                return Result.Failure("TenantId is required");

            if (string.IsNullOrEmpty(request.CaseId))
                return Result.Failure("CaseId is required");

            if (string.IsNullOrEmpty(request.OfferId))
                return Result.Failure("OfferId is required");

            if (string.IsNullOrEmpty(request.TargetLogicalId))
                return Result.Failure("TargetLogicalId is required");

            if (request.TargetLogicalId != LogicalId)
                return Result.Failure($"This service only supports {LogicalId} target");

            return Result.Success();
        }

        protected virtual async Task<Result<ExternalSynchronization>> CreateSyncRecordAsync(
            string tenantId,
            string caseId,
            string offerId,
            string targetLogicalId,
            string status,
            System.Threading.CancellationToken cancellationToken)
        {
            try
            {
                var upsert = new ExternalSynchronizationUpsert
                {
                    Id = Guid.NewGuid().ToString(),
                    CaseId = caseId,
                    OfferId = offerId,
                    TargetLogicalId = targetLogicalId,
                    Status = status,
                    ById = "system"
                };

                var result = await _externalSyncService.CreateAsync(tenantId, upsert, cancellationToken);
                if (!result.IsSuccess)
                    return Result<ExternalSynchronization>.Failure(result.Errors);

                // Create a simple record to return - the base class will handle the final record creation
                var syncRecord = new ExternalSynchronization
                {
                    Id = upsert.Id,
                    CaseId = upsert.CaseId,
                    OfferId = upsert.OfferId,
                    TargetLogicalId = upsert.TargetLogicalId,
                    Status = upsert.Status
                };
                return Result<ExternalSynchronization>.Success(syncRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ServiceName}] Failed to create sync record", GetType().Name);
                return Result<ExternalSynchronization>.Failure($"Failed to create sync record: {ex.Message}");
            }
        }

        protected virtual async Task<Result> UpdateSyncRecordStatusAsync(
            string tenantId,
            string syncId,
            string status,
            string[] errors,
            System.Threading.CancellationToken cancellationToken)
        {
            try
            {
                var errorList = new List<ExternalSynchronizationError>();
                if (errors != null && errors.Length > 0)
                {
                    foreach (var error in errors)
                    {
                        errorList.Add(new ExternalSynchronizationError
                        {
                            Step = status == "Failed" ? "Failed" : status,
                            Code = "SYNC_ERROR",
                            Message = error,
                            OccurredAt = DateTime.UtcNow
                        });
                    }
                }

                var upsert = new ExternalSynchronizationUpsert
                {
                    Id = syncId,
                    Status = status,
                    Errors = errorList.Count > 0 ? Newtonsoft.Json.JsonConvert.SerializeObject(errorList) : null,
                    ById = "system"
                };

                await _externalSyncService.UpdateAsync(tenantId, upsert, cancellationToken);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ServiceName}] Failed to update sync record status", GetType().Name);
                return Result.Failure($"Failed to update sync record: {ex.Message}");
            }
        }

        protected virtual async Task<Result<ExternalSynchronization>> UpdateSyncRecordFinalAsync(
            string tenantId,
            string syncId,
            string status,
            JToken payload,
            JToken response,
            System.Threading.CancellationToken cancellationToken)
        {
            try
            {
                var upsert = new ExternalSynchronizationUpsert
                {
                    Id = syncId,
                    Status = status,
                    Payload = payload?.ToString(),
                    Response = response?.ToString(),
                    LastSuccessfullySyncAt = status == "Sent" ? DateTime.UtcNow : null,
                    ById = "system"
                };

                var result = await _externalSyncService.UpdateAsync(tenantId, upsert, cancellationToken);
                if (!result.IsSuccess)
                    return Result<ExternalSynchronization>.Failure(result.Errors);

                // Create a complete record to return - the final sync record should include all the information
                var syncRecord = new ExternalSynchronization
                {
                    Id = syncId,
                    Status = status,
                    Payload = payload != null ? payload : null,
                    Response = response != null ? response : null,
                    LastSuccessfullySyncAt = upsert.LastSuccessfullySyncAt
                };

                return Result<ExternalSynchronization>.Success(syncRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{ServiceName}] Failed to update sync record final", GetType().Name);
                return Result<ExternalSynchronization>.Failure($"Failed to update sync record: {ex.Message}");
            }
        }

    }
}