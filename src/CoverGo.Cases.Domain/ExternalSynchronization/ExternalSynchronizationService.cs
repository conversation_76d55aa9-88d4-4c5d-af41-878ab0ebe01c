using CoverGo.Applications.Domain;

namespace CoverGo.Cases.Domain
{
    public class ExternalSynchronizationService : CoverGoGenericDataServiceBase<ExternalSynchronization, ExternalSynchronizationUpsert, ExternalSynchronizationFilter, IExternalSynchronizationRepository>, IExternalSynchronizationService
    {
        public ExternalSynchronizationService(IExternalSynchronizationRepository repository) : base(repository)
        {
        }
    }
}