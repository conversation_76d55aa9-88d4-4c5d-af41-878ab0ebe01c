using System;
using System.Collections.Generic;
using CoverGo.DomainUtils;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Domain
{
    public class ExternalSynchronizationError
    {
        public string Step { get; set; }
        public string Code { get; set; }
        public string Message { get; set; }
        public string Details { get; set; }
        public DateTime OccurredAt { get; set; }
    }

    public class ExternalSynchronization : SystemObject, IUniqueSystemObject
    {
        public string Id { get; set; }
        public string CaseId { get; set; }
        public string OfferId { get; set; }
        public string TargetLogicalId { get; set; }
        public string Status { get; set; }
        public JToken Payload { get; set; }
        public JToken Response { get; set; }
        public List<ExternalSynchronizationError> Errors { get; set; } = new List<ExternalSynchronizationError>();
        public DateTime? LastSuccessfullySyncAt { get; set; }
    }

    public class ExternalSynchronizationUpsert : IUniqueSystemObject
    {
        public string Id { get; set; }
        public string CaseId { get; set; }
        public string OfferId { get; set; }
        public string TargetLogicalId { get; set; }
        public string Status { get; set; }
        public string Payload { get; set; }
        public string Response { get; set; }
        public string Errors { get; set; } // JSON string representation of List<ExternalSynchronizationError>
        public DateTime? LastSuccessfullySyncAt { get; set; }
        public string ById { get; set; }
    }

    public class ExternalSynchronizationFilter
    {
        [FilterCondition(FilterCondition.Eq)]
        public string Id { get; set; }

        [FilterCondition(FilterCondition.Eq, nameof(ExternalSynchronization.Id), flag: false)]
        public string Id_neq { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronization.Id))]
        public List<string> Id_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronization.Id))]
        public string Id_contains { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string CaseId { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronization.CaseId))]
        public List<string> CaseId_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronization.CaseId))]
        public string CaseId_contains { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string OfferId { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronization.OfferId))]
        public List<string> OfferId_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronization.OfferId))]
        public string OfferId_contains { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string TargetLogicalId { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronization.TargetLogicalId))]
        public List<string> TargetLogicalId_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronization.TargetLogicalId))]
        public string TargetLogicalId_contains { get; set; }

        [FilterCondition(FilterCondition.Eq)]
        public string Status { get; set; }

        [FilterCondition(FilterCondition.In, nameof(ExternalSynchronization.Status))]
        public List<string> Status_in { get; set; }

        [FilterCondition(FilterCondition.Contains, nameof(ExternalSynchronization.Status))]
        public string Status_contains { get; set; }

        [FilterCondition(FilterCondition.Gte, nameof(ExternalSynchronization.LastSuccessfullySyncAt))]
        public DateTime? LastSuccessfullySyncAt_gte { get; set; }

        [FilterCondition(FilterCondition.Lte, nameof(ExternalSynchronization.LastSuccessfullySyncAt))]
        public DateTime? LastSuccessfullySyncAt_lte { get; set; }

        [FilterCondition(FilterCondition.Gte, nameof(ExternalSynchronization.CreatedAt))]
        public DateTime? CreatedAt_gte { get; set; }

        [FilterCondition(FilterCondition.Lte, nameof(ExternalSynchronization.CreatedAt))]
        public DateTime? CreatedAt_lte { get; set; }

        [FilterCondition(FilterCondition.Gte, nameof(ExternalSynchronization.LastModifiedAt))]
        public DateTime? UpdatedAt_gte { get; set; }

        [FilterCondition(FilterCondition.Lte, nameof(ExternalSynchronization.LastModifiedAt))]
        public DateTime? UpdatedAt_lte { get; set; }
    }
}