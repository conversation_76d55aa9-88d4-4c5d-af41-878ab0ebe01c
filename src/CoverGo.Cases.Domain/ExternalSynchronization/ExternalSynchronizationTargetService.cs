using CoverGo.Applications.Domain;

namespace CoverGo.Cases.Domain
{
    public class ExternalSynchronizationTargetService : CoverGoGenericDataServiceBase<ExternalSynchronizationTarget, ExternalSynchronizationTargetUpsert, ExternalSynchronizationTargetFilter, IExternalSynchronizationTargetRepository>, IExternalSynchronizationTargetService
    {
        public ExternalSynchronizationTargetService(IExternalSynchronizationTargetRepository repository) : base(repository)
        {
        }
    }
}