using System;
using System.Collections.Generic;
using CoverGo.Products.Client;
using Newtonsoft.Json.Linq;


namespace CoverGo.Cases.Domain
{

    public class JacketInstance
    {
        public string Id { get; set; }
        public int Order { get; set; }
        public string JacketId { get; set; }
        public bool StoreJacketByValue { get; set; }
        public Jacket Jacket { get; set; }
        public override bool Equals(object obj) => obj is JacketInstance instance && Id == instance.Id && Order == instance.Order && JacketId == instance.JacketId;
        public override int GetHashCode() => HashCode.Combine(Id, Order, JacketId);
    }

    public class JacketCommandBatch
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public List<AddJacketInstanceCommand> AddJacketInstanceCommands { get; set; }
        public List<UpdateJacketInstanceCommand> UpdateJacketInstanceCommands { get; set; }
    }

    public class AddJacketsToPolicyCommand
    {
        public string ById { get; set; }

        public List<AddJacketInstanceCommand> AddJacketInstanceCommands { get; set; }
    }

    public class AddJacketInstanceCommand
    {
        public string InstanceId { get; set; }
        public string JacketId { get; set; }
        public bool StoreJacketByValue { get; set; }
        public Jacket Jacket { get; set; }
        public int Order { get; set; }
        public string AddedById { get; set; }
    }

    public class UpdateJacketInstanceCommand
    {
        public string InstanceId { get; set; }
        public int Order { get; set; }
        public string ModifiedById { get; set; }
    }

    public class RemoveJacketInstanceCommand
    {
        public string ProposalId { get; set; }
        public string OfferId { get; set; }
        public string InstanceId { get; set; }
        public string RemovedById { get; set; }
    }
}