﻿using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.MariaDb.Integration;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration
{
    public class CasesCreationTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public CasesCreationTests()
        {
            _tenantId = "dbs_uat";
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_empty_caseNumber_WHEN_creating_case_for_tenant_dbs_uat_THEN_case_number_should_comply_with_dbs_requirement()
        {
            Case case1 = await CreateCaseWithSpecificTenant(_tenantId);
            Case case2 = await CreateCaseWithSpecificTenant(_tenantId);

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { case1.Id, case2.Id }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(_tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(_tenantId, case1.Id, new DeleteCaseCommand { DeletedById = case1.Id });
            await _client.Case_DeleteAsync(_tenantId, case2.Id, new DeleteCaseCommand { DeletedById = case2.Id });

            string currentDate = DateTime.Now.ToString("yyMMdd");

            cases.Count.Should().Be(2);
            string firstCaseNumber = cases[0].CaseNumber;
            firstCaseNumber.Should().Contain($"APP-{currentDate}");
            string firstCaseNumberCountSuffix = firstCaseNumber.Substring(firstCaseNumber.Length - 2);
            int firstCaseNumberIncrementInteger = int.Parse(firstCaseNumberCountSuffix.TrimStart('0'));

            string laterCaseNumber = cases[1].CaseNumber;
            laterCaseNumber.Should().Contain($"APP-{currentDate}");
            string laterCaseNumberCountSuffix = laterCaseNumber.Substring(laterCaseNumber.Length - 2);
            int laterCaseNumberIncrementInteger = int.Parse(laterCaseNumberCountSuffix.TrimStart('0'));
            laterCaseNumberIncrementInteger.Should().BeGreaterThan(firstCaseNumberIncrementInteger);
        }

        public async Task<Case> CreateCaseWithSpecificTenant(string tenantId)
        {
            Case newCase = new()
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString()
            };

            CreateCaseCommand command = new()
            {
                Name = newCase.Name,
                Description = newCase.Description
            };

            ResultOfString result = await _client.Case_CreateAsync(tenantId, command);
            newCase.Id = result!.Value;

            return newCase;
        }
    }
}
