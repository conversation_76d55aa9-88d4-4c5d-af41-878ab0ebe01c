﻿using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Xunit;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    public class OfferCrudTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public OfferCrudTests()
        {
            _tenantId = "dbs_uat";
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_offer_WHEN_executing_fact_batch_THEN_facts_are_modified()
        {
            CreateCaseCommand createCaseCommand = new();
            ResultOfString createCaseResult = await _client.Case_CreateAsync(_tenantId, createCaseCommand);
            string caseId = createCaseResult!.Value;

            AddProposalCommand addProposalCommand = new();
            ResultOfString createdProposalResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, addProposalCommand, null);
            string proposalId = createdProposalResult!.Value;

            AddOfferCommand addOfferCommand = new() { ProposalId = proposalId };
            ResultOfString createdOfferResult = await _client.Proposal_AddOfferAsync(_tenantId, caseId, addOfferCommand, null);
            string offerId = createdOfferResult!.Value;

            FactCommandBatch factCommandBatch = new()
            {
                ProposalId = proposalId,
                OfferId = offerId,
                AddFactCommands = new List<AddFactCommand>
                {
                    new()
                    {
                        Type = "test"
                    }
                }
            };

            Result factBatchResponse1 = await _client.Proposal_FactBatchAsync(_tenantId, caseId, factCommandBatch);
            factBatchResponse1!.Status.Should().Be("success");

            CaseWhere where = new() { Id = caseId };
            Case @case = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();
            Fact addedFact = @case.Proposals.First().Basket.First().Facts.First();
            addedFact.Type.Should().Be("test");

            var factCommandBatch2 = new FactCommandBatch
            {
                ProposalId = proposalId,
                OfferId = offerId,
                UpdateFactCommands = new List<UpdateFactCommand>
                {
                    new()
                    {
                        Id = addedFact.Id,
                        Type = "tested",
                        IsTypeChanged = true
                    }
                }
            };

            Result factBatchResponse2 = await _client.Proposal_FactBatchAsync(_tenantId, caseId, factCommandBatch2);
            factBatchResponse2!.Status.Should().Be("success");

            Case updatedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();
            Fact updatedFact = updatedCase!.Proposals!.First().Basket!.First().Facts!.First();
            updatedFact.Type.Should().Be("tested");
        }
    }
}
