﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Configuration;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    [SuppressMessage("ReSharper", "PropertyCanBePrivate.Global", Justification = "Config is setup by configuration binder")]
    [SuppressMessage("ReSharper", "PropertyCanBeMadeInitOnly.Global", Justification = "Config is setup by configuration binder")]
    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Config is setup by configuration binder")]
    public class Config
    {
        public string CasesUrl { get; set; }

        public static readonly Config Local = new() { CasesUrl = "http://localhost:60600" };
        private const string EnvironmentVariablePrefix = "CASES_MARIADB_INTEGRATION_TEST-";
        public static Config Load(string prefix = EnvironmentVariablePrefix)
        {
            var cfg = new Config();
            var builder = new ConfigurationBuilder();
            builder.AddEnvironmentVariables(source =>
            {
                source.Prefix = prefix;
            });
            builder.Build().Bind(cfg);
            return string.IsNullOrWhiteSpace(cfg.CasesUrl) ? Local : cfg;
        }
    }
}