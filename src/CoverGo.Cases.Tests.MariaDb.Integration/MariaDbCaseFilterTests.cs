﻿using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    public class MariaDbCaseFilterTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public MariaDbCaseFilterTests()
        {
            _tenantId = "dbs_uat";
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_Id_THEN_receive_case()
        {
            var command = new CreateCaseCommand();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            var where = new CaseWhere { Id = caseId };
            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase!.Id.Should().Be(caseId);
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_wrong_Id_THEN_receive_no_case()
        {
            var command = new CreateCaseCommand();
            await _client.Case_CreateAsync(_tenantId, command);

            var where = new CaseWhere { Id = "wrongId" };
            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_Id_in_THEN_receive_case()
        {
            var command = new CreateCaseCommand();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            var where = new CaseWhere { Id_in = new List<string> { caseId, "otherId" } };
            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase!.Id.Should().Be(caseId);
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_wrong_Id_in_THEN_receive_no_case()
        {
            var command = new CreateCaseCommand();
            await _client.Case_CreateAsync(_tenantId, command);

            var where = new CaseWhere { Id_in = new List<string> { "wrongId1", "wrongId2" } };
            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_status_THEN_receive_case()
        {
            var command = new CreateCaseCommand {
                Status = "pending"
            };
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result.Value;

            var where = new CaseWhere { And = new List<CaseWhere> { new() { Id = caseId } } };
            var statusWhere = new CaseWhere { Status = "pending" };
            where.And.Add(statusWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase!.CreatedAt.Should().BeOnOrAfter(DateTime.UtcNow.AddMinutes(-1));
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_createdAt_gt_THEN_receive_case()
        {
            var command = new CreateCaseCommand();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result.Value;

            var where = new CaseWhere { And = new List<CaseWhere> { new() { Id = caseId } } };
            var createdAtGtWhere = new CaseWhere { CreatedAt_gt = DateTime.UtcNow.AddMinutes(-1) };
            where.And.Add(createdAtGtWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase!.CreatedAt.Should().BeOnOrAfter(DateTime.UtcNow.AddMinutes(-1));
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_wrong_createdAt_gt_THEN_receive_no_case()
        {
            var command = new CreateCaseCommand();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            var where = new CaseWhere { And = new List<CaseWhere> { new() { Id = caseId } } };
            var createdAtGtWhere = new CaseWhere { CreatedAt_gt = DateTime.UtcNow };
            where.And.Add(createdAtGtWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_createdAt_lt_THEN_receive_case()
        {
            CreateCaseCommand command = new();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            CaseWhere where = new() { And = new List<CaseWhere> { new() { Id = caseId } } };
            CaseWhere createdAtLtWhere = new() { CreatedAt_lt = DateTime.UtcNow.AddMinutes(1) };
            where.And.Add(createdAtLtWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase!.CreatedAt.Should().BeOnOrBefore(DateTime.UtcNow.AddMinutes(1));
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_wrong_createdAt_lt_THEN_receive_no_case()
        {
            CreateCaseCommand command = new();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            CaseWhere where = new () { And = new List<CaseWhere> { new() { Id = caseId } } };
            CaseWhere createdAtLtWhere = new() { CreatedAt_lt = DateTime.UtcNow.AddMinutes(-1) };
            where.And.Add(createdAtLtWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_case_WHEN_query_with_Name_contains_THEN_receive_case()
        {
            Case case1 = await CreateCase();
            Case case2 = await CreateCase();

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Name_contains = case1.Name.Substring(5, 5)
                }
            };

            List<Case> resp = await _client.Case_GetAsync(_tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(_tenantId, case1.Id, new DeleteCaseCommand { DeletedById = case1.Id });
            await _client.Case_DeleteAsync(_tenantId, case2.Id, new DeleteCaseCommand { DeletedById = case2.Id });

            cases.Count.Should().Be(1);
            cases[0].Id.Should().Be(case1.Id);
            cases[0].CaseNumber.Should().Be(case1.CaseNumber);
            cases[0].Name.Should().Be(case1.Name);
            cases[0].Description.Should().Be(case1.Description);
        }

        private async Task<Case> CreateCase()
        {
            Case newCase = new()
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString(),
                CaseNumber = Guid.NewGuid().ToString()
            };

            CreateCaseCommand command = new()
            {
                Name = newCase.Name,
                Description = newCase.Description,
                CaseNumber = newCase.CaseNumber
            };

            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            newCase.Id = result!.Value;

            return newCase;
        }
    }
}
