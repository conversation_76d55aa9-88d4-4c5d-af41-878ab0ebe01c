﻿using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    public class MariaDbProposalFilterTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;
        public MariaDbProposalFilterTests()
        {
            _tenantId = "dbs_uat";
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_case_AND_proposal_WHEN_query_with_Proposals_exist_THEN_receive_case()
        {
            CreateCaseCommand createCaseCommand = new();
            ResultOfString createCaseResult = await _client.Case_CreateAsync(_tenantId, createCaseCommand);
            string caseId = createCaseResult!.Value;

            AddProposalCommand addProposalCommand = new();
            await _client.Proposal_AddProposalAsync(_tenantId, caseId, addProposalCommand, null);

            CaseWhere where = new() { And = new List<CaseWhere> { new() { Id = caseId } } };
            CaseWhere proposalsExistWhere = new() { Proposals_exist = true };
            where.And.Add(proposalsExistWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase!.Proposals!.Any().Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_case_AND_no_proposals_WHEN_query_with_Proposals_exist_THEN_receive_no_case()
        {
            CreateCaseCommand createCaseCommand = new();
            ResultOfString createCaseResult = await _client.Case_CreateAsync(_tenantId, createCaseCommand);
            string caseId = createCaseResult!.Value;

            CaseWhere where = new() { And = new List<CaseWhere> { new() { Id = caseId } } };
            CaseWhere proposalsExistWhere = new() { Proposals_exist = true };
            where.And.Add(proposalsExistWhere);
            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase.Should().BeNull();
        }

        [Fact]
        public async Task GIVEN_case_AND_proposal_WHEN_query_with_Proposals_exist_false_THEN_receive_case()
        {
            CreateCaseCommand createCaseCommand = new();
            ResultOfString createCaseResult = await _client.Case_CreateAsync(_tenantId, createCaseCommand);
            string caseId = createCaseResult!.Value;

            CaseWhere where = new() { And = new List<CaseWhere> { new() { Id = caseId } } };
            CaseWhere proposalsExistWhere = new() { Proposals_exist = false };
            where.And.Add(proposalsExistWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase?.Proposals?.Any().Should().BeFalse();
        }

        [Fact]
        public async Task GIVEN_case_AND_no_proposals_WHEN_query_with_Proposals_exist_false_THEN_receive_no_case()
        {
            CreateCaseCommand createCaseCommand = new();
            ResultOfString createCaseResult = await _client.Case_CreateAsync(_tenantId, createCaseCommand);
            string caseId = createCaseResult!.Value;

            AddProposalCommand addProposalCommand = new();
            await _client.Proposal_AddProposalAsync(_tenantId, caseId, addProposalCommand, null);

            CaseWhere where = new() { And = new List<CaseWhere> { new() { Id = caseId } } };
            CaseWhere proposalsExistWhere = new() { Proposals_exist = false };
            where.And.Add(proposalsExistWhere);

            Case expectedCase = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where }))!.FirstOrDefault();

            expectedCase.Should().BeNull();
        }
    }
}
