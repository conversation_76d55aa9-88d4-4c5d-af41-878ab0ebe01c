﻿using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.DomainUtils;
using Xunit;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    public class MariaDbCaseQueryArgumentsTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public MariaDbCaseQueryArgumentsTests()
        {
            _tenantId = "dbs_uat";
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_2_cases_WHEN_query_with_limit_1_THEN_receive_1_case()
        {
            var command = new CreateCaseCommand();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            ResultOfString result2 = await _client.Case_CreateAsync(_tenantId, command);
            string caseId2 = result2!.Value;

            var where = new CaseWhere { Id_in = new List<string> { caseId, caseId2 } };
            List<Case> expectedCases = await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where, First = 1 });

            expectedCases.Should().HaveCount(1);
        }

        [Fact]
        public async Task GIVEN_2_cases_WHEN_query_with_sort_by_createdAt_desc_THEN_receive_second_case_first()
        {
            var command1 = new CreateCaseCommand ();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command1);
            string caseId = result!.Value;

            var command2 = new CreateCaseCommand ();
            ResultOfString result2 = await _client.Case_CreateAsync(_tenantId, command2);
            string caseId2 = result2!.Value;

            var where = new CaseWhere { Id_in = new List<string> { caseId, caseId2 } };
            List<Case> expectedCases = await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where, OrderBy =  new OrderBy { FieldName = "createdAt", Type = OrderByType.DSC } });

            expectedCases!.First().Id.Should().Be(caseId2);
        }

        [Fact]
        public async Task GIVEN_2_cases_WHEN_query_with_skip_1_and_orderBy_createdAt_desc_THEN_receive_first_case()
        {
            CreateCaseCommand command = new();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            ResultOfString result2 = await _client.Case_CreateAsync(_tenantId, command);
            string caseId2 = result2!.Value;

            CaseWhere where = new() { Id_in = new List<string> { caseId, caseId2 } };
            List<Case> expectedCases = await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where, OrderBy = new OrderBy { FieldName = "createdAt", Type = OrderByType.DSC }, Skip = 1 });

            expectedCases.Should().HaveCount(1);
            expectedCases!.First().Id.Should().Be(caseId);
        }

        [Fact]
        public async Task GIVEN_2_cases_WHEN_query_with_skip_1_and_orderBy_createdAt_THEN_receive_second_case()
        {
            CreateCaseCommand command = new();
            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = result!.Value;

            ResultOfString result2 = await _client.Case_CreateAsync(_tenantId, command);
            string caseId2 = result2!.Value;

            CaseWhere where = new() { Id_in = new List<string> { caseId, caseId2 } };
            List<Case> expectedCases = await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = where, OrderBy = new OrderBy { FieldName = "createdAt" }, Skip = 1 });

            expectedCases.Should().HaveCount(1);
            expectedCases!.First().Id.Should().Be(caseId2);
        }
    }
}
