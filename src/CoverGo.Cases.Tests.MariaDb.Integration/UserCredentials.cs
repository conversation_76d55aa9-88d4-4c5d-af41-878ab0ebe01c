﻿using System.Diagnostics.CodeAnalysis;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "User credentials should be accessible for integration tests for creating own copies")]
    public class UserCredentials
    {
        public string TenantId { get; init; }
        public string ClientId { get; init; }
        public string UserName { get; init; }
        public string Password { get; init; }

        public static UserCredentials Admin =>
            new()
            {
                ClientId = "admin",
                Password = "V9K&KobcZO3",
                UserName = "<EMAIL>",
                TenantId = "covergo"
            };
    }
}