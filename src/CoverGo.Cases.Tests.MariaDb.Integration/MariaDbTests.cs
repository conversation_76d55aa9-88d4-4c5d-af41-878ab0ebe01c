﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using Xunit;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    public class MariaDbCRUDTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public MariaDbCRUDTests()
        {
            _tenantId = "dbs_uat";
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_id_in_filter_WHEN_getting_cases_THEN_cases_filtered_by_id_are_returned()
        {
            Case case1 = await CreateCase();
            Case case2 = await CreateCase();
            Case case3 = await CreateCase();

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { case1.Id, case2.Id }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(_tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(_tenantId, case1.Id, new DeleteCaseCommand { DeletedById = case1.Id });
            await _client.Case_DeleteAsync(_tenantId, case2.Id, new DeleteCaseCommand { DeletedById = case2.Id });
            await _client.Case_DeleteAsync(_tenantId, case3.Id, new DeleteCaseCommand { DeletedById = case3.Id });

            cases.Count.Should().Be(2);
            cases[0].Id.Should().Be(case1.Id);
            cases[0].CaseNumber.Should().Be(case1.CaseNumber);
            cases[0].Name.Should().Be(case1.Name);
            cases[0].Description.Should().Be(case1.Description);
            cases[1].Id.Should().Be(case2.Id);
            cases[1].CaseNumber.Should().Be(case2.CaseNumber);
            cases[1].Name.Should().Be(case2.Name);
            cases[1].Description.Should().Be(case2.Description);
        }

        [Fact]
        public async Task GIVEN_empty_id_in_filter_WHEN_getting_cases_THEN_no_cases_are_returned()
        {
            Case case1 = await CreateCase();
            Case case2 = await CreateCase();
            Case case3 = await CreateCase();

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(_tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(_tenantId, case1.Id, new DeleteCaseCommand { DeletedById = case1.Id });
            await _client.Case_DeleteAsync(_tenantId, case2.Id, new DeleteCaseCommand { DeletedById = case2.Id });
            await _client.Case_DeleteAsync(_tenantId, case3.Id, new DeleteCaseCommand { DeletedById = case3.Id });

            cases.Count.Should().Be(0);
        }

        [Fact]
        public async Task GIVEN_case_WHEN_getting_totalCount_THEN_totalCount_is_more_than_0()
        {
            Case @case = await CreateCase();

            long totalCount = await _client.Case_GetTotalCountAsync(_tenantId, new CaseWhere());

            totalCount.Should().BeGreaterThan(0);

            await _client.Case_DeleteAsync(_tenantId, @case.Id, new DeleteCaseCommand { DeletedById = @case.Id });
        }

        [Fact]
        public async Task GIVEN_case_WHEN_deleting_it_THEN_it_is_no_longer_queryable()
        {
            Case @case = await CreateCase();
            await _client.Case_DeleteAsync(_tenantId, @case.Id, new DeleteCaseCommand { DeletedById = @case.Id });

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id = @case.Id
                }
            };

            List<Case> resp = await _client.Case_GetAsync(_tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            cases.Should().BeNullOrEmpty();
        }

        public async Task<Case> CreateCase()
        {
            Case newCase = new()
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString(),
                CaseNumber = Guid.NewGuid().ToString()
            };

            CreateCaseCommand command = new() {
                Name = newCase.Name,
                Description = newCase.Description,
                CaseNumber = newCase.CaseNumber
            };

            ResultOfString result = await _client.Case_CreateAsync(_tenantId, command);
            newCase.Id = result!.Value;

            return newCase;
        }
    }
}
