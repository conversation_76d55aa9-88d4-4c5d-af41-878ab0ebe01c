﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CoverGo.Cases.Client.Rest;
using CoverGo.DomainUtils;
using FluentAssertions;
using Xunit;

namespace CoverGo.Cases.Tests.MariaDb.Integration
{
    public class ProposalsCreationTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public ProposalsCreationTests()
        {
            _tenantId = UserCredentials.Admin.TenantId;
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_proposal_WHEN_issue_proposals_for_tenant_dbs_uat_THEN_issuedAt_populated()
        {
            string tenantId = "dbs_uat";
            Proposal proposal = await CreateProposalWithSpecificTenant(tenantId);

            Result ret = await _client.Proposal_IssueProposalAsync(tenantId, proposal.CaseId, new IssueProposalCommand
            {
                ProposalId = proposal.Id,
                IssuedById = Guid.NewGuid().ToString()
            });

            ret!.Status.Should().Be("success");

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { proposal.CaseId }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            cases.Count.Should().Be(1);
            cases[0].Proposals.Count.Should().Be(1);
            cases[0].Proposals.First().IssuedAt.HasValue.Should().Be(true);

            await _client.Case_DeleteAsync(tenantId, proposal.CaseId, new DeleteCaseCommand { DeletedById = proposal.CaseId });
        }

        public async Task<Case> CreateCase(string tenantId)
        {
            Case newCase = new()
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString(),
                CaseNumber = Guid.NewGuid().ToString()
            };

            CreateCaseCommand command = new()
            {
                Name = newCase.Name,
                Description = newCase.Description,
                CaseNumber = newCase.CaseNumber
            };

            ResultOfString result = await _client.Case_CreateAsync(tenantId, command);
            newCase.Id = result!.Value;

            return newCase;
        }

        public async Task<Proposal> CreateProposalWithSpecificTenant(string tenantId)
        {
            Case newCase = new()
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString()
            };

            Proposal newProposal = new()
            {
                Name = Guid.NewGuid().ToString()
            };

            CreateCaseCommand command = new()
            {
                Name = newCase.Name,
                Description = newCase.Description
            };

            ResultOfString result = await _client.Case_CreateAsync(tenantId, command);
            newCase.Id = result!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = newProposal.Name
            };

            ResultOfString addResult = await _client.Proposal_AddProposalAsync(tenantId, newCase.Id, proposalCommand, null);
            newProposal.Id = addResult!.Value;
            newProposal.CaseId = newCase.Id;

            return newProposal;
        }
    }
}
