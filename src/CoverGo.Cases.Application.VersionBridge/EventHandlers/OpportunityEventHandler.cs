using CoverGo.BuildingBlocks.MessageBus.Abstractions.Handlers;
using CoverGo.Cases.Application.VersionBridge.IntegrationEvents;
using CoverGo.Cases.Application.VersionBridge.Interfaces;
using CoverGo.Cases.Application.VersionBridge.Mappers;
using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using Microsoft.Extensions.Logging;

namespace CoverGo.Cases.Application.VersionBridge.EventHandlers;

public class OpportunityEventHandler : IIntegrationEventHandler<RepositoryEvent<OpportunityAggregate, ValueObjectId<OpportunityAggregate>>>
{
    private readonly MapperResolver _mapperResolver;
    private readonly CaseRepositoryResolver _caseRepositoryResolver;
    private readonly ILogger<OpportunityEventHandler> _logger;
    
    public OpportunityEventHandler(MapperResolver mapperResolver, CaseRepositoryResolver caseRepositoryResolver, ILogger<OpportunityEventHandler> logger)
    {
        _mapperResolver = mapperResolver;
        _caseRepositoryResolver = caseRepositoryResolver;
        _logger = logger;
    }
    
    public async Task Handle(
        RepositoryEvent<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> notification,
        CancellationToken cancellationToken)
    {
        var caseRepositoryDecorator = _caseRepositoryResolver(notification.TenantId) as IRepositoryDecorator<ICaseRepository>;
        ICaseRepository? caseRepository = caseRepositoryDecorator?.Original;
        if (caseRepository == null)
        {
            _logger.LogError("Case repository is null");
            return;
        }

        string? tenantId = notification.TenantId;
        if (tenantId == null)
        {
            _logger.LogError("Tenant ID is null");
            return;
        }

        switch (notification)
        {
            case { Operation: RepositoryOperation.Upsert, UpsertedPayload: not null }:
                await HandleUpsertAsync(notification.UpsertedPayload, tenantId, caseRepository, cancellationToken);
                break;
            case { Operation: RepositoryOperation.Delete, DeletedPayload: not null }:
                await HandleDeleteAsync(notification.DeletedPayload, tenantId, caseRepository, cancellationToken);
                break;
        }
    }

    private async Task HandleDeleteAsync(List<ValueObjectId<OpportunityAggregate>> notificationDeletedPayload, string tenantId, ICaseRepository caseRepository, CancellationToken cancellationToken)
    {
        try
        {
            if (notificationDeletedPayload.Count == 0)
            {
                _logger.LogInformation("No opportunity IDs to delete. Skipping.");
                return;
            }

            _logger.LogInformation("Deleting {Count} cases corresponding to deleted opportunities", notificationDeletedPayload.Count);

            foreach (ValueObjectId<OpportunityAggregate> opportunityId in notificationDeletedPayload)
            {
                string caseId = opportunityId.Value;
                await caseRepository.DeleteAsync(tenantId, caseId, cancellationToken);
                _logger.LogDebug("Deleted case {CaseId} corresponding to opportunity {OpportunityId}", caseId, opportunityId.Value);
            }

            _logger.LogInformation("Successfully deleted {Count} cases", notificationDeletedPayload.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting cases. Opportunity IDs: {Ids}", notificationDeletedPayload.Select(id => id.Value));
            throw;
        }
    }

    private async Task HandleUpsertAsync(List<OpportunityAggregate> notificationUpsertedPayload, string tenantId, ICaseRepository caseRepository, CancellationToken cancellationToken)
    {
        try
        {
            OpportunityAggregateToCaseMapper mapper = _mapperResolver.GetOpportunityAggregateToCaseMapper();
            
            var cases = notificationUpsertedPayload
                .Select(mapper.ToCase)
                .Where(c => c != null)
                .ToList();

            if (cases.Count == 0)
            {
                _logger.LogInformation("No valid cases to upsert. Skipping.");
                return;
            }

            _logger.LogInformation("Upserting {Count} cases from opportunities", cases.Count);

            foreach (Case @case in cases)
            {
                await caseRepository.UpsertAsync(tenantId, @case.Id, @case, cancellationToken);
                _logger.LogDebug("Upserted case {CaseId}", @case.Id);
            }

            _logger.LogInformation("Successfully upserted {Count} cases", cases.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while upserting cases. Opportunity IDs: {Ids}", notificationUpsertedPayload.Select(o => o.Id.Value));
            throw;
        }
    }
}