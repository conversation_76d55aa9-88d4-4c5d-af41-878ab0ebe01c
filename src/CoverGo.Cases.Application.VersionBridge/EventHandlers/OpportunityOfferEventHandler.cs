using CoverGo.BuildingBlocks.MessageBus.Abstractions.Handlers;
using CoverGo.Cases.Application.VersionBridge.IntegrationEvents;
using CoverGo.Cases.Application.VersionBridge.Interfaces;
using CoverGo.Cases.Application.VersionBridge.Mappers;
using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using Microsoft.Extensions.Logging;

namespace CoverGo.Cases.Application.VersionBridge.EventHandlers;

public class OpportunityOfferEventHandler : IIntegrationEventHandler<RepositoryEvent<OfferAggregate, ValueObjectId<OfferAggregate>>>
{
    private readonly MapperResolver _mapperResolver;
    private readonly CaseRepositoryResolver _caseRepositoryResolver;
    private readonly ILogger<OpportunityOfferEventHandler> _logger;
    
    public OpportunityOfferEventHandler(MapperResolver mapperResolver, CaseRepositoryResolver caseRepositoryResolver, ILogger<OpportunityOfferEventHandler> logger)
    {
        _mapperResolver = mapperResolver;
        _caseRepositoryResolver = caseRepositoryResolver;
        _logger = logger;
    }
    
    public async Task Handle(
        RepositoryEvent<OfferAggregate, ValueObjectId<OfferAggregate>> notification,
        CancellationToken cancellationToken)
    {
        var caseRepositoryDecorator = _caseRepositoryResolver(notification.TenantId) as IRepositoryDecorator<ICaseRepository>;
        ICaseRepository? caseRepository = caseRepositoryDecorator?.Original;
        if (caseRepository == null)
        {
            _logger.LogError("Case repository is null");
            return;
        }
        
        string? tenantId = notification.TenantId;
        if (tenantId == null)
        {
            _logger.LogError("Tenant ID is null");
            return;
        }

        switch (notification)
        {
            case { Operation: RepositoryOperation.Upsert, UpsertedPayload: not null }:
                await HandleUpsertAsync(notification.UpsertedPayload, tenantId, caseRepository, cancellationToken);
                break;
            case { Operation: RepositoryOperation.Delete, DeletedPayload: not null }:
                await HandleDeleteAsync(notification.DeletedPayload, tenantId, caseRepository, cancellationToken);
                break;
        }
    }

    private Task HandleDeleteAsync(List<ValueObjectId<OfferAggregate>> notificationDeletedPayload, string tenantId, ICaseRepository caseRepository, CancellationToken cancellationToken)
    {
        // Note: In the Cases domain, offers belong to proposals and cases.
        // Offer synchronization from Quotation service to Cases service is handled through
        // case/proposal synchronization, not individual offer events.
        // In cases domain, offers are not directly deleted, but rather the containing proposal or case is deleted,
        // so we can safely ignore offer deletion events.
        _logger.LogInformation("Offer deletion is managed through case/proposal synchronization. Offer IDs: {Ids}", 
            notificationDeletedPayload.Select(id => id.Value));
        
        return Task.CompletedTask;
    }

    private async Task HandleUpsertAsync(List<OfferAggregate> notificationUpsertedPayload, string tenantId, ICaseRepository caseRepository, CancellationToken cancellationToken)
    {
        try
        {
            if (notificationUpsertedPayload.Count == 0)
            {
                _logger.LogInformation("No offer aggregates to upsert. Skipping.");
                return;
            }

            OfferAggregateToCaseOfferMapper mapper = _mapperResolver.GetOfferAggregateToCaseOfferMapper();
            
            var caseOffers = notificationUpsertedPayload
                .Select(mapper.ToCaseOffer)
                .Where(o => o != null)
                .ToList();

            if (caseOffers.Count == 0) return;

            // Group offers by case ID for batch processing
            var offersByCaseId = caseOffers
                .Where(o => o.CaseId != null)
                .GroupBy(o => o.CaseId)
                .ToList();

            _logger.LogInformation("Processing offers for {CaseCount} cases", offersByCaseId.Count);

            foreach (IGrouping<string, Offer> caseOfferGroup in offersByCaseId)
            {
                string caseId = caseOfferGroup.Key;
                var offers = caseOfferGroup.ToList();

                // Retrieve the case
                IEnumerable<Case>? cases = await caseRepository.GetAsync(tenantId, new CaseWhere { Id = caseId }, cancellationToken: cancellationToken);
                Case? caseEntity = cases?.FirstOrDefault();
                if (caseEntity == null)
                {
                    _logger.LogWarning("Case {CaseId} not found, skipping {OfferCount} offers", caseId, offers.Count);
                    continue;
                }

                bool caseModified = false;

                // Update offers in proposal baskets
                foreach (Offer offer in offers)
                {
                    if (offer.ProposalId == null)
                    {
                        _logger.LogWarning("Offer {OfferId} has no proposal ID, skipping", offer.Id);
                        continue;
                    }

                    Proposal? proposal = caseEntity.Proposals?.FirstOrDefault(p => p.Id == offer.ProposalId);
                    if (proposal == null)
                    {
                        _logger.LogWarning("Proposal {ProposalId} not found in case {CaseId}, skipping offer {OfferId}", 
                            offer.ProposalId, caseId, offer.Id);
                        continue;
                    }

                    if (proposal.Basket == null)
                    {
                        _logger.LogWarning("No basket found in proposal {ProposalId}, cannot add offer {OfferId}", 
                            offer.ProposalId, offer.Id);
                        continue;
                    }

                    // Get the current offers in the basket
                    var offersList = proposal.Basket.ToList();
                    int existingOfferIndex = offersList.FindIndex(o => o.Id == offer.Id);

                    if (existingOfferIndex >= 0)
                    {
                        // Update existing offer
                        offersList[existingOfferIndex] = offer;
                        proposal.Basket = offersList;
                        caseModified = true;
                        _logger.LogDebug("Updated offer {OfferId} in basket for proposal {ProposalId}", 
                            offer.Id, offer.ProposalId);
                    }
                    else
                    {
                        // Add new offer to basket
                        offersList.Add(offer);
                        proposal.Basket = offersList;
                        caseModified = true;
                        _logger.LogDebug("Added offer {OfferId} to basket for proposal {ProposalId}", 
                            offer.Id, offer.ProposalId);
                    }
                }


                if (!caseModified) continue;
                
                // Save the updated case if any modifications were made
                await caseRepository.UpsertAsync(tenantId, caseId, caseEntity, cancellationToken);
                
                _logger.LogInformation("Successfully upserted case {CaseId} with {OfferCount} updated offers", 
                    caseId, offers.Count);
            }

            _logger.LogInformation("Completed offer upsert processing for {OfferCount} offers across {CaseCount} cases", 
                caseOffers.Count, offersByCaseId.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while processing offer upsert event. Offer IDs: {Ids}", 
                notificationUpsertedPayload.Select(o => o.Id.Value));
            throw;
        }
    }
}