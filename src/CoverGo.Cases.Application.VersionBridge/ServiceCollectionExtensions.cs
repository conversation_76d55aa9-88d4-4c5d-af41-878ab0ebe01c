using CoverGo.Cases.Application.VersionBridge.Mappers;
using CoverGo.Cases.Application.VersionBridge.Mappers.Tenants.AsiaEb;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Cases.Application.VersionBridge;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddVersionBridgeMappers(this IServiceCollection services) =>
        services
            // Base mappers
            .AddSingleton<OpportunityAggregateToCaseMapper>()
            .AddSingleton<OfferAggregateToCaseOfferMapper>()
            
            // Tenant specific mappers
            .AddSingleton<AsiaEbOpportunityAggregateToCaseMapper>()
            .AddSingleton<AsiaEbOfferAggregateToCaseOfferMapper>()
            
            // Mapper resolver - resolves the correct mapper for the given tenant - must be scoped
            .AddScoped<MapperResolver>();
}