using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Offers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using CaseProductId = CoverGo.Cases.Domain.ProductId;
using CoverGo.Quotation.Domain.Pricing;

namespace CoverGo.Cases.Application.VersionBridge.Mappers;

public class OfferAggregateToCaseOfferMapper
{
    public virtual Offer? ToCaseOffer(OfferAggregate offerAggregate)
    {
        return new Offer
        {
            Id = offerAggregate.Id.Value,
            CaseId = offerAggregate.OpportunityId.Value,
            ProposalId = offerAggregate.OpportunityId.Value, // Proposal maps to Opportunity
            Status = MapStatus(offerAggregate.Status),
            OfferNumber = offerAggregate.InternalCode ?? string.Empty,
            PolicyNumber = offerAggregate.PolicyDetails != null 
                ? $"{offerAggregate.PolicyDetails.StartDate:yyyyMMdd}" 
                : string.Empty,
            ProductId = MapProductId(offerAggregate.ProductVersionId),
            StartDate = offerAggregate.PolicyDetails?.StartDate.ToDateTime(TimeOnly.MinValue),
            EndDate = offerAggregate.PolicyDetails?.EndDate.ToDateTime(TimeOnly.MinValue),
            Premium = MapPremium(offerAggregate.Pricing),
            IsPremiumOverridden = false, // Default value, can be adjusted based on business logic
            Values = new JObject(), // Empty by default, can be populated with custom fields
            BenefitOptions = new List<BenefitOption>(),
            Exclusions = new List<Exclusion>(),
            Clauses = new List<Clause>(),
            Jackets = new List<JacketInstance>(),
            Facts = new List<Fact>(),
            Pricing = offerAggregate.Pricing != null ? JsonConvert.SerializeObject(offerAggregate.Pricing) : null,
            Underwriting = null, // Can be populated from MemberUnderwriting if needed
            Fields = null,
            Fields2 = MapFields(offerAggregate),
            FieldsSchemaId = offerAggregate.ProductVersionId.ProductId.Plan,
            ProductTreeId = offerAggregate.ProductVersionId.ProductId.Type,
            ProductTreeRecords = new List<ProductTreeRecord>(),
            DistributorID = null, // Not available in OfferAggregate
            CampaignCodes = new List<string>(),
            Commissions = new List<Commission>()
        };
    }

    private static string MapStatus(OfferStatus status)
    {
        return status switch
        {
            OfferStatus.Draft => "DRAFT",
            OfferStatus.Issued => "ISSUED",
            OfferStatus.Accepted => "ACCEPTED",
            OfferStatus.Rejected => "REJECTED",
            OfferStatus.Expired => "EXPIRED",
            _ => "DRAFT"
        };
    }

    private static CaseProductId? MapProductId(ProductVersionId productVersionId)
    {
        return new CaseProductId
        {
            Plan = productVersionId.ProductId.Plan,
            Type = productVersionId.ProductId.Type,
            Version = productVersionId.Version
        };
    }

    private static Premium? MapPremium(QuotationPricing? pricing)
    {
        if (pricing?.Totals?.Total == null) return null;

        CurrencyCode currencyCode = Enum.TryParse<CurrencyCode>(pricing.Totals.Total.CurrencyCode.Value, true, out var parsedCode)
            ? parsedCode
            : CurrencyCode.USD; // Default to USD if parsing fails

        return new Premium
        {
            Amount = pricing.Totals.Total.Amount,
            CurrencyCode = currencyCode,
            OriginalPrice = pricing.Totals.Premiums?.Amount
        };
    }

    private static JToken? MapFields(OfferAggregate offerAggregate)
    {
        var fields = new JObject();

        // Map billing information
        if (offerAggregate.BillingInformation != null)
        {
            var billingInfo = new JObject();
            if (offerAggregate.BillingInformation.BillingFrequency != null)
                billingInfo["billingFrequency"] = offerAggregate.BillingInformation.BillingFrequency;
            if (offerAggregate.BillingInformation.BillingPricingDateBasis != null)
                billingInfo["billingPricingDateBasis"] = offerAggregate.BillingInformation.BillingPricingDateBasis;
            if (offerAggregate.BillingInformation.BillingYearMode != null)
                billingInfo["billingYearMode"] = offerAggregate.BillingInformation.BillingYearMode.ToString();
            if (offerAggregate.BillingInformation.PayorId != null)
                billingInfo["payorId"] = offerAggregate.BillingInformation.PayorId.Value;

            if (billingInfo.Count > 0)
                fields["billing"] = billingInfo;
        }

        // Map classes
        if (offerAggregate.Classes != null && offerAggregate.Classes.Any())
        {
            var classes = new JArray();
            foreach (var offerClass in offerAggregate.Classes)
            {
                var classObj = new JObject
                {
                    ["name"] = offerClass.Name.Value
                };
                if (offerClass.BenefitSelection != null)
                {
                    classObj["benefitSelection"] = JsonConvert.SerializeObject(offerClass.BenefitSelection);
                }
                classes.Add(classObj);
            }
            fields["classes"] = classes;
        }

        // Map insured groups
        if (offerAggregate.InsuredGroups != null && offerAggregate.InsuredGroups.Any())
        {
            var insuredGroups = new JArray();
            foreach (var group in offerAggregate.InsuredGroups)
            {
                var groupObj = new JObject
                {
                    ["count"] = group.Count,
                    ["type"] = group.Type.ToString()
                };
                if (group.Class != null)
                    groupObj["class"] = group.Class.Value;
                if (group.PlanId != null)
                    groupObj["planId"] = group.PlanId.Value;
                
                insuredGroups.Add(groupObj);
            }
            fields["insuredGroups"] = insuredGroups;
        }

        // Map expiration date
        if (offerAggregate.ExpirationDate != null)
        {
            fields["expirationDate"] = offerAggregate.ExpirationDate.Value;
        }

        // Map rejected reason
        if (!string.IsNullOrWhiteSpace(offerAggregate.RejectedReason))
        {
            fields["rejectedReason"] = offerAggregate.RejectedReason;
        }

        // Map version - OfferVersion is a record with Major, Minor, Patch properties
        if (offerAggregate.Version != null)
        {
            fields["version"] = $"{offerAggregate.Version.Major}.{offerAggregate.Version.Minor}.{offerAggregate.Version.Patch}";
        }

        return fields.Count > 0 ? fields : null;
    }
}