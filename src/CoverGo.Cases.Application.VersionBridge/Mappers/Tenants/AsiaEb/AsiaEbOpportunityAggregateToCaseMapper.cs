using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Cases.Application.VersionBridge.Mappers.Tenants.AsiaEb;

public class AsiaEbOpportunityAggregateToCaseMapper : OpportunityAggregateToCaseMapper
{
    public override Case? ToCase(OpportunityAggregate opportunityAggregate)
    {
        var @case = base.ToCase(opportunityAggregate);
        
        // AsiaEB specific mapping
        
        return @case;
    }
}