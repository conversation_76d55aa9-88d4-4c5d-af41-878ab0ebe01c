using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Cases.Application.VersionBridge.Mappers.Tenants.AsiaEb;

public class AsiaEbOfferAggregateToCaseOfferMapper : OfferAggregateToCaseOfferMapper
{
    public override Offer ToCaseOffer(OfferAggregate offerAggregate)
    {
        Offer offer = base.ToCaseOffer(offerAggregate);
        
        // AsiaEB specific mapping
        
        return offer;
    }
}