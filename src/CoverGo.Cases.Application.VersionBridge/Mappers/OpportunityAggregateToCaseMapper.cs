using CoverGo.Cases.Domain;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Opportunities;
using Newtonsoft.Json.Linq;

namespace CoverGo.Cases.Application.VersionBridge.Mappers;

public class OpportunityAggregateToCaseMapper
{
    public virtual Case? ToCase(OpportunityAggregate opportunityAggregate)
    {
        string caseId = opportunityAggregate.Id.Value;

        var @case = new Case
        {
            Id = caseId,
            CaseNumber = opportunityAggregate.LegacyCaseId?.Value ?? caseId,
            Name = ResolveCaseName(opportunityAggregate, caseId),
            Description = null,
            Source = opportunityAggregate.Source?.Id,
            HolderId = opportunityAggregate.ClientId?.Value,
            OtherHolderIds = BuildSupplementaryClientIds(opportunityAggregate),
            InsuredIds = BuildBeneficiaryIds(opportunityAggregate),
            Status = MapStatus(opportunityAggregate),
            Notes = [],
            Stakeholders = BuildStakeholders(opportunityAggregate),
            BeneficiaryEligibilities = [],
            PaymentInfos = [],
            ComponentId = opportunityAggregate.ProductVersionId.ToString(),
            Fields = BuildFields(opportunityAggregate),
            FieldsSchemaId = null,
            WorkflowSchemaId = null,
            IssuedPoliciesIds = new List<string>(),
            AccessPolicy = null,
            ChannelId = opportunityAggregate.SalesChannelId.Value,
            IsReadOnly = opportunityAggregate.Status == OpportunityStatus.Closed,
        };

        return @case;
    }

    private static string ResolveCaseName(OpportunityAggregate opportunityAggregate, string fallback)
    {
        string? clientName = opportunityAggregate.Client?.Name;
        return string.IsNullOrWhiteSpace(clientName) ? fallback : clientName;
    }

    private static List<string> BuildSupplementaryClientIds(OpportunityAggregate opportunityAggregate)
    {
        return opportunityAggregate.SupplementaryClientIds?
            .Select(id => id.Value)
            .Where(value => !string.IsNullOrWhiteSpace(value))
            .Distinct()
            .ToList()
            ?? new List<string>();
    }

    private static List<string> BuildBeneficiaryIds(OpportunityAggregate opportunityAggregate)
    {
        return opportunityAggregate.BeneficiaryIds?
            .Select(id => id.Value)
            .Where(value => !string.IsNullOrWhiteSpace(value))
            .Distinct()
            .ToList()
            ?? new List<string>();
    }

    private static string MapStatus(OpportunityAggregate opportunityAggregate)
    {
        return opportunityAggregate.Status switch
        {
            OpportunityStatus.Open => CaseStatus.InProgress,
            OpportunityStatus.Closed => MapClosedStatus(opportunityAggregate.CloseReason),
            _ => CaseStatus.InProgress
        };
    }

    private static string MapClosedStatus(OpportunityCloseReasonBase? closeReason)
    {
        return closeReason switch
        {
            OpportunityAutoCloseReason { CloseReasonValue: OpportunityCloseReasonValue.PolicyIssued } => CaseStatus.Issued,
            OpportunityAutoCloseReason { CloseReasonValue: OpportunityCloseReasonValue.Rejected } => CaseStatus.OfferRejected,
            OpportunityAutoCloseReason { CloseReasonValue: OpportunityCloseReasonValue.ProposalExpired } => CaseStatus.AwaitingClientFeedback,
            _ => CaseStatus.OfferAccepted
        };
    }

    private static IEnumerable<Stakeholder> BuildStakeholders(OpportunityAggregate opportunityAggregate)
    {
        var stakeholders = new List<Stakeholder>();

        if (opportunityAggregate.ClientId is { } clientId)
        {
            stakeholders.Add(new Stakeholder
            {
                Id = clientId.Value,
                EntityId = clientId.Value,
                Type = "Client",
                Name = opportunityAggregate.Client?.Name ?? clientId.Value,
                Code = opportunityAggregate.Client?.ClientType?.ToString()
            });
        }

        if (opportunityAggregate.PrimaryAgentId is { } primaryAgentId)
        {
            stakeholders.Add(CreateAgentStakeholder(primaryAgentId, opportunityAggregate.PrimaryAgent));
        }

        if (opportunityAggregate.SecondaryAgentId is { } secondaryAgentId)
        {
            stakeholders.Add(CreateAgentStakeholder(secondaryAgentId, null));
        }

        if (!string.IsNullOrWhiteSpace(opportunityAggregate.DistributorId?.Value))
        {
            string distributorId = opportunityAggregate.DistributorId.Value;
            stakeholders.Add(new Stakeholder
            {
                Id = distributorId,
                EntityId = distributorId,
                Type = "Distributor",
                Name = distributorId,
                Code = distributorId
            });
        }

        return stakeholders
            .GroupBy(s => s.Id)
            .Select(g => g.First())
            .ToList();
    }

    private static Stakeholder CreateAgentStakeholder(AgentId agentId, OpportunityAgent? agentDetails)
    {
        string agentIdValue = agentId.Value;

        return new Stakeholder
        {
            Id = agentIdValue,
            EntityId = agentIdValue,
            Type = "Agent",
            Name = BuildAgentName(agentDetails) ?? agentIdValue,
            Code = agentDetails?.AgentNumber
        };
    }

    private static string? BuildAgentName(OpportunityAgent? agentDetails)
    {
        if (agentDetails == null) return null;

        var parts = new[] { agentDetails.FirstName, agentDetails.LastName }
            .Where(part => !string.IsNullOrWhiteSpace(part));

        string fullName = string.Join(" ", parts);
        return string.IsNullOrWhiteSpace(fullName) ? null : fullName;
    }

    private static JToken? BuildFields(OpportunityAggregate opportunityAggregate)
    {
        JObject fields = new();

        if (opportunityAggregate.ProductVersionId is { } productVersionId)
        {
            fields["productVersion"] = new JObject
            {
                ["plan"] = productVersionId.ProductId.Plan,
                ["type"] = productVersionId.ProductId.Type,
                ["version"] = productVersionId.Version
            };
        }

        if (!string.IsNullOrWhiteSpace(opportunityAggregate.DistributorId?.Value))
        {
            fields["distributorId"] = opportunityAggregate.DistributorId.Value;
        }

        if (opportunityAggregate.BuyFlowType.HasValue)
        {
            fields["buyFlowType"] = opportunityAggregate.BuyFlowType.Value.ToString();
        }

        if (opportunityAggregate.AllowSupplementaryClients.HasValue)
        {
            fields["allowSupplementaryClients"] = opportunityAggregate.AllowSupplementaryClients.Value;
        }

        if (opportunityAggregate.AllowMultipleBeneficiaries.HasValue)
        {
            fields["allowMultipleBeneficiaries"] = opportunityAggregate.AllowMultipleBeneficiaries.Value;
        }

        if (opportunityAggregate.SecondaryAgentId is { } secondaryAgentId)
        {
            fields["secondaryAgentId"] = secondaryAgentId.Value;
        }

        if (opportunityAggregate.PrimaryAgent?.AgentNumber is { } agentNumber)
        {
            fields["primaryAgentNumber"] = agentNumber;
        }

        if (opportunityAggregate.Client?.ClientType is ClientType clientType)
        {
            fields["clientType"] = clientType.ToString();
        }

        if (opportunityAggregate.ClientLinksFromAgents?.Any() == true)
        {
            fields["clientLinksFromAgents"] = new JArray(opportunityAggregate.ClientLinksFromAgents);
        }

        if (opportunityAggregate.LegacyProposalId is { } legacyProposalId)
        {
            fields["legacyProposalId"] = legacyProposalId.Value;
        }

        if (opportunityAggregate.CloseReason is OpportunityAutoCloseReason autoCloseReason)
        {
            fields["closeReason"] = autoCloseReason.CloseReasonValue.ToString();
        }

        return fields.Count > 0 ? fields : null;
    }
}
