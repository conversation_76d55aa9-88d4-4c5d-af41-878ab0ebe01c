using CoverGo.Cases.Application.VersionBridge.Mappers.Tenants.AsiaEb;
using CoverGo.Multitenancy;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Cases.Application.VersionBridge.Mappers;

public class MapperResolver
{
    private readonly TenantId _tenantId;
    private readonly IServiceProvider _serviceProvider;
    
    public MapperResolver(TenantId tenantId, IServiceProvider serviceProvider)
    {
        _tenantId = tenantId;
        _serviceProvider = serviceProvider;
    }
    
    public OpportunityAggregateToCaseMapper GetOpportunityAggregateToCaseMapper() =>
        _tenantId.Value switch
        {
            "asia_dev" or "asia_preprod" or "asia_prod"
                => _serviceProvider.GetRequiredService<AsiaEbOpportunityAggregateToCaseMapper>(),
            _
                => _serviceProvider.GetRequiredService<OpportunityAggregateToCaseMapper>(),
        };

    public OfferAggregateToCaseOfferMapper GetOfferAggregateToCaseOfferMapper() =>
        _tenantId.Value switch
        {
            "asia_dev" or "asia_preprod" or "asia_prod"
                => _serviceProvider.GetRequiredService<AsiaEbOfferAggregateToCaseOfferMapper>(),
            _
                => _serviceProvider.GetRequiredService<OfferAggregateToCaseOfferMapper>(),
        };
}