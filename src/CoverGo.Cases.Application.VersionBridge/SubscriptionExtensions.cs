﻿using CoverGo.BuildingBlocks.MessageBus.Dapr.Subscriptions;
using CoverGo.Cases.Application.VersionBridge.IntegrationEvents;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;


namespace CoverGo.Cases.Application.VersionBridge;

public static class SubscriptionExtensions
{
    public static ISubscription SubscribeToV2SyncEvents(this ISubscription subscription) =>
        subscription
            .SubscribeToEvent<RepositoryEvent<OpportunityAggregate, ValueObjectId<OpportunityAggregate>>>()
            .SubscribeToEvent<RepositoryEvent<OfferAggregate, ValueObjectId<OfferAggregate>>>();
}