using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.BuildingBlocks.MessageBus.Contracts;
using CoverGo.DomainUtils;

namespace CoverGo.Cases.Application.VersionBridge.IntegrationEvents;

public record RepositoryEvent<TAggregateRoot, TIdentity> : IntegrationEvent where TAggregateRoot : IAggregateRoot<TIdentity>
{
    public required RepositoryOperation Operation { get; set; }
    public List<TAggregateRoot>? UpsertedPayload { get; set; }
    public List<TIdentity>? DeletedPayload { get; set; }
}
public record RepositoryEvent<T> : IntegrationEvent where T : SystemObject
{
    public required RepositoryOperation Operation { get; set; }
    public List<T>? UpsertedPayload { get; set; }
    public List<string>? DeletedPayload { get; set; }
}