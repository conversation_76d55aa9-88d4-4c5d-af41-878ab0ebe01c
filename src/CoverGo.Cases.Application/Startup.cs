using CoverGo.Applications.AttachedRules.Services;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Metrics.GraphQl;
using CoverGo.Applications.Monitoring;
using CoverGo.Applications.Startup;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.Brokers;
using CoverGo.Cases.Domain.Counters;
using CoverGo.Cases.Domain.Custom;
using CoverGo.Cases.Domain.Custom.Bupa;
using CoverGo.Cases.Domain.DataSchemas;
using CoverGo.Cases.Domain.OfferStatusPermissions;
using CoverGo.Cases.Domain.OfferStatusPermissions.Bupa;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.Cases.Infrastructure.Adapters.EF;
using CoverGo.Cases.Infrastructure.Adapters.Mongo;
using CoverGo.Cases.Infrastructure.OtherServices;
using CoverGo.Configuration;
using CoverGo.JsonUtils;
using CoverGo.MongoUtils;
using CoverGo.Products.Client;
using CoverGo.Products.Infrastructure.Brokers.Adapters.Mongo;
using CoverGo.Products.Infrastructure.Counters.Adapters.Mongo;
using CoverGo.Products.Infrastructure.DataSchemas.Adapters.Mongo;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.IdGenerators;
using MongoDB.Bson.Serialization.Serializers;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Sentry;
using System;
using System.Collections.Generic;
using System.Linq;
using CoverGo.BuildingBlocks.Auth.Extensions;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.BuildingBlocks.MessageBus.Dapr;
using CoverGo.Users.Client;
using Formatting = Newtonsoft.Json.Formatting;
using CoverGo.Cases.Application.Filters;
using CoverGo.Cases.Infrastructure;
using CoverGo.Policies.Client;
using Microsoft.Net.Http.Headers;
using CoverGo.Cases.Application.GraphQl.AgentAssignment;
using CoverGo.Cases.Application.VersionBridge;
using CoverGo.Cases.Infrastructure.Decorators.RepositoryDecorators;
using CoverGo.Cases.Infrastructure.Decorators;
using CoverGo.BuildingBlocks.MessageBus;
using CoverGo.BuildingBlocks.MessageBus.InMemory;
using CoverGo.Multitenancy.AspNetCore;

namespace CoverGo.Cases.Application
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Configuration = configuration;
            Environment = environment;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            //Testing
            if (System.Environment.GetEnvironmentVariable("appName") == null)
                System.Environment.SetEnvironmentVariable("appName", "covergo-cases");

            services
                .AddControllers()
                .AddNewtonsoftJson(
                    options =>
                    {
                        options.SerializerSettings.Converters.Add(new StringEnumConverter());
                        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                        options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;

                        if (Environment.IsDevelopment())
                            options.SerializerSettings.Formatting = Formatting.Indented;
                    }
                );
            
            services.AddMultitenancy()
                .AddTenantProvider<FromClaimTenantProvider>()
                .AddTenantProvider<FromHeaderTenantProvider>()
                .AddTenantProvider<FromRouteTenantProvider>()
                .Services
                    .AddMutlitenancyAutofac();
            
            services.AddMultiTenantFeatureManagement(Configuration);
            
            services.AddVersionBridgeMappers();

            services.AddMessageBus(Configuration, builder =>
            {
                bool useInMemoryBus = builder.Configuration.GetValue<bool>("UseInMemoryBus", false);
                if (useInMemoryBus)
                {
                    builder.AddInMemoryMessageBus();
                }
                else
                {
                    builder.AddDaprMessageBus(Configuration);
                }
            });

            services.AddMemoryCache();
            services.AddHttpContextAccessor();
            services.AddScoped<ChannelIdFilterAttribute>();
            services.AddScoped<ICaseService, CaseService>();
            services.AddSingleton<DataSchemaService>();
            services.AddSingleton<IDataSchemaRepository, MongoDataSchemaRepository>();
            services.AddSingleton<IBrokerRepository, MongoBrokerRepository>();
            services.AddSingleton<IBrokerService, BrokerService>();
            services.AddSingleton<ICounterRepository, MongoCounterRepository>();
            services.AddSingleton<ICounterService, CounterService>();
            services.AddSingleton<IExternalSynchronizationTargetRepository, MongoExternalSynchronizationTargetRepository>();
            services.AddSingleton<IExternalSynchronizationTargetService, ExternalSynchronizationTargetService>();
            services.AddSingleton<IExternalSynchronizationRepository, MongoExternalSynchronizationRepository>();
            services.AddSingleton<IExternalSynchronizationService, ExternalSynchronizationService>();

            // Synchronization services
            services.AddScoped<ISynchronizeDataService, BupaSynchronizeDataService>();

            services.AddSingleton<IJsonProjector, JsonProjector>();
            services.AddTransient<SentryHttpMessageHandler>();

            services.AddSingleton<IGatewayService, CoverGoGatewayService>();
            services.AddHttpClient<IGatewayService, CoverGoGatewayService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:gateway"]}"))
                    .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<IPricingService, CoverGoPricingService>();
            services.AddHttpClient<IPricingService, CoverGoPricingService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:pricing"]}"))
                    .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<IPolicyService, CoverGoPolicyService>();
            services.AddHttpClient<IPolicyService, CoverGoPolicyService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:policies"]}"))
                    .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<IPoliciesClient, PoliciesClient>();
            services.AddHttpClient<IPoliciesClient, PoliciesClient>((serviceProvider, client) =>
            {
                client.BaseAddress = new Uri($"{Configuration[$"serviceUrls:policies"]}");
                var context = serviceProvider.GetRequiredService<IHttpContextAccessor>()?.HttpContext;
                if (context != null)
                    foreach (var headerName in new[] { HeaderNames.Authorization, "Tenant" })
                        if (context.Request.Headers.TryGetValue(headerName, out var value))
                            client.DefaultRequestHeaders.TryAddWithoutValidation(headerName, value.ToArray());
            });
            services.AddSingleton<IUsersService, CoverGoUsersService>();
            services.AddSingleton<IUsersClient, UsersClient>();
            services.AddHttpClient<IUsersClient, UsersClient>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:users"]}"))
                .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<IProductsClient, ProductsClient>();
            services.AddHttpClient<IProductsClient, ProductsClient>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:products"]}"))
                    .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<INotificationService, CoverGoNotificationService>();
            services.AddHttpClient<INotificationService, CoverGoNotificationService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:notifications"]}"))
                    .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<IAuthService, CoverGoAuthService>();
            services.AddHttpClient<IAuthService, CoverGoAuthService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:auth"]}"))
                    .AddHttpMessageHandler<SentryHttpMessageHandler>();
            services.AddSingleton<ITransactionService, CoverGoTransactionService>();
            services.AddHttpClient<ITransactionService, CoverGoTransactionService>(c => c.BaseAddress = new Uri($"{Configuration["serviceUrls:transactions"]}"));
            services.AddChannelManagementClient().ConfigureHttpClient(
                client => client.BaseAddress = new UriBuilder(Configuration["serviceUrls:channel-management"]!) { Path = "/graphql" }.Uri,
                clientBuilder => clientBuilder.AddAuthorizationFromQueryString());

            services.AddSingleton<IEventCustomRules, EventCustomRules>();
            services.AddSingleton<IEventCustomProcessingRule, BupaProposalStatusUpdateWhenOfferAccepted>();
            services.AddSingleton<IMessageBrokerClient, AzureMessageBrokerClient>(provider =>
            {
                var configuration = provider.GetService<IConfiguration>();
                var messageBrokerIsEnabled = configuration.GetValue<bool?>("AzureServiceBus:IsEnabled", false);
                string connectionString = configuration.GetValue<string>("AzureServiceBus:ConnectionString");
                string queueOrTopicName = configuration.GetValue<string>("AzureServiceBus:QueueOrTopicName");
                return new AzureMessageBrokerClient(messageBrokerIsEnabled, connectionString, queueOrTopicName, provider.GetService<ILogger<AzureMessageBrokerClient>>());
            });
            if (DbConfig.GetConfigs().Any(c => c.ProviderId == "mongoDb"))
            {
                services.AddSingleton<MongoDbEventStore>();
                services.AddSingleton<MessageSender>();
                services.AddSingleton<MongoDbCaseRepository>()
                    .Decorate<MongoDbCaseRepository, MongoDbCaseRepositoryDecorator>();
                services.AddSingleton<MongoDbReferenceGenerator>();
                services.AddSingleton<MongoDbOfferRepository>()
                    .Decorate<MongoDbOfferRepository, MongoDbOfferRepositoryDecorator>();

                var conventionPack = new ConventionPack
                {
                    new CamelCaseElementNameConvention(),
                    new EnumRepresentationConvention(BsonType.String),
                    new IgnoreExtraElementsConvention(true),
                    new IgnoreIfNullConvention(true)
                };
                ConventionRegistry.Register("all", conventionPack, t => true);

                BsonClassMap.RegisterClassMap<CaseEvent>(cm =>
                {
                    cm.AutoMap();
                    cm.MapIdProperty(c => c.Id)
                        .SetIdGenerator(StringObjectIdGenerator.Instance)
                        .SetSerializer(new StringSerializer(BsonType.ObjectId));
                });
                BsonSerializer.RegisterSerializer(new JTokenSerializer());
                BsonSerializer.RegisterSerializer(typeof(decimal), new DecimalSerializer(BsonType.Decimal128));
                BsonSerializer.RegisterSerializer(typeof(decimal?), new NullableSerializer<decimal>(new DecimalSerializer(BsonType.Decimal128)));

                services.AddAttachedRules(Configuration, "cases_");

                services.AddCoverGoEventSourcingMongoDb();
                services.AddCoverGoEventSourcing();
            }

            if (DbConfig.GetConfigs().Any(c => c.ProviderId == "mariaDb"))
            {
                services.AddSingleton<MariaDbCaseEventStore>();
                services.AddSingleton<MariaDbCaseRepository>();
                services.AddSingleton<MariaDbReferenceGenerator>();

                services.AddSingleton<CasesDbContextFactory>();
            }

            services.AddSingleton<EventStoreResolver>(sp => tenant =>
            {
                var dbConfig = DbConfig.GetConfig(tenant);
                string databaseDriver = (dbConfig != null) ? dbConfig.ProviderId : "mongoDb";

                switch (databaseDriver)
                {
                    case "mariaDb":
                        return sp.GetService<MariaDbCaseEventStore>();
                    default:
                        return sp.GetService<MongoDbEventStore>();
                }
            });

            services.AddSingleton<CaseRepositoryResolver>(sp => tenant =>
            {
                var dbConfig = DbConfig.GetConfig(tenant);
                string databaseDriver = (dbConfig != null) ? dbConfig.ProviderId : "mongoDb";

                switch (databaseDriver)
                {
                    case "mariaDb":
                        return sp.GetService<MariaDbCaseRepository>();
                    default:
                        return sp.GetService<MongoDbCaseRepository>();
                }
            });

            services.AddSingleton<ReferenceGeneratorResolver>(sp => tenant =>
            {
                var dbConfig = DbConfig.GetConfig(tenant);
                string databaseDriver = (dbConfig != null) ? dbConfig.ProviderId : "mongoDb";

                switch (databaseDriver)
                {
                    case "mariaDb":
                        return sp.GetService<MariaDbReferenceGenerator>();
                    default:
                        return sp.GetService<MongoDbReferenceGenerator>();
                }
            });
            
            AppContext.SetSwitch("System.Net.Http.SocketsHttpHandler.Http2UnencryptedSupport", true); // Needed this if .Net <5.0
            services.AddCoverGoOpenTelemetryTracingIfEnabled();

            services.AddSwaggerGen(c =>
            {
                c.CustomSchemaIds(type => type.ToString());
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "CoverGo Cases", Version = "v1" });
            });
            services.AddCoverGoApplicationMonitoring();

            services.AddScoped<ICaseServiceFactory, CaseServiceFactory>();

            services.AddCoverGoCurrentUser();

            services.AddCoverGoAuthorization(Configuration);
            services.AddCoverGoGraphQl(
                "cases",
                executionTimeoutSec: Configuration.GetValue("graphql:timeOutSec", 30))
                .AddGraphQlMetricsIfEnabled()
                .AddType<AssignAgentCommandInputType>()
                .AddType<UnassignAgentCommandInputType>()
                .AddType<AssignHandlersCommandInputType>()
                .AddType<UnassignHandlersCommandInputType>();

            services.AddSingleton<PermissionValidator>();
            services.AddScoped<BuildingBlocks.Auth.Permissions.IPermissionValidator, BuildingBlocks.Auth.Permissions.PermissionValidator>();
            services.AddSingleton<IOfferStatusTransitionPermissionValidator, BupaOfferStatusTransitionPermissionValidator>();
            services.AddScoped<IRestrictedCaseService, RestrictedCaseService>();
        }


        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory, IEnumerable<ILoggerProvider> loggerProviders, IHttpContextAccessor httpContextAccessor)
        {
            app.UseCoverGoMetrics();
            app.UseAddAuthorizationFromQueryString();

            DbConfig mariaDbConfig = DbConfig.GetConfigs().FirstOrDefault(c => c.ProviderId == "mariaDb"); //take first as should only be one per _service anyways
            if (mariaDbConfig != null)
            {
                var factory = new CasesDbContextFactory();
                using CasesDbContext context = factory.CreateDbContext(mariaDbConfig);
                context.Database.Migrate();
            }
            
            DbConfig mongoDbConfig = DbConfig.GetConfigs().FirstOrDefault(c => c.ProviderId == "mongoDb"); //take first as should only be one per _service anyways
            if (mongoDbConfig != null)
            {
                var webApplication = app as WebApplication;
                webApplication?.MessageBus(webApplication.Configuration)
                    .SubscribeToV2SyncEvents();
            }

            if (env.IsDevelopment())
                app.UseDeveloperExceptionPage();
            else
            {
                foreach (ILoggerProvider loggerProvider in loggerProviders)
                    loggerFactory.AddProvider(loggerProvider);
                loggerFactory.AddSentry();
            }

            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "CoverGo Cases v1"));

            app.UseLogs();
            app.UseSentryTracing();
            app.UseRouting();
            app.UseCoverGoGraphQl();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapVersionEndpoint();
            });
            app.UseCoverGoApplicationMonitoring();

            HttpClientExtensions.HttpContextAccessor = httpContextAccessor;
        }
    }
}
