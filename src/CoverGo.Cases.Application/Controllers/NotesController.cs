﻿using System.Threading;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;

using System.Threading.Tasks;
using CoverGo.Cases.Application.Filters;

namespace CoverGo.Cases.Application.Controllers
{
    [ApiController]
    [ServiceFilter(typeof(ChannelIdFilterAttribute))]
    [Route("{tenantId}/api/v1/cases/{caseId}/notes")]
    public class NotesController
    {
        private readonly ICaseService _caseService;

        public NotesController(ICaseServiceFactory caseServiceFactory)
        {
            _caseService = caseServiceFactory.Build();
        }

        [HttpPost("add")]
        public async Task<ActionResult<Result>> AddNoteToCase(string tenantId, string caseId, [FromBody] AddNoteCommand command, CancellationToken cancellationToken = default)
        {
            Result result = await _caseService.AddNoteToCaseAsync(tenantId, caseId, command, cancellationToken);
            return result;
        }

        [HttpPost("update")]
        public async Task<ActionResult<Result>> UpdateNote(string tenantId, string caseId, [FromBody] UpdateNoteCommand command, CancellationToken cancellationToken = default)
        {
            Result result = await _caseService.UpdateNoteOfCaseAsync(tenantId, caseId, command, cancellationToken);
            return result;
        }

        [HttpPost("remove")]
        public async Task<ActionResult<Result>> RemoveNote(string tenantId, string caseId, [FromBody] RemoveNoteCommand command, CancellationToken cancellationToken = default)
        {
            Result result = await _caseService.RemoveNoteFromCaseAsync(tenantId, caseId, command, cancellationToken);
            return result;
        }
    }
}
