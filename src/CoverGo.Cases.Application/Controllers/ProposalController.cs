﻿using System.Threading;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;

using System.Threading.Tasks;
using CoverGo.Cases.Application.Filters;

namespace CoverGo.Cases.Application.Controllers
{
    [ApiController]
    [ServiceFilter(typeof(ChannelIdFilterAttribute))]
    [Route("{tenantId}/api/v1/cases/{caseId}/proposals")]
    public class ProposalController
    {
        private readonly ICaseService _service;
        public ProposalController(ICaseServiceFactory serviceFactory)
        {
            _service = serviceFactory.Build();
        }

        [HttpPost("add")]
        public async Task<Result<string>> AddProposalAsync(string tenantId, string caseId, [FromBody] AddProposalCommand command, string accessToken, CancellationToken cancellationToken = default) =>
          await _service.AddProposalAsync(tenantId, caseId, command, accessToken, cancellationToken);

        [HttpPost("copy")]
        public async Task<Result<string>> CopyProposalAsync(string tenantId, string caseId, [FromBody] CopyProposalCommand command, CancellationToken cancellationToken = default) =>
            await _service.CopyProposalAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("renew")]
        public async Task<Result<string>> RenewProposalAsync(string tenantId, string caseId, [FromBody] RenewProposalCommand command, string accessToken, CancellationToken cancellationToken = default) =>
          await _service.RenewProposalAsync(tenantId, caseId, command, accessToken, cancellationToken);

        [HttpPost("issue")]
        public async Task<Result> IssueProposalAsync(string tenantId, string caseId, [FromBody] IssueProposalCommand command, CancellationToken cancellationToken = default) => await _service.IssueProposalAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("reject")]
        public async Task<Result> RejectProposalAsync(string tenantId, string caseId, [FromBody] RejectProposalCommand command, CancellationToken cancellationToken = default) => await _service.RejectProposalAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("update")]
        public async Task<Result> UpdateProposalAsync(string tenantId, string caseId, [FromBody] UpdateProposalCommand command, CancellationToken cancellationToken = default) => await _service.UpdateProposalAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("remove")]
        public async Task<Result> RemoveProposalAsync(string tenantId, string caseId, [FromBody] RemoveCommand command, CancellationToken cancellationToken = default) => await _service.RemoveProposalAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("stakeholders/add")]
        public async Task<Result<string>> AddStakeholderOfProposalAsync(string tenantId, string caseId, [FromBody] AddStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.AddStakeholderToProposalAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("stakeholders/update")]
        public async Task<Result> UpdateStakeholderOfProposalAsync(string tenantId, string caseId, [FromBody] UpdateStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.UpdateStakeholderOfProposalAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("stakeholders/remove")]
        public async Task<Result> RemoveStakeholderFromProposalAsync(string tenantId, string caseId, [FromBody] RemoveStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.RemoveStakeHolderFromProposalAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/add")]
        public async Task<Result<string>> AddOfferAsync(string tenantId, string caseId, [FromBody] AddOfferCommand command, string accessToken, CancellationToken cancellationToken = default) =>
          await _service.AddOfferAsync(tenantId, caseId, command, accessToken, cancellationToken);

        [HttpPost("offers/update")]
        public async Task<Result> UpdateOfferAsync(string tenantId, string caseId, [FromBody] UpdateOfferCommand command, CancellationToken cancellationToken = default) => await _service.UpdateOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/remove")]
        public async Task<Result> RemoveOfferAsync(string tenantId, string caseId, [FromBody] RemoveOfferFromProposalCommand command, CancellationToken cancellationToken = default) => await _service.RemoveOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/discounts/add")]
        public async Task<Result<string>> AddDiscountToOfferAsync(string tenantId, string caseId, [FromBody] AddDiscountToOfferCommand command, CancellationToken cancellationToken = default) => await _service.AddDiscountToOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/discounts/update")]
        public async Task<Result> UpdateDiscountOfOfferAsync(string tenantId, string caseId, [FromBody] UpdateDiscountOfOfferCommand command, CancellationToken cancellationToken = default) => await _service.UpdateDiscountOfOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/discounts/remove")]
        public async Task<Result> RemoveDiscountFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveDiscountFromOfferCommand command, CancellationToken cancellationToken = default) => await _service.RemoveDiscountFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/loadings/add")]
        public async Task<Result<string>> AddLoadingsToOfferAsync(string tenantId, string caseId, [FromBody] AddLoadingCommand command, CancellationToken cancellationToken = default) => await _service.AddLoadingToOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/loadings/update")]
        public async Task<Result> UpdateLoadingsOfOfferAsync(string tenantId, string caseId, [FromBody] UpdateLoadingCommand command, CancellationToken cancellationToken = default) => await _service.UpdateLoadingOfOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/loadings/remove")]
        public async Task<Result> RemoveLoadingsFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveLoadingCommand command, CancellationToken cancellationToken = default) => await _service.RemoveLoadingFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/exclusions/add")]
        public async Task<Result<CreatedStatus>> AddExclusionToOfferAsync(string tenantId, string caseId, [FromBody] AddExclusionCommand command, CancellationToken cancellationToken = default) => await _service.AddExclusionToOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/exclusions/remove")]
        public async Task<Result> RemoveExclusionFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveExclusionCommand command, CancellationToken cancellationToken = default) => await _service.RemoveExclusionFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/clauses/add")]
        public async Task<Result<string>> AddClauseToOfferAsync(string tenantId, string caseId, [FromBody] AddClauseCommand command, CancellationToken cancellationToken = default) => await _service.AddClauseToOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/clauses/update")]
        public async Task<Result> UpdateClauseToOfferAsync(string tenantId, string caseId, [FromBody] UpdateClauseCommand command, CancellationToken cancellationToken = default) => await _service.UpdateClauseOfOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/clauses/remove")]
        public async Task<Result> RemoveClauseFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveClauseCommand command, CancellationToken cancellationToken = default) => await _service.RemoveClauseFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/clauses/batch")]
        public async Task<Result> ClauseBatchAsync(string tenantId, string caseId, [FromBody] ClauseCommandBatch commandBatch, CancellationToken cancellationToken = default) => await _service.OfferClauseBatchAsync(tenantId, caseId, commandBatch, cancellationToken);
        [HttpPost("offers/jackets/batch")]
        public async Task<Result> JacketBatchAsync(string tenantId, string caseId, [FromBody] JacketCommandBatch commandBatch, CancellationToken cancellationToken = default) => await _service.OfferJacketBatchAsync(tenantId, caseId, commandBatch, cancellationToken);
        [HttpPost("offers/jackets/remove")]
        public async Task<Result> RemoveJacketFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveJacketInstanceCommand command, CancellationToken cancellationToken = default) => await _service.RemoveJacketFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/facts/batch")]
        public async Task<Result> FactBatchAsync(string tenantId, string caseId, [FromBody] FactCommandBatch batch, CancellationToken cancellationToken = default) => await _service.OfferFactBatchAsync(tenantId, caseId, batch, cancellationToken);
        [HttpPost("offers/facts/add")]
        public async Task<Result<string>> AddFactToOfferAsync(string tenantId, string caseId, [FromBody] AddFactCommand command, CancellationToken cancellationToken = default) => await _service.AddFactToOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/facts/update")]
        public async Task<Result> UpdateFactToOfferAsync(string tenantId, string caseId, [FromBody] UpdateFactCommand command, CancellationToken cancellationToken = default) => await _service.UpdateFactOfOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/facts/remove")]
        public async Task<Result> RemoveFactFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveFactCommand command, CancellationToken cancellationToken = default) => await _service.RemoveFactFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/benefitOptions/upsert")]
        public async Task<Result> UpsertBenefitOptionsOfOfferAsync(string tenantId, string caseId, [FromBody] UpsertBenefitOptionCommand command, CancellationToken cancellationToken = default) => await _service.UpsertBenefitOptionOfOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/benefitOptions/remove")]
        public async Task<Result> RemoveBenefitOptionFromOfferAsync(string tenantId, string caseId, [FromBody] RemoveBenefitOptionCommand command, CancellationToken cancellationToken = default) => await _service.RemoveBenefitOptionFromOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/stakeholders/add")]
        public async Task<Result<string>> AddStakeholderOfProposalOfferAsync(string tenantId, string caseId, [FromBody] AddStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.AddStakeholderToProposalOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/stakeholders/update")]
        public async Task<Result> UpdateStakeholderOfProposalOfferAsync(string tenantId, string caseId, [FromBody] UpdateStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.UpdateStakeholderOfProposalOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/stakeholders/remove")]
        public async Task<Result> RemoveStakeholderFromProposalOfferAsync(string tenantId, string caseId, [FromBody] RemoveStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.RemoveStakeHolderFromProposalOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/associatedContracts/add")]
        public async Task<Result> AddAssociatedContractToProposalOfferAsync(string tenantId, string caseId, [FromBody] AddAssociatedContractCommand command, CancellationToken cancellationToken = default) => await _service.AddAssociatedContractToProposalOfferAsync(tenantId, caseId, command, cancellationToken);
        [HttpPost("offers/associatedContracts/remove")]
        public async Task<Result> RemoveAssociatedContractFromProposalOfferAsync(string tenantId, string caseId, [FromBody] RemoveAssociatedContractCommand command, CancellationToken cancellationToken = default) => await _service.RemoveAssociatedContractFromProposalOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/commissions/add")]
        public async Task<Result> AddCommission(string tenantId, string caseId, [FromBody] AddCommissionCommand command, CancellationToken cancellationToken = default) => await _service.AddCommissionToOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/commissions/update")]
        public async Task<Result> UpdateCommission(string tenantId, string caseId, [FromBody] UpdateCommissionCommand command, CancellationToken cancellationToken = default) => await _service.UpdateCommissionOfOfferAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("offers/commissions/remove")]
        public async Task<Result> RemoveCommission(string tenantId, string caseId, [FromBody] RemoveCommissionCommand command, CancellationToken cancellationToken = default) => await _service.RemoveCommissionFromOfferAsync(tenantId, caseId, command, cancellationToken);
    }
}
