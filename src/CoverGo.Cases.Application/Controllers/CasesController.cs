using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Attributes;
using CoverGo.Cases.Application.Filters;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.OtherServices;
using CoverGo.DomainUtils;
using CoverGo.Users.Client;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Cases.Application.Controllers
{
    [ApiController]
    [Route("{tenantId}/api/v1/cases")]
    public class CaseController : ControllerBase
    {
        private readonly ICaseService _service;
        private readonly IUsersService _usersService;

        public CaseController(ICaseServiceFactory caseServiceFactory, IUsersService usersService)
        {
            _service = caseServiceFactory.Build();
            _usersService = usersService;
        }

        [HttpPost("replay")]
        public async Task<ActionResult> ReplayCases(string tenantId, [FromBody] ReplayCommand command, CancellationToken cancellationToken = default)
        {
            if (command.Everything)
                await _service.ReplayAllAsync(tenantId, cancellationToken);
            else
                await _service.ReplayAsync(tenantId, command.CaseIds, cancellationToken);

            return Ok();
        }

        public class ReplayCommand
        {
            public bool Everything { get; set; }
            public List<string> CaseIds { get; set; }
        }

        [HttpPost("queryProposalIds")]
        [AuthorizeRestrictedIndividual("queryArguments")]
        [RestrictDefaultAccessPolicyFilter("queryArguments", typeof(QueryArguments<CaseWhere>))]
        public async Task<IEnumerable<string>> GetProposalIds(string tenantId, [FromBody] QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken = default)
            => await _service.GetProposalIdsAsync(tenantId, AddChannelIdFilterIfClaimExisted(queryArguments), cancellationToken);

        [HttpPost("queryOfferIds")]
        [AuthorizeRestrictedIndividual("queryArguments")]
        [RestrictDefaultAccessPolicyFilter("queryArguments", typeof(QueryArguments<CaseWhere>))]
        public async Task<IEnumerable<string>> GetOfferIds(string tenantId, [FromBody] QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken = default)
            => await _service.GetOfferIdsAsync(tenantId, AddChannelIdFilterIfClaimExisted(queryArguments), cancellationToken);

        [HttpPost("queryids")]
        [AuthorizeRestrictedIndividual("queryArguments")]
        [RestrictDefaultAccessPolicyFilter("queryArguments", typeof(QueryArguments<CaseWhere>))]
        public async Task<IEnumerable<string>> GetIdsAsync(string tenantId, [FromBody] QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken = default)
            => await _service.GetIdsAsync(tenantId, AddChannelIdFilterIfClaimExisted(queryArguments), cancellationToken);

        [HttpPost("query")]
        [AuthorizeRestrictedIndividual("queryArguments")]
        [RestrictDefaultAccessPolicyFilter("queryArguments", typeof(QueryArguments<CaseWhere>))]
        public async Task<IEnumerable<Case>> GetAsync(string tenantId, [FromBody] QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken = default)
        {
            var @cases = await _service.GetAsync(tenantId, AddChannelIdFilterIfClaimExisted(queryArguments), cancellationToken);

            var userClaims = HttpContext.User?.Claims;

            var channelIds = userClaims?.Where(c => c.Type == "accessChannels").Select(x => x.Value);

            if (channelIds?.Any() != true)
                return @cases;

            return @cases.Select(c => channelIds.Contains(c.ChannelId) ? c : new Case
            {
                Id = c.Id,
                CaseNumber = c.CaseNumber,
                Name = c.Name,
                Description = c.Description,
                HolderId = c.HolderId,
                OtherHolderIds = c.OtherHolderIds,
                InsuredIds = c.InsuredIds,
                Status = c.Status,
                Proposals = c.Proposals?.Select(p => new Proposal {
                    Id = p.Id,
                    CaseId = p.CaseId,
                    Name = p.Name,
                    Description = p.Description,
                    Status = p.Status,
                    ProposalNumber = p.ProposalNumber,
                    CreatedAt = p.CreatedAt,
                    LastModifiedAt = p.LastModifiedAt,
                    CreatedById = p.CreatedById,
                    LastModifiedById = p.LastModifiedById
                }),
                ChannelId = c.ChannelId,
                CreatedAt = c.CreatedAt,
                LastModifiedAt = c.LastModifiedAt,
                CreatedById = c.CreatedById,
                LastModifiedById = c.LastModifiedById
            });
        }

        [HttpPost("queryReport")]
        [AuthorizeRestrictedIndividual("queryArguments")]
        [RestrictDefaultAccessPolicyFilter("queryArguments", typeof(QueryArguments<CaseWhere>))]
        public async Task<IEnumerable<CasesReport>> GetReportAsync(string tenantId, [FromBody] QueryArguments<CaseWhere> queryArguments, CancellationToken cancellationToken = default)
            => await _service.GetReportAsync(tenantId, AddChannelIdFilterIfClaimExisted(queryArguments), cancellationToken);

        [HttpPost("events")]
        public async Task<IEnumerable<EventLog>> GetEvents(string tenantId, [FromBody] EventQuery query, CancellationToken cancellationToken = default) => await _service.GetEventLogsAsync(tenantId, query, cancellationToken);

        [HttpPost("totalCount")]
        [AuthorizeRestrictedIndividual("where")]
        [RestrictDefaultAccessPolicyFilter("where", typeof(CaseWhere))]
        public async Task<long> GetTotalCountAsync(string tenantId, [FromBody] CaseWhere where, CancellationToken cancellationToken = default)
            => await _service.GetTotalCountAsync(tenantId, where, cancellationToken);

        [HttpPost("create")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result<string>> CreateAsync(string tenantId, [FromBody] CreateCaseCommand command, CancellationToken cancellationToken = default)
        {
            var ownerCompany = await _usersService.GetCompanyByIdAsync(tenantId, command.HolderId, cancellationToken);
            command.AccessPolicy = ownerCompany?.AccessPolicy switch
            {
                AccessPolicy.Restricted => BuildingBlocks.Auth.Common.AccessPolicy.Restricted,
                AccessPolicy.Standard => BuildingBlocks.Auth.Common.AccessPolicy.Standard,
                null => null,
                _ => throw new ArgumentException(nameof(command.AccessPolicy))
            };
            return await _service.CreateAsync(tenantId, command, cancellationToken);
        }

        [HttpPost("{caseId}/update")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result> UpdateAsync(string tenantId, string caseId, [FromBody] UpdateCaseCommand command, CancellationToken cancellationToken = default) => await _service.UpdateAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/delete")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result> DeleteAsync(string tenantId, string caseId, [FromBody] DeleteCaseCommand command, CancellationToken cancellationToken = default) => await _service.DeleteAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/generatePolicies")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result<CreatedStatus>> GeneratePoliciesAsync(string tenantId, string caseId, [FromBody] GeneratePoliciesFromProposalCommand command, string accessToken, CancellationToken cancellationToken = default) =>
            await _service.GeneratePoliciesAsync(tenantId, caseId, command, accessToken, cancellationToken);

        [HttpPost("{caseId}/issuedPolicies/add")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public Task<Result> AddIssuedPolicyAsync(string tenantId, string caseId, [FromBody] AddIssuedPolicyCommand command, CancellationToken cancellationToken) => _service.AddIssuedPolicyAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/stakeholders/add")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result<string>> AddStakeholderOfCaseAsync(string tenantId, string caseId, [FromBody] AddStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.AddStakeholderToCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/stakeholders/update")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result> UpdateStakeholderOfCaseAsync(string tenantId, string caseId, [FromBody] UpdateStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.UpdateStakeholderOfCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/stakeholders/remove")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result> RemoveStakeholderFromCaseAsync(string tenantId, string caseId, [FromBody] RemoveStakeholderCommand command, CancellationToken cancellationToken = default) => await _service.RemoveStakeHolderFromCaseAsync(tenantId, caseId, command, cancellationToken);


        [HttpPost("{caseId}/beneficiaryEligibilities/add")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result<CreatedStatus>> AddBeneficiaryEligibilityToCaseAsync(string tenantId, string caseId, AddBeneficiaryEligibilityCommand command, CancellationToken cancellationToken = default) =>
           await _service.AddBeneficiaryEligibilityToCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/beneficiaryEligibilities/update")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result> UpdateBeneficiaryEligibilityOfCaseAsync(string tenantId, string caseId, UpdateBeneficiaryEligibilityCommand command, CancellationToken cancellationToken = default) =>
            await _service.UpdateBeneficiaryEligibilityOfCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/beneficiaryEligibilities/remove")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<ActionResult<Result>> RemoveBeneficiaryEligibilityFromCaseAsync(string tenantId, string caseId, RemoveCommand command, CancellationToken cancellationToken = default) =>
            await _service.RemoveBeneficiaryEligibilityFromCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/paymentInfos/add")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result<CreatedStatus>> AddPaymentInfoToCaseAsync(string tenantId, string caseId, AddPaymentInfoCommand command, CancellationToken cancellationToken = default) =>
         await _service.AddPaymentInfoToCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/paymentInfos/update")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<Result> UpdatePaymentInfoOfCaseAsync(string tenantId, string caseId, UpdatePaymentInfoCommand command, CancellationToken cancellationToken = default) =>
            await _service.UpdatePaymentInfoOfCaseAsync(tenantId, caseId, command, cancellationToken);

        [HttpPost("{caseId}/paymentInfos/remove")]
        [ServiceFilter(typeof(ChannelIdFilterAttribute))]
        public async Task<ActionResult<Result>> RemovePaymentInfoFromCaseAsync(string tenantId, string caseId, RemoveCommand command, CancellationToken cancellationToken = default) =>
            await _service.RemovePaymentInfoFromCaseAsync(tenantId, caseId, command, cancellationToken);

        private QueryArguments<CaseWhere> AddChannelIdFilterIfClaimExisted(QueryArguments<CaseWhere> queryArguments)
        {
            var userClaims = HttpContext.User?.Claims;

            var channelIds = userClaims?.Where(c => c.Type == "accessChannels").Select(x => x.Value);

            if (channelIds?.Any() != true)
                return queryArguments;

            return new QueryArguments<CaseWhere>
            {
                AsOf = queryArguments.AsOf,
                First = queryArguments.First,
                GroupBy = queryArguments.GroupBy,
                IncludeEvents = queryArguments.IncludeEvents,
                OrderBy = queryArguments.OrderBy,
                OrderBy2 = queryArguments.OrderBy2,
                Skip = queryArguments.Skip,
                Where = new CaseWhere
                {
                    And = new List<CaseWhere>
                    {
                        new CaseWhere
                        {
                            Or = channelIds.Select(c => new CaseWhere
                            {
                                ChannelId = c
                            }).ToList()
                        },
                        queryArguments.Where
                    }
                }
            };
        }
    }
}

