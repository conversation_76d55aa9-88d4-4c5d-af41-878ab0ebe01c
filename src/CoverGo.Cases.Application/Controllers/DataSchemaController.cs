﻿using CoverGo.Cases.Domain.DataSchemas;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.Controllers
{
    [Route("{tenantId}/api/v1/dataschemas")]
    [ApiController]
    public class DataSchemasController : ControllerBase
    {
        readonly DataSchemaService _service;

        public DataSchemasController(DataSchemaService service) =>
            _service = service;

        [HttpPost("query")]
        public async Task<ActionResult<IEnumerable<DataSchema>>> GetAllAsync(string tenantId, [FromBody] DataSchemaWhere where, CancellationToken cancellationToken = default)
        {
            IEnumerable<DataSchema> scripts = await _service.GetAsync(tenantId, where, cancellationToken);
            return Ok(scripts);
        }

        [HttpPost]
        public async Task<Result<CreatedStatus>> CreateAsync(string tenantId, [FromBody] CreateDataSchemaCommand command, CancellationToken cancellationToken = default) =>
            await _service.CreateAsync(tenantId, command, cancellationToken);

        [HttpPut]
        public async Task<ActionResult<Result>> UpdateAsync(string tenantId, [FromBody] UpdateDataSchemaCommand command, CancellationToken cancellationToken = default) =>
            await _service.UpdateAsync(tenantId, command, cancellationToken);

        [HttpDelete("{dataSchemaId}")]
        public async Task<ActionResult<Result>> DeleteAsync(string tenantId, string dataSchemaId, [FromBody] DeleteCommand command, CancellationToken cancellationToken = default) =>
            await _service.DeleteAsync(tenantId, dataSchemaId, command, cancellationToken);


        [HttpPost("uiSchemas")]
        public Task<Result> AddUiSchemaToDataSchemaAsync(string tenantId, [FromBody] AddUiSchemaToDataSchemaCommand command, CancellationToken cancellationToken = default) =>
            _service.AddUiSchemaToDataSchema(tenantId, command, cancellationToken);

        [HttpDelete("uiSchemas")]
        public Task<Result> RemoveUiSchemaFromDataSchemaAsync(string tenantId, [FromBody] RemoveUiSchemaFromDataSchemaCommand command, CancellationToken cancellationToken = default) =>
            _service.RemoveUiSchemaFromDataSchema(tenantId, command, cancellationToken);
    }
}