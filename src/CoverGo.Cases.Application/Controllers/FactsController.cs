﻿using System.Threading;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using Microsoft.AspNetCore.Mvc;

using System.Threading.Tasks;
using CoverGo.Cases.Application.Filters;

namespace CoverGo.Cases.Application.Controllers
{
    [ApiController]
    [ServiceFilter(typeof(ChannelIdFilterAttribute))]
    [Route("{tenantId}/api/v1/cases/{caseId}/facts")]
    public class FactsController
    {
        private readonly ICaseService _caseService;

        public FactsController(ICaseServiceFactory caseServiceFactory)
        {
            _caseService = caseServiceFactory.Build();
        }

        [HttpPost("add")]
        public async Task<Result<CreatedStatus>> AddFactToCase(string tenantId, string caseId, [FromBody] AddFactCommand command, CancellationToken cancellationToken = default)
        {
            Result<CreatedStatus> result = await _caseService.AddFactToCaseAsync(tenantId, caseId, command, cancellationToken);
            return result;
        }

        [HttpPost("update")]
        public async Task<Result> UpdateFactOfCase(string tenantId, string caseId, [FromBody] UpdateFactCommand command, CancellationToken cancellationToken = default)
        {
            Result result = await _caseService.UpdateFactOfCaseAsync(tenantId, caseId, command, cancellationToken);
            return result;
        }

        [HttpPost("remove")]
        public async Task<Result> RemoveFactFromCase(string tenantId, string caseId, [FromBody] RemoveFactCommand command, CancellationToken cancellationToken = default)
        {
            Result result = await _caseService.RemoveFactFromCaseAsync(tenantId, caseId, command, cancellationToken);
            return result;
        }

        [HttpPost("batch")]
        public async Task<Result> FactBatchAsync(string tenantId, string caseId, [FromBody] FactCommandBatch batch, CancellationToken cancellationToken = default) => await _caseService.CaseFactBatchAsync(tenantId, caseId, batch, cancellationToken);
    }
}
