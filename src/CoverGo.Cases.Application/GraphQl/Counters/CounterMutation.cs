﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain.Counters;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.GraphQl
{
    [ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
    public class CounterMutation
    {
        private readonly ICounterService _service;

        public CounterMutation([Service] ICounterService service)
        {
            _service = service;
        }

        public async Task<Result<long>> IncreaseAsync(
            [GlobalState] string tenantId,
            [GlobalState] string loginId,
            IncreaseCounterCommand command,
            CancellationToken cancellation = default)
        {
            var update = new CounterUpsert
            {
                ById = loginId,
                CounterKey = command.CounterKey,
                Scope = command.Scope,
            };

            var counter = await _service.GetNextAsync(tenantId, update, cancellation);
            return Result<long>.Success(counter);
        }
    }
}