using System.Threading;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain.Counters;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.Counters;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class Counters : CoverGoGraphQlQueryBase<Counter, CounterUpsert, CounterFilter, ICounterRepository>
{
    public override CoverGoGraphQlQueryInterface<
        Counter,
        CounterUpsert,
        CounterUpsert,
        CounterUpsert,
        EntityBatch<
            CounterUpsert, CounterUpsert, CounterUpsert
        >,
        QueryArguments<
            Filter<CounterFilter>
        >,
        CounterFilter,
        ICounterRepository>
        Query(
            [GlobalState] string tenantId,
            QueryArguments<Filter<CounterFilter>> where,
            CancellationToken cancellation = default)
    {
        return base.Query(tenantId, where, cancellation);
    }
}

public class CounterType : ObjectType<Counter>
{
}
