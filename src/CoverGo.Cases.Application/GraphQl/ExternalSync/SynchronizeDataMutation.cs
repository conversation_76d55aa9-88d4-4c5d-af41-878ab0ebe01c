using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;
using Microsoft.Extensions.Logging;

namespace CoverGo.Cases.Application.GraphQl.ExternalSync
{
    [ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
    public class SynchronizeDataMutation
    {
        private readonly IEnumerable<ISynchronizeDataService> _synchronizeDataServices;
        private readonly IExternalSynchronizationService _externalSynchronizationService;
        private readonly ILogger<SynchronizeDataMutation> _logger;

        public SynchronizeDataMutation(
            IEnumerable<ISynchronizeDataService> synchronizeDataServices,
            IExternalSynchronizationService externalSynchronizationService,
            ILogger<SynchronizeDataMutation> logger)
        {
            _synchronizeDataServices = synchronizeDataServices;
            _externalSynchronizationService = externalSynchronizationService;
            _logger = logger;
        }

        /// <summary>
        /// Synchronizes case and offer data with external system
        /// </summary>
        /// <param name="tenantId">Tenant identifier</param>
        /// <param name="input">Synchronization input</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result containing synchronization status</returns>
        public async Task<Result<SynchronizeDataPayload>> SynchronizeData(
            [GlobalState] string tenantId,
            SynchronizeDataInput input,
            CancellationToken cancellationToken)
        {
            try
            {
                // Input validation
                if (string.IsNullOrEmpty(input.CaseId))
                    return Result<SynchronizeDataPayload>.Failure("CaseId is required");

                if (string.IsNullOrEmpty(input.OfferId))
                    return Result<SynchronizeDataPayload>.Failure("OfferId is required");

                if (string.IsNullOrEmpty(input.ExternalSynchronizationTargetLogicalId))
                    return Result<SynchronizeDataPayload>.Failure("ExternalSynchronizationTargetLogicalId is required");

                _logger.LogInformation("[SynchronizeDataMutation] Starting synchronization for case {CaseId}, offer {OfferId}, target {TargetLogicalId}",
                    input.CaseId, input.OfferId, input.ExternalSynchronizationTargetLogicalId);

                // Check if data has already been synchronized
                var existingFilter = new QueryArguments<Filter<ExternalSynchronizationFilter>>
                {
                    Where = new Filter<ExternalSynchronizationFilter>
                    {
                        Where = new ExternalSynchronizationFilter
                        {
                            CaseId = input.CaseId,
                            OfferId = input.OfferId,
                            TargetLogicalId = input.ExternalSynchronizationTargetLogicalId,
                            Status = "Sent"
                        }
                    }
                };

                var existingSyncs = await _externalSynchronizationService.QueryAsync(tenantId, existingFilter, cancellationToken);

                if (existingSyncs.Any())
                {
                    _logger.LogWarning("[SynchronizeDataMutation] Data already synchronized for case {CaseId}, offer {OfferId}, target {TargetLogicalId}",
                        input.CaseId, input.OfferId, input.ExternalSynchronizationTargetLogicalId);
                    return Result<SynchronizeDataPayload>.Failure("Data has already been synchronized for this case, offer, and target system");
                }

                // Find the appropriate synchronization service
                var syncService = _synchronizeDataServices
                    .FirstOrDefault(s => s.LogicalId == input.ExternalSynchronizationTargetLogicalId);

                if (syncService == null)
                {
                    return Result<SynchronizeDataPayload>.Failure($"No synchronization service found for target logical ID: {input.ExternalSynchronizationTargetLogicalId}");
                }

                // Create the synchronization request
                var syncRequest = new SynchronizeDataRequest
                {
                    TenantId = tenantId,
                    CaseId = input.CaseId,
                    OfferId = input.OfferId,
                    TargetLogicalId = input.ExternalSynchronizationTargetLogicalId,
                    CancellationToken = cancellationToken
                };

                // Execute the synchronization
                var synchronizeResult = await syncService.ProcessAsync(syncRequest);

                if (!synchronizeResult.IsSuccess)
                {
                    return Result<SynchronizeDataPayload>.Failure($"Synchronization failed: {string.Join(", ", synchronizeResult.Errors)}");
                }

                _logger.LogInformation("[SynchronizeDataMutation] Synchronization completed successfully for case {CaseId}, offer {OfferId}",
                    input.CaseId, input.OfferId);

                return Result<SynchronizeDataPayload>.Success(new SynchronizeDataPayload
                {
                    ExternalSynchronization = synchronizeResult.Value,
                    Message = "Data synchronized successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SynchronizeDataMutation] Unexpected error during synchronization for case {CaseId}, offer {OfferId}",
                    input?.CaseId, input?.OfferId);
                return Result<SynchronizeDataPayload>.Failure($"Unexpected error: {ex.Message}");
            }
        }

        // All data retrieval, transformation, and synchronization logic has been moved to service layer
    }

    public class SynchronizeDataInput
    {
        public string CaseId { get; set; }
        public string OfferId { get; set; }
        public string ExternalSynchronizationTargetLogicalId { get; set; }
    }

    public class SynchronizeDataPayload
    {
        public ExternalSynchronization ExternalSynchronization { get; set; }
        public string Message { get; set; }
    }
}