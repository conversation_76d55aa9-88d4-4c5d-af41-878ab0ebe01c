using System.Security.Claims;
using System.Threading;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Cases.Application.GraphQl.ExternalSync
{
    [ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
    public class ExternalSynchronizations : CoverGoGraphQlQueryBase<ExternalSynchronization, ExternalSynchronizationUpsert, ExternalSynchronizationFilter, IExternalSynchronizationRepository>
    {
        private readonly PermissionValidator _permissionValidator;

        public ExternalSynchronizations(PermissionValidator permissionValidator)
        {
            _permissionValidator = permissionValidator;
        }

        public CoverGoGraphQlQueryInterface<
            ExternalSynchronization,
            ExternalSynchronizationUpsert,
            ExternalSynchronizationUpsert,
            ExternalSynchronizationUpsert,
            EntityBatch<
                ExternalSynchronizationUpsert, ExternalSynchronizationUpsert, ExternalSynchronizationUpsert
            >,
            QueryArguments<
                Filter<ExternalSynchronizationFilter>
            >,
            ExternalSynchronizationFilter,
            IExternalSynchronizationRepository>
            Query(
                [GlobalState] string tenantId,
                [GlobalState] ClaimsIdentity identity,
                QueryArguments<Filter<ExternalSynchronizationFilter>> where,
                CancellationToken cancellation = default)
        {
            _permissionValidator.AuthorizeWithAsync(identity, "readExternalSynchronization");
            return base.Query(tenantId, where, cancellation);
        }
    }

    public class ExternalSynchronizationType : ObjectType<ExternalSynchronization>
    {
        protected override void Configure(IObjectTypeDescriptor<ExternalSynchronization> descriptor)
        {
            descriptor.Field(x => x.Payload).Resolve(x => x.Parent<ExternalSynchronization>().Payload?.ToString());
            descriptor.Field(x => x.Response).Resolve(x => x.Parent<ExternalSynchronization>().Response?.ToString());
        }
    }
}