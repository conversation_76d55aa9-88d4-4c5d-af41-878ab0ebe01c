﻿namespace CoverGo.Cases.Application.GraphQl.Admin;

public record class AddQuotationInput(string CaseId, QuotationProposalInput Proposal, QuotationOfferInput Offer);
public record class QuotationProposalInput(string ProposalNumber);
public record class QuotationOfferInput(string? Pricing, string? Fields, string? Status, ProductIdInput ProductId);
public record class ProductIdInput(string Plan, string Type, string? Version);