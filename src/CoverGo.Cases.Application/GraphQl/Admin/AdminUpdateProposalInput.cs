﻿using CoverGo.Cases.Domain;
using CoverGo.SettableValues;
using System;

namespace CoverGo.Cases.Application.GraphQl.Admin
{
    public class AdminUpdateProposalInput
    {
        public Settable<string> Name { get; set; }
        public Settable<DateTime?> ExpiryDate { get; set; }
        public Settable<AdminUpdatePremiumInput> TotalPrice { get; set; }
        public Settable<RenewalHistory> RenewalHistory { get; set; }
        public Settable<string> ReferralCode { get; set; }
    }
}