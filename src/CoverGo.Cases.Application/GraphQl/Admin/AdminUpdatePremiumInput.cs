﻿using CoverGo.Cases.Domain;
using CoverGo.SettableValues;
using System.Collections.Generic;

namespace CoverGo.Cases.Application.GraphQl.Admin
{
    public class AdminUpdatePremiumInput
    {
        public Settable<decimal?> Amount { get; set; }
        public Settable<decimal?> GrossAmount { get; set; }
        public Settable<CurrencyCode?> CurrencyCode { get; set; }
        public Settable<IEnumerable<string>> DiscountCodes { get; set; }
        public Settable<bool> IsPricedAtStartDate { get; set; }
    }
}
