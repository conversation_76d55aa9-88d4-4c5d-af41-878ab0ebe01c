﻿using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System;
using CoverGo.Cases.Domain;

namespace CoverGo.Cases.Application.GraphQl.Admin
{
    public class AdminOfferInput
    {
        public ProductId ProductId { get; set; }
        public PremiumInput Premium { get; set; }
        public bool? IsPremiumOverridden { get; set; } = false;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? Timestamp { get; set; } = DateTime.UtcNow;

        public string Pricing { get; set; }
        public string Underwriting { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }
        public string ProductTreeId { get; set; }
        public List<ProductTreeRecord> ProductTreeRecords { get; set; }
    }
}
