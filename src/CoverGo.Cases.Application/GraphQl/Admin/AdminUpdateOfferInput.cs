﻿using System.Collections.Generic;
using System;
using CoverGo.Cases.Domain;
using CoverGo.SettableValues;

namespace CoverGo.Cases.Application.GraphQl.Admin
{
    public class AdminUpdateOfferInput
    {
        public Settable<string> Status { get; set; }
        public Settable<AdminProductIdInput> ProductId { get; set; }
        public Settable<AdminUpdatePremiumInput> Premium { get; set; }
        public Settable<bool?> IsPremiumOverridden { get; set; }
        public Settable<DateTime?> StartDate { get; set; }
        public Settable<DateTime?> EndDate { get; set; }
        public Settable<DateTime?> Timestamp { get; set; }

        public Settable<string> Pricing { get; set; }
        public Settable<string> Underwriting { get; set; }
        public Settable<string> Fields { get; set; }
        public Settable<string> FieldsSchemaId { get; set; }
        public Settable<string> ProductTreeId { get; set; }
    }
}