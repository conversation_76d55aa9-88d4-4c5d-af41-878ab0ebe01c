﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Products.Client;
using CoverGo.Proxies.Auth;
using HotChocolate;
using HotChocolate.Types;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using static CoverGo.Cases.Application.GraphQl.PermissionGroupCheckExtensions;
using IAuthService = CoverGo.Proxies.Auth.IAuthService;
using Result = CoverGo.DomainUtils.Result;

namespace CoverGo.Cases.Application.GraphQl.Admin;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class SalesAdminMutation
{
    private readonly ICaseService _service;
    private readonly IAuthService _authService;
    private const string AdminStakeholderType = "System Admin";
    public SalesAdminMutation([Service] ICaseServiceFactory caseServiceFactory, IAuthService authService)
    {
        _service = caseServiceFactory.Build();
        _authService = authService;
    }

    /// <summary>
    /// Assigns self to case as admin if user is admin, allows handler to assign admin.
    /// </summary>  
    public async Task<AssignAdminToCasePayload> AssignAdminToCase(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        AssignAdminToCaseInput input,
        CancellationToken cancellationToken)
    {
        string entityIdFromLoginId = null;
        if (input.adminEntityId != null)
        {
            Result result = await ValidateAgentIsStakeholder(_service, tenantId, identity, input.caseId, cancellationToken);
            if (result.Errors_2?.FirstOrDefault() != null)
                return new AssignAdminToCasePayload(Result<string>.Failure(result.Errors_2.FirstOrDefault()));
        }
        else
        {
            bool containsAdminPermissionGroup = await ContainsPermissionGroup(_authService, tenantId, identity, AdminStakeholderType, cancellationToken);
            if (!containsAdminPermissionGroup)
                return new AssignAdminToCasePayload(Result<string>.Failure(new DomainUtils.Error { Code = "NOT_AN_ADMIN", Message = "User is not an admin and cannot be assigned to the case." }));
            entityIdFromLoginId = await GetEntityIdFromLoginId(tenantId, loginId);
            if (entityIdFromLoginId == null)
                return new AssignAdminToCasePayload(Result<string>.Failure(new DomainUtils.Error { Code = "NO_ASSOCIATED_ENTITY", Message = "No associated entity found." }));
        }

        var addStakeholderCommand = new AddStakeholderCommand()
        {
            Id = Guid.NewGuid().ToString(),
            Type = AdminStakeholderType,
            EntityId = input.adminEntityId ?? entityIdFromLoginId,
            AddedById = loginId
        };

        return new AssignAdminToCasePayload(await _service.AddStakeholderToCaseAsync(tenantId, input.caseId, addStakeholderCommand, cancellationToken));
    }

    private async Task<string> GetEntityIdFromLoginId(string tenantId, string loginId)
    {
        Login login = (await _authService.GetLoginsAsync(tenantId, new LoginWhere { Ids = new List<string> { loginId }, ExcludePermissions = true })).FirstOrDefault();
        return login?.EntityId;
    }
}
