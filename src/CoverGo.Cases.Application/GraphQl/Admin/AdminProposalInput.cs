﻿using CoverGo.Cases.Domain;
using System;

namespace CoverGo.Cases.Application.GraphQl.Admin
{
    public class AdminProposalInput
    {
        public string Name { get; set; }
        public string ProposalNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public PremiumInput TotalPrice { get; set; }
        public RenewalHistory RenewalHistory { get; set; }
        public string ReferralCode { get; set; }
    }
}
