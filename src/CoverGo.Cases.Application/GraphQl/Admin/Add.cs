﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain;
using HotChocolate;
using HotChocolate.Types;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using static CoverGo.Cases.Application.GraphQl.PermissionGroupCheckExtensions;
using IAuthService = CoverGo.Proxies.Auth.IAuthService;
using Result = CoverGo.DomainUtils.Result;

namespace CoverGo.Cases.Application.GraphQl.Admin;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class Add
{
    private readonly ICaseService _service;
    private readonly IAuthService _authService;
    public Add([Service] ICaseServiceFactory caseServiceFactory, IAuthService authService)
    {
        _service = caseServiceFactory.Build();
        _authService = authService;
    }

    public async Task<AddQuotationPayload> Quotation(
       [GlobalState] string tenantId,
       [GlobalState] string loginId,
       [GlobalState] ClaimsIdentity identity,
       AddQuotationInput input,
       CancellationToken cancellationToken)
    {
        Result isStakeholderResult = await ValidateAgentIsStakeholder(_service, tenantId, identity, input.CaseId, cancellationToken);

        if (!isStakeholderResult.IsSuccess)
            return new AddQuotationPayload
            {
                Errors = isStakeholderResult.Errors,
                Errors_2 = isStakeholderResult.Errors_2,
            };


        var addProposalResult = await _service.AddProposalAsync(tenantId, input.CaseId, new AddProposalCommand()
        {
            ProposalNumber = input.Proposal?.ProposalNumber,
            AddedById = loginId
        });

        if (!addProposalResult.IsSuccess)
            return new AddQuotationPayload()
            {
                Errors = addProposalResult.Errors,
                Errors_2 = addProposalResult.Errors_2,
            };

        var addOfferResult = await _service
            .AddOfferAsync(tenantId, input.CaseId, new AddOfferCommand()
            {

                AddedById = loginId,
                ProductId = new Domain.ProductId
                {
                    Plan = input.Offer.ProductId.Plan,
                    Type = input.Offer.ProductId.Type,
                    Version = input.Offer.ProductId.Version,
                },
                ProposalId = addProposalResult.Value,
                Fields = input.Offer.Fields,
                Pricing = input.Offer.Pricing,
                Status = input.Offer.Status

            }, cancellationToken: cancellationToken);

        if (!addOfferResult.IsSuccess)
            return new AddQuotationPayload()
            {
                Errors = addOfferResult.Errors,
                Errors_2 = addOfferResult.Errors_2,
            };

        return new AddQuotationPayload
        {
            ProposalId = addProposalResult.Value,
            OfferId = addOfferResult.Value,
            Status = "success"
        };
    }
}
