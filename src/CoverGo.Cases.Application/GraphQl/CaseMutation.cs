using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Cases.Application.GraphQl
{

    [ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
    public class CaseMutation
    {
        private readonly ICaseService _service;

        public CaseMutation(ICaseServiceFactory serviceFactory)
        {
            _service = serviceFactory.Build();
        }

        /// <summary>
        /// Modifies CreatedAt and LastModifiedAt data in the case
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="caseId"></param>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<Result> SetSystemDates([GlobalState] string tenantId, string caseId, AdminSetSystemDatesCommand input, CancellationToken cancellationToken)
        {
            return _service.SetSystemDatesAsync(tenantId, caseId, input, cancellationToken);
        }

        /// <summary>
        /// Sets read only value, when IsReadOnly is true there is no more way to execute any write operation in the case
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="caseId"></param>
        /// <param name="input"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<Result> SetReadOnly([GlobalState] string tenantId, string caseId, AdminSetReadOnlyCommand input, CancellationToken cancellationToken)
        {
            return _service.SetReadOnlyAsync(tenantId, caseId, input, cancellationToken);
        }
    }
}