﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Application.GraphQl.Admin;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.OfferStatusPermissions;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.GraphQl
{
    [ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
    public class OfferMutation
    {
        private readonly ICaseService _service;
        private readonly IOfferStatusTransitionPermissionValidator _offerStatusTransitionPermissionValidator;

        public OfferMutation([Service] ICaseServiceFactory caseServiceFactory, [Service] IOfferStatusTransitionPermissionValidator offerStatusTransitionPermissionValidator)
        {
            _service = caseServiceFactory.Build();
            this._offerStatusTransitionPermissionValidator = offerStatusTransitionPermissionValidator;
        }

        public Task<Result> RecordQuoteAsync(string tenantId, string caseId, RecordQuoteCommand command, CancellationToken cancellationToken) =>
            _service.ProcessCommandToEventSourcing(tenantId, caseId, CaseEventType.recordQuote, command, cancellationToken);

        public async Task<Result> SecureUpdateOfferAsync(
            [GlobalState] string tenantId,
            [GlobalState] string loginId,
            [GlobalState] ClaimsIdentity identity, string caseId, string proposalId, string offerId, AdminUpdateOfferInput adminUpdateOfferInput, CancellationToken cancellationToken)
        {
            UpdateOfferCommand updateOfferCommand = adminUpdateOfferInput.ToUpdateOfferCommand(proposalId, offerId, loginId);
            Case @case = (await _service.GetAsync(tenantId, new QueryArguments<CaseWhere>() { Where = new CaseWhere() { Id = caseId } }, cancellationToken)).Single();
            Offer offer = @case.Proposals.Single((proposal) => proposal.Id == updateOfferCommand.ProposalId).Basket.Single((offer) => offer.Id == updateOfferCommand.OfferId);
            TransitionValidationResult transitionValidationResult = _offerStatusTransitionPermissionValidator.IsValidStatusTransitionForOffer(offer.Status, updateOfferCommand.Status, identity);
            if (!transitionValidationResult.IsValid)
            {
                return Result.Failure(transitionValidationResult.Message);
            }
            return await _service.UpdateOfferAsync(tenantId, caseId, updateOfferCommand, cancellationToken);
        }
    }
}
