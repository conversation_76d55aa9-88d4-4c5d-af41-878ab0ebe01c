﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.HandlerAssignment;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.GraphQl.HandlerAssignment;

[CoverGoGraphQlIgnoreClassName]
[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class HandlerAssignmentMutation
{
    private readonly ICaseService _caseService;
    private readonly PermissionValidator _permissionValidator;

    public HandlerAssignmentMutation(ICaseService caseService, PermissionValidator permissionValidator)
    {
        _caseService = caseService;
        _permissionValidator = permissionValidator;
    }

    [GraphQLName("cases_AssignHandlers")]
    public async Task<Result<CreatedStatus>> AssignHandlers(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        AssignHandlersCommand input, CancellationToken cancellationToken
        )
    {
        if (!input.PortalUserIds?.Any() ?? true)
            return Result<CreatedStatus>.Failure("No handler login ids provided");

        IEnumerable<IGrouping<string, string>> duplicates = input.PortalUserIds
            .GroupBy(n => n)
            .Where(g => g.Count() > 1);
        if (duplicates.Any())
            return Result<CreatedStatus>.Failure($"Duplicate portalUserIds `{string.Join(',', duplicates.Select(d => d.Key))}` found in input.");

        await _permissionValidator.AuthorizeWithAsync(identity, "writeCases");
        input.AssignedById = loginId;

        return await _caseService.AssignHandlersAsync(tenantId, input, cancellationToken);
    }

    [GraphQLName("cases_UnassignHandlers")]
    public async Task<Result> UnassignHandlers(
       [GlobalState] string tenantId,
       [GlobalState] string loginId,
       [GlobalState] ClaimsIdentity identity,
       UnassignHandlersCommand input, CancellationToken cancellationToken
       )
    {
        if (!input.PortalUserIds?.Any() ?? true)
            return Result.Failure("No handler login ids provided");

        await _permissionValidator.AuthorizeWithAsync(identity, "writeCases");
        input.UnassignedById = loginId;

        return await _caseService.UnassignHandlersAsync(tenantId, input, cancellationToken);
    }
}
