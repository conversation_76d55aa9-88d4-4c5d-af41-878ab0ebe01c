﻿using CoverGo.Cases.Domain.AgentAssignment;
using CoverGo.Cases.Domain.HandlerAssignment;
using HotChocolate.Types;

namespace CoverGo.Cases.Application.GraphQl.AgentAssignment;
public class AssignHandlersCommandInputType : InputObjectType<AssignHandlersCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<AssignHandlersCommand> descriptor)
    {
        descriptor.Name("cases_AssignHandlersInput");
        descriptor.BindFieldsImplicitly();
    }
}

public class UnassignHandlersCommandInputType : InputObjectType<UnassignHandlersCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<UnassignHandlersCommand> descriptor)
    {
        descriptor.Name("cases_UnassignHandlersInput");
        descriptor.BindFieldsImplicitly();
    }
}