using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Proxies.Auth;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.GraphQl;

public static class PermissionGroupCheckExtensions
{
    public const string ProposalCreatorPermGroupName = "proposalCreator", AgentPermGroupName = "agent", ProposalApproverPermGroupName = "proposalApprover";

    public static async Task<Error> ValidateUserIsAgentStakeholderOrAdmin(IAuthService authService, ICaseService caseService, string tenantId, ClaimsIdentity identity, string caseId, CancellationToken cancellationToken)
    {
        if (await ContainsPermissionGroup(authService, tenantId, identity, ProposalCreatorPermGroupName, cancellationToken))
            return null;

        if (await ContainsPermissionGroup(authService, tenantId, identity, AgentPermGroupName, cancellationToken))
        {
            Result result = await ValidateAgentIsStakeholder(caseService, tenantId, identity, caseId, cancellationToken);
            return result.IsSuccess
                ? null
                : result.Errors_2.FirstOrDefault();
        }

        return new Error() { Code = "NOT_AN_AGENT_AND_NOT_A_PROPOSAL_CREATOR", Message = "User is not an agent and not a proposal creator." };
    }

    public static async Task<Result> ValidateAgentIsStakeholder(ICaseService caseService, string tenantId, ClaimsIdentity identity,  string caseId, CancellationToken cancellationToken)
    {
        Case @case = (await caseService.GetAsync(tenantId, new QueryArguments<CaseWhere>() { Where = new CaseWhere() { Id = caseId } }, cancellationToken)).Single();
        return !@case.Stakeholders.Any((stakeholder) => identity.HasClaim("entityId", stakeholder.EntityId))
            ? Result.Failure(new Error() { Code = "AGENT_IS_NOT_STAKEHOLDER", Message = "Agent is not a stakeholder for this case." })
            : Result.Success();
    }

    public static async Task<bool> ContainsPermissionGroup(IAuthService authService, string tenantId, ClaimsIdentity identity, string permissionGroupName, CancellationToken cancellationToken = default)
    {
        IEnumerable<Claim> groupsClaims = identity.Claims.Where(c => c.Type == "groups");
        if (!groupsClaims.Any()) return false;
        IEnumerable<PermissionGroup> permissionGroups = await authService.GetPermissionGroupsAsync(tenantId, new PermissionGroupWhere { Id_in = groupsClaims.Select(c => c.Value) }, cancellationToken) ?? Array.Empty<PermissionGroup>();
        return permissionGroups.Any(g => g.Name == permissionGroupName);
    }
}