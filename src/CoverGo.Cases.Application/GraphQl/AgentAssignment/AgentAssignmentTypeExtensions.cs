﻿using CoverGo.Cases.Domain.AgentAssignment;
using HotChocolate.Types;

namespace CoverGo.Cases.Application.GraphQl.AgentAssignment;

public class AssignAgentCommandInputType : InputObjectType<AssignAgentCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<AssignAgentCommand> descriptor)
    {
        descriptor.Name("cases_AssignAgentInput");
        descriptor.BindFieldsImplicitly();
    }
}

public class UnassignAgentCommandInputType : InputObjectType<UnassignAgentsCommand>
{
    protected override void Configure(IInputObjectTypeDescriptor<UnassignAgentsCommand> descriptor)
    {
        descriptor.Name("cases_UnassignAgentsInput");
        descriptor.BindFieldsImplicitly();
    }
}

