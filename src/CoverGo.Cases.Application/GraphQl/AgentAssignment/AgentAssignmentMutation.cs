﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Domain.AgentAssignment;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.GraphQl.AgentAssignment;

[CoverGoGraphQlIgnoreClassName]
[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class AgentAssignmentMutation
{
    private readonly ICaseService _caseService;
    private readonly PermissionValidator _permissionValidator;

    public AgentAssignmentMutation(ICaseService caseService, PermissionValidator permissionValidator)
    {
        _caseService = caseService;
        _permissionValidator = permissionValidator;
    }

    [GraphQLName("cases_AssignAgents")]
    public async Task<Result<CreatedStatus>> AssignAgents(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        AssignAgentsInput input, CancellationToken cancellationToken
        )
    {
        if (!input.Agents?.Any() ?? true)
            return Result<CreatedStatus>.Failure("No agent inputs provided");

        IEnumerable<IGrouping<string, AssignAgentCommand>> duplicates = input.Agents
            .GroupBy(n => n.AgentId)
            .Where(g => g.Count() > 1);
        if (duplicates.Any())
            return Result<CreatedStatus>.Failure($"Duplicate agentIds `{string.Join(',', duplicates.Select(d => d.Key))}` found in input.");

        await _permissionValidator.AuthorizeWithAsync(identity, "writeCases");
        foreach (AssignAgentCommand agent in input.Agents)
            agent.AssignedById = loginId;

        return await _caseService.AssignAgentsAsync(tenantId, input.CaseId, input.Agents, cancellationToken);
    }

    [GraphQLName("cases_UnassignAgents")]
    public async Task<Result> UnassignAgents(
       [GlobalState] string tenantId,
       [GlobalState] string loginId,
       [GlobalState] ClaimsIdentity identity,
       UnassignAgentsCommand input, CancellationToken cancellationToken
       )
    {
        if (!input.AgentIds?.Any() ?? true)
            return Result.Failure("No agent inputs provided");

        await _permissionValidator.AuthorizeWithAsync(identity, "writeCases");
        input.UnassignedById = loginId;

        return await _caseService.UnassignAgentsAsync(tenantId, input, cancellationToken);
    }
}
