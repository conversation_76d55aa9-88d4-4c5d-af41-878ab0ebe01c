﻿using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;
using CoverGo.Cases.Domain.Brokers;
using System.Collections.Generic;
using System.Linq;
using CoverGo.BuildingBlocks.Auth.Permissions;

namespace CoverGo.Products.Application.GraphQl.Brokers;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class BrokersMutation : CoverGoGraphQlMutationsBase<Broker, BrokerUpsert, BrokerFilter, IBrokerService>
{
    private readonly IBrokerService _service;
    private readonly PermissionValidator _permissionValidator;

    public BrokersMutation(
        [Service] IBrokerService service
        , PermissionValidator permissionValidator
        )
        : base(service)
    {
        _service = service;
        _permissionValidator = permissionValidator;
    }

    public async Task<Result<CreatedStatus>> Create(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        BrokerUpsert create,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeWithAsync(identity, "writeDataSchemas");
        create.ById = loginId;

        DomainUtils.Error error = await ValidateIfBrokerWithSameNameAndCodeExists(tenantId, create.Code, create.Name, create.Id);
        if (error != null)
        {
            return Result<CreatedStatus>.Failure(error);
        }
        return await base.Create(tenantId, create, cancellation);
    }

    public async Task<Result> UpdateAsync(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        BrokerUpsert update,
        CancellationToken cancellation = default)
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeDataSchemas", update.Id);
        update.ById = loginId;
        
        DomainUtils.Error error = await ValidateIfBrokerWithSameNameAndCodeExists(tenantId, update.Code, update.Name, update.Id);
        if (error != null)
        {
            return Result.Failure(error);
        }
        return await base.UpdateAsync(tenantId, update, cancellation);
    }

    public async Task<Result> DeleteAsync(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        BrokerUpsert delete,
        CancellationToken cancellation = new CancellationToken())
    {
        await _permissionValidator.AuthorizeAsync(identity, "writeDataSchemas", delete.Id);

        delete.ById = loginId;
        return await base.DeleteAsync(tenantId, delete, cancellation);
    }

    private async Task<DomainUtils.Error> ValidateIfBrokerWithSameNameAndCodeExists(string tenantId, string code, string name, string id)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return null; // for backwards compatibility we allow brokers without name.
        }
        IReadOnlyCollection<Broker> brokers = await _service.QueryAsync(tenantId, new QueryArguments<Filter<BrokerFilter>>()
        {
            Where = new Filter<BrokerFilter>()
            {
                And = new List<Filter<BrokerFilter>>
                    {
                        new Filter<BrokerFilter>() { Where = new BrokerFilter() { Id_neq = id } },
                        new Filter<BrokerFilter>() { Where = new BrokerFilter() { NormalizedName = name.ToUpperInvariant() } },
                        new Filter<BrokerFilter>() { Where = new BrokerFilter() { NormalizedCode_in = new List<string> { code?.ToUpperInvariant() } } }
                    }
            }
        });
        if (brokers.Count > 0)
        {
            return new DomainUtils.Error() { Code = "BROKER_WITH_SAME_NAME_EXISTS", Message = $"Broker with the name {name} already exists." };
        }
        return null;
    }
}
