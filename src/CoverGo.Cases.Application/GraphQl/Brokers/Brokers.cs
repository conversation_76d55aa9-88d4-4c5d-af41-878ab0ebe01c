using System.Security.Claims;
using System.Threading;
using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.Cases.Domain.Brokers;
using CoverGo.DomainUtils;
using HotChocolate;
using HotChocolate.Types;

namespace CoverGo.Products.Application.GraphQl.Brokers;

[ExtendObjectType(CoverGoGraphQl.QueriesRoot)]
public class Brokers : CoverGoGraphQlQueryBase<Broker, BrokerUpsert, BrokerFilter, IBrokerRepository>
{
    private readonly PermissionValidator _permissionValidator;

    public Brokers(PermissionValidator permissionValidator)
    {
        _permissionValidator = permissionValidator;
    }

    public CoverGoGraphQlQueryInterface<
        Broker,
        BrokerUpsert,
        BrokerUpsert,
        BrokerUpsert,
        EntityBatch<
            BrokerUpsert, BrokerUpsert, BrokerUpsert
        >,
        QueryArguments<
            Filter<BrokerFilter>
        >,
        BrokerFilter,
        IBrokerRepository>
        Query(
            [GlobalState] string tenantId,
            [GlobalState] ClaimsIdentity identity,
            QueryArguments<Filter<BrokerFilter>> where,
            CancellationToken cancellation = default)
    {
        _permissionValidator.AuthorizeWithAsync(identity, "readDataSchemas");
        return base.Query(tenantId, where, cancellation);
    }
}

public class BrokerType : ObjectType<Broker>
{
    protected override void Configure(IObjectTypeDescriptor<Broker> descriptor)
    {
        descriptor.Field(x => x.Fields).Resolve(x => x.Parent<Broker>().Fields?.ToString());
    }
}
