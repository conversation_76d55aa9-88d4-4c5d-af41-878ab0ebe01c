﻿using CoverGo.Cases.Application.GraphQl.Admin;
using CoverGo.Cases.Domain;

namespace CoverGo.Cases.Application.GraphQl
{
    public static class InputExtensions
    {
        public static UpdateOfferCommand ToUpdateOfferCommand(this AdminUpdateOfferInput adminUpdateOfferInput, string proposalId, string offerId, string loginId)
        {
            UpdateOfferCommand updateOfferCommand = new()
            {
                OfferId = offerId,
                ProposalId = proposalId,
                ModifiedById = loginId,
            };
            SetOfferValuesFromInput(adminUpdateOfferInput, updateOfferCommand);
            return updateOfferCommand;
        }

        private static void SetOfferValuesFromInput(AdminUpdateOfferInput input, UpdateOfferCommand updateOfferCommand)
        {
            if (input.Status != null)
            {
                updateOfferCommand.Status = input.Status.Value;
                updateOfferCommand.IsStatusChanged = true;
            }
            if (input.StartDate != null)
            {
                updateOfferCommand.StartDate = input.StartDate.Value;
                updateOfferCommand.IsStartDateChanged = true;
            }
            if (input.EndDate != null)
            {
                updateOfferCommand.EndDate = input.EndDate.Value;
                updateOfferCommand.IsEndDateChanged = true;
            }
            if (input.ProductTreeId != null)
            {
                updateOfferCommand.ProductTreeId = input.ProductTreeId.Value;
                updateOfferCommand.IsProductTreeIdChanged = true;
            }
            if (input.Pricing != null)
            {
                updateOfferCommand.Pricing = input.Pricing.Value;
                updateOfferCommand.IsPricingChanged = true;
            }
            if (input.Underwriting != null)
            {
                updateOfferCommand.Underwriting = input.Underwriting.Value;
                updateOfferCommand.IsUnderwritingChanged = true;
            }
            if (input.Fields != null)
            {
                updateOfferCommand.Fields = input.Fields.Value;
                updateOfferCommand.IsFieldsChanged = true;
            }
            if (input.FieldsSchemaId != null)
            {
                updateOfferCommand.FieldsSchemaId = input.FieldsSchemaId.Value;
                updateOfferCommand.IsFieldsSchemaIdChanged = true;
            }
            if (input.IsPremiumOverridden != null)
            {
                updateOfferCommand.IsPremiumOverridden = input.IsPremiumOverridden.Value;
            }
            if (input.Premium != null)
            {
                SetOfferOfferPremiumValues(input, updateOfferCommand);
            }
            if (input.ProductId != null)
            {
                SetOfferProductValues(input, updateOfferCommand);
            }
        }

        private static void SetOfferProductValues(AdminUpdateOfferInput input, UpdateOfferCommand updateOfferCommand)
        {
            updateOfferCommand.ProductId = new();
            updateOfferCommand.IsProductIdChanged = true;
            if (input.ProductId.Value.Plan != null)
            {
                updateOfferCommand.ProductId.Plan = input.ProductId.Value.Plan.Value;
                updateOfferCommand.ProductId.IsPlanChanged = true;
            }
            if (input.ProductId.Value.Type != null)
            {
                updateOfferCommand.ProductId.Type = input.ProductId.Value.Type.Value;
                updateOfferCommand.ProductId.IsTypeChanged = true;
            }
            if (input.ProductId.Value.Version != null)
            {
                updateOfferCommand.ProductId.Version = input.ProductId.Value.Version.Value;
                updateOfferCommand.ProductId.IsVersionChanged = true;
            }
        }

        private static void SetOfferOfferPremiumValues(AdminUpdateOfferInput input, UpdateOfferCommand updateOfferCommand)
        {
            updateOfferCommand.Premium = new();
            updateOfferCommand.IsPremiumChanged = true;
            if (input.Premium.Value.Amount != null)
            {
                updateOfferCommand.Premium.Amount = input.Premium.Value.Amount.Value;
                updateOfferCommand.Premium.IsAmountChanged = true;
            }
            if (input.Premium.Value.CurrencyCode != null)
            {
                updateOfferCommand.Premium.CurrencyCode = input.Premium.Value.CurrencyCode.Value;
                updateOfferCommand.Premium.IsCurrencyCodeChanged = true;
            }
            if (input.Premium.Value.DiscountCodes != null)
            {
                updateOfferCommand.Premium.DiscountCodes = input.Premium.Value.DiscountCodes.Value;
                updateOfferCommand.Premium.IsDiscountCodesChanged = true;
            }
            if (input.Premium.Value.GrossAmount != null)
            {
                updateOfferCommand.Premium.GrossAmount = input.Premium.Value.GrossAmount.Value;
                updateOfferCommand.Premium.IsGrossAmountChanged = true;
            }
            if (input.Premium.Value.IsPricedAtStartDate != null)
            {
                updateOfferCommand.Premium.IsPricedAtStartDate = input.Premium.Value.IsPricedAtStartDate.Value;
                updateOfferCommand.Premium.IsIsPricedAtStartDateChanged = true;
            }
        }
    }
}
