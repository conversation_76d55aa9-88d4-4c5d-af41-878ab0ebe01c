﻿using CoverGo.Applications.Http.GraphQl.Services;
using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Proxies.Auth;
using HotChocolate;
using HotChocolate.Types;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using CoverGo.BuildingBlocks.Auth.Permissions;
using static CoverGo.Cases.Application.GraphQl.PermissionGroupCheckExtensions;

namespace CoverGo.Cases.Application.GraphQl.Agent;

[ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
public class AgentMutation
{
    private readonly ICaseService _service;
    private readonly PermissionValidator _permissionValidator;
    private readonly IAuthService _authService;

    public AgentMutation([Service] ICaseServiceFactory caseServiceFactory, PermissionValidator permissionValidator, IAuthService authService)
    {
        _service = caseServiceFactory.Build();
        _permissionValidator = permissionValidator;
        _authService = authService;
    }

    /// <summary>
    /// Create case for agent.
    /// </summary>
    /// <param name="tenantId">The tenant identifier.</param>
    /// <param name="loginId">The login identifier.</param>
    /// <param name="identity">The identity.</param>
    /// <param name="input">The input.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns></returns>
    public async Task<Result<string>> AgentCreateCase(
        [GlobalState] string tenantId,
        [GlobalState] string loginId,
        [GlobalState] ClaimsIdentity identity,
        AgentCreateCaseInput input, CancellationToken cancellationToken)
    {
        DomainUtils.Error error = await ValidateUserIsAgentOrAdmin(tenantId, identity);
        if (error != null)
            return Result<string>.Failure(error);
        await _permissionValidator.AuthorizeWithAsync(identity, "writeCases");

        var hasAgentGroup = await ContainsPermissionGroup(_authService, tenantId, identity, AgentPermGroupName, cancellationToken);

        if (!hasAgentGroup && string.IsNullOrEmpty(input.AssignedAgentEntityId))
            return Result<string>.Failure(new DomainUtils.Error { Code = "NO_AGENT_ASSIGNED", Message = $"Missing `{nameof(input.AssignedAgentEntityId)}`." });
        var currentEntityId = identity.Claims.First((claim) => claim.Type == "entityId").Value;
        if (hasAgentGroup && !string.IsNullOrEmpty(input.AssignedAgentEntityId) && input.AssignedAgentEntityId != currentEntityId)
            return Result<string>.Failure(new DomainUtils.Error { Code = "CANNOT_ASSIGN_AGENT", Message = $"Field `{nameof(input.AssignedAgentEntityId)}` cannot be set by an agent." });
        var agentEntityId = input.AssignedAgentEntityId ?? currentEntityId;

        AgentCreateCaseCommand agentCreateCaseCommand = new()
        {
            OtherHolderIds = input.OtherHolderIds,
            ComponentId = input.ComponentId,
            HolderId = input.HolderId,
            Description = input.Description,
            Source = input.Source,
            Fields = input.Fields,
            FieldsSchemaId = input.FieldsSchemaId,
            Name = input.Name,
            InsuredIds = input.InsuredIds,
            CreatedById = loginId,
            Status = input.Status ?? CaseStatus.Submitted,
            Stakeholders = new List<Stakeholder>
            {
                new()
                {
                    Id = Guid.NewGuid().ToString(),
                    Type = "Agent",
                    EntityId = agentEntityId
                }
            }
        };
        return await _service.AgentCreateAsync(tenantId, agentCreateCaseCommand, cancellationToken);
    }

    private async Task<DomainUtils.Error> ValidateUserIsAgentOrAdmin(string tenantId, ClaimsIdentity identity) =>
        !await ContainsPermissionGroup(_authService, tenantId, identity, AgentPermGroupName, CancellationToken.None) && !await ContainsPermissionGroup(_authService, tenantId, identity, ProposalCreatorPermGroupName, CancellationToken.None)
            ? new DomainUtils.Error() { Code = "NOT_AN_AGENT_AND_NOT_A_PROPOSAL_CREATOR", Message = "User is not an agent and not a proposal creator." }
            : null;
}
