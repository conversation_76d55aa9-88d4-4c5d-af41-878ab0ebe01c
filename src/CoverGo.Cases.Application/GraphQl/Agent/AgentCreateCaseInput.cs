﻿using System.Collections.Generic;

namespace CoverGo.Cases.Application.GraphQl.Agent
{
    public class AgentCreateCaseInput
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Source { get; set; }
        public string HolderId { get; set; }
        public List<string> OtherHolderIds { get; set; }
        public List<string> InsuredIds { get; set; }
        public string ComponentId { get; set; }

        public string Status { get; set; }
        public string Fields { get; set; }
        public string FieldsSchemaId { get; set; }

        public string AssignedAgentEntityId { get; set; }
    }
}
