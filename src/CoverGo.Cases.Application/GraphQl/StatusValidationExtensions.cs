﻿using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application.GraphQl;

public static class StatusValidationExtensions
{
    public static async Task<Case> FetchCaseMatchingCurrentStatuses(
        ICaseService service,
        string tenantId,
        string caseId,
        List<string> caseStatuses,
        string proposalId = null,
        string proposalStatus = null,
        CancellationToken cancellationToken = default)
    {
        CaseWhere where = new()
        {
            And = new List<CaseWhere>
            {
                new()
                {
                    Id = caseId,
                },
            }
        };

        IEnumerable<CaseWhere> caseStatusFilters = caseStatuses.Select(s => new CaseWhere
        {
            Status = s
        });
        where.And.Add(new CaseWhere { Or = caseStatusFilters.ToList() });

        if (proposalStatus != null)
        {
            CaseWhere proposalStatusFilter = new()
            {
                Proposals_contains = new()
                {
                    Status = proposalStatus,
                },
            };

            where.And.Add(proposalStatusFilter);
        }

        Case @case = (await service.GetAsync(tenantId, new QueryArguments<CaseWhere> { Where = where, First = 1 }, cancellationToken)).FirstOrDefault();
        if (proposalStatus == null || (@case?.Proposals.Any((proposal) => proposal.Id == proposalId && proposal.Status == proposalStatus) ?? false))
        {
            return @case;
        }
        return null;
    }
}
