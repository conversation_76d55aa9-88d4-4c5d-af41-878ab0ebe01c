﻿using CoverGo.Applications.Http.GraphQl.Services.GraphQlTypeDescriptors;
using HotChocolate.Types;
using HotChocolate;
using System.Threading.Tasks;
using System.Threading;
using CoverGo.DomainUtils;
using System;
using CoverGo.Cases.Domain;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Cases.Application.GraphQl.Offers
{
    [ExtendObjectType(CoverGoGraphQl.MutationsRoot)]
    public class ConvertOffer
    {
        private readonly ICaseService _caseService;

        public ConvertOffer([Service] ICaseServiceFactory caseServiceFactory)
        {
            _caseService = caseServiceFactory.Build();
        }

        public async Task<ConvertOfferToApplicationPayload> ToApplication(
            [GlobalState] string tenantId,
            [GlobalState] string loginId,
            ConvertOfferToApplicationCommand input,
            [Service] IHttpContextAccessor httpContextAccessor,
            CancellationToken cancellation = default)
        {
            httpContextAccessor.HttpContext.Request.Headers.TryGetValue("Authorization", out var authorization);
            var accessToken = authorization.FirstOrDefault()?.Replace("Bearer ", string.Empty);
            var result = await _caseService.ConvertOfferToApplication(tenantId, loginId, input, accessToken: accessToken, cancellationToken: cancellation);
            if (result.IsSuccess)
                return new ConvertOfferToApplicationPayload(Result<string>.Success(result.Value.Ids.FirstOrDefault()));

            return new ConvertOfferToApplicationPayload(Result<string>.Failure(result.Errors_2));
        }
    }

    public class ConvertOfferToApplicationPayload : Result<string>
    {
        public ConvertOfferToApplicationPayload() { }

        public ConvertOfferToApplicationPayload(Result<string> result)
        {
            Status = result.Status;
            Errors = result.Errors;
            Errors_2 = result.Errors_2;
            Value = result.Value;
        }
    }

    public class ConvertOfferToApplicationPayloadType : ObjectType<ConvertOfferToApplicationPayload>
    {
        protected override void Configure(IObjectTypeDescriptor<ConvertOfferToApplicationPayload> descriptor)
        {
            descriptor.Name("ConvertOfferToApplicationPayload");
            descriptor
                .Field(f => f.Value)
                .Name("generatedPolicyId");
        }
    }

    public class ConvertOfferToApplicationCommandType : InputObjectType<ConvertOfferToApplicationCommand>
    {
        protected override void Configure(IInputObjectTypeDescriptor<ConvertOfferToApplicationCommand> descriptor)
        {
            descriptor.Name("ConvertOfferToApplicationInput");
            descriptor.Field(f => f.ClientId).Ignore();
            descriptor.Field(f => f.AcceptedById).Ignore();
        }
    }
}
