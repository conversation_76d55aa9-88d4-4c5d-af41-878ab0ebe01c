﻿using System;
using System.Linq;
using CoverGo.Cases.Domain;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace CoverGo.Cases.Application.Filters
{
    [AttributeUsage(AttributeTargets.All)]
    public class ChannelIdFilterAttribute : Attribute, IAuthorizationFilter
    {
        private readonly ICaseService _service;

        public ChannelIdFilterAttribute(ICaseServiceFactory serviceFactory)
        {
            _service = serviceFactory.Build();
        }

        public async void OnAuthorization(AuthorizationFilterContext context)
        {
            // Get the current user's claims
            var claims = context.HttpContext.User.Claims;
            var channelIds = claims?.Where(c => c.Type == "accessChannels").Select(x => x.Value);

            // Get the caseId value from the route parameter
            var routeData = context.RouteData;
            var tenantId = routeData.Values["tenantId"]?.ToString();
            var caseId = routeData.Values["caseId"]?.ToString();

            if (!string.IsNullOrEmpty(caseId) && channelIds?.Any() == true)
            {
                var @case = await _service.GetAsync(tenantId, new DomainUtils.QueryArguments<CaseWhere> { Where = new CaseWhere { Id = caseId } }, default);
                if (@case != null && !channelIds.Contains(caseId))
                {
                    context.Result = new StatusCodeResult(403);
                }
            }

            // Check if the user has a claim with type "caseId" and the desired value
            var hasMatchingClaim = claims.Any(claim =>
                claim.Type == "caseId" && claim.Value == caseId);       
        }
    }
}

