{"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore.Server.Kestrel": "Fatal", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}], "Enrich": ["FromLogContext"], "Properties": {"Service": "covergo-cases"}}, "FeatureManagement": {"ENABLE_PERMISSION_VERSION_2_IN_GRAPHQL_INTERCEPTOR": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["covergo", "coverHealth_dev", "gms_dev"]}}]}, "GENERATE_SEQUENTIAL_NUMBER_IN_CASE_FIELDS": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["asia_preprod", "asia_prod", "asia_dev"]}}]}}, "PubSubConfiguration": {"SiloModel": {"Tenants": ["coverhealth_dev", "asia_dev", "asia_preprod", "asia_prod"]}}}