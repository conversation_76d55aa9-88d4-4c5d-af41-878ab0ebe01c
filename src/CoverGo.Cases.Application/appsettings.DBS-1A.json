{
  "Serilog": {
    // When doing development we use normal formating and avoid doing json output
    "Using": [
      "Serilog.Sinks.Console"
    ],

    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore.Server.Kestrel": "Fatal",
        "System": "Warning"
      }
    }
  },

  "serviceUrls": {
    "logging": "http://logging-1A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "pricing": "http://localhost:60030/",
    "policies": "http://policies-1A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "products": "http://product-1A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "notifications": "http://notification-1A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "gateway": "http://ibg-gateway-1A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "auth": "http://auth-1A.rmhk.cbg.prd.cloudnow.dbs.com/",
    "transactions": "http://localhost:60120/",
    "users": "http://localhost:60010/",
    "channel-management": "http://localhost:64483/"
  }
}
