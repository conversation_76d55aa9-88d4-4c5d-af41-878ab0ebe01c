using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.HeaderPropagation;
using Microsoft.Extensions.Primitives;
using System.Threading.Tasks;
using System.Net.Http;
using System.Threading;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Cases.Application;

internal class AddAuthorizationFromQueryString
{
    private readonly RequestDelegate _next;
    private readonly HeaderPropagationValues _values;
    public AddAuthorizationFromQueryString(RequestDelegate next, HeaderPropagationValues values)
    {
        _next = next ?? throw new ArgumentNullException("next");
        _values = values ?? throw new ArgumentNullException("values");
    }

    public Task Invoke(HttpContext context)
    {
        var headers = _values.Headers ?? (_values.Headers = new Dictionary<string, StringValues>(StringComparer.OrdinalIgnoreCase));

        if (context.Request.Headers.TryGetValue("Authorization", out var header) && !string.IsNullOrWhiteSpace(headers.ToString()))
        {
            headers.Add("Authorization", header);
        }
        else if (context.Request.Query.TryGetValue("accessToken", out var query))
        {
            var accessToken = query.ToString();
            if (!string.IsNullOrEmpty(accessToken)) headers.Add("Authorization", "Bearer " + accessToken);
        }
        return _next(context);
    }
}
internal class AddAuthorizationMessageHandler : DelegatingHandler
{
    private readonly HeaderPropagationValues _values;
    public AddAuthorizationMessageHandler(HeaderPropagationValues values)
    {
        _values = values ?? throw new ArgumentNullException("values");
    }
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var headers = _values.Headers;
        if (headers != null)
        {
            foreach (var pair in headers)
            {
                var (key, value) = pair;

                bool flag = request.Content != null;
                if (request.Headers.TryGetValues(key, out IEnumerable<string> values)
                || (flag && request.Content.Headers.TryGetValues(key, out values))
                || StringValues.IsNullOrEmpty(value))
                {
                    continue;
                }

                if (value.Count == 1)
                {
                    string value2 = value.ToString();
                    if (!request.Headers.TryAddWithoutValidation(key, value2) && flag)
                    {
                        request.Content.Headers.TryAddWithoutValidation(key, value2);
                    }
                }
                else
                {
                    string[] values2 = value.ToArray();
                    if (!request.Headers.TryAddWithoutValidation(key, values2) && flag)
                    {
                        request.Content.Headers.TryAddWithoutValidation(key, values2);
                    }
                }
            }
        }

        return base.SendAsync(request, cancellationToken);
    }
}
internal static class AddAuthorizationFromQueryStringExtension
{
    public static IApplicationBuilder UseAddAuthorizationFromQueryString(this IApplicationBuilder app)
    {
        if (app == null)
        {
            throw new ArgumentNullException("app");
        }

        if (app.ApplicationServices.GetService<HeaderPropagationValues>() == null)
        {
            throw new InvalidOperationException("Unable to find the required services. Please add all the required services by calling 'IServiceCollection.AddHeaderPropagation' inside the call to 'ConfigureServices(...)' in the application startup code.");
        }

        app.UseMiddleware<AddAuthorizationFromQueryString>();
        return app;
    }
    public static IHttpClientBuilder AddAuthorizationFromQueryString(this IHttpClientBuilder builder)
    {
        if (builder == null)
        {
            throw new ArgumentNullException("builder");
        }

        builder.Services.AddHeaderPropagation();
        builder.AddHttpMessageHandler(services => new AddAuthorizationMessageHandler(services.GetRequiredService<HeaderPropagationValues>()));
        return builder;
    }
}
