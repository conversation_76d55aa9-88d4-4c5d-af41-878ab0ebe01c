<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>e74e56fb-c313-4174-8281-76795c9f8f5c</UserSecretsId>
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Local' " />
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Developer Machine' " />
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'DBS MariaDb' " />
  <PropertyGroup Condition=" '$(RunConfiguration)' == '12factor' " />
  <ItemGroup>
    <PackageReference Include="CoverGo.Multitenancy" />
    <PackageReference Include="CoverGo.Multitenancy.AspNetCore" />
    <PackageReference Include="CoverGo.Multitenancy.Autofac" />
    <PackageReference Include="CoverGo.SettableValues" />
    <PackageReference Include="HotChocolate" />
    <PackageReference Include="HotChocolate.AspNetCore" />
    <PackageReference Include="HotChocolate.AspNetCore.Authorization" />
    <PackageReference Include="HotChocolate.Data.MongoDb" />
    <PackageReference Include="HotChocolate.Stitching" />
    <PackageReference Include="HotChocolate.Types" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="Microsoft.Extensions.Logging.Configuration" />
    <PackageReference Include="ProductBuilder.Client" />
    <PackageReference Include="prometheus-net" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Covergo.BuildingBlocks.Auth" />
    <PackageReference Include="CoverGo.Applications.HealthCheck" />
    <PackageReference Include="CoverGo.Applications.Http.GraphQl.Services" />
    <PackageReference Include="CoverGo.Applications.Http.Rest" />
    <PackageReference Include="CoverGo.Applications.Startup" />
    <PackageReference Include="CoverGo.Sentry" />
    <PackageReference Include="CoverGo.Applications.AttachedRules.Services" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Cases.Domain\CoverGo.Cases.Domain.csproj" />
    <ProjectReference Include="..\CoverGo.Cases.Infrasctructure\CoverGo.Cases.Infrastructure.csproj" />
    <ProjectReference Include="..\CoverGo.Cases.Infrastructure.Decorators\CoverGo.Cases.Infrastructure.Decorators.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="secrets.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
