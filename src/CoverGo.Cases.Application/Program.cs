﻿using CoverGo.Applications.Startup;
using CoverGo.Configuration;
using CoverGo.MongoUtils;
using CoverGo.Sentry;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Serilog;
using Serilog.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace CoverGo.Cases.Application
{
    public class Program
    {
        // See: https://github.com/serilog/serilog-aspnetcore#enabling-microsoftextensionsloggingiloggerproviders
        static readonly LoggerProviderCollection Providers = new LoggerProviderCollection();
        public static void Main(string[] args)
        {
            string appName = "covergo-cases";
            if (Environment.GetEnvironmentVariable("appName") == null)
                Environment.SetEnvironmentVariable("appName", appName);

            string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                // Default some values
                .AddJsonFile("appsettings.json")
                // Override by environment
                .AddJsonFile($"appsettings.{environment}.json")
                .AddEnvironmentVariables()
                .Build();

            Log.Logger = new LoggerConfiguration()
                .WriteTo.Providers(Providers)
                .ReadFrom.Configuration(configuration)
                .CreateLogger();

            Log.Logger.Information($"APPNAME: {appName}");
            string datacenterId = Environment.GetEnvironmentVariable("datacenterId");
            Log.Logger.Information($"DATACENTERID: {datacenterId}");

            string databaseDriver = Environment.GetEnvironmentVariable("DATABASE_DRIVER");

            StartupMethods.AddMongoDBMetrics();

            // initialize connectivity 
            if (datacenterId == "local")
            {
                var covergoDbConfig = new DbConfig();
                covergoDbConfig.Load<Program>();
                DbConfig.AddConfig("default", covergoDbConfig);
                MongoTools.GetOrAddMongoClient(covergoDbConfig);
                Task.Run(() => MongoTools.IndexingAsync(covergoDbConfig, "cases", $"/-cases$/g", CasesIndices()));
            }

            if (datacenterId == "developer-machine" && databaseDriver == "mongoDb")
            {
                var defaultConfig = new DbConfig();
                defaultConfig.Load<Program>();
                DbConfig.AddConfig("default", defaultConfig);
                MongoTools.GetOrAddMongoClient(defaultConfig);
            }

            if (datacenterId == "covergo-dockerComposeOnJenkins-hk")
            {
                var covergoDbConfig = new DbConfig();
                covergoDbConfig.Load<Program>();
                DbConfig.AddConfig("default", covergoDbConfig);

                MongoTools.GetOrAddMongoClient(covergoDbConfig);
                Task.Run(() => MongoTools.IndexingAsync(covergoDbConfig, "cases", $"/-cases$/g", CasesIndices()));
            }

            if (datacenterId == "covergo-aliyun-hk")
            {
                var covergoDbConfig = new DbConfig();
                covergoDbConfig.Load<Program>();
                DbConfig.AddConfig("default", covergoDbConfig);
                MongoTools.GetOrAddMongoClient(covergoDbConfig);
                Task.Run(() => MongoTools.IndexingAsync(covergoDbConfig, "cases", $"/-cases$/g", CasesIndices()));
            }

            if (datacenterId == "tahoe-aws-hk")
            {
                var tahoeConfig = new DbConfig();
                tahoeConfig.Load<Program>();
                DbConfig.AddConfig("default", tahoeConfig);
                MongoTools.GetOrAddMongoClient(tahoeConfig);
                Task.Run(() => MongoTools.IndexingAsync(tahoeConfig, "cases", $"/-cases$/g", CasesIndices()));
            }

            if (datacenterId == "dbs-hk")
            {
                var dbsConfig = new DbConfig
                {
                    ProviderId = "mariaDb",
                    Options = new Dictionary<string, dynamic>
                    {
                        { "mySqlServerVersion", "10.3.24" }
                    }
                };
                dbsConfig.Load<Program>();
                DbConfig.AddConfig("dbs_uat", dbsConfig);
            }

            if (datacenterId == "12factor" && databaseDriver == "mongoDb")
            {
                var defaultConfig = new DbConfig();
                defaultConfig.Load<Program>();
                DbConfig.AddConfig("default", defaultConfig);
                MongoTools.GetOrAddMongoClient(defaultConfig);
                Task.Run(() => MongoTools.IndexingAsync(defaultConfig, "cases", $"/-cases$/g", CasesIndices()));

                if (!string.IsNullOrEmpty(Environment.GetEnvironmentVariable("DBS_DATABASE_CONNECT_STRING")))
                {
                    var dbsConfig = new DbConfig
                    {
                        ProviderId = "mariaDb",
                        Options = new Dictionary<string, dynamic>
                    {
                        { "mySqlServerVersion", "10.3.24" }
                    }
                    };
                    dbsConfig.Load<Program>("DBS_");
                    DbConfig.AddConfig("dbs_uat", dbsConfig);
                }
            }

            if (datacenterId == "tcb-aws" && databaseDriver == "mongoDb")
            {
                var defaultConfig = new DbConfig();
                defaultConfig.Load<Program>();
                DbConfig.AddConfig("default", defaultConfig);
                MongoTools.GetOrAddMongoClient(defaultConfig);

                try
                {
                    MongoTools.IndexingAsync(defaultConfig, "cases", "tcb_uat-cases", CasesIndices(), true).Wait();
                    MongoTools.IndexingAsync(defaultConfig, "cases", "tcb-cases", CasesIndices(), true).Wait();
                    Log.Logger.Information($"Adding cases index completed");
                }
                catch (Exception ex)
                {
                    Log.Logger.Error(ex, ex.Message);
                }
            }

            CreateWebHostBuilder(args)
            .UseCoverGoSentry(configuration)
            .Build()
            .Run();
        }

        private static List<CreateIndexModel<BsonDocument>> CasesIndices() => new List<CreateIndexModel<BsonDocument>>
        {
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("_id")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("holderId")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("proposals._id")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("proposals.basket._id")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("createdAt")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Descending("lastModifiedAt")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("status")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("createdById")),
            new CreateIndexModel<BsonDocument>(Builders<BsonDocument>.IndexKeys.Ascending("source")),
        };

        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                // We suppress default logs that are not structured
                .SuppressStatusMessages(false)
                .ConfigureLogging(logging => logging.ClearProviders())
                .UseSerilog(providers: Providers)
                .UseStartup<Startup>();
    }
}
