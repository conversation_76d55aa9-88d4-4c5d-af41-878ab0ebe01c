﻿{
  "Serilog": {
    "Enrich": ["WithSpan", "FromLogContext"],

    "WriteTo": [
      {
        "Args": {
          "formatter": "Serilog.Formatting.Compact.RenderedCompactJsonFormatter, Serilog.Formatting.Compact"
        }
      }
    ]
  },

  "serviceUrls": {
    "logging": "http://covergo-logging:9200/",
    "pricing": "http://covergo-pricing:8080/",
    "policies": "http://covergo-policies:8080/",
    "products": "http://covergo-products:8080/",
    "notifications": "http://covergo-notifications:8080/",
    "gateway": "http://covergo-gateway:8080/",
    "auth": "http://covergo-auth:8080/",
    "transactions": "http://covergo-transactions:8080/",
    "productbuilder": "http://covergo-product-builder:80/",
    "users": "http://covergo-users:8080/",
    "channel-management": "http://covergo-channel-management/"
  }
}
