{
  "Serilog": {
    // When doing development we use normal formating and avoid doing json output
    "Using": [
      "Serilog.Sinks.Console"
    ],

    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore.Server.Kestrel": "Fatal",
        "System": "Warning"
      }
    }
  },

  "serviceUrls": {
    "logging": "http://logging-UAT1.rmhk.ibg.dev.cloudnow.dbs.com/",
    "pricing": "http://localhost:60030/",
    "policies": "http://policies-UAT1.rmhk.ibg.dev.cloudnow.dbs.com/",
    "products": "http://product-UAT1.rmhk.ibg.dev.cloudnow.dbs.com/",
    "notifications": "http://notification-UAT1.rmhk.ibg.dev.cloudnow.dbs.com/",
    "gateway": "http://ibg-gateway-UAT1.rmhk.ibg.dev.cloudnow.dbs.com/",
    "auth": "http://auth-UAT1.rmhk.ibg.dev.cloudnow.dbs.com/",
    "transactions": "http://localhost:60120/",
    "users": "http://localhost:60010/",
    "channel-management": "http://localhost:64483/"
  }
}
