// <auto-generated> This file has been auto generated. </auto-generated>

using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace CoverGo.Cases.Client
{
    #region base classes
    public struct FieldMetadata
    {
        public string Name { get; set; }
        public bool IsComplex { get; set; }
        public Type QueryBuilderType { get; set; }
    }
    
    public enum Formatting
    {
        None,
        Indented
    }
    
    public class GraphQlObjectTypeAttribute : Attribute
    {
        public string TypeName { get; }
    
        public GraphQlObjectTypeAttribute(string typeName) => TypeName = typeName;
    }
    
    internal static class GraphQlQueryHelper
    {
        private static readonly Regex RegexWhiteSpace = new Regex(@"\s", RegexOptions.Compiled);
        private static readonly Regex RegexGraphQlIdentifier = new Regex(@"^[_A-Za-z][_0-9A-Za-z]*$", RegexOptions.Compiled);
    
        public static string GetIndentation(int level, byte indentationSize)
        {
            return new String(' ', level * indentationSize);
        }
    
        public static string BuildArgumentValue(object value, string formatMask, Formatting formatting, int level, byte indentationSize)
        {
            if (value is null)
                return "null";
    
            var enumerable = value as IEnumerable;
            if (!String.IsNullOrEmpty(formatMask) && enumerable == null)
                return
                    value is IFormattable formattable
                        ? "\"" + formattable.ToString(formatMask, CultureInfo.InvariantCulture) + "\""
                        : throw new ArgumentException($"Value must implement {nameof(IFormattable)} interface to use a format mask. ", nameof(value));
    
            if (value is Enum @enum)
                return ConvertEnumToString(@enum);
    
            if (value is bool @bool)
                return @bool ? "true" : "false";
    
            if (value is DateTime dateTime)
                return "\"" + dateTime.ToString("O") + "\"";
    
            if (value is DateTimeOffset dateTimeOffset)
                return "\"" + dateTimeOffset.ToString("O") + "\"";
    
            if (value is IGraphQlInputObject inputObject)
                return BuildInputObject(inputObject, formatting, level + 2, indentationSize);
    
            if (value is String || value is Guid)
                return "\"" + value + "\"";
    
            if (enumerable != null)
                return BuildEnumerableArgument(enumerable, formatMask, formatting, level, indentationSize, '[', ']');
    
            if (value is short || value is ushort || value is byte || value is int || value is uint || value is long || value is ulong || value is float || value is double || value is decimal)
                return Convert.ToString(value, CultureInfo.InvariantCulture);
    
            var argumentValue = Convert.ToString(value, CultureInfo.InvariantCulture);
            return "\"" + argumentValue + "\"";
        }
    
        private static string BuildEnumerableArgument(IEnumerable enumerable, string formatMask, Formatting formatting, int level, byte indentationSize, char openingSymbol, char closingSymbol)
        {
            var builder = new StringBuilder();
            builder.Append(openingSymbol);
            var delimiter = String.Empty;
            foreach (var item in enumerable)
            {
                builder.Append(delimiter);
    
                if (formatting == Formatting.Indented)
                {
                    builder.AppendLine();
                    builder.Append(GetIndentation(level + 1, indentationSize));
                }
    
                builder.Append(BuildArgumentValue(item, formatMask, formatting, level, indentationSize));
                delimiter = ",";
            }
    
            builder.Append(closingSymbol);
            return builder.ToString();
        }
    
        public static string BuildInputObject(IGraphQlInputObject inputObject, Formatting formatting, int level, byte indentationSize)
        {
            var builder = new StringBuilder();
            builder.Append("{");
    
            var isIndentedFormatting = formatting == Formatting.Indented;
            string valueSeparator;
            if (isIndentedFormatting)
            {
                builder.AppendLine();
                valueSeparator = ": ";
            }
            else
                valueSeparator = ":";
    
            var separator = String.Empty;
            foreach (var propertyValue in inputObject.GetPropertyValues())
            {
                var queryBuilderParameter = propertyValue.Value as QueryBuilderParameter;
                var value =
                    queryBuilderParameter?.Name != null
                        ? "$" + queryBuilderParameter.Name
                        : BuildArgumentValue(queryBuilderParameter?.Value ?? propertyValue.Value, propertyValue.FormatMask, formatting, level, indentationSize);
    
                builder.Append(isIndentedFormatting ? GetIndentation(level, indentationSize) : separator);
                builder.Append(propertyValue.Name);
                builder.Append(valueSeparator);
                builder.Append(value);
    
                separator = ",";
    
                if (isIndentedFormatting)
                    builder.AppendLine();
            }
    
            if (isIndentedFormatting)
                builder.Append(GetIndentation(level - 1, indentationSize));
    
            builder.Append("}");
    
            return builder.ToString();
        }
    
        public static void ValidateGraphQlIdentifier(string name, string identifier)
        {
            if (identifier != null && !RegexGraphQlIdentifier.IsMatch(identifier))
                throw new ArgumentException("value must match [_A-Za-z][_0-9A-Za-z]*", name);
        }
    
        private static string ConvertEnumToString(Enum @enum)
        {
            var enumMember = @enum.GetType().GetField(@enum.ToString());
                if (enumMember == null)
                    throw new InvalidOperationException("enum member resolution failed");
    
            var enumMemberAttribute = (EnumMemberAttribute)enumMember.GetCustomAttribute(typeof(EnumMemberAttribute));
    
            return enumMemberAttribute == null
                ? @enum.ToString()
                : enumMemberAttribute.Value;
        }
    }
    
    internal struct InputPropertyInfo
    {
        public string Name { get; set; }
        public object Value { get; set; }
        public string FormatMask { get; set; }
    }
    
    internal interface IGraphQlInputObject
    {
        IEnumerable<InputPropertyInfo> GetPropertyValues();
    }
    
    public interface IGraphQlQueryBuilder
    {
        void Clear();
        void IncludeAllFields();
        string Build(Formatting formatting = Formatting.None, byte indentationSize = 2);
    }
    
    public struct QueryBuilderArgumentInfo
    {
        public string ArgumentName { get; set; }
        public QueryBuilderParameter ArgumentValue { get; set; }
        public string FormatMask { get; set; }
    }
    
    public abstract class QueryBuilderParameter
    {
        private string _name;
    
        internal string GraphQlTypeName { get; }
        internal object Value { get; set; }
    
        public string Name
        {
            get => _name;
            set
            {
                GraphQlQueryHelper.ValidateGraphQlIdentifier(nameof(Name), value);
                _name = value;
            }
        }
    
        protected QueryBuilderParameter(string name, string graphQlTypeName, object value)
        {
            Name = name?.Trim();
            GraphQlTypeName = graphQlTypeName?.Replace(" ", null).Replace("\t", null).Replace("\n", null).Replace("\r", null);
            Value = value;
        }
    
        protected QueryBuilderParameter(object value) => Value = value;
    }
    
    public class QueryBuilderParameter<T> : QueryBuilderParameter
    {
        public new T Value
        {
            get => (T)base.Value;
            set => base.Value = value;
        }
    
        protected QueryBuilderParameter(string name, string graphQlTypeName, T value) : base(name, graphQlTypeName, value)
        {
            if (String.IsNullOrWhiteSpace(graphQlTypeName))
                throw new ArgumentException("value required", nameof(graphQlTypeName));
        }
    
        private QueryBuilderParameter(T value) : base(value)
        {
        }
    
        public static implicit operator QueryBuilderParameter<T>(T value) => new QueryBuilderParameter<T>(value);
    
        public static implicit operator T(QueryBuilderParameter<T> parameter) => parameter.Value;
    }
    
    public class GraphQlQueryParameter<T> : QueryBuilderParameter<T>
    {
        private string _formatMask;
    
        public string FormatMask
        {
            get => _formatMask;
            set => _formatMask =
                typeof(IFormattable).IsAssignableFrom(typeof(T))
                    ? value
                    : throw new InvalidOperationException($"Value must be of {nameof(IFormattable)} type. ");
        }
    
        public GraphQlQueryParameter(string name, string graphQlTypeName, T value) : base(name, graphQlTypeName, value)
        {
        }
    
        public GraphQlQueryParameter(string name, T value, bool isNullable = true) : base(name, GetGraphQlTypeName(value, isNullable), value)
        {
        }
    
        private static string GetGraphQlTypeName(T value, bool isNullable)
        {
            var graphQlTypeName = GetGraphQlTypeName(typeof(T));
            if (!isNullable)
                graphQlTypeName += "!";
    
            return graphQlTypeName;
        }
    
        private static string GetGraphQlTypeName(Type valueType)
        {
            valueType = Nullable.GetUnderlyingType(valueType) ?? valueType;
    
            if (valueType.IsArray)
            {
                var arrayItemType = GetGraphQlTypeName(valueType.GetElementType());
                return arrayItemType == null ? null : "[" + arrayItemType + "]";
            }
    
            if (typeof(IEnumerable).IsAssignableFrom(valueType))
            {
                var genericArguments = valueType.GetGenericArguments();
                if (genericArguments.Length == 1)
                {
                    var listItemType = GetGraphQlTypeName(valueType.GetGenericArguments()[0]);
                    return listItemType == null ? null : "[" + listItemType + "]";
                }
            }
    
            if (GraphQlTypes.ReverseMapping.TryGetValue(valueType, out var graphQlTypeName))
                return graphQlTypeName;
    
            if (valueType == typeof(bool))
                return "Boolean";
    
            if (valueType == typeof(float) || valueType == typeof(double) || valueType == typeof(decimal))
                return "Float";
    
            if (valueType == typeof(Guid))
                return "ID";
    
            if (valueType == typeof(sbyte) || valueType == typeof(byte) || valueType == typeof(short) || valueType == typeof(ushort) || valueType == typeof(int) || valueType == typeof(uint) ||
                valueType == typeof(long) || valueType == typeof(ulong))
                return "Int";
    
            if (valueType == typeof(string))
                return "String";
    
            return null;
        }
    }
    
    public abstract class GraphQlDirective
    {
        private readonly Dictionary<string, QueryBuilderParameter> _arguments = new Dictionary<string, QueryBuilderParameter>();
    
        internal IEnumerable<KeyValuePair<string, QueryBuilderParameter>> Arguments => _arguments;
    
        public string Name { get; }
    
        protected GraphQlDirective(string name)
        {
            GraphQlQueryHelper.ValidateGraphQlIdentifier(nameof(name), name);
            Name = name;
        }
    
        protected void AddArgument(string name, QueryBuilderParameter value)
        {
            if (value != null)
                _arguments[name] = value;
        }
    }
    
    public abstract class GraphQlQueryBuilder : IGraphQlQueryBuilder
    {
        private readonly Dictionary<string, GraphQlFieldCriteria> _fieldCriteria = new Dictionary<string, GraphQlFieldCriteria>();
    
        private readonly string _operationType;
        private readonly string _operationName;
        private Dictionary<string, GraphQlFragmentCriteria> _fragments;
        private List<QueryBuilderArgumentInfo> _queryParameters;
    
        protected abstract string TypeName { get; }
    
        public abstract IReadOnlyList<FieldMetadata> AllFields { get; }
    
        protected GraphQlQueryBuilder(string operationType, string operationName)
        {
            GraphQlQueryHelper.ValidateGraphQlIdentifier(nameof(operationName), operationName);
            _operationType = operationType;
            _operationName = operationName;
        }
    
        public virtual void Clear()
        {
            _fieldCriteria.Clear();
            _fragments?.Clear();
            _queryParameters?.Clear();
        }
    
        void IGraphQlQueryBuilder.IncludeAllFields()
        {
            IncludeAllFields();
        }
    
        public string Build(Formatting formatting = Formatting.Indented, byte indentationSize = 2)
        {
            return Build(formatting, 1, indentationSize);
        }
    
        protected void IncludeAllFields()
        {
            IncludeFields(AllFields);
        }
    
        protected virtual string Build(Formatting formatting, int level, byte indentationSize)
        {
            var isIndentedFormatting = formatting == Formatting.Indented;
            var separator = String.Empty;
            var indentationSpace = isIndentedFormatting ? " " : String.Empty;
            var builder = new StringBuilder();
    
            if (!String.IsNullOrEmpty(_operationType))
            {
                builder.Append(_operationType);
    
                if (!String.IsNullOrEmpty(_operationName))
                {
                    builder.Append(" ");
                    builder.Append(_operationName);
                }
    
                if (_queryParameters?.Count > 0)
                {
                    builder.Append(indentationSpace);
                    builder.Append("(");
    
                    foreach (var queryParameterInfo in _queryParameters)
                    {
                        if (isIndentedFormatting)
                        {
                            builder.AppendLine(separator);
                            builder.Append(GraphQlQueryHelper.GetIndentation(level, indentationSize));
                        }
                        else
                            builder.Append(separator);
                        
                        builder.Append("$");
                        builder.Append(queryParameterInfo.ArgumentValue.Name);
                        builder.Append(":");
                        builder.Append(indentationSpace);
    
                        builder.Append(queryParameterInfo.ArgumentValue.GraphQlTypeName);
    
                        if (!queryParameterInfo.ArgumentValue.GraphQlTypeName.EndsWith("!"))
                        {
                            builder.Append(indentationSpace);
                            builder.Append("=");
                            builder.Append(indentationSpace);
                            builder.Append(GraphQlQueryHelper.BuildArgumentValue(queryParameterInfo.ArgumentValue.Value, queryParameterInfo.FormatMask, formatting, 0, indentationSize));
                        }
    
                        separator = ",";
                    }
    
                    builder.Append(")");
                }
            }
    
            builder.Append(indentationSpace);
            builder.Append("{");
    
            if (isIndentedFormatting)
                builder.AppendLine();
    
            separator = String.Empty;
            
            foreach (var criteria in _fieldCriteria.Values.Concat(_fragments?.Values ?? Enumerable.Empty<GraphQlFragmentCriteria>()))
            {
                var fieldCriteria = criteria.Build(formatting, level, indentationSize);
                if (isIndentedFormatting)
                    builder.AppendLine(fieldCriteria);
                else if (!String.IsNullOrEmpty(fieldCriteria))
                {
                    builder.Append(separator);
                    builder.Append(fieldCriteria);
                }
    
                separator = ",";
            }
    
            if (isIndentedFormatting)
                builder.Append(GraphQlQueryHelper.GetIndentation(level - 1, indentationSize));
            
            builder.Append("}");
    
            return builder.ToString();
        }
    
        protected void IncludeScalarField(string fieldName, IList<QueryBuilderArgumentInfo> args)
        {
            _fieldCriteria[fieldName] = new GraphQlScalarFieldCriteria(fieldName, args);
        }
    
        protected void IncludeObjectField(string fieldName, GraphQlQueryBuilder objectFieldQueryBuilder, IList<QueryBuilderArgumentInfo> args)
        {
            _fieldCriteria[fieldName] = new GraphQlObjectFieldCriteria(fieldName, objectFieldQueryBuilder, args);
        }
    
        protected void IncludeFragment(GraphQlQueryBuilder objectFieldQueryBuilder)
        {
            _fragments = _fragments ?? new Dictionary<string, GraphQlFragmentCriteria>();
            _fragments[objectFieldQueryBuilder.TypeName] = new GraphQlFragmentCriteria(objectFieldQueryBuilder);
        }
    
        protected void ExcludeField(string fieldName)
        {
            if (fieldName == null)
                throw new ArgumentNullException(nameof(fieldName));
    
            _fieldCriteria.Remove(fieldName);
        }
    
        protected void IncludeFields(IEnumerable<FieldMetadata> fields)
        {
            IncludeFields(fields, null);
        }
    
        private void IncludeFields(IEnumerable<FieldMetadata> fields, List<Type> parentTypes)
        {
            foreach (var field in fields)
            {
                if (field.QueryBuilderType == null)
                    IncludeScalarField(field.Name, null);
                else
                {
                    var builderType = GetType();
    
                    if (parentTypes != null && parentTypes.Any(t => t.IsAssignableFrom(field.QueryBuilderType)))
                        continue;
    
                    parentTypes?.Add(builderType);
    
                    var queryBuilder = InitializeChildBuilder(builderType, field.QueryBuilderType, parentTypes);
    
                    var includeFragmentMethods = field.QueryBuilderType.GetMethods().Where(IsIncludeFragmentMethod);
    
                    foreach (var includeFragmentMethod in includeFragmentMethods)
                        includeFragmentMethod.Invoke(queryBuilder, new object[] { InitializeChildBuilder(builderType, includeFragmentMethod.GetParameters()[0].ParameterType, parentTypes) });
    
                    IncludeObjectField(field.Name, queryBuilder, null);
                }
            }
        }
    
        private static GraphQlQueryBuilder InitializeChildBuilder(Type parentQueryBuilderType, Type queryBuilderType, List<Type> parentTypes)
        {
            var queryBuilder = (GraphQlQueryBuilder)Activator.CreateInstance(queryBuilderType);
            queryBuilder.IncludeFields(queryBuilder.AllFields, parentTypes ?? new List<Type> { parentQueryBuilderType });
            return queryBuilder;
        }
    
        private static bool IsIncludeFragmentMethod(MethodInfo methodInfo)
        {
            if (!methodInfo.Name.StartsWith("With") || !methodInfo.Name.EndsWith("Fragment"))
                return false;
    
            var parameters = methodInfo.GetParameters();
            return parameters.Length == 1 && parameters[0].ParameterType.IsSubclassOf(typeof(GraphQlQueryBuilder));
        }
    
        protected void AddParameter<T>(GraphQlQueryParameter<T> parameter)
        {
            if (_queryParameters == null)
                _queryParameters = new List<QueryBuilderArgumentInfo>();
            
            _queryParameters.Add(new QueryBuilderArgumentInfo { ArgumentValue = parameter, FormatMask = parameter.FormatMask });
        }
    
        private abstract class GraphQlFieldCriteria
        {
            private readonly IList<QueryBuilderArgumentInfo> _args;
            private readonly GraphQlDirective[] _directives;
    
            protected readonly string FieldName;
    
            protected static string GetIndentation(Formatting formatting, int level, byte indentationSize) =>
                formatting == Formatting.Indented ? GraphQlQueryHelper.GetIndentation(level, indentationSize) : null;
    
            protected GraphQlFieldCriteria(string fieldName, IList<QueryBuilderArgumentInfo> args)
            {
                FieldName = fieldName;
                _args = args;
            }
    
            public abstract string Build(Formatting formatting, int level, byte indentationSize);
    
            protected string BuildArgumentClause(Formatting formatting, int level, byte indentationSize)
            {
                var separator = formatting == Formatting.Indented ? " " : null;
                var argumentCount = _args?.Count ?? 0;
                if (argumentCount == 0)
                    return String.Empty;
    
                var arguments =
                    _args.Select(
                        a => $"{a.ArgumentName}:{separator}{(a.ArgumentValue.Value != null ? GraphQlQueryHelper.BuildArgumentValue(a.ArgumentValue.Value, a.FormatMask, formatting, level, indentationSize) : "$" + a.ArgumentValue.Name)}");
    
                return $"({String.Join($",{separator}", arguments)})";
            }
        }
    
        private class GraphQlScalarFieldCriteria : GraphQlFieldCriteria
        {
            public GraphQlScalarFieldCriteria(string fieldName, IList<QueryBuilderArgumentInfo> args)
                : base(fieldName, args)
            {
            }
    
            public override string Build(Formatting formatting, int level, byte indentationSize) =>
                GetIndentation(formatting, level, indentationSize) +
                FieldName +
                BuildArgumentClause(formatting, level, indentationSize);
        }
    
        private class GraphQlObjectFieldCriteria : GraphQlFieldCriteria
        {
            private readonly GraphQlQueryBuilder _objectQueryBuilder;
    
            public GraphQlObjectFieldCriteria(string fieldName, GraphQlQueryBuilder objectQueryBuilder, IList<QueryBuilderArgumentInfo> args)
                : base(fieldName, args)
            {
                _objectQueryBuilder = objectQueryBuilder;
            }
    
            public override string Build(Formatting formatting, int level, byte indentationSize) =>
                _objectQueryBuilder._fieldCriteria.Count > 0 || _objectQueryBuilder._fragments?.Count > 0
                    ? GetIndentation(formatting, level, indentationSize) + FieldName +
                      BuildArgumentClause(formatting, level, indentationSize) + _objectQueryBuilder.Build(formatting, level + 1, indentationSize)
                    : null;
        }
    
        private class GraphQlFragmentCriteria : GraphQlFieldCriteria
        {
            private readonly GraphQlQueryBuilder _objectQueryBuilder;
    
            public GraphQlFragmentCriteria(GraphQlQueryBuilder objectQueryBuilder) : base(objectQueryBuilder.TypeName, null)
            {
                _objectQueryBuilder = objectQueryBuilder;
            }
    
            public override string Build(Formatting formatting, int level, byte indentationSize) =>
                _objectQueryBuilder._fieldCriteria.Count == 0
                    ? null
                    : GetIndentation(formatting, level, indentationSize) + "..." + (formatting == Formatting.Indented ? " " : null) + "on " +
                      FieldName + BuildArgumentClause(formatting, level, indentationSize) + _objectQueryBuilder.Build(formatting, level + 1, indentationSize);
        }
    }
    
    public abstract class GraphQlQueryBuilder<TQueryBuilder> : GraphQlQueryBuilder where TQueryBuilder : GraphQlQueryBuilder<TQueryBuilder>
    {
        protected GraphQlQueryBuilder(string operationType = null, string operationName = null) : base(operationType, operationName)
        {
        }
    
        public TQueryBuilder WithAllFields()
        {
            IncludeAllFields();
            return (TQueryBuilder)this;
        }
    
        public TQueryBuilder WithAllScalarFields()
        {
            IncludeFields(AllFields.Where(f => !f.IsComplex));
            return (TQueryBuilder)this;
        }
    
        public TQueryBuilder ExceptField(string fieldName)
        {
            ExcludeField(fieldName);
            return (TQueryBuilder)this;
        }
    
        public TQueryBuilder WithTypeName()
        {
            IncludeScalarField("__typename", null);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithScalarField(string fieldName, IList<QueryBuilderArgumentInfo> args = null)
        {
            IncludeScalarField(fieldName, args);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithObjectField(string fieldName, GraphQlQueryBuilder queryBuilder, IList<QueryBuilderArgumentInfo> args = null)
        {
            IncludeObjectField(fieldName, queryBuilder, args);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithFragment(GraphQlQueryBuilder queryBuilder)
        {
            IncludeFragment(queryBuilder);
            return (TQueryBuilder)this;
        }
    
        protected TQueryBuilder WithParameterInternal<T>(GraphQlQueryParameter<T> parameter)
        {
            AddParameter(parameter);
            return (TQueryBuilder)this;
        }
    }
    
    public abstract class GraphQlResponse<TDataContract>
    {
        public TDataContract Data { get; set; }
        public ICollection<QueryError> Errors { get; set; }
    }
    
    public class QueryError
    {
        public string Message { get; set; }
        public ICollection<ErrorLocation> Locations { get; set; }
    }
    
    public class ErrorLocation
    {
        public int Line { get; set; }
        public int Column { get; set; }
    }
    
    public class AbstractConverter<TReal, TAbstract> : JsonConverter<TReal> where TReal : TAbstract {
        public override void WriteJson(JsonWriter writer, TReal value, JsonSerializer serializer) =>
            throw new NotImplementedException();
    
        public override TReal ReadJson(JsonReader reader, Type objectType, TReal existingValue, bool hasExistingValue,
            JsonSerializer serializer) {
            if (objectType != typeof(TAbstract)) {
                serializer.ContractResolver.ResolveContract(objectType).Converter = null;
            }
    
            return serializer.Deserialize<TReal>(reader);
        }
    }
    
    public class ConcreteConverter<TReal> : JsonConverter<TReal> {
        public override void WriteJson(JsonWriter writer, TReal value, JsonSerializer serializer) =>
            throw new NotImplementedException();
    
        public override TReal ReadJson(JsonReader reader, Type objectType, TReal existingValue, bool hasExistingValue,
            JsonSerializer serializer) {
    
            serializer.ContractResolver.ResolveContract(objectType).Converter = null;
            return serializer.Deserialize<TReal>(reader);
        }
    }
    #endregion

    #region GraphQL type helpers
    public static class GraphQlTypes
    {
        public const string Boolean = "Boolean";
        public const string DateTime = "DateTime";
        public const string Decimal = "Decimal";
        public const string Float = "Float";
        public const string Int = "Int";
        public const string Long = "Long";
        public const string String = "String";

        public const string ApplyPolicy = "ApplyPolicy";
        public const string cases_AgentRoleType = "cases_AgentRoleType";
        public const string cases_AttachedRuleAllowAction = "cases_AttachedRuleAllowAction";
        public const string cases_CurrencyCode = "cases_CurrencyCode";
        public const string cases_FieldsWhereCondition = "cases_FieldsWhereCondition";
        public const string cases_OrderByType = "cases_OrderByType";

        public const string _SchemaDefinition = "_SchemaDefinition";
        public const string cases_AddQuotationPayload = "cases_AddQuotationPayload";
        public const string cases_AssignAdminToCasePayload = "cases_AssignAdminToCasePayload";
        public const string cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilter = "cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilter";
        public const string cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilter = "cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilter";
        public const string cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilter = "cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilter";
        public const string cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilter = "cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilter";
        public const string cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilter = "cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilter";
        public const string cases_Broker = "cases_Broker";
        public const string cases_casesAttachedCommandPermission = "cases_casesAttachedCommandPermission";
        public const string cases_casesAttachedExternalApiCall = "cases_casesAttachedExternalApiCall";
        public const string cases_casesAttachedFilter = "cases_casesAttachedFilter";
        public const string cases_casesAttachedObjectValidation = "cases_casesAttachedObjectValidation";
        public const string cases_casesAttachedPostHandleValidation = "cases_casesAttachedPostHandleValidation";
        public const string cases_Counter = "cases_Counter";
        public const string cases_CreatedStatus = "cases_CreatedStatus";
        public const string cases_Error = "cases_Error";
        public const string cases_ExternalApiCall = "cases_ExternalApiCall";
        public const string cases_GenericBroker8QueryInterface = "cases_GenericBroker8QueryInterface";
        public const string cases_GenericCounter8QueryInterface = "cases_GenericCounter8QueryInterface";
        public const string cases_KeyValuePairOfStringAndInt64 = "cases_KeyValuePairOfStringAndInt64";
        public const string cases_KeyValuePairOfStringAndString = "cases_KeyValuePairOfStringAndString";
        public const string cases_Result = "cases_Result";
        public const string cases_ResultOfCreatedStatus = "cases_ResultOfCreatedStatus";
        public const string cases_ResultOfInt64 = "cases_ResultOfInt64";
        public const string cases_ResultOfString = "cases_ResultOfString";
        public const string ConvertOfferToApplicationPayload = "ConvertOfferToApplicationPayload";
        public const string Mutation = "Mutation";
        public const string Query = "Query";

        public const string cases_AddQuotationInput = "cases_AddQuotationInput";
        public const string cases_AdminProductIdInput = "cases_AdminProductIdInput";
        public const string cases_AdminSetReadOnlyCommandInput = "cases_AdminSetReadOnlyCommandInput";
        public const string cases_AdminSetSystemDatesCommandInput = "cases_AdminSetSystemDatesCommandInput";
        public const string cases_AdminUpdateOfferInput = "cases_AdminUpdateOfferInput";
        public const string cases_AdminUpdatePremiumInput = "cases_AdminUpdatePremiumInput";
        public const string cases_AgentCreateCaseInput = "cases_AgentCreateCaseInput";
        public const string cases_AssignAdminToCaseInput = "cases_AssignAdminToCaseInput";
        public const string cases_AssignAgentInput = "cases_AssignAgentInput";
        public const string cases_AssignAgentsInput = "cases_AssignAgentsInput";
        public const string cases_AssignHandlersInput = "cases_AssignHandlersInput";
        public const string cases_BrokerFilterInput = "cases_BrokerFilterInput";
        public const string cases_BrokerUpsertInput = "cases_BrokerUpsertInput";
        public const string cases_cases_AttachedRuleFilterInput = "cases_cases_AttachedRuleFilterInput";
        public const string cases_casesAttachedCommandPermissionInput = "cases_casesAttachedCommandPermissionInput";
        public const string cases_casesAttachedExternalApiCallInput = "cases_casesAttachedExternalApiCallInput";
        public const string cases_casesAttachedFilterInput = "cases_casesAttachedFilterInput";
        public const string cases_casesAttachedObjectValidationInput = "cases_casesAttachedObjectValidationInput";
        public const string cases_casesAttachedPostHandleValidationInput = "cases_casesAttachedPostHandleValidationInput";
        public const string cases_CounterFilterInput = "cases_CounterFilterInput";
        public const string cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput = "cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput";
        public const string cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput = "cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput";
        public const string cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput = "cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput";
        public const string cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput = "cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput";
        public const string cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput = "cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput";
        public const string cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput = "cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput";
        public const string cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput = "cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput";
        public const string cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput = "cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput";
        public const string cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput = "cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput";
        public const string cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput = "cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput";
        public const string cases_ExternalApiCallInput = "cases_ExternalApiCallInput";
        public const string cases_FieldsWhereInput = "cases_FieldsWhereInput";
        public const string cases_GenericBroker3BatchInput = "cases_GenericBroker3BatchInput";
        public const string cases_GenericBrokerFilterInput = "cases_GenericBrokerFilterInput";
        public const string cases_GenericBrokerQueryInput = "cases_GenericBrokerQueryInput";
        public const string cases_GenericCases_AttachedRuleFilterInput = "cases_GenericCases_AttachedRuleFilterInput";
        public const string cases_GenericCases_AttachedRuleQueryInput = "cases_GenericCases_AttachedRuleQueryInput";
        public const string cases_GenericCounterFilterInput = "cases_GenericCounterFilterInput";
        public const string cases_GenericCounterQueryInput = "cases_GenericCounterQueryInput";
        public const string cases_GroupByInput = "cases_GroupByInput";
        public const string cases_IncreaseCounterCommandInput = "cases_IncreaseCounterCommandInput";
        public const string cases_KeyScalarValueInput = "cases_KeyScalarValueInput";
        public const string cases_KeyValuePairOfStringAndStringInput = "cases_KeyValuePairOfStringAndStringInput";
        public const string cases_OrderByInput = "cases_OrderByInput";
        public const string cases_ProductIdInput = "cases_ProductIdInput";
        public const string cases_ProductTreeRecordInput = "cases_ProductTreeRecordInput";
        public const string cases_QuotationOfferInput = "cases_QuotationOfferInput";
        public const string cases_QuotationProposalInput = "cases_QuotationProposalInput";
        public const string cases_RecordQuoteCommandInput = "cases_RecordQuoteCommandInput";
        public const string cases_ScalarValueInput = "cases_ScalarValueInput";
        public const string cases_SettableOfAdminProductIdInput = "cases_SettableOfAdminProductIdInput";
        public const string cases_SettableOfAdminUpdatePremiumInput = "cases_SettableOfAdminUpdatePremiumInput";
        public const string cases_SettableOfBooleanInput = "cases_SettableOfBooleanInput";
        public const string cases_SettableOfIEnumerableOfStringInput = "cases_SettableOfIEnumerableOfStringInput";
        public const string cases_SettableOfNullableOfBooleanInput = "cases_SettableOfNullableOfBooleanInput";
        public const string cases_SettableOfNullableOfCurrencyCodeInput = "cases_SettableOfNullableOfCurrencyCodeInput";
        public const string cases_SettableOfNullableOfDateTimeInput = "cases_SettableOfNullableOfDateTimeInput";
        public const string cases_SettableOfNullableOfDecimalInput = "cases_SettableOfNullableOfDecimalInput";
        public const string cases_SettableOfStringInput = "cases_SettableOfStringInput";
        public const string cases_UnassignAgentsInput = "cases_UnassignAgentsInput";
        public const string cases_UnassignHandlersInput = "cases_UnassignHandlersInput";
        public const string cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput = "cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput";
        public const string cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput = "cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput";
        public const string cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput = "cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput";
        public const string cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput = "cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput";
        public const string cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput = "cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput";
        public const string ConvertOfferToApplicationInput = "ConvertOfferToApplicationInput";

        public static readonly IReadOnlyDictionary<Type, string> ReverseMapping =
            new Dictionary<Type, string>
            {
                { typeof(ApplyPolicy), "ApplyPolicy" },
                { typeof(cases_AgentRoleType), "cases_AgentRoleType" },
                { typeof(cases_AttachedRuleAllowAction), "cases_AttachedRuleAllowAction" },
                { typeof(cases_CurrencyCode), "cases_CurrencyCode" },
                { typeof(cases_FieldsWhereCondition), "cases_FieldsWhereCondition" },
                { typeof(cases_OrderByType), "cases_OrderByType" },
                { typeof(string), "String" },
                { typeof(bool), "Boolean" },
                { typeof(DateTime), "DateTime" },
                { typeof(int), "Int" },
                { typeof(cases_AddQuotationInput), "cases_AddQuotationInput" },
                { typeof(cases_AdminProductIdInput), "cases_AdminProductIdInput" },
                { typeof(cases_AdminSetReadOnlyCommandInput), "cases_AdminSetReadOnlyCommandInput" },
                { typeof(cases_AdminSetSystemDatesCommandInput), "cases_AdminSetSystemDatesCommandInput" },
                { typeof(cases_AdminUpdateOfferInput), "cases_AdminUpdateOfferInput" },
                { typeof(cases_AdminUpdatePremiumInput), "cases_AdminUpdatePremiumInput" },
                { typeof(cases_AgentCreateCaseInput), "cases_AgentCreateCaseInput" },
                { typeof(cases_AssignAdminToCaseInput), "cases_AssignAdminToCaseInput" },
                { typeof(cases_AssignAgentInput), "cases_AssignAgentInput" },
                { typeof(cases_AssignAgentsInput), "cases_AssignAgentsInput" },
                { typeof(cases_AssignHandlersInput), "cases_AssignHandlersInput" },
                { typeof(cases_BrokerFilterInput), "cases_BrokerFilterInput" },
                { typeof(cases_BrokerUpsertInput), "cases_BrokerUpsertInput" },
                { typeof(cases_cases_AttachedRuleFilterInput), "cases_cases_AttachedRuleFilterInput" },
                { typeof(cases_casesAttachedCommandPermissionInput), "cases_casesAttachedCommandPermissionInput" },
                { typeof(cases_casesAttachedExternalApiCallInput), "cases_casesAttachedExternalApiCallInput" },
                { typeof(cases_casesAttachedFilterInput), "cases_casesAttachedFilterInput" },
                { typeof(cases_casesAttachedObjectValidationInput), "cases_casesAttachedObjectValidationInput" },
                { typeof(cases_casesAttachedPostHandleValidationInput), "cases_casesAttachedPostHandleValidationInput" },
                { typeof(cases_CounterFilterInput), "cases_CounterFilterInput" },
                { typeof(cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput), "cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput" },
                { typeof(cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput), "cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput" },
                { typeof(cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput), "cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput" },
                { typeof(cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput), "cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput" },
                { typeof(cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput), "cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput" },
                { typeof(cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput), "cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput" },
                { typeof(cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput), "cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput" },
                { typeof(cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput), "cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput" },
                { typeof(cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput), "cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput" },
                { typeof(cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput), "cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput" },
                { typeof(cases_ExternalApiCallInput), "cases_ExternalApiCallInput" },
                { typeof(cases_FieldsWhereInput), "cases_FieldsWhereInput" },
                { typeof(cases_GenericBroker3BatchInput), "cases_GenericBroker3BatchInput" },
                { typeof(cases_GenericBrokerFilterInput), "cases_GenericBrokerFilterInput" },
                { typeof(cases_GenericBrokerQueryInput), "cases_GenericBrokerQueryInput" },
                { typeof(cases_GenericCases_AttachedRuleFilterInput), "cases_GenericCases_AttachedRuleFilterInput" },
                { typeof(cases_GenericCases_AttachedRuleQueryInput), "cases_GenericCases_AttachedRuleQueryInput" },
                { typeof(cases_GenericCounterFilterInput), "cases_GenericCounterFilterInput" },
                { typeof(cases_GenericCounterQueryInput), "cases_GenericCounterQueryInput" },
                { typeof(cases_GroupByInput), "cases_GroupByInput" },
                { typeof(cases_IncreaseCounterCommandInput), "cases_IncreaseCounterCommandInput" },
                { typeof(cases_KeyScalarValueInput), "cases_KeyScalarValueInput" },
                { typeof(cases_KeyValuePairOfStringAndStringInput), "cases_KeyValuePairOfStringAndStringInput" },
                { typeof(cases_OrderByInput), "cases_OrderByInput" },
                { typeof(cases_ProductIdInput), "cases_ProductIdInput" },
                { typeof(cases_ProductTreeRecordInput), "cases_ProductTreeRecordInput" },
                { typeof(cases_QuotationOfferInput), "cases_QuotationOfferInput" },
                { typeof(cases_QuotationProposalInput), "cases_QuotationProposalInput" },
                { typeof(cases_RecordQuoteCommandInput), "cases_RecordQuoteCommandInput" },
                { typeof(cases_ScalarValueInput), "cases_ScalarValueInput" },
                { typeof(cases_SettableOfAdminProductIdInput), "cases_SettableOfAdminProductIdInput" },
                { typeof(cases_SettableOfAdminUpdatePremiumInput), "cases_SettableOfAdminUpdatePremiumInput" },
                { typeof(cases_SettableOfBooleanInput), "cases_SettableOfBooleanInput" },
                { typeof(cases_SettableOfIEnumerableOfStringInput), "cases_SettableOfIEnumerableOfStringInput" },
                { typeof(cases_SettableOfNullableOfBooleanInput), "cases_SettableOfNullableOfBooleanInput" },
                { typeof(cases_SettableOfNullableOfCurrencyCodeInput), "cases_SettableOfNullableOfCurrencyCodeInput" },
                { typeof(cases_SettableOfNullableOfDateTimeInput), "cases_SettableOfNullableOfDateTimeInput" },
                { typeof(cases_SettableOfNullableOfDecimalInput), "cases_SettableOfNullableOfDecimalInput" },
                { typeof(cases_SettableOfStringInput), "cases_SettableOfStringInput" },
                { typeof(cases_UnassignAgentsInput), "cases_UnassignAgentsInput" },
                { typeof(cases_UnassignHandlersInput), "cases_UnassignHandlersInput" },
                { typeof(cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput), "cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput" },
                { typeof(cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput), "cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput" },
                { typeof(cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput), "cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput" },
                { typeof(cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput), "cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput" },
                { typeof(cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput), "cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput" },
                { typeof(ConvertOfferToApplicationInput), "ConvertOfferToApplicationInput" }
            };
}
    #endregion

    #region enums
    public enum ApplyPolicy
    {
        BEFORE_RESOLVER,
        AFTER_RESOLVER
    }

    public enum cases_AgentRoleType
    {
        PRIMARY,
        SECONDARY,
        SERVICING
    }

    public enum cases_OrderByType
    {
        ASC,
        DSC
    }

    public enum cases_AttachedRuleAllowAction
    {
        ALLOW,
        DENY
    }

    public enum cases_CurrencyCode
    {
        UNDEFINED,
        AFN,
        EUR,
        ALL,
        DZD,
        USD,
        AOA,
        XCD,
        ARS,
        AMD,
        AWG,
        AUD,
        AZN,
        BSD,
        BHD,
        BDT,
        BBD,
        BYN,
        BZD,
        XOF,
        BMD,
        INR,
        BTN,
        BOB,
        BOV,
        BAM,
        BWP,
        NOK,
        BRL,
        BND,
        BGN,
        BIF,
        CVE,
        KHR,
        XAF,
        CAD,
        KYD,
        CLP,
        CLF,
        CNY,
        COP,
        COU,
        KMF,
        CDF,
        NZD,
        CRC,
        HRK,
        CUP,
        CUC,
        ANG,
        CZK,
        DKK,
        DJF,
        DOP,
        EGP,
        SVC,
        ERN,
        ETB,
        FKP,
        FJD,
        XPF,
        GMD,
        GEL,
        GHS,
        GIP,
        GTQ,
        GBP,
        GNF,
        GYD,
        HTG,
        HNL,
        HKD,
        HUF,
        ISK,
        IDR,
        XDR,
        IRR,
        IQD,
        ILS,
        JMD,
        JPY,
        JOD,
        KZT,
        KES,
        KPW,
        KRW,
        KWD,
        KGS,
        LAK,
        LBP,
        LSL,
        ZAR,
        LRD,
        LYD,
        CHF,
        MOP,
        MKD,
        MGA,
        MWK,
        MYR,
        MVR,
        MRO,
        MUR,
        XUA,
        MXN,
        MXV,
        MDL,
        MNT,
        MAD,
        MZN,
        MMK,
        NAD,
        NPR,
        NIO,
        NGN,
        OMR,
        PKR,
        PAB,
        PGK,
        PYG,
        PEN,
        PHP,
        PLN,
        QAR,
        RON,
        RUB,
        RWF,
        SHP,
        WST,
        STD,
        SAR,
        RSD,
        SCR,
        SLL,
        SGD,
        XSU,
        SBD,
        SOS,
        SSP,
        LKR,
        SDG,
        SRD,
        SZL,
        SEK,
        CHE,
        CHW,
        SYP,
        TWD,
        TJS,
        TZS,
        THB,
        TOP,
        TTD,
        TND,
        TRY,
        TMT,
        UGX,
        UAH,
        AED,
        USN,
        UYU,
        UYI,
        UZS,
        VUV,
        VEF,
        VND,
        YER,
        ZMW,
        ZWL,
        XBA,
        XBB,
        XBC,
        XBD,
        XTS,
        XXX,
        XAU,
        XPD,
        XPT,
        XAG,
        AFA,
        FIM,
        ALK,
        ADP,
        ESP,
        FRF,
        AOK,
        AON,
        AOR,
        ARA,
        ARP,
        ARY,
        RUR,
        ATS,
        AYM,
        AZM,
        BYR,
        BYB,
        BEC,
        BEF,
        BEL,
        BOP,
        BAD,
        BRB,
        BRC,
        BRE,
        BRN,
        BRR,
        BGJ,
        BGK,
        BGL,
        BUK,
        CNX,
        HRD,
        CYP,
        CSJ,
        CSK,
        ECS,
        ECV,
        GQE,
        EEK,
        XEU,
        GEK,
        DDM,
        DEM,
        GHC,
        GHP,
        GRD,
        GNE,
        GNS,
        GWE,
        GWP,
        ITL,
        ISJ,
        IEP,
        ILP,
        ILR,
        LAJ,
        LVL,
        LVR,
        LSM,
        ZAL,
        LTL,
        LTT,
        LUC,
        LUF,
        LUL,
        MGF,
        MVQ,
        MLF,
        MTL,
        MTP,
        MZE,
        MZM,
        NLG,
        NIC,
        PEH,
        PEI,
        PES,
        PLZ,
        PTE,
        ROK,
        ROL,
        CSD,
        SKK,
        SIT,
        RHD,
        ESA,
        ESB,
        SDD,
        SDP,
        SRG,
        CHC,
        TJR,
        TPE,
        TRL,
        TMM,
        UGS,
        UGW,
        UAK,
        SUR,
        USS,
        UYN,
        UYP,
        VEB,
        VNC,
        YDD,
        YUD,
        YUM,
        YUN,
        ZRN,
        ZRZ,
        ZMK,
        ZWC,
        ZWD,
        ZWN,
        ZWR,
        XFO,
        XRE,
        XFU
    }

    public enum cases_FieldsWhereCondition
    {
        EQUALS,
        IN,
        STRING_CONTAINS,
        ARRAY_CONTAINS,
        LESS_THAN,
        GREATER_THAN,
        EXISTS
    }
    #endregion

    #nullable enable
    #region builder classes
    public class _SchemaDefinitionBuilder : GraphQlQueryBuilder<_SchemaDefinitionBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "name" },
            new FieldMetadata { Name = "document" },
            new FieldMetadata { Name = "extensionDocuments", IsComplex = true }
        };

        protected override string TypeName { get; } = "_SchemaDefinition";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public _SchemaDefinitionBuilder name() => WithScalarField("name");

        public _SchemaDefinitionBuilder document() => WithScalarField("document");

        public _SchemaDefinitionBuilder extensionDocuments() => WithScalarField("extensionDocuments");
    }

    public class QueryBuilder : GraphQlQueryBuilder<QueryBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "_schemaDefinition", IsComplex = true, QueryBuilderType = typeof(_SchemaDefinitionBuilder) },
            new FieldMetadata { Name = "countersQuery", IsComplex = true, QueryBuilderType = typeof(cases_GenericCounter8QueryInterfaceBuilder) },
            new FieldMetadata { Name = "brokersQuery", IsComplex = true, QueryBuilderType = typeof(cases_GenericBroker8QueryInterfaceBuilder) },
            new FieldMetadata { Name = "cases_AttachedCommandPermissionsQuery", IsComplex = true, QueryBuilderType = typeof(cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder) },
            new FieldMetadata { Name = "cases_AttachedExternalApiCallsQuery", IsComplex = true, QueryBuilderType = typeof(cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder) },
            new FieldMetadata { Name = "cases_AttachedFiltersQuery", IsComplex = true, QueryBuilderType = typeof(cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder) },
            new FieldMetadata { Name = "cases_AttachedObjectValidationsQuery", IsComplex = true, QueryBuilderType = typeof(cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder) },
            new FieldMetadata { Name = "cases_AttachedPostHandleValidationsQuery", IsComplex = true, QueryBuilderType = typeof(cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder) }
        };

        protected override string TypeName { get; } = "Query";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public QueryBuilder(string? operationName = null) : base("query", operationName)
        {
        }

        public QueryBuilder WithParameter<T>(GraphQlQueryParameter<T> parameter) => WithParameterInternal(parameter);

        public record _schemaDefinitionArgs(string configuration);
        public QueryBuilder _schemaDefinition(_schemaDefinitionArgs arguments, _SchemaDefinitionBuilder _SchemaDefinitionBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            args.Add(new QueryBuilderArgumentInfo { ArgumentName = "configuration", ArgumentValue = new GraphQlQueryParameter<string>("configuration", arguments.configuration) } );
            return WithObjectField("_schemaDefinition", _SchemaDefinitionBuilder, args);
        }

        public record countersQueryArgs(cases_GenericCounterQueryInput? where = null);
        public QueryBuilder countersQuery(countersQueryArgs arguments, cases_GenericCounter8QueryInterfaceBuilder cases_GenericCounter8QueryInterfaceBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.where != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "where", ArgumentValue = new GraphQlQueryParameter<cases_GenericCounterQueryInput?>("where", arguments.where) } );

            return WithObjectField("countersQuery", cases_GenericCounter8QueryInterfaceBuilder, args);
        }

        public record brokersQueryArgs(cases_GenericBrokerQueryInput? where = null);
        public QueryBuilder brokersQuery(brokersQueryArgs arguments, cases_GenericBroker8QueryInterfaceBuilder cases_GenericBroker8QueryInterfaceBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.where != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "where", ArgumentValue = new GraphQlQueryParameter<cases_GenericBrokerQueryInput?>("where", arguments.where) } );

            return WithObjectField("brokersQuery", cases_GenericBroker8QueryInterfaceBuilder, args);
        }

        public record cases_AttachedCommandPermissionsQueryArgs(cases_GenericCases_AttachedRuleQueryInput? queryArguments = null);
        public QueryBuilder cases_AttachedCommandPermissionsQuery(cases_AttachedCommandPermissionsQueryArgs arguments, cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.queryArguments != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "queryArguments", ArgumentValue = new GraphQlQueryParameter<cases_GenericCases_AttachedRuleQueryInput?>("queryArguments", arguments.queryArguments) } );

            return WithObjectField("cases_AttachedCommandPermissionsQuery", cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder, args);
        }

        public record cases_AttachedExternalApiCallsQueryArgs(cases_GenericCases_AttachedRuleQueryInput? queryArguments = null);
        public QueryBuilder cases_AttachedExternalApiCallsQuery(cases_AttachedExternalApiCallsQueryArgs arguments, cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.queryArguments != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "queryArguments", ArgumentValue = new GraphQlQueryParameter<cases_GenericCases_AttachedRuleQueryInput?>("queryArguments", arguments.queryArguments) } );

            return WithObjectField("cases_AttachedExternalApiCallsQuery", cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder, args);
        }

        public record cases_AttachedFiltersQueryArgs(cases_GenericCases_AttachedRuleQueryInput? queryArguments = null);
        public QueryBuilder cases_AttachedFiltersQuery(cases_AttachedFiltersQueryArgs arguments, cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.queryArguments != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "queryArguments", ArgumentValue = new GraphQlQueryParameter<cases_GenericCases_AttachedRuleQueryInput?>("queryArguments", arguments.queryArguments) } );

            return WithObjectField("cases_AttachedFiltersQuery", cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder, args);
        }

        public record cases_AttachedObjectValidationsQueryArgs(cases_GenericCases_AttachedRuleQueryInput? queryArguments = null);
        public QueryBuilder cases_AttachedObjectValidationsQuery(cases_AttachedObjectValidationsQueryArgs arguments, cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.queryArguments != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "queryArguments", ArgumentValue = new GraphQlQueryParameter<cases_GenericCases_AttachedRuleQueryInput?>("queryArguments", arguments.queryArguments) } );

            return WithObjectField("cases_AttachedObjectValidationsQuery", cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder, args);
        }

        public record cases_AttachedPostHandleValidationsQueryArgs(cases_GenericCases_AttachedRuleQueryInput? queryArguments = null);
        public QueryBuilder cases_AttachedPostHandleValidationsQuery(cases_AttachedPostHandleValidationsQueryArgs arguments, cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.queryArguments != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "queryArguments", ArgumentValue = new GraphQlQueryParameter<cases_GenericCases_AttachedRuleQueryInput?>("queryArguments", arguments.queryArguments) } );

            return WithObjectField("cases_AttachedPostHandleValidationsQuery", cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder, args);
        }
    }

    public class MutationBuilder : GraphQlQueryBuilder<MutationBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "brokersMutationCreate", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "brokersMutationUpdate", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "brokersMutationDelete", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "brokersMutationBatch", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "caseMutationSetSystemDates", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "caseMutationSetReadOnly", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "counterMutationIncrease", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfInt64Builder) },
            new FieldMetadata { Name = "offerMutationRecordQuote", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "offerMutationSecureUpdateOffer", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AssignHandlers", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_UnassignHandlers", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "convertOfferToApplication", IsComplex = true, QueryBuilderType = typeof(ConvertOfferToApplicationPayloadBuilder) },
            new FieldMetadata { Name = "agentMutationAgentCreateCase", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfStringBuilder) },
            new FieldMetadata { Name = "cases_AssignAgents", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_UnassignAgents", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "addQuotation", IsComplex = true, QueryBuilderType = typeof(cases_AddQuotationPayloadBuilder) },
            new FieldMetadata { Name = "salesAdminMutationAssignAdminToCase", IsComplex = true, QueryBuilderType = typeof(cases_AssignAdminToCasePayloadBuilder) },
            new FieldMetadata { Name = "cases_AttachedCommandPermissionMutationCreate", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_AttachedCommandPermissionMutationUpdate", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedCommandPermissionMutationDelete", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedExternalApiCallMutationCreate", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_AttachedExternalApiCallMutationUpdate", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedExternalApiCallMutationDelete", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedFilterMutationCreate", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_AttachedFilterMutationUpdate", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedFilterMutationDelete", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedObjectValidationMutationCreate", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_AttachedObjectValidationMutationUpdate", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedObjectValidationMutationDelete", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedPostHandleValidationMutationCreate", IsComplex = true, QueryBuilderType = typeof(cases_ResultOfCreatedStatusBuilder) },
            new FieldMetadata { Name = "cases_AttachedPostHandleValidationMutationUpdate", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) },
            new FieldMetadata { Name = "cases_AttachedPostHandleValidationMutationDelete", IsComplex = true, QueryBuilderType = typeof(cases_ResultBuilder) }
        };

        protected override string TypeName { get; } = "Mutation";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public MutationBuilder(string? operationName = null) : base("mutation", operationName)
        {
        }

        public MutationBuilder WithParameter<T>(GraphQlQueryParameter<T> parameter) => WithParameterInternal(parameter);

        public record brokersMutationCreateArgs(cases_BrokerUpsertInput? create = null);
        public MutationBuilder brokersMutationCreate(brokersMutationCreateArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.create != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "create", ArgumentValue = new GraphQlQueryParameter<cases_BrokerUpsertInput?>("create", arguments.create) } );

            return WithObjectField("brokersMutationCreate", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record brokersMutationUpdateArgs(cases_BrokerUpsertInput? update = null);
        public MutationBuilder brokersMutationUpdate(brokersMutationUpdateArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.update != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "update", ArgumentValue = new GraphQlQueryParameter<cases_BrokerUpsertInput?>("update", arguments.update) } );

            return WithObjectField("brokersMutationUpdate", cases_ResultBuilder, args);
        }

        public record brokersMutationDeleteArgs(cases_BrokerUpsertInput? delete = null);
        public MutationBuilder brokersMutationDelete(brokersMutationDeleteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.delete != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "delete", ArgumentValue = new GraphQlQueryParameter<cases_BrokerUpsertInput?>("delete", arguments.delete) } );

            return WithObjectField("brokersMutationDelete", cases_ResultBuilder, args);
        }

        public record brokersMutationBatchArgs(cases_GenericBroker3BatchInput? batch = null);
        public MutationBuilder brokersMutationBatch(brokersMutationBatchArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.batch != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "batch", ArgumentValue = new GraphQlQueryParameter<cases_GenericBroker3BatchInput?>("batch", arguments.batch) } );

            return WithObjectField("brokersMutationBatch", cases_ResultBuilder, args);
        }

        public record caseMutationSetSystemDatesArgs(string? caseId = null, cases_AdminSetSystemDatesCommandInput? input = null);
        public MutationBuilder caseMutationSetSystemDates(caseMutationSetSystemDatesArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.caseId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "caseId", ArgumentValue = new GraphQlQueryParameter<string?>("caseId", arguments.caseId) } );

            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AdminSetSystemDatesCommandInput?>("input", arguments.input) } );

            return WithObjectField("caseMutationSetSystemDates", cases_ResultBuilder, args);
        }

        public record caseMutationSetReadOnlyArgs(string? caseId = null, cases_AdminSetReadOnlyCommandInput? input = null);
        public MutationBuilder caseMutationSetReadOnly(caseMutationSetReadOnlyArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.caseId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "caseId", ArgumentValue = new GraphQlQueryParameter<string?>("caseId", arguments.caseId) } );

            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AdminSetReadOnlyCommandInput?>("input", arguments.input) } );

            return WithObjectField("caseMutationSetReadOnly", cases_ResultBuilder, args);
        }

        public record counterMutationIncreaseArgs(cases_IncreaseCounterCommandInput? command = null);
        public MutationBuilder counterMutationIncrease(counterMutationIncreaseArgs arguments, cases_ResultOfInt64Builder cases_ResultOfInt64Builder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_IncreaseCounterCommandInput?>("command", arguments.command) } );

            return WithObjectField("counterMutationIncrease", cases_ResultOfInt64Builder, args);
        }

        public record offerMutationRecordQuoteArgs(string? tenantId = null, string? caseId = null, cases_RecordQuoteCommandInput? command = null);
        public MutationBuilder offerMutationRecordQuote(offerMutationRecordQuoteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.tenantId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "tenantId", ArgumentValue = new GraphQlQueryParameter<string?>("tenantId", arguments.tenantId) } );

            if (arguments.caseId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "caseId", ArgumentValue = new GraphQlQueryParameter<string?>("caseId", arguments.caseId) } );

            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_RecordQuoteCommandInput?>("command", arguments.command) } );

            return WithObjectField("offerMutationRecordQuote", cases_ResultBuilder, args);
        }

        public record offerMutationSecureUpdateOfferArgs(string? caseId = null, string? proposalId = null, string? offerId = null, cases_AdminUpdateOfferInput? adminUpdateOfferInput = null);
        public MutationBuilder offerMutationSecureUpdateOffer(offerMutationSecureUpdateOfferArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.caseId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "caseId", ArgumentValue = new GraphQlQueryParameter<string?>("caseId", arguments.caseId) } );

            if (arguments.proposalId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "proposalId", ArgumentValue = new GraphQlQueryParameter<string?>("proposalId", arguments.proposalId) } );

            if (arguments.offerId != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "offerId", ArgumentValue = new GraphQlQueryParameter<string?>("offerId", arguments.offerId) } );

            if (arguments.adminUpdateOfferInput != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "adminUpdateOfferInput", ArgumentValue = new GraphQlQueryParameter<cases_AdminUpdateOfferInput?>("adminUpdateOfferInput", arguments.adminUpdateOfferInput) } );

            return WithObjectField("offerMutationSecureUpdateOffer", cases_ResultBuilder, args);
        }

        public record cases_AssignHandlersArgs(cases_AssignHandlersInput? input = null);
        public MutationBuilder cases_AssignHandlers(cases_AssignHandlersArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AssignHandlersInput?>("input", arguments.input) } );

            return WithObjectField("cases_AssignHandlers", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_UnassignHandlersArgs(cases_UnassignHandlersInput? input = null);
        public MutationBuilder cases_UnassignHandlers(cases_UnassignHandlersArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_UnassignHandlersInput?>("input", arguments.input) } );

            return WithObjectField("cases_UnassignHandlers", cases_ResultBuilder, args);
        }

        public record convertOfferToApplicationArgs(ConvertOfferToApplicationInput? input = null);
        public MutationBuilder convertOfferToApplication(convertOfferToApplicationArgs arguments, ConvertOfferToApplicationPayloadBuilder convertOfferToApplicationPayloadBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<ConvertOfferToApplicationInput?>("input", arguments.input) } );

            return WithObjectField("convertOfferToApplication", convertOfferToApplicationPayloadBuilder, args);
        }

        public record agentMutationAgentCreateCaseArgs(cases_AgentCreateCaseInput? input = null);
        public MutationBuilder agentMutationAgentCreateCase(agentMutationAgentCreateCaseArgs arguments, cases_ResultOfStringBuilder cases_ResultOfStringBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AgentCreateCaseInput?>("input", arguments.input) } );

            return WithObjectField("agentMutationAgentCreateCase", cases_ResultOfStringBuilder, args);
        }

        public record cases_AssignAgentsArgs(cases_AssignAgentsInput? input = null);
        public MutationBuilder cases_AssignAgents(cases_AssignAgentsArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AssignAgentsInput?>("input", arguments.input) } );

            return WithObjectField("cases_AssignAgents", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_UnassignAgentsArgs(cases_UnassignAgentsInput? input = null);
        public MutationBuilder cases_UnassignAgents(cases_UnassignAgentsArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_UnassignAgentsInput?>("input", arguments.input) } );

            return WithObjectField("cases_UnassignAgents", cases_ResultBuilder, args);
        }

        public record addQuotationArgs(cases_AddQuotationInput? input = null);
        public MutationBuilder addQuotation(addQuotationArgs arguments, cases_AddQuotationPayloadBuilder cases_AddQuotationPayloadBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AddQuotationInput?>("input", arguments.input) } );

            return WithObjectField("addQuotation", cases_AddQuotationPayloadBuilder, args);
        }

        public record salesAdminMutationAssignAdminToCaseArgs(cases_AssignAdminToCaseInput? input = null);
        public MutationBuilder salesAdminMutationAssignAdminToCase(salesAdminMutationAssignAdminToCaseArgs arguments, cases_AssignAdminToCasePayloadBuilder cases_AssignAdminToCasePayloadBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.input != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "input", ArgumentValue = new GraphQlQueryParameter<cases_AssignAdminToCaseInput?>("input", arguments.input) } );

            return WithObjectField("salesAdminMutationAssignAdminToCase", cases_AssignAdminToCasePayloadBuilder, args);
        }

        public record cases_AttachedCommandPermissionMutationCreateArgs(cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput? command = null);
        public MutationBuilder cases_AttachedCommandPermissionMutationCreate(cases_AttachedCommandPermissionMutationCreateArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedCommandPermissionMutationCreate", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_AttachedCommandPermissionMutationUpdateArgs(cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput? command = null);
        public MutationBuilder cases_AttachedCommandPermissionMutationUpdate(cases_AttachedCommandPermissionMutationUpdateArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedCommandPermissionMutationUpdate", cases_ResultBuilder, args);
        }

        public record cases_AttachedCommandPermissionMutationDeleteArgs(cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput? command = null);
        public MutationBuilder cases_AttachedCommandPermissionMutationDelete(cases_AttachedCommandPermissionMutationDeleteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedCommandPermissionMutationDelete", cases_ResultBuilder, args);
        }

        public record cases_AttachedExternalApiCallMutationCreateArgs(cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput? command = null);
        public MutationBuilder cases_AttachedExternalApiCallMutationCreate(cases_AttachedExternalApiCallMutationCreateArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedExternalApiCallMutationCreate", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_AttachedExternalApiCallMutationUpdateArgs(cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput? command = null);
        public MutationBuilder cases_AttachedExternalApiCallMutationUpdate(cases_AttachedExternalApiCallMutationUpdateArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedExternalApiCallMutationUpdate", cases_ResultBuilder, args);
        }

        public record cases_AttachedExternalApiCallMutationDeleteArgs(cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput? command = null);
        public MutationBuilder cases_AttachedExternalApiCallMutationDelete(cases_AttachedExternalApiCallMutationDeleteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedExternalApiCallMutationDelete", cases_ResultBuilder, args);
        }

        public record cases_AttachedFilterMutationCreateArgs(cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput? command = null);
        public MutationBuilder cases_AttachedFilterMutationCreate(cases_AttachedFilterMutationCreateArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedFilterMutationCreate", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_AttachedFilterMutationUpdateArgs(cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput? command = null);
        public MutationBuilder cases_AttachedFilterMutationUpdate(cases_AttachedFilterMutationUpdateArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedFilterMutationUpdate", cases_ResultBuilder, args);
        }

        public record cases_AttachedFilterMutationDeleteArgs(cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput? command = null);
        public MutationBuilder cases_AttachedFilterMutationDelete(cases_AttachedFilterMutationDeleteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedFilterMutationDelete", cases_ResultBuilder, args);
        }

        public record cases_AttachedObjectValidationMutationCreateArgs(cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput? command = null);
        public MutationBuilder cases_AttachedObjectValidationMutationCreate(cases_AttachedObjectValidationMutationCreateArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedObjectValidationMutationCreate", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_AttachedObjectValidationMutationUpdateArgs(cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput? command = null);
        public MutationBuilder cases_AttachedObjectValidationMutationUpdate(cases_AttachedObjectValidationMutationUpdateArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedObjectValidationMutationUpdate", cases_ResultBuilder, args);
        }

        public record cases_AttachedObjectValidationMutationDeleteArgs(cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput? command = null);
        public MutationBuilder cases_AttachedObjectValidationMutationDelete(cases_AttachedObjectValidationMutationDeleteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedObjectValidationMutationDelete", cases_ResultBuilder, args);
        }

        public record cases_AttachedPostHandleValidationMutationCreateArgs(cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput? command = null);
        public MutationBuilder cases_AttachedPostHandleValidationMutationCreate(cases_AttachedPostHandleValidationMutationCreateArgs arguments, cases_ResultOfCreatedStatusBuilder cases_ResultOfCreatedStatusBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedPostHandleValidationMutationCreate", cases_ResultOfCreatedStatusBuilder, args);
        }

        public record cases_AttachedPostHandleValidationMutationUpdateArgs(cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput? command = null);
        public MutationBuilder cases_AttachedPostHandleValidationMutationUpdate(cases_AttachedPostHandleValidationMutationUpdateArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedPostHandleValidationMutationUpdate", cases_ResultBuilder, args);
        }

        public record cases_AttachedPostHandleValidationMutationDeleteArgs(cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput? command = null);
        public MutationBuilder cases_AttachedPostHandleValidationMutationDelete(cases_AttachedPostHandleValidationMutationDeleteArgs arguments, cases_ResultBuilder cases_ResultBuilder)
        {
            var args = new List<QueryBuilderArgumentInfo>();
            if (arguments.command != null)
                args.Add(new QueryBuilderArgumentInfo { ArgumentName = "command", ArgumentValue = new GraphQlQueryParameter<cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput?>("command", arguments.command) } );

            return WithObjectField("cases_AttachedPostHandleValidationMutationDelete", cases_ResultBuilder, args);
        }
    }

    public class cases_CounterBuilder : GraphQlQueryBuilder<cases_CounterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "scope" },
            new FieldMetadata { Name = "counters", IsComplex = true, QueryBuilderType = typeof(cases_KeyValuePairOfStringAndInt64Builder) },
            new FieldMetadata { Name = "createdAt" },
            new FieldMetadata { Name = "lastModifiedAt" },
            new FieldMetadata { Name = "createdById" },
            new FieldMetadata { Name = "lastModifiedById" }
        };

        protected override string TypeName { get; } = "cases_Counter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_CounterBuilder id() => WithScalarField("id");

        public cases_CounterBuilder scope() => WithScalarField("scope");

        public cases_CounterBuilder counters(cases_KeyValuePairOfStringAndInt64Builder cases_KeyValuePairOfStringAndInt64Builder) => WithObjectField("counters", cases_KeyValuePairOfStringAndInt64Builder);

        public cases_CounterBuilder createdAt() => WithScalarField("createdAt");

        public cases_CounterBuilder lastModifiedAt() => WithScalarField("lastModifiedAt");

        public cases_CounterBuilder createdById() => WithScalarField("createdById");

        public cases_CounterBuilder lastModifiedById() => WithScalarField("lastModifiedById");
    }

    public class cases_BrokerBuilder : GraphQlQueryBuilder<cases_BrokerBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "fields" },
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "code" },
            new FieldMetadata { Name = "normalizedCode" },
            new FieldMetadata { Name = "name" },
            new FieldMetadata { Name = "normalizedName" },
            new FieldMetadata { Name = "channel" },
            new FieldMetadata { Name = "group" },
            new FieldMetadata { Name = "description" },
            new FieldMetadata { Name = "contactPerson" },
            new FieldMetadata { Name = "contactPersonTelNo" },
            new FieldMetadata { Name = "contactPersonFaxNo" },
            new FieldMetadata { Name = "contactPersonEmail" },
            new FieldMetadata { Name = "createdAt" },
            new FieldMetadata { Name = "lastModifiedAt" },
            new FieldMetadata { Name = "createdById" },
            new FieldMetadata { Name = "lastModifiedById" }
        };

        protected override string TypeName { get; } = "cases_Broker";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_BrokerBuilder fields() => WithScalarField("fields");

        public cases_BrokerBuilder id() => WithScalarField("id");

        public cases_BrokerBuilder code() => WithScalarField("code");

        public cases_BrokerBuilder normalizedCode() => WithScalarField("normalizedCode");

        public cases_BrokerBuilder name() => WithScalarField("name");

        public cases_BrokerBuilder normalizedName() => WithScalarField("normalizedName");

        public cases_BrokerBuilder channel() => WithScalarField("channel");

        public cases_BrokerBuilder group() => WithScalarField("group");

        public cases_BrokerBuilder description() => WithScalarField("description");

        public cases_BrokerBuilder contactPerson() => WithScalarField("contactPerson");

        public cases_BrokerBuilder contactPersonTelNo() => WithScalarField("contactPersonTelNo");

        public cases_BrokerBuilder contactPersonFaxNo() => WithScalarField("contactPersonFaxNo");

        public cases_BrokerBuilder contactPersonEmail() => WithScalarField("contactPersonEmail");

        public cases_BrokerBuilder createdAt() => WithScalarField("createdAt");

        public cases_BrokerBuilder lastModifiedAt() => WithScalarField("lastModifiedAt");

        public cases_BrokerBuilder createdById() => WithScalarField("createdById");

        public cases_BrokerBuilder lastModifiedById() => WithScalarField("lastModifiedById");
    }

    public class ConvertOfferToApplicationPayloadBuilder : GraphQlQueryBuilder<ConvertOfferToApplicationPayloadBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "generatedPolicyId" },
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "ConvertOfferToApplicationPayload";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public ConvertOfferToApplicationPayloadBuilder generatedPolicyId() => WithScalarField("generatedPolicyId");

        public ConvertOfferToApplicationPayloadBuilder status() => WithScalarField("status");

        public ConvertOfferToApplicationPayloadBuilder errors() => WithScalarField("errors");

        public ConvertOfferToApplicationPayloadBuilder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public ConvertOfferToApplicationPayloadBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_KeyValuePairOfStringAndInt64Builder : GraphQlQueryBuilder<cases_KeyValuePairOfStringAndInt64Builder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "key" },
            new FieldMetadata { Name = "value", IsComplex = true }
        };

        protected override string TypeName { get; } = "cases_KeyValuePairOfStringAndInt64";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_KeyValuePairOfStringAndInt64Builder key() => WithScalarField("key");

        public cases_KeyValuePairOfStringAndInt64Builder value() => WithScalarField("value");
    }

    public class cases_ErrorBuilder : GraphQlQueryBuilder<cases_ErrorBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "code" },
            new FieldMetadata { Name = "message" }
        };

        protected override string TypeName { get; } = "cases_Error";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_ErrorBuilder code() => WithScalarField("code");

        public cases_ErrorBuilder message() => WithScalarField("message");
    }

    public class cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder : GraphQlQueryBuilder<cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "count", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_casesAttachedCommandPermissionBuilder) }
        };

        protected override string TypeName { get; } = "cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder count() => WithScalarField("count");

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilterBuilder list(cases_casesAttachedCommandPermissionBuilder cases_casesAttachedCommandPermissionBuilder) => WithObjectField("list", cases_casesAttachedCommandPermissionBuilder);
    }

    public class cases_AssignAdminToCasePayloadBuilder : GraphQlQueryBuilder<cases_AssignAdminToCasePayloadBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "value" },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "cases_AssignAdminToCasePayload";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AssignAdminToCasePayloadBuilder status() => WithScalarField("status");

        public cases_AssignAdminToCasePayloadBuilder errors() => WithScalarField("errors");

        public cases_AssignAdminToCasePayloadBuilder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public cases_AssignAdminToCasePayloadBuilder value() => WithScalarField("value");

        public cases_AssignAdminToCasePayloadBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_AddQuotationPayloadBuilder : GraphQlQueryBuilder<cases_AddQuotationPayloadBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "proposalId" },
            new FieldMetadata { Name = "offerId" },
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "cases_AddQuotationPayload";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AddQuotationPayloadBuilder proposalId() => WithScalarField("proposalId");

        public cases_AddQuotationPayloadBuilder offerId() => WithScalarField("offerId");

        public cases_AddQuotationPayloadBuilder status() => WithScalarField("status");

        public cases_AddQuotationPayloadBuilder errors() => WithScalarField("errors");

        public cases_AddQuotationPayloadBuilder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public cases_AddQuotationPayloadBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_ResultOfStringBuilder : GraphQlQueryBuilder<cases_ResultOfStringBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "value" },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "cases_ResultOfString";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_ResultOfStringBuilder status() => WithScalarField("status");

        public cases_ResultOfStringBuilder errors() => WithScalarField("errors");

        public cases_ResultOfStringBuilder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public cases_ResultOfStringBuilder value() => WithScalarField("value");

        public cases_ResultOfStringBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_ResultOfInt64Builder : GraphQlQueryBuilder<cases_ResultOfInt64Builder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "value", IsComplex = true },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "cases_ResultOfInt64";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_ResultOfInt64Builder status() => WithScalarField("status");

        public cases_ResultOfInt64Builder errors() => WithScalarField("errors");

        public cases_ResultOfInt64Builder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public cases_ResultOfInt64Builder value() => WithScalarField("value");

        public cases_ResultOfInt64Builder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_ResultBuilder : GraphQlQueryBuilder<cases_ResultBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "cases_Result";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_ResultBuilder status() => WithScalarField("status");

        public cases_ResultBuilder errors() => WithScalarField("errors");

        public cases_ResultBuilder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public cases_ResultBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_ResultOfCreatedStatusBuilder : GraphQlQueryBuilder<cases_ResultOfCreatedStatusBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "status" },
            new FieldMetadata { Name = "errors", IsComplex = true },
            new FieldMetadata { Name = "errors_2", IsComplex = true, QueryBuilderType = typeof(cases_ErrorBuilder) },
            new FieldMetadata { Name = "value", IsComplex = true, QueryBuilderType = typeof(cases_CreatedStatusBuilder) },
            new FieldMetadata { Name = "isSuccess" }
        };

        protected override string TypeName { get; } = "cases_ResultOfCreatedStatus";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_ResultOfCreatedStatusBuilder status() => WithScalarField("status");

        public cases_ResultOfCreatedStatusBuilder errors() => WithScalarField("errors");

        public cases_ResultOfCreatedStatusBuilder errors_2(cases_ErrorBuilder cases_ErrorBuilder) => WithObjectField("errors_2", cases_ErrorBuilder);

        public cases_ResultOfCreatedStatusBuilder value(cases_CreatedStatusBuilder cases_CreatedStatusBuilder) => WithObjectField("value", cases_CreatedStatusBuilder);

        public cases_ResultOfCreatedStatusBuilder isSuccess() => WithScalarField("isSuccess");
    }

    public class cases_GenericBroker8QueryInterfaceBuilder : GraphQlQueryBuilder<cases_GenericBroker8QueryInterfaceBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "totalCount", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_BrokerBuilder) }
        };

        protected override string TypeName { get; } = "cases_GenericBroker8QueryInterface";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_GenericBroker8QueryInterfaceBuilder totalCount() => WithScalarField("totalCount");

        public cases_GenericBroker8QueryInterfaceBuilder list(cases_BrokerBuilder cases_BrokerBuilder) => WithObjectField("list", cases_BrokerBuilder);
    }

    public class cases_GenericCounter8QueryInterfaceBuilder : GraphQlQueryBuilder<cases_GenericCounter8QueryInterfaceBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "totalCount", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_CounterBuilder) }
        };

        protected override string TypeName { get; } = "cases_GenericCounter8QueryInterface";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_GenericCounter8QueryInterfaceBuilder totalCount() => WithScalarField("totalCount");

        public cases_GenericCounter8QueryInterfaceBuilder list(cases_CounterBuilder cases_CounterBuilder) => WithObjectField("list", cases_CounterBuilder);
    }

    public class cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder : GraphQlQueryBuilder<cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "count", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_casesAttachedExternalApiCallBuilder) }
        };

        protected override string TypeName { get; } = "cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder count() => WithScalarField("count");

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilterBuilder list(cases_casesAttachedExternalApiCallBuilder cases_casesAttachedExternalApiCallBuilder) => WithObjectField("list", cases_casesAttachedExternalApiCallBuilder);
    }

    public class cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder : GraphQlQueryBuilder<cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "count", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_casesAttachedFilterBuilder) }
        };

        protected override string TypeName { get; } = "cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder count() => WithScalarField("count");

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilterBuilder list(cases_casesAttachedFilterBuilder cases_casesAttachedFilterBuilder) => WithObjectField("list", cases_casesAttachedFilterBuilder);
    }

    public class cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder : GraphQlQueryBuilder<cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "count", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_casesAttachedObjectValidationBuilder) }
        };

        protected override string TypeName { get; } = "cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder count() => WithScalarField("count");

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilterBuilder list(cases_casesAttachedObjectValidationBuilder cases_casesAttachedObjectValidationBuilder) => WithObjectField("list", cases_casesAttachedObjectValidationBuilder);
    }

    public class cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder : GraphQlQueryBuilder<cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "count", IsComplex = true },
            new FieldMetadata { Name = "list", IsComplex = true, QueryBuilderType = typeof(cases_casesAttachedPostHandleValidationBuilder) }
        };

        protected override string TypeName { get; } = "cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder count() => WithScalarField("count");

        public cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilterBuilder list(cases_casesAttachedPostHandleValidationBuilder cases_casesAttachedPostHandleValidationBuilder) => WithObjectField("list", cases_casesAttachedPostHandleValidationBuilder);
    }

    public class cases_casesAttachedPostHandleValidationBuilder : GraphQlQueryBuilder<cases_casesAttachedPostHandleValidationBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "action" },
            new FieldMetadata { Name = "eventExpression" },
            new FieldMetadata { Name = "objectExpression" },
            new FieldMetadata { Name = "group" },
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "order", IsComplex = true },
            new FieldMetadata { Name = "lang" },
            new FieldMetadata { Name = "langVersion" },
            new FieldMetadata { Name = "description" },
            new FieldMetadata { Name = "userExpression" }
        };

        protected override string TypeName { get; } = "cases_casesAttachedPostHandleValidation";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_casesAttachedPostHandleValidationBuilder action() => WithScalarField("action");

        public cases_casesAttachedPostHandleValidationBuilder eventExpression() => WithScalarField("eventExpression");

        public cases_casesAttachedPostHandleValidationBuilder objectExpression() => WithScalarField("objectExpression");

        public cases_casesAttachedPostHandleValidationBuilder group() => WithScalarField("group");

        public cases_casesAttachedPostHandleValidationBuilder id() => WithScalarField("id");

        public cases_casesAttachedPostHandleValidationBuilder order() => WithScalarField("order");

        public cases_casesAttachedPostHandleValidationBuilder lang() => WithScalarField("lang");

        public cases_casesAttachedPostHandleValidationBuilder langVersion() => WithScalarField("langVersion");

        public cases_casesAttachedPostHandleValidationBuilder description() => WithScalarField("description");

        public cases_casesAttachedPostHandleValidationBuilder userExpression() => WithScalarField("userExpression");
    }

    public class cases_casesAttachedObjectValidationBuilder : GraphQlQueryBuilder<cases_casesAttachedObjectValidationBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "action" },
            new FieldMetadata { Name = "objectExpression" },
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "order", IsComplex = true },
            new FieldMetadata { Name = "lang" },
            new FieldMetadata { Name = "langVersion" },
            new FieldMetadata { Name = "description" },
            new FieldMetadata { Name = "userExpression" }
        };

        protected override string TypeName { get; } = "cases_casesAttachedObjectValidation";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_casesAttachedObjectValidationBuilder action() => WithScalarField("action");

        public cases_casesAttachedObjectValidationBuilder objectExpression() => WithScalarField("objectExpression");

        public cases_casesAttachedObjectValidationBuilder id() => WithScalarField("id");

        public cases_casesAttachedObjectValidationBuilder order() => WithScalarField("order");

        public cases_casesAttachedObjectValidationBuilder lang() => WithScalarField("lang");

        public cases_casesAttachedObjectValidationBuilder langVersion() => WithScalarField("langVersion");

        public cases_casesAttachedObjectValidationBuilder description() => WithScalarField("description");

        public cases_casesAttachedObjectValidationBuilder userExpression() => WithScalarField("userExpression");
    }

    public class cases_casesAttachedFilterBuilder : GraphQlQueryBuilder<cases_casesAttachedFilterBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "objectExpression" },
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "order", IsComplex = true },
            new FieldMetadata { Name = "lang" },
            new FieldMetadata { Name = "langVersion" },
            new FieldMetadata { Name = "description" },
            new FieldMetadata { Name = "userExpression" }
        };

        protected override string TypeName { get; } = "cases_casesAttachedFilter";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_casesAttachedFilterBuilder objectExpression() => WithScalarField("objectExpression");

        public cases_casesAttachedFilterBuilder id() => WithScalarField("id");

        public cases_casesAttachedFilterBuilder order() => WithScalarField("order");

        public cases_casesAttachedFilterBuilder lang() => WithScalarField("lang");

        public cases_casesAttachedFilterBuilder langVersion() => WithScalarField("langVersion");

        public cases_casesAttachedFilterBuilder description() => WithScalarField("description");

        public cases_casesAttachedFilterBuilder userExpression() => WithScalarField("userExpression");
    }

    public class cases_casesAttachedExternalApiCallBuilder : GraphQlQueryBuilder<cases_casesAttachedExternalApiCallBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "apiCalls", IsComplex = true, QueryBuilderType = typeof(cases_ExternalApiCallBuilder) },
            new FieldMetadata { Name = "forEach" },
            new FieldMetadata { Name = "eventExpression" },
            new FieldMetadata { Name = "objectExpression" },
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "order", IsComplex = true },
            new FieldMetadata { Name = "lang" },
            new FieldMetadata { Name = "langVersion" },
            new FieldMetadata { Name = "description" },
            new FieldMetadata { Name = "userExpression" }
        };

        protected override string TypeName { get; } = "cases_casesAttachedExternalApiCall";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_casesAttachedExternalApiCallBuilder apiCalls(cases_ExternalApiCallBuilder cases_ExternalApiCallBuilder) => WithObjectField("apiCalls", cases_ExternalApiCallBuilder);

        public cases_casesAttachedExternalApiCallBuilder forEach() => WithScalarField("forEach");

        public cases_casesAttachedExternalApiCallBuilder eventExpression() => WithScalarField("eventExpression");

        public cases_casesAttachedExternalApiCallBuilder objectExpression() => WithScalarField("objectExpression");

        public cases_casesAttachedExternalApiCallBuilder id() => WithScalarField("id");

        public cases_casesAttachedExternalApiCallBuilder order() => WithScalarField("order");

        public cases_casesAttachedExternalApiCallBuilder lang() => WithScalarField("lang");

        public cases_casesAttachedExternalApiCallBuilder langVersion() => WithScalarField("langVersion");

        public cases_casesAttachedExternalApiCallBuilder description() => WithScalarField("description");

        public cases_casesAttachedExternalApiCallBuilder userExpression() => WithScalarField("userExpression");
    }

    public class cases_CreatedStatusBuilder : GraphQlQueryBuilder<cases_CreatedStatusBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "ids", IsComplex = true }
        };

        protected override string TypeName { get; } = "cases_CreatedStatus";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_CreatedStatusBuilder id() => WithScalarField("id");

        public cases_CreatedStatusBuilder ids() => WithScalarField("ids");
    }

    public class cases_casesAttachedCommandPermissionBuilder : GraphQlQueryBuilder<cases_casesAttachedCommandPermissionBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "command" },
            new FieldMetadata { Name = "action" },
            new FieldMetadata { Name = "commandExpression" },
            new FieldMetadata { Name = "objectExpression" },
            new FieldMetadata { Name = "id" },
            new FieldMetadata { Name = "order", IsComplex = true },
            new FieldMetadata { Name = "lang" },
            new FieldMetadata { Name = "langVersion" },
            new FieldMetadata { Name = "description" },
            new FieldMetadata { Name = "userExpression" }
        };

        protected override string TypeName { get; } = "cases_casesAttachedCommandPermission";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_casesAttachedCommandPermissionBuilder command() => WithScalarField("command");

        public cases_casesAttachedCommandPermissionBuilder action() => WithScalarField("action");

        public cases_casesAttachedCommandPermissionBuilder commandExpression() => WithScalarField("commandExpression");

        public cases_casesAttachedCommandPermissionBuilder objectExpression() => WithScalarField("objectExpression");

        public cases_casesAttachedCommandPermissionBuilder id() => WithScalarField("id");

        public cases_casesAttachedCommandPermissionBuilder order() => WithScalarField("order");

        public cases_casesAttachedCommandPermissionBuilder lang() => WithScalarField("lang");

        public cases_casesAttachedCommandPermissionBuilder langVersion() => WithScalarField("langVersion");

        public cases_casesAttachedCommandPermissionBuilder description() => WithScalarField("description");

        public cases_casesAttachedCommandPermissionBuilder userExpression() => WithScalarField("userExpression");
    }

    public class cases_ExternalApiCallBuilder : GraphQlQueryBuilder<cases_ExternalApiCallBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "url" },
            new FieldMetadata { Name = "method" },
            new FieldMetadata { Name = "stringContent" },
            new FieldMetadata { Name = "contentType" },
            new FieldMetadata { Name = "headers", IsComplex = true, QueryBuilderType = typeof(cases_KeyValuePairOfStringAndStringBuilder) },
            new FieldMetadata { Name = "timeout" },
            new FieldMetadata { Name = "beforeCall", IsComplex = true },
            new FieldMetadata { Name = "afterCall", IsComplex = true },
            new FieldMetadata { Name = "contextSlug" },
            new FieldMetadata { Name = "itemValidationExpression" },
            new FieldMetadata { Name = "expectedResponseExpression" },
            new FieldMetadata { Name = "expectedContentExpression" }
        };

        protected override string TypeName { get; } = "cases_ExternalApiCall";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_ExternalApiCallBuilder url() => WithScalarField("url");

        public cases_ExternalApiCallBuilder method() => WithScalarField("method");

        public cases_ExternalApiCallBuilder stringContent() => WithScalarField("stringContent");

        public cases_ExternalApiCallBuilder contentType() => WithScalarField("contentType");

        public cases_ExternalApiCallBuilder headers(cases_KeyValuePairOfStringAndStringBuilder cases_KeyValuePairOfStringAndStringBuilder) => WithObjectField("headers", cases_KeyValuePairOfStringAndStringBuilder);

        public cases_ExternalApiCallBuilder timeout() => WithScalarField("timeout");

        public cases_ExternalApiCallBuilder beforeCall() => WithScalarField("beforeCall");

        public cases_ExternalApiCallBuilder afterCall() => WithScalarField("afterCall");

        public cases_ExternalApiCallBuilder contextSlug() => WithScalarField("contextSlug");

        public cases_ExternalApiCallBuilder itemValidationExpression() => WithScalarField("itemValidationExpression");

        public cases_ExternalApiCallBuilder expectedResponseExpression() => WithScalarField("expectedResponseExpression");

        public cases_ExternalApiCallBuilder expectedContentExpression() => WithScalarField("expectedContentExpression");
    }

    public class cases_KeyValuePairOfStringAndStringBuilder : GraphQlQueryBuilder<cases_KeyValuePairOfStringAndStringBuilder>
    {
        private static readonly FieldMetadata[] AllFieldMetadata =
        {
            new FieldMetadata { Name = "key" },
            new FieldMetadata { Name = "value" }
        };

        protected override string TypeName { get; } = "cases_KeyValuePairOfStringAndString";

        public override IReadOnlyList<FieldMetadata> AllFields { get; } = AllFieldMetadata;

        public cases_KeyValuePairOfStringAndStringBuilder key() => WithScalarField("key");

        public cases_KeyValuePairOfStringAndStringBuilder value() => WithScalarField("value");
    }
    #endregion

    #region input classes
    public record ConvertOfferToApplicationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _proposalId;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public string? proposalId
        {
            get => (string?)_proposalId.Value;
            init => _proposalId = new InputPropertyInfo { Name = "proposalId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_proposalId.Name != null) yield return _proposalId;
        }
    }

    public record cases_AssignAgentInput : IGraphQlInputObject
    {
        private InputPropertyInfo _agentId;
        private InputPropertyInfo _roleType;

        public string? agentId
        {
            get => (string?)_agentId.Value;
            init => _agentId = new InputPropertyInfo { Name = "agentId", Value = value };
        }

        public cases_AgentRoleType? roleType
        {
            get => (cases_AgentRoleType?)_roleType.Value;
            init => _roleType = new InputPropertyInfo { Name = "roleType", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_agentId.Name != null) yield return _agentId;
            if (_roleType.Name != null) yield return _roleType;
        }
    }

    public record cases_UnassignAgentsInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _agentIds;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public ICollection<string?>? agentIds
        {
            get => (ICollection<string?>?)_agentIds.Value;
            init => _agentIds = new InputPropertyInfo { Name = "agentIds", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_agentIds.Name != null) yield return _agentIds;
        }
    }

    public record cases_AssignHandlersInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _portalUserIds;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public ICollection<string?>? portalUserIds
        {
            get => (ICollection<string?>?)_portalUserIds.Value;
            init => _portalUserIds = new InputPropertyInfo { Name = "portalUserIds", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_portalUserIds.Name != null) yield return _portalUserIds;
        }
    }

    public record cases_UnassignHandlersInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _portalUserIds;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public ICollection<string?>? portalUserIds
        {
            get => (ICollection<string?>?)_portalUserIds.Value;
            init => _portalUserIds = new InputPropertyInfo { Name = "portalUserIds", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_portalUserIds.Name != null) yield return _portalUserIds;
        }
    }

    public record cases_GenericCases_AttachedRuleQueryInput : IGraphQlInputObject
    {
        private InputPropertyInfo _where;
        private InputPropertyInfo _asOf;
        private InputPropertyInfo _orderBy;
        private InputPropertyInfo _orderBy2;
        private InputPropertyInfo _first;
        private InputPropertyInfo _skip;
        private InputPropertyInfo _includeEvents;
        private InputPropertyInfo _groupBy;

        public cases_GenericCases_AttachedRuleFilterInput? where
        {
            get => (cases_GenericCases_AttachedRuleFilterInput?)_where.Value;
            init => _where = new InputPropertyInfo { Name = "where", Value = value };
        }

        public DateTime? asOf
        {
            get => (DateTime?)_asOf.Value;
            init => _asOf = new InputPropertyInfo { Name = "asOf", Value = value };
        }

        public cases_OrderByInput? orderBy
        {
            get => (cases_OrderByInput?)_orderBy.Value;
            init => _orderBy = new InputPropertyInfo { Name = "orderBy", Value = value };
        }

        public ICollection<cases_OrderByInput>? orderBy2
        {
            get => (ICollection<cases_OrderByInput>?)_orderBy2.Value;
            init => _orderBy2 = new InputPropertyInfo { Name = "orderBy2", Value = value };
        }

        public int? first
        {
            get => (int?)_first.Value;
            init => _first = new InputPropertyInfo { Name = "first", Value = value };
        }

        public int? skip
        {
            get => (int?)_skip.Value;
            init => _skip = new InputPropertyInfo { Name = "skip", Value = value };
        }

        public bool? includeEvents
        {
            get => (bool?)_includeEvents.Value;
            init => _includeEvents = new InputPropertyInfo { Name = "includeEvents", Value = value };
        }

        public cases_GroupByInput? groupBy
        {
            get => (cases_GroupByInput?)_groupBy.Value;
            init => _groupBy = new InputPropertyInfo { Name = "groupBy", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_where.Name != null) yield return _where;
            if (_asOf.Name != null) yield return _asOf;
            if (_orderBy.Name != null) yield return _orderBy;
            if (_orderBy2.Name != null) yield return _orderBy2;
            if (_first.Name != null) yield return _first;
            if (_skip.Name != null) yield return _skip;
            if (_includeEvents.Name != null) yield return _includeEvents;
            if (_groupBy.Name != null) yield return _groupBy;
        }
    }

    public record cases_DeleteAttachedRuleCommandOfcasesAttachedCommandPermissionInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
        }
    }

    public record cases_UpdateAttachedRuleCommandOfcasesAttachedCommandPermissionInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _update;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedCommandPermissionInput? update
        {
            get => (cases_casesAttachedCommandPermissionInput?)_update.Value;
            init => _update = new InputPropertyInfo { Name = "update", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_update.Name != null) yield return _update;
        }
    }

    public record cases_CreateAttachedRuleCommandOfcasesAttachedCommandPermissionInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _create;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedCommandPermissionInput? create
        {
            get => (cases_casesAttachedCommandPermissionInput?)_create.Value;
            init => _create = new InputPropertyInfo { Name = "create", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_create.Name != null) yield return _create;
        }
    }

    public record cases_AssignAdminToCaseInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _adminEntityId;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public string? adminEntityId
        {
            get => (string?)_adminEntityId.Value;
            init => _adminEntityId = new InputPropertyInfo { Name = "adminEntityId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_adminEntityId.Name != null) yield return _adminEntityId;
        }
    }

    public record cases_AddQuotationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _proposal;
        private InputPropertyInfo _offer;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public cases_QuotationProposalInput? proposal
        {
            get => (cases_QuotationProposalInput?)_proposal.Value;
            init => _proposal = new InputPropertyInfo { Name = "proposal", Value = value };
        }

        public cases_QuotationOfferInput? offer
        {
            get => (cases_QuotationOfferInput?)_offer.Value;
            init => _offer = new InputPropertyInfo { Name = "offer", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_proposal.Name != null) yield return _proposal;
            if (_offer.Name != null) yield return _offer;
        }
    }

    public record cases_AssignAgentsInput : IGraphQlInputObject
    {
        private InputPropertyInfo _caseId;
        private InputPropertyInfo _agents;

        public string? caseId
        {
            get => (string?)_caseId.Value;
            init => _caseId = new InputPropertyInfo { Name = "caseId", Value = value };
        }

        public ICollection<cases_AssignAgentInput?>? agents
        {
            get => (ICollection<cases_AssignAgentInput?>?)_agents.Value;
            init => _agents = new InputPropertyInfo { Name = "agents", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_caseId.Name != null) yield return _caseId;
            if (_agents.Name != null) yield return _agents;
        }
    }

    public record cases_AgentCreateCaseInput : IGraphQlInputObject
    {
        private InputPropertyInfo _name;
        private InputPropertyInfo _description;
        private InputPropertyInfo _source;
        private InputPropertyInfo _holderId;
        private InputPropertyInfo _otherHolderIds;
        private InputPropertyInfo _insuredIds;
        private InputPropertyInfo _componentId;
        private InputPropertyInfo _status;
        private InputPropertyInfo _fields;
        private InputPropertyInfo _fieldsSchemaId;
        private InputPropertyInfo _assignedAgentEntityId;

        public string? name
        {
            get => (string?)_name.Value;
            init => _name = new InputPropertyInfo { Name = "name", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? source
        {
            get => (string?)_source.Value;
            init => _source = new InputPropertyInfo { Name = "source", Value = value };
        }

        public string? holderId
        {
            get => (string?)_holderId.Value;
            init => _holderId = new InputPropertyInfo { Name = "holderId", Value = value };
        }

        public ICollection<string?>? otherHolderIds
        {
            get => (ICollection<string?>?)_otherHolderIds.Value;
            init => _otherHolderIds = new InputPropertyInfo { Name = "otherHolderIds", Value = value };
        }

        public ICollection<string?>? insuredIds
        {
            get => (ICollection<string?>?)_insuredIds.Value;
            init => _insuredIds = new InputPropertyInfo { Name = "insuredIds", Value = value };
        }

        public string? componentId
        {
            get => (string?)_componentId.Value;
            init => _componentId = new InputPropertyInfo { Name = "componentId", Value = value };
        }

        public string? status
        {
            get => (string?)_status.Value;
            init => _status = new InputPropertyInfo { Name = "status", Value = value };
        }

        public string? fields
        {
            get => (string?)_fields.Value;
            init => _fields = new InputPropertyInfo { Name = "fields", Value = value };
        }

        public string? fieldsSchemaId
        {
            get => (string?)_fieldsSchemaId.Value;
            init => _fieldsSchemaId = new InputPropertyInfo { Name = "fieldsSchemaId", Value = value };
        }

        public string? assignedAgentEntityId
        {
            get => (string?)_assignedAgentEntityId.Value;
            init => _assignedAgentEntityId = new InputPropertyInfo { Name = "assignedAgentEntityId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_name.Name != null) yield return _name;
            if (_description.Name != null) yield return _description;
            if (_source.Name != null) yield return _source;
            if (_holderId.Name != null) yield return _holderId;
            if (_otherHolderIds.Name != null) yield return _otherHolderIds;
            if (_insuredIds.Name != null) yield return _insuredIds;
            if (_componentId.Name != null) yield return _componentId;
            if (_status.Name != null) yield return _status;
            if (_fields.Name != null) yield return _fields;
            if (_fieldsSchemaId.Name != null) yield return _fieldsSchemaId;
            if (_assignedAgentEntityId.Name != null) yield return _assignedAgentEntityId;
        }
    }

    public record cases_AdminUpdateOfferInput : IGraphQlInputObject
    {
        private InputPropertyInfo _status;
        private InputPropertyInfo _productId;
        private InputPropertyInfo _premium;
        private InputPropertyInfo _isPremiumOverridden;
        private InputPropertyInfo _startDate;
        private InputPropertyInfo _endDate;
        private InputPropertyInfo _timestamp;
        private InputPropertyInfo _pricing;
        private InputPropertyInfo _underwriting;
        private InputPropertyInfo _fields;
        private InputPropertyInfo _fieldsSchemaId;
        private InputPropertyInfo _productTreeId;

        public cases_SettableOfStringInput? status
        {
            get => (cases_SettableOfStringInput?)_status.Value;
            init => _status = new InputPropertyInfo { Name = "status", Value = value };
        }

        public cases_SettableOfAdminProductIdInput? productId
        {
            get => (cases_SettableOfAdminProductIdInput?)_productId.Value;
            init => _productId = new InputPropertyInfo { Name = "productId", Value = value };
        }

        public cases_SettableOfAdminUpdatePremiumInput? premium
        {
            get => (cases_SettableOfAdminUpdatePremiumInput?)_premium.Value;
            init => _premium = new InputPropertyInfo { Name = "premium", Value = value };
        }

        public cases_SettableOfNullableOfBooleanInput? isPremiumOverridden
        {
            get => (cases_SettableOfNullableOfBooleanInput?)_isPremiumOverridden.Value;
            init => _isPremiumOverridden = new InputPropertyInfo { Name = "isPremiumOverridden", Value = value };
        }

        public cases_SettableOfNullableOfDateTimeInput? startDate
        {
            get => (cases_SettableOfNullableOfDateTimeInput?)_startDate.Value;
            init => _startDate = new InputPropertyInfo { Name = "startDate", Value = value };
        }

        public cases_SettableOfNullableOfDateTimeInput? endDate
        {
            get => (cases_SettableOfNullableOfDateTimeInput?)_endDate.Value;
            init => _endDate = new InputPropertyInfo { Name = "endDate", Value = value };
        }

        public cases_SettableOfNullableOfDateTimeInput? timestamp
        {
            get => (cases_SettableOfNullableOfDateTimeInput?)_timestamp.Value;
            init => _timestamp = new InputPropertyInfo { Name = "timestamp", Value = value };
        }

        public cases_SettableOfStringInput? pricing
        {
            get => (cases_SettableOfStringInput?)_pricing.Value;
            init => _pricing = new InputPropertyInfo { Name = "pricing", Value = value };
        }

        public cases_SettableOfStringInput? underwriting
        {
            get => (cases_SettableOfStringInput?)_underwriting.Value;
            init => _underwriting = new InputPropertyInfo { Name = "underwriting", Value = value };
        }

        public cases_SettableOfStringInput? fields
        {
            get => (cases_SettableOfStringInput?)_fields.Value;
            init => _fields = new InputPropertyInfo { Name = "fields", Value = value };
        }

        public cases_SettableOfStringInput? fieldsSchemaId
        {
            get => (cases_SettableOfStringInput?)_fieldsSchemaId.Value;
            init => _fieldsSchemaId = new InputPropertyInfo { Name = "fieldsSchemaId", Value = value };
        }

        public cases_SettableOfStringInput? productTreeId
        {
            get => (cases_SettableOfStringInput?)_productTreeId.Value;
            init => _productTreeId = new InputPropertyInfo { Name = "productTreeId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_status.Name != null) yield return _status;
            if (_productId.Name != null) yield return _productId;
            if (_premium.Name != null) yield return _premium;
            if (_isPremiumOverridden.Name != null) yield return _isPremiumOverridden;
            if (_startDate.Name != null) yield return _startDate;
            if (_endDate.Name != null) yield return _endDate;
            if (_timestamp.Name != null) yield return _timestamp;
            if (_pricing.Name != null) yield return _pricing;
            if (_underwriting.Name != null) yield return _underwriting;
            if (_fields.Name != null) yield return _fields;
            if (_fieldsSchemaId.Name != null) yield return _fieldsSchemaId;
            if (_productTreeId.Name != null) yield return _productTreeId;
        }
    }

    public record cases_RecordQuoteCommandInput : IGraphQlInputObject
    {
        private InputPropertyInfo _proposalId;
        private InputPropertyInfo _offerId;
        private InputPropertyInfo _productTreeRecords;
        private InputPropertyInfo _fields;

        public string? proposalId
        {
            get => (string?)_proposalId.Value;
            init => _proposalId = new InputPropertyInfo { Name = "proposalId", Value = value };
        }

        public string? offerId
        {
            get => (string?)_offerId.Value;
            init => _offerId = new InputPropertyInfo { Name = "offerId", Value = value };
        }

        public ICollection<cases_ProductTreeRecordInput?>? productTreeRecords
        {
            get => (ICollection<cases_ProductTreeRecordInput?>?)_productTreeRecords.Value;
            init => _productTreeRecords = new InputPropertyInfo { Name = "productTreeRecords", Value = value };
        }

        public string? fields
        {
            get => (string?)_fields.Value;
            init => _fields = new InputPropertyInfo { Name = "fields", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_proposalId.Name != null) yield return _proposalId;
            if (_offerId.Name != null) yield return _offerId;
            if (_productTreeRecords.Name != null) yield return _productTreeRecords;
            if (_fields.Name != null) yield return _fields;
        }
    }

    public record cases_IncreaseCounterCommandInput : IGraphQlInputObject
    {
        private InputPropertyInfo _scope;
        private InputPropertyInfo _counterKey;

        public string? scope
        {
            get => (string?)_scope.Value;
            init => _scope = new InputPropertyInfo { Name = "scope", Value = value };
        }

        public string? counterKey
        {
            get => (string?)_counterKey.Value;
            init => _counterKey = new InputPropertyInfo { Name = "counterKey", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_scope.Name != null) yield return _scope;
            if (_counterKey.Name != null) yield return _counterKey;
        }
    }

    public record cases_AdminSetReadOnlyCommandInput : IGraphQlInputObject
    {
        private InputPropertyInfo _readOnly;

        public bool? readOnly
        {
            get => (bool?)_readOnly.Value;
            init => _readOnly = new InputPropertyInfo { Name = "readOnly", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_readOnly.Name != null) yield return _readOnly;
        }
    }

    public record cases_AdminSetSystemDatesCommandInput : IGraphQlInputObject
    {
        private InputPropertyInfo _createdAt;
        private InputPropertyInfo _lastModifiedAt;

        public DateTime? createdAt
        {
            get => (DateTime?)_createdAt.Value;
            init => _createdAt = new InputPropertyInfo { Name = "createdAt", Value = value };
        }

        public DateTime? lastModifiedAt
        {
            get => (DateTime?)_lastModifiedAt.Value;
            init => _lastModifiedAt = new InputPropertyInfo { Name = "lastModifiedAt", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_createdAt.Name != null) yield return _createdAt;
            if (_lastModifiedAt.Name != null) yield return _lastModifiedAt;
        }
    }

    public record cases_GenericBroker3BatchInput : IGraphQlInputObject
    {
        private InputPropertyInfo _create;
        private InputPropertyInfo _update;
        private InputPropertyInfo _delete;

        public ICollection<cases_BrokerUpsertInput?>? create
        {
            get => (ICollection<cases_BrokerUpsertInput?>?)_create.Value;
            init => _create = new InputPropertyInfo { Name = "create", Value = value };
        }

        public ICollection<cases_BrokerUpsertInput?>? update
        {
            get => (ICollection<cases_BrokerUpsertInput?>?)_update.Value;
            init => _update = new InputPropertyInfo { Name = "update", Value = value };
        }

        public ICollection<cases_BrokerUpsertInput?>? delete
        {
            get => (ICollection<cases_BrokerUpsertInput?>?)_delete.Value;
            init => _delete = new InputPropertyInfo { Name = "delete", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_create.Name != null) yield return _create;
            if (_update.Name != null) yield return _update;
            if (_delete.Name != null) yield return _delete;
        }
    }

    public record cases_BrokerUpsertInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _code;
        private InputPropertyInfo _name;
        private InputPropertyInfo _channel;
        private InputPropertyInfo _group;
        private InputPropertyInfo _contactPerson;
        private InputPropertyInfo _description;
        private InputPropertyInfo _contactPersonTelNo;
        private InputPropertyInfo _contactPersonFaxNo;
        private InputPropertyInfo _contactPersonEmail;
        private InputPropertyInfo _fields;
        private InputPropertyInfo _byId;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? code
        {
            get => (string?)_code.Value;
            init => _code = new InputPropertyInfo { Name = "code", Value = value };
        }

        public string? name
        {
            get => (string?)_name.Value;
            init => _name = new InputPropertyInfo { Name = "name", Value = value };
        }

        public string? channel
        {
            get => (string?)_channel.Value;
            init => _channel = new InputPropertyInfo { Name = "channel", Value = value };
        }

        public string? group
        {
            get => (string?)_group.Value;
            init => _group = new InputPropertyInfo { Name = "group", Value = value };
        }

        public string? contactPerson
        {
            get => (string?)_contactPerson.Value;
            init => _contactPerson = new InputPropertyInfo { Name = "contactPerson", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? contactPersonTelNo
        {
            get => (string?)_contactPersonTelNo.Value;
            init => _contactPersonTelNo = new InputPropertyInfo { Name = "contactPersonTelNo", Value = value };
        }

        public string? contactPersonFaxNo
        {
            get => (string?)_contactPersonFaxNo.Value;
            init => _contactPersonFaxNo = new InputPropertyInfo { Name = "contactPersonFaxNo", Value = value };
        }

        public string? contactPersonEmail
        {
            get => (string?)_contactPersonEmail.Value;
            init => _contactPersonEmail = new InputPropertyInfo { Name = "contactPersonEmail", Value = value };
        }

        public string? fields
        {
            get => (string?)_fields.Value;
            init => _fields = new InputPropertyInfo { Name = "fields", Value = value };
        }

        public string? byId
        {
            get => (string?)_byId.Value;
            init => _byId = new InputPropertyInfo { Name = "byId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_code.Name != null) yield return _code;
            if (_name.Name != null) yield return _name;
            if (_channel.Name != null) yield return _channel;
            if (_group.Name != null) yield return _group;
            if (_contactPerson.Name != null) yield return _contactPerson;
            if (_description.Name != null) yield return _description;
            if (_contactPersonTelNo.Name != null) yield return _contactPersonTelNo;
            if (_contactPersonFaxNo.Name != null) yield return _contactPersonFaxNo;
            if (_contactPersonEmail.Name != null) yield return _contactPersonEmail;
            if (_fields.Name != null) yield return _fields;
            if (_byId.Name != null) yield return _byId;
        }
    }

    public record cases_GenericBrokerQueryInput : IGraphQlInputObject
    {
        private InputPropertyInfo _where;
        private InputPropertyInfo _asOf;
        private InputPropertyInfo _orderBy;
        private InputPropertyInfo _orderBy2;
        private InputPropertyInfo _first;
        private InputPropertyInfo _skip;
        private InputPropertyInfo _includeEvents;
        private InputPropertyInfo _groupBy;

        public cases_GenericBrokerFilterInput? where
        {
            get => (cases_GenericBrokerFilterInput?)_where.Value;
            init => _where = new InputPropertyInfo { Name = "where", Value = value };
        }

        public DateTime? asOf
        {
            get => (DateTime?)_asOf.Value;
            init => _asOf = new InputPropertyInfo { Name = "asOf", Value = value };
        }

        public cases_OrderByInput? orderBy
        {
            get => (cases_OrderByInput?)_orderBy.Value;
            init => _orderBy = new InputPropertyInfo { Name = "orderBy", Value = value };
        }

        public ICollection<cases_OrderByInput>? orderBy2
        {
            get => (ICollection<cases_OrderByInput>?)_orderBy2.Value;
            init => _orderBy2 = new InputPropertyInfo { Name = "orderBy2", Value = value };
        }

        public int? first
        {
            get => (int?)_first.Value;
            init => _first = new InputPropertyInfo { Name = "first", Value = value };
        }

        public int? skip
        {
            get => (int?)_skip.Value;
            init => _skip = new InputPropertyInfo { Name = "skip", Value = value };
        }

        public bool? includeEvents
        {
            get => (bool?)_includeEvents.Value;
            init => _includeEvents = new InputPropertyInfo { Name = "includeEvents", Value = value };
        }

        public cases_GroupByInput? groupBy
        {
            get => (cases_GroupByInput?)_groupBy.Value;
            init => _groupBy = new InputPropertyInfo { Name = "groupBy", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_where.Name != null) yield return _where;
            if (_asOf.Name != null) yield return _asOf;
            if (_orderBy.Name != null) yield return _orderBy;
            if (_orderBy2.Name != null) yield return _orderBy2;
            if (_first.Name != null) yield return _first;
            if (_skip.Name != null) yield return _skip;
            if (_includeEvents.Name != null) yield return _includeEvents;
            if (_groupBy.Name != null) yield return _groupBy;
        }
    }

    public record cases_GenericCounterQueryInput : IGraphQlInputObject
    {
        private InputPropertyInfo _where;
        private InputPropertyInfo _asOf;
        private InputPropertyInfo _orderBy;
        private InputPropertyInfo _orderBy2;
        private InputPropertyInfo _first;
        private InputPropertyInfo _skip;
        private InputPropertyInfo _includeEvents;
        private InputPropertyInfo _groupBy;

        public cases_GenericCounterFilterInput? where
        {
            get => (cases_GenericCounterFilterInput?)_where.Value;
            init => _where = new InputPropertyInfo { Name = "where", Value = value };
        }

        public DateTime? asOf
        {
            get => (DateTime?)_asOf.Value;
            init => _asOf = new InputPropertyInfo { Name = "asOf", Value = value };
        }

        public cases_OrderByInput? orderBy
        {
            get => (cases_OrderByInput?)_orderBy.Value;
            init => _orderBy = new InputPropertyInfo { Name = "orderBy", Value = value };
        }

        public ICollection<cases_OrderByInput>? orderBy2
        {
            get => (ICollection<cases_OrderByInput>?)_orderBy2.Value;
            init => _orderBy2 = new InputPropertyInfo { Name = "orderBy2", Value = value };
        }

        public int? first
        {
            get => (int?)_first.Value;
            init => _first = new InputPropertyInfo { Name = "first", Value = value };
        }

        public int? skip
        {
            get => (int?)_skip.Value;
            init => _skip = new InputPropertyInfo { Name = "skip", Value = value };
        }

        public bool? includeEvents
        {
            get => (bool?)_includeEvents.Value;
            init => _includeEvents = new InputPropertyInfo { Name = "includeEvents", Value = value };
        }

        public cases_GroupByInput? groupBy
        {
            get => (cases_GroupByInput?)_groupBy.Value;
            init => _groupBy = new InputPropertyInfo { Name = "groupBy", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_where.Name != null) yield return _where;
            if (_asOf.Name != null) yield return _asOf;
            if (_orderBy.Name != null) yield return _orderBy;
            if (_orderBy2.Name != null) yield return _orderBy2;
            if (_first.Name != null) yield return _first;
            if (_skip.Name != null) yield return _skip;
            if (_includeEvents.Name != null) yield return _includeEvents;
            if (_groupBy.Name != null) yield return _groupBy;
        }
    }

    public record cases_CreateAttachedRuleCommandOfcasesAttachedExternalApiCallInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _create;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedExternalApiCallInput? create
        {
            get => (cases_casesAttachedExternalApiCallInput?)_create.Value;
            init => _create = new InputPropertyInfo { Name = "create", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_create.Name != null) yield return _create;
        }
    }

    public record cases_UpdateAttachedRuleCommandOfcasesAttachedExternalApiCallInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _update;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedExternalApiCallInput? update
        {
            get => (cases_casesAttachedExternalApiCallInput?)_update.Value;
            init => _update = new InputPropertyInfo { Name = "update", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_update.Name != null) yield return _update;
        }
    }

    public record cases_DeleteAttachedRuleCommandOfcasesAttachedExternalApiCallInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
        }
    }

    public record cases_CreateAttachedRuleCommandOfcasesAttachedFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _create;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedFilterInput? create
        {
            get => (cases_casesAttachedFilterInput?)_create.Value;
            init => _create = new InputPropertyInfo { Name = "create", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_create.Name != null) yield return _create;
        }
    }

    public record cases_UpdateAttachedRuleCommandOfcasesAttachedFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _update;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedFilterInput? update
        {
            get => (cases_casesAttachedFilterInput?)_update.Value;
            init => _update = new InputPropertyInfo { Name = "update", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_update.Name != null) yield return _update;
        }
    }

    public record cases_DeleteAttachedRuleCommandOfcasesAttachedFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
        }
    }

    public record cases_CreateAttachedRuleCommandOfcasesAttachedObjectValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _create;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedObjectValidationInput? create
        {
            get => (cases_casesAttachedObjectValidationInput?)_create.Value;
            init => _create = new InputPropertyInfo { Name = "create", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_create.Name != null) yield return _create;
        }
    }

    public record cases_UpdateAttachedRuleCommandOfcasesAttachedObjectValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _update;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedObjectValidationInput? update
        {
            get => (cases_casesAttachedObjectValidationInput?)_update.Value;
            init => _update = new InputPropertyInfo { Name = "update", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_update.Name != null) yield return _update;
        }
    }

    public record cases_DeleteAttachedRuleCommandOfcasesAttachedObjectValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
        }
    }

    public record cases_CreateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _create;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedPostHandleValidationInput? create
        {
            get => (cases_casesAttachedPostHandleValidationInput?)_create.Value;
            init => _create = new InputPropertyInfo { Name = "create", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_create.Name != null) yield return _create;
        }
    }

    public record cases_UpdateAttachedRuleCommandOfcasesAttachedPostHandleValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _update;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public cases_casesAttachedPostHandleValidationInput? update
        {
            get => (cases_casesAttachedPostHandleValidationInput?)_update.Value;
            init => _update = new InputPropertyInfo { Name = "update", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_update.Name != null) yield return _update;
        }
    }

    public record cases_DeleteAttachedRuleCommandOfcasesAttachedPostHandleValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
        }
    }

    public record cases_casesAttachedPostHandleValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _action;
        private InputPropertyInfo _eventExpression;
        private InputPropertyInfo _objectExpression;
        private InputPropertyInfo _group;
        private InputPropertyInfo _id;
        private InputPropertyInfo _order;
        private InputPropertyInfo _lang;
        private InputPropertyInfo _langVersion;
        private InputPropertyInfo _description;
        private InputPropertyInfo _userExpression;

        public cases_AttachedRuleAllowAction? action
        {
            get => (cases_AttachedRuleAllowAction?)_action.Value;
            init => _action = new InputPropertyInfo { Name = "action", Value = value };
        }

        public string? eventExpression
        {
            get => (string?)_eventExpression.Value;
            init => _eventExpression = new InputPropertyInfo { Name = "eventExpression", Value = value };
        }

        public string? objectExpression
        {
            get => (string?)_objectExpression.Value;
            init => _objectExpression = new InputPropertyInfo { Name = "objectExpression", Value = value };
        }

        public int? group
        {
            get => (int?)_group.Value;
            init => _group = new InputPropertyInfo { Name = "group", Value = value };
        }

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public object? order
        {
            get => (object?)_order.Value;
            init => _order = new InputPropertyInfo { Name = "order", Value = value };
        }

        public string? lang
        {
            get => (string?)_lang.Value;
            init => _lang = new InputPropertyInfo { Name = "lang", Value = value };
        }

        public string? langVersion
        {
            get => (string?)_langVersion.Value;
            init => _langVersion = new InputPropertyInfo { Name = "langVersion", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? userExpression
        {
            get => (string?)_userExpression.Value;
            init => _userExpression = new InputPropertyInfo { Name = "userExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_action.Name != null) yield return _action;
            if (_eventExpression.Name != null) yield return _eventExpression;
            if (_objectExpression.Name != null) yield return _objectExpression;
            if (_group.Name != null) yield return _group;
            if (_id.Name != null) yield return _id;
            if (_order.Name != null) yield return _order;
            if (_lang.Name != null) yield return _lang;
            if (_langVersion.Name != null) yield return _langVersion;
            if (_description.Name != null) yield return _description;
            if (_userExpression.Name != null) yield return _userExpression;
        }
    }

    public record cases_casesAttachedObjectValidationInput : IGraphQlInputObject
    {
        private InputPropertyInfo _action;
        private InputPropertyInfo _objectExpression;
        private InputPropertyInfo _id;
        private InputPropertyInfo _order;
        private InputPropertyInfo _lang;
        private InputPropertyInfo _langVersion;
        private InputPropertyInfo _description;
        private InputPropertyInfo _userExpression;

        public cases_AttachedRuleAllowAction? action
        {
            get => (cases_AttachedRuleAllowAction?)_action.Value;
            init => _action = new InputPropertyInfo { Name = "action", Value = value };
        }

        public string? objectExpression
        {
            get => (string?)_objectExpression.Value;
            init => _objectExpression = new InputPropertyInfo { Name = "objectExpression", Value = value };
        }

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public object? order
        {
            get => (object?)_order.Value;
            init => _order = new InputPropertyInfo { Name = "order", Value = value };
        }

        public string? lang
        {
            get => (string?)_lang.Value;
            init => _lang = new InputPropertyInfo { Name = "lang", Value = value };
        }

        public string? langVersion
        {
            get => (string?)_langVersion.Value;
            init => _langVersion = new InputPropertyInfo { Name = "langVersion", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? userExpression
        {
            get => (string?)_userExpression.Value;
            init => _userExpression = new InputPropertyInfo { Name = "userExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_action.Name != null) yield return _action;
            if (_objectExpression.Name != null) yield return _objectExpression;
            if (_id.Name != null) yield return _id;
            if (_order.Name != null) yield return _order;
            if (_lang.Name != null) yield return _lang;
            if (_langVersion.Name != null) yield return _langVersion;
            if (_description.Name != null) yield return _description;
            if (_userExpression.Name != null) yield return _userExpression;
        }
    }

    public record cases_casesAttachedFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _objectExpression;
        private InputPropertyInfo _id;
        private InputPropertyInfo _order;
        private InputPropertyInfo _lang;
        private InputPropertyInfo _langVersion;
        private InputPropertyInfo _description;
        private InputPropertyInfo _userExpression;

        public string? objectExpression
        {
            get => (string?)_objectExpression.Value;
            init => _objectExpression = new InputPropertyInfo { Name = "objectExpression", Value = value };
        }

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public object? order
        {
            get => (object?)_order.Value;
            init => _order = new InputPropertyInfo { Name = "order", Value = value };
        }

        public string? lang
        {
            get => (string?)_lang.Value;
            init => _lang = new InputPropertyInfo { Name = "lang", Value = value };
        }

        public string? langVersion
        {
            get => (string?)_langVersion.Value;
            init => _langVersion = new InputPropertyInfo { Name = "langVersion", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? userExpression
        {
            get => (string?)_userExpression.Value;
            init => _userExpression = new InputPropertyInfo { Name = "userExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_objectExpression.Name != null) yield return _objectExpression;
            if (_id.Name != null) yield return _id;
            if (_order.Name != null) yield return _order;
            if (_lang.Name != null) yield return _lang;
            if (_langVersion.Name != null) yield return _langVersion;
            if (_description.Name != null) yield return _description;
            if (_userExpression.Name != null) yield return _userExpression;
        }
    }

    public record cases_casesAttachedExternalApiCallInput : IGraphQlInputObject
    {
        private InputPropertyInfo _apiCalls;
        private InputPropertyInfo _forEach;
        private InputPropertyInfo _eventExpression;
        private InputPropertyInfo _objectExpression;
        private InputPropertyInfo _id;
        private InputPropertyInfo _order;
        private InputPropertyInfo _lang;
        private InputPropertyInfo _langVersion;
        private InputPropertyInfo _description;
        private InputPropertyInfo _userExpression;

        public ICollection<cases_ExternalApiCallInput>? apiCalls
        {
            get => (ICollection<cases_ExternalApiCallInput>?)_apiCalls.Value;
            init => _apiCalls = new InputPropertyInfo { Name = "apiCalls", Value = value };
        }

        public string? forEach
        {
            get => (string?)_forEach.Value;
            init => _forEach = new InputPropertyInfo { Name = "forEach", Value = value };
        }

        public string? eventExpression
        {
            get => (string?)_eventExpression.Value;
            init => _eventExpression = new InputPropertyInfo { Name = "eventExpression", Value = value };
        }

        public string? objectExpression
        {
            get => (string?)_objectExpression.Value;
            init => _objectExpression = new InputPropertyInfo { Name = "objectExpression", Value = value };
        }

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public object? order
        {
            get => (object?)_order.Value;
            init => _order = new InputPropertyInfo { Name = "order", Value = value };
        }

        public string? lang
        {
            get => (string?)_lang.Value;
            init => _lang = new InputPropertyInfo { Name = "lang", Value = value };
        }

        public string? langVersion
        {
            get => (string?)_langVersion.Value;
            init => _langVersion = new InputPropertyInfo { Name = "langVersion", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? userExpression
        {
            get => (string?)_userExpression.Value;
            init => _userExpression = new InputPropertyInfo { Name = "userExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_apiCalls.Name != null) yield return _apiCalls;
            if (_forEach.Name != null) yield return _forEach;
            if (_eventExpression.Name != null) yield return _eventExpression;
            if (_objectExpression.Name != null) yield return _objectExpression;
            if (_id.Name != null) yield return _id;
            if (_order.Name != null) yield return _order;
            if (_lang.Name != null) yield return _lang;
            if (_langVersion.Name != null) yield return _langVersion;
            if (_description.Name != null) yield return _description;
            if (_userExpression.Name != null) yield return _userExpression;
        }
    }

    public record cases_GenericCounterFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _and;
        private InputPropertyInfo _or;
        private InputPropertyInfo _where;

        public ICollection<cases_GenericCounterFilterInput?>? and
        {
            get => (ICollection<cases_GenericCounterFilterInput?>?)_and.Value;
            init => _and = new InputPropertyInfo { Name = "and", Value = value };
        }

        public ICollection<cases_GenericCounterFilterInput?>? or
        {
            get => (ICollection<cases_GenericCounterFilterInput?>?)_or.Value;
            init => _or = new InputPropertyInfo { Name = "or", Value = value };
        }

        public cases_CounterFilterInput? where
        {
            get => (cases_CounterFilterInput?)_where.Value;
            init => _where = new InputPropertyInfo { Name = "where", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_and.Name != null) yield return _and;
            if (_or.Name != null) yield return _or;
            if (_where.Name != null) yield return _where;
        }
    }

    public record cases_GenericBrokerFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _and;
        private InputPropertyInfo _or;
        private InputPropertyInfo _where;

        public ICollection<cases_GenericBrokerFilterInput?>? and
        {
            get => (ICollection<cases_GenericBrokerFilterInput?>?)_and.Value;
            init => _and = new InputPropertyInfo { Name = "and", Value = value };
        }

        public ICollection<cases_GenericBrokerFilterInput?>? or
        {
            get => (ICollection<cases_GenericBrokerFilterInput?>?)_or.Value;
            init => _or = new InputPropertyInfo { Name = "or", Value = value };
        }

        public cases_BrokerFilterInput? where
        {
            get => (cases_BrokerFilterInput?)_where.Value;
            init => _where = new InputPropertyInfo { Name = "where", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_and.Name != null) yield return _and;
            if (_or.Name != null) yield return _or;
            if (_where.Name != null) yield return _where;
        }
    }

    public record cases_ProductTreeRecordInput : IGraphQlInputObject
    {
        private InputPropertyInfo _type;
        private InputPropertyInfo _recordId;

        public string? type
        {
            get => (string?)_type.Value;
            init => _type = new InputPropertyInfo { Name = "type", Value = value };
        }

        public string? recordId
        {
            get => (string?)_recordId.Value;
            init => _recordId = new InputPropertyInfo { Name = "recordId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_type.Name != null) yield return _type;
            if (_recordId.Name != null) yield return _recordId;
        }
    }

    public record cases_SettableOfNullableOfDateTimeInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public DateTime? value
        {
            get => (DateTime?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfNullableOfBooleanInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public bool? value
        {
            get => (bool?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfAdminUpdatePremiumInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public cases_AdminUpdatePremiumInput? value
        {
            get => (cases_AdminUpdatePremiumInput?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfAdminProductIdInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public cases_AdminProductIdInput? value
        {
            get => (cases_AdminProductIdInput?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfStringInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public string? value
        {
            get => (string?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_QuotationOfferInput : IGraphQlInputObject
    {
        private InputPropertyInfo _pricing;
        private InputPropertyInfo _fields;
        private InputPropertyInfo _status;
        private InputPropertyInfo _productId;

        public string? pricing
        {
            get => (string?)_pricing.Value;
            init => _pricing = new InputPropertyInfo { Name = "pricing", Value = value };
        }

        public string? fields
        {
            get => (string?)_fields.Value;
            init => _fields = new InputPropertyInfo { Name = "fields", Value = value };
        }

        public string? status
        {
            get => (string?)_status.Value;
            init => _status = new InputPropertyInfo { Name = "status", Value = value };
        }

        public cases_ProductIdInput? productId
        {
            get => (cases_ProductIdInput?)_productId.Value;
            init => _productId = new InputPropertyInfo { Name = "productId", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_pricing.Name != null) yield return _pricing;
            if (_fields.Name != null) yield return _fields;
            if (_status.Name != null) yield return _status;
            if (_productId.Name != null) yield return _productId;
        }
    }

    public record cases_QuotationProposalInput : IGraphQlInputObject
    {
        private InputPropertyInfo _proposalNumber;

        public string? proposalNumber
        {
            get => (string?)_proposalNumber.Value;
            init => _proposalNumber = new InputPropertyInfo { Name = "proposalNumber", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_proposalNumber.Name != null) yield return _proposalNumber;
        }
    }

    public record cases_casesAttachedCommandPermissionInput : IGraphQlInputObject
    {
        private InputPropertyInfo _command;
        private InputPropertyInfo _action;
        private InputPropertyInfo _commandExpression;
        private InputPropertyInfo _objectExpression;
        private InputPropertyInfo _id;
        private InputPropertyInfo _order;
        private InputPropertyInfo _lang;
        private InputPropertyInfo _langVersion;
        private InputPropertyInfo _description;
        private InputPropertyInfo _userExpression;

        public string? command
        {
            get => (string?)_command.Value;
            init => _command = new InputPropertyInfo { Name = "command", Value = value };
        }

        public cases_AttachedRuleAllowAction? action
        {
            get => (cases_AttachedRuleAllowAction?)_action.Value;
            init => _action = new InputPropertyInfo { Name = "action", Value = value };
        }

        public string? commandExpression
        {
            get => (string?)_commandExpression.Value;
            init => _commandExpression = new InputPropertyInfo { Name = "commandExpression", Value = value };
        }

        public string? objectExpression
        {
            get => (string?)_objectExpression.Value;
            init => _objectExpression = new InputPropertyInfo { Name = "objectExpression", Value = value };
        }

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public object? order
        {
            get => (object?)_order.Value;
            init => _order = new InputPropertyInfo { Name = "order", Value = value };
        }

        public string? lang
        {
            get => (string?)_lang.Value;
            init => _lang = new InputPropertyInfo { Name = "lang", Value = value };
        }

        public string? langVersion
        {
            get => (string?)_langVersion.Value;
            init => _langVersion = new InputPropertyInfo { Name = "langVersion", Value = value };
        }

        public string? description
        {
            get => (string?)_description.Value;
            init => _description = new InputPropertyInfo { Name = "description", Value = value };
        }

        public string? userExpression
        {
            get => (string?)_userExpression.Value;
            init => _userExpression = new InputPropertyInfo { Name = "userExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_command.Name != null) yield return _command;
            if (_action.Name != null) yield return _action;
            if (_commandExpression.Name != null) yield return _commandExpression;
            if (_objectExpression.Name != null) yield return _objectExpression;
            if (_id.Name != null) yield return _id;
            if (_order.Name != null) yield return _order;
            if (_lang.Name != null) yield return _lang;
            if (_langVersion.Name != null) yield return _langVersion;
            if (_description.Name != null) yield return _description;
            if (_userExpression.Name != null) yield return _userExpression;
        }
    }

    public record cases_GroupByInput : IGraphQlInputObject
    {
        private InputPropertyInfo _fieldName;

        public string? fieldName
        {
            get => (string?)_fieldName.Value;
            init => _fieldName = new InputPropertyInfo { Name = "fieldName", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_fieldName.Name != null) yield return _fieldName;
        }
    }

    public record cases_OrderByInput : IGraphQlInputObject
    {
        private InputPropertyInfo _fieldName;
        private InputPropertyInfo _type;

        public string? fieldName
        {
            get => (string?)_fieldName.Value;
            init => _fieldName = new InputPropertyInfo { Name = "fieldName", Value = value };
        }

        public cases_OrderByType? type
        {
            get => (cases_OrderByType?)_type.Value;
            init => _type = new InputPropertyInfo { Name = "type", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_fieldName.Name != null) yield return _fieldName;
            if (_type.Name != null) yield return _type;
        }
    }

    public record cases_GenericCases_AttachedRuleFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _and;
        private InputPropertyInfo _or;
        private InputPropertyInfo _where;

        public ICollection<cases_GenericCases_AttachedRuleFilterInput?>? and
        {
            get => (ICollection<cases_GenericCases_AttachedRuleFilterInput?>?)_and.Value;
            init => _and = new InputPropertyInfo { Name = "and", Value = value };
        }

        public ICollection<cases_GenericCases_AttachedRuleFilterInput?>? or
        {
            get => (ICollection<cases_GenericCases_AttachedRuleFilterInput?>?)_or.Value;
            init => _or = new InputPropertyInfo { Name = "or", Value = value };
        }

        public cases_cases_AttachedRuleFilterInput? where
        {
            get => (cases_cases_AttachedRuleFilterInput?)_where.Value;
            init => _where = new InputPropertyInfo { Name = "where", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_and.Name != null) yield return _and;
            if (_or.Name != null) yield return _or;
            if (_where.Name != null) yield return _where;
        }
    }

    public record cases_cases_AttachedRuleFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _userExpression;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? userExpression
        {
            get => (string?)_userExpression.Value;
            init => _userExpression = new InputPropertyInfo { Name = "userExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_userExpression.Name != null) yield return _userExpression;
        }
    }

    public record cases_ProductIdInput : IGraphQlInputObject
    {
        private InputPropertyInfo _plan;
        private InputPropertyInfo _type;
        private InputPropertyInfo _version;

        public string? plan
        {
            get => (string?)_plan.Value;
            init => _plan = new InputPropertyInfo { Name = "plan", Value = value };
        }

        public string? type
        {
            get => (string?)_type.Value;
            init => _type = new InputPropertyInfo { Name = "type", Value = value };
        }

        public string? version
        {
            get => (string?)_version.Value;
            init => _version = new InputPropertyInfo { Name = "version", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_plan.Name != null) yield return _plan;
            if (_type.Name != null) yield return _type;
            if (_version.Name != null) yield return _version;
        }
    }

    public record cases_AdminProductIdInput : IGraphQlInputObject
    {
        private InputPropertyInfo _plan;
        private InputPropertyInfo _version;
        private InputPropertyInfo _type;

        public cases_SettableOfStringInput? plan
        {
            get => (cases_SettableOfStringInput?)_plan.Value;
            init => _plan = new InputPropertyInfo { Name = "plan", Value = value };
        }

        public cases_SettableOfStringInput? version
        {
            get => (cases_SettableOfStringInput?)_version.Value;
            init => _version = new InputPropertyInfo { Name = "version", Value = value };
        }

        public cases_SettableOfStringInput? type
        {
            get => (cases_SettableOfStringInput?)_type.Value;
            init => _type = new InputPropertyInfo { Name = "type", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_plan.Name != null) yield return _plan;
            if (_version.Name != null) yield return _version;
            if (_type.Name != null) yield return _type;
        }
    }

    public record cases_AdminUpdatePremiumInput : IGraphQlInputObject
    {
        private InputPropertyInfo _amount;
        private InputPropertyInfo _grossAmount;
        private InputPropertyInfo _currencyCode;
        private InputPropertyInfo _discountCodes;
        private InputPropertyInfo _isPricedAtStartDate;

        public cases_SettableOfNullableOfDecimalInput? amount
        {
            get => (cases_SettableOfNullableOfDecimalInput?)_amount.Value;
            init => _amount = new InputPropertyInfo { Name = "amount", Value = value };
        }

        public cases_SettableOfNullableOfDecimalInput? grossAmount
        {
            get => (cases_SettableOfNullableOfDecimalInput?)_grossAmount.Value;
            init => _grossAmount = new InputPropertyInfo { Name = "grossAmount", Value = value };
        }

        public cases_SettableOfNullableOfCurrencyCodeInput? currencyCode
        {
            get => (cases_SettableOfNullableOfCurrencyCodeInput?)_currencyCode.Value;
            init => _currencyCode = new InputPropertyInfo { Name = "currencyCode", Value = value };
        }

        public cases_SettableOfIEnumerableOfStringInput? discountCodes
        {
            get => (cases_SettableOfIEnumerableOfStringInput?)_discountCodes.Value;
            init => _discountCodes = new InputPropertyInfo { Name = "discountCodes", Value = value };
        }

        public cases_SettableOfBooleanInput? isPricedAtStartDate
        {
            get => (cases_SettableOfBooleanInput?)_isPricedAtStartDate.Value;
            init => _isPricedAtStartDate = new InputPropertyInfo { Name = "isPricedAtStartDate", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_amount.Name != null) yield return _amount;
            if (_grossAmount.Name != null) yield return _grossAmount;
            if (_currencyCode.Name != null) yield return _currencyCode;
            if (_discountCodes.Name != null) yield return _discountCodes;
            if (_isPricedAtStartDate.Name != null) yield return _isPricedAtStartDate;
        }
    }

    public record cases_BrokerFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _id;
        private InputPropertyInfo _id_neq;
        private InputPropertyInfo _id_in;
        private InputPropertyInfo _id_contains;
        private InputPropertyInfo _code_in;
        private InputPropertyInfo _normalizedCode_in;
        private InputPropertyInfo _name;
        private InputPropertyInfo _name_in;
        private InputPropertyInfo _name_contains;
        private InputPropertyInfo _normalizedName;
        private InputPropertyInfo _normalizedName_in;
        private InputPropertyInfo _normalizedName_contains;
        private InputPropertyInfo _channel_in;
        private InputPropertyInfo _group_in;
        private InputPropertyInfo _contactPerson_in;
        private InputPropertyInfo _contactPerson_contains;
        private InputPropertyInfo _contactPersonTelNo_in;
        private InputPropertyInfo _contactPersonFaxNo_in;
        private InputPropertyInfo _contactPersonEmail_in;
        private InputPropertyInfo _contactPersonEmail_contains;
        private InputPropertyInfo _fields;

        public string? id
        {
            get => (string?)_id.Value;
            init => _id = new InputPropertyInfo { Name = "id", Value = value };
        }

        public string? id_neq
        {
            get => (string?)_id_neq.Value;
            init => _id_neq = new InputPropertyInfo { Name = "id_neq", Value = value };
        }

        public ICollection<string?>? id_in
        {
            get => (ICollection<string?>?)_id_in.Value;
            init => _id_in = new InputPropertyInfo { Name = "id_in", Value = value };
        }

        public string? id_contains
        {
            get => (string?)_id_contains.Value;
            init => _id_contains = new InputPropertyInfo { Name = "id_contains", Value = value };
        }

        public ICollection<string?>? code_in
        {
            get => (ICollection<string?>?)_code_in.Value;
            init => _code_in = new InputPropertyInfo { Name = "code_in", Value = value };
        }

        public ICollection<string?>? normalizedCode_in
        {
            get => (ICollection<string?>?)_normalizedCode_in.Value;
            init => _normalizedCode_in = new InputPropertyInfo { Name = "normalizedCode_in", Value = value };
        }

        public string? name
        {
            get => (string?)_name.Value;
            init => _name = new InputPropertyInfo { Name = "name", Value = value };
        }

        public ICollection<string?>? name_in
        {
            get => (ICollection<string?>?)_name_in.Value;
            init => _name_in = new InputPropertyInfo { Name = "name_in", Value = value };
        }

        public string? name_contains
        {
            get => (string?)_name_contains.Value;
            init => _name_contains = new InputPropertyInfo { Name = "name_contains", Value = value };
        }

        public string? normalizedName
        {
            get => (string?)_normalizedName.Value;
            init => _normalizedName = new InputPropertyInfo { Name = "normalizedName", Value = value };
        }

        public string? normalizedName_in
        {
            get => (string?)_normalizedName_in.Value;
            init => _normalizedName_in = new InputPropertyInfo { Name = "normalizedName_in", Value = value };
        }

        public string? normalizedName_contains
        {
            get => (string?)_normalizedName_contains.Value;
            init => _normalizedName_contains = new InputPropertyInfo { Name = "normalizedName_contains", Value = value };
        }

        public ICollection<string?>? channel_in
        {
            get => (ICollection<string?>?)_channel_in.Value;
            init => _channel_in = new InputPropertyInfo { Name = "channel_in", Value = value };
        }

        public ICollection<string?>? group_in
        {
            get => (ICollection<string?>?)_group_in.Value;
            init => _group_in = new InputPropertyInfo { Name = "group_in", Value = value };
        }

        public ICollection<string?>? contactPerson_in
        {
            get => (ICollection<string?>?)_contactPerson_in.Value;
            init => _contactPerson_in = new InputPropertyInfo { Name = "contactPerson_in", Value = value };
        }

        public string? contactPerson_contains
        {
            get => (string?)_contactPerson_contains.Value;
            init => _contactPerson_contains = new InputPropertyInfo { Name = "contactPerson_contains", Value = value };
        }

        public ICollection<string?>? contactPersonTelNo_in
        {
            get => (ICollection<string?>?)_contactPersonTelNo_in.Value;
            init => _contactPersonTelNo_in = new InputPropertyInfo { Name = "contactPersonTelNo_in", Value = value };
        }

        public ICollection<string?>? contactPersonFaxNo_in
        {
            get => (ICollection<string?>?)_contactPersonFaxNo_in.Value;
            init => _contactPersonFaxNo_in = new InputPropertyInfo { Name = "contactPersonFaxNo_in", Value = value };
        }

        public ICollection<string?>? contactPersonEmail_in
        {
            get => (ICollection<string?>?)_contactPersonEmail_in.Value;
            init => _contactPersonEmail_in = new InputPropertyInfo { Name = "contactPersonEmail_in", Value = value };
        }

        public string? contactPersonEmail_contains
        {
            get => (string?)_contactPersonEmail_contains.Value;
            init => _contactPersonEmail_contains = new InputPropertyInfo { Name = "contactPersonEmail_contains", Value = value };
        }

        public cases_FieldsWhereInput? fields
        {
            get => (cases_FieldsWhereInput?)_fields.Value;
            init => _fields = new InputPropertyInfo { Name = "fields", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_id.Name != null) yield return _id;
            if (_id_neq.Name != null) yield return _id_neq;
            if (_id_in.Name != null) yield return _id_in;
            if (_id_contains.Name != null) yield return _id_contains;
            if (_code_in.Name != null) yield return _code_in;
            if (_normalizedCode_in.Name != null) yield return _normalizedCode_in;
            if (_name.Name != null) yield return _name;
            if (_name_in.Name != null) yield return _name_in;
            if (_name_contains.Name != null) yield return _name_contains;
            if (_normalizedName.Name != null) yield return _normalizedName;
            if (_normalizedName_in.Name != null) yield return _normalizedName_in;
            if (_normalizedName_contains.Name != null) yield return _normalizedName_contains;
            if (_channel_in.Name != null) yield return _channel_in;
            if (_group_in.Name != null) yield return _group_in;
            if (_contactPerson_in.Name != null) yield return _contactPerson_in;
            if (_contactPerson_contains.Name != null) yield return _contactPerson_contains;
            if (_contactPersonTelNo_in.Name != null) yield return _contactPersonTelNo_in;
            if (_contactPersonFaxNo_in.Name != null) yield return _contactPersonFaxNo_in;
            if (_contactPersonEmail_in.Name != null) yield return _contactPersonEmail_in;
            if (_contactPersonEmail_contains.Name != null) yield return _contactPersonEmail_contains;
            if (_fields.Name != null) yield return _fields;
        }
    }

    public record cases_CounterFilterInput : IGraphQlInputObject
    {
        private InputPropertyInfo _scope;

        public string? scope
        {
            get => (string?)_scope.Value;
            init => _scope = new InputPropertyInfo { Name = "scope", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_scope.Name != null) yield return _scope;
        }
    }

    public record cases_ExternalApiCallInput : IGraphQlInputObject
    {
        private InputPropertyInfo _url;
        private InputPropertyInfo _method;
        private InputPropertyInfo _stringContent;
        private InputPropertyInfo _contentType;
        private InputPropertyInfo _headers;
        private InputPropertyInfo _timeout;
        private InputPropertyInfo _beforeCall;
        private InputPropertyInfo _afterCall;
        private InputPropertyInfo _contextSlug;
        private InputPropertyInfo _itemValidationExpression;
        private InputPropertyInfo _expectedResponseExpression;
        private InputPropertyInfo _expectedContentExpression;

        public string? url
        {
            get => (string?)_url.Value;
            init => _url = new InputPropertyInfo { Name = "url", Value = value };
        }

        public string? method
        {
            get => (string?)_method.Value;
            init => _method = new InputPropertyInfo { Name = "method", Value = value };
        }

        public string? stringContent
        {
            get => (string?)_stringContent.Value;
            init => _stringContent = new InputPropertyInfo { Name = "stringContent", Value = value };
        }

        public string? contentType
        {
            get => (string?)_contentType.Value;
            init => _contentType = new InputPropertyInfo { Name = "contentType", Value = value };
        }

        public ICollection<cases_KeyValuePairOfStringAndStringInput>? headers
        {
            get => (ICollection<cases_KeyValuePairOfStringAndStringInput>?)_headers.Value;
            init => _headers = new InputPropertyInfo { Name = "headers", Value = value };
        }

        public string? timeout
        {
            get => (string?)_timeout.Value;
            init => _timeout = new InputPropertyInfo { Name = "timeout", Value = value };
        }

        public ICollection<string>? beforeCall
        {
            get => (ICollection<string>?)_beforeCall.Value;
            init => _beforeCall = new InputPropertyInfo { Name = "beforeCall", Value = value };
        }

        public ICollection<string>? afterCall
        {
            get => (ICollection<string>?)_afterCall.Value;
            init => _afterCall = new InputPropertyInfo { Name = "afterCall", Value = value };
        }

        public string? contextSlug
        {
            get => (string?)_contextSlug.Value;
            init => _contextSlug = new InputPropertyInfo { Name = "contextSlug", Value = value };
        }

        public string? itemValidationExpression
        {
            get => (string?)_itemValidationExpression.Value;
            init => _itemValidationExpression = new InputPropertyInfo { Name = "itemValidationExpression", Value = value };
        }

        public string? expectedResponseExpression
        {
            get => (string?)_expectedResponseExpression.Value;
            init => _expectedResponseExpression = new InputPropertyInfo { Name = "expectedResponseExpression", Value = value };
        }

        public string? expectedContentExpression
        {
            get => (string?)_expectedContentExpression.Value;
            init => _expectedContentExpression = new InputPropertyInfo { Name = "expectedContentExpression", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_url.Name != null) yield return _url;
            if (_method.Name != null) yield return _method;
            if (_stringContent.Name != null) yield return _stringContent;
            if (_contentType.Name != null) yield return _contentType;
            if (_headers.Name != null) yield return _headers;
            if (_timeout.Name != null) yield return _timeout;
            if (_beforeCall.Name != null) yield return _beforeCall;
            if (_afterCall.Name != null) yield return _afterCall;
            if (_contextSlug.Name != null) yield return _contextSlug;
            if (_itemValidationExpression.Name != null) yield return _itemValidationExpression;
            if (_expectedResponseExpression.Name != null) yield return _expectedResponseExpression;
            if (_expectedContentExpression.Name != null) yield return _expectedContentExpression;
        }
    }

    public record cases_KeyValuePairOfStringAndStringInput : IGraphQlInputObject
    {
        private InputPropertyInfo _key;
        private InputPropertyInfo _value;

        public string? key
        {
            get => (string?)_key.Value;
            init => _key = new InputPropertyInfo { Name = "key", Value = value };
        }

        public string? value
        {
            get => (string?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_key.Name != null) yield return _key;
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_FieldsWhereInput : IGraphQlInputObject
    {
        private InputPropertyInfo _path;
        private InputPropertyInfo _value;
        private InputPropertyInfo _condition;
        private InputPropertyInfo _and;
        private InputPropertyInfo _or;

        public string? path
        {
            get => (string?)_path.Value;
            init => _path = new InputPropertyInfo { Name = "path", Value = value };
        }

        public cases_ScalarValueInput? value
        {
            get => (cases_ScalarValueInput?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        public cases_FieldsWhereCondition? condition
        {
            get => (cases_FieldsWhereCondition?)_condition.Value;
            init => _condition = new InputPropertyInfo { Name = "condition", Value = value };
        }

        public ICollection<cases_FieldsWhereInput?>? and
        {
            get => (ICollection<cases_FieldsWhereInput?>?)_and.Value;
            init => _and = new InputPropertyInfo { Name = "and", Value = value };
        }

        public ICollection<cases_FieldsWhereInput?>? or
        {
            get => (ICollection<cases_FieldsWhereInput?>?)_or.Value;
            init => _or = new InputPropertyInfo { Name = "or", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_path.Name != null) yield return _path;
            if (_value.Name != null) yield return _value;
            if (_condition.Name != null) yield return _condition;
            if (_and.Name != null) yield return _and;
            if (_or.Name != null) yield return _or;
        }
    }

    public record cases_SettableOfBooleanInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public bool? value
        {
            get => (bool?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfIEnumerableOfStringInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public ICollection<string>? value
        {
            get => (ICollection<string>?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfNullableOfCurrencyCodeInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public cases_CurrencyCode? value
        {
            get => (cases_CurrencyCode?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_SettableOfNullableOfDecimalInput : IGraphQlInputObject
    {
        private InputPropertyInfo _value;

        public object? value
        {
            get => (object?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_value.Name != null) yield return _value;
        }
    }

    public record cases_ScalarValueInput : IGraphQlInputObject
    {
        private InputPropertyInfo _stringValue;
        private InputPropertyInfo _numberValue;
        private InputPropertyInfo _doubleValue;
        private InputPropertyInfo _booleanValue;
        private InputPropertyInfo _dateValue;
        private InputPropertyInfo _objectValue;
        private InputPropertyInfo _arrayValue;

        public string? stringValue
        {
            get => (string?)_stringValue.Value;
            init => _stringValue = new InputPropertyInfo { Name = "stringValue", Value = value };
        }

        public object? numberValue
        {
            get => (object?)_numberValue.Value;
            init => _numberValue = new InputPropertyInfo { Name = "numberValue", Value = value };
        }

        public decimal? doubleValue
        {
            get => (decimal?)_doubleValue.Value;
            init => _doubleValue = new InputPropertyInfo { Name = "doubleValue", Value = value };
        }

        public bool? booleanValue
        {
            get => (bool?)_booleanValue.Value;
            init => _booleanValue = new InputPropertyInfo { Name = "booleanValue", Value = value };
        }

        public DateTime? dateValue
        {
            get => (DateTime?)_dateValue.Value;
            init => _dateValue = new InputPropertyInfo { Name = "dateValue", Value = value };
        }

        public ICollection<cases_KeyScalarValueInput?>? objectValue
        {
            get => (ICollection<cases_KeyScalarValueInput?>?)_objectValue.Value;
            init => _objectValue = new InputPropertyInfo { Name = "objectValue", Value = value };
        }

        public ICollection<cases_ScalarValueInput?>? arrayValue
        {
            get => (ICollection<cases_ScalarValueInput?>?)_arrayValue.Value;
            init => _arrayValue = new InputPropertyInfo { Name = "arrayValue", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_stringValue.Name != null) yield return _stringValue;
            if (_numberValue.Name != null) yield return _numberValue;
            if (_doubleValue.Name != null) yield return _doubleValue;
            if (_booleanValue.Name != null) yield return _booleanValue;
            if (_dateValue.Name != null) yield return _dateValue;
            if (_objectValue.Name != null) yield return _objectValue;
            if (_arrayValue.Name != null) yield return _arrayValue;
        }
    }

    public record cases_KeyScalarValueInput : IGraphQlInputObject
    {
        private InputPropertyInfo _key;
        private InputPropertyInfo _value;

        public string? key
        {
            get => (string?)_key.Value;
            init => _key = new InputPropertyInfo { Name = "key", Value = value };
        }

        public cases_ScalarValueInput? value
        {
            get => (cases_ScalarValueInput?)_value.Value;
            init => _value = new InputPropertyInfo { Name = "value", Value = value };
        }

        IEnumerable<InputPropertyInfo> IGraphQlInputObject.GetPropertyValues()
        {
            if (_key.Name != null) yield return _key;
            if (_value.Name != null) yield return _value;
        }
    }
    #endregion

    #region data classes
    public record _SchemaDefinition
    {
        public string? name { get; init; }
        public string? document { get; init; }
        public ICollection<string>? extensionDocuments { get; init; }
    }

    public record Query
    {
        public _SchemaDefinition? _schemaDefinition { get; init; }
        public cases_GenericCounter8QueryInterface? countersQuery { get; init; }
        public cases_GenericBroker8QueryInterface? brokersQuery { get; init; }
        public cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilter? cases_AttachedCommandPermissionsQuery { get; init; }
        public cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilter? cases_AttachedExternalApiCallsQuery { get; init; }
        public cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilter? cases_AttachedFiltersQuery { get; init; }
        public cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilter? cases_AttachedObjectValidationsQuery { get; init; }
        public cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilter? cases_AttachedPostHandleValidationsQuery { get; init; }
    }

    public record Mutation
    {
        public cases_ResultOfCreatedStatus? brokersMutationCreate { get; init; }
        public cases_Result? brokersMutationUpdate { get; init; }
        public cases_Result? brokersMutationDelete { get; init; }
        public cases_Result? brokersMutationBatch { get; init; }
        public cases_Result? caseMutationSetSystemDates { get; init; }
        public cases_Result? caseMutationSetReadOnly { get; init; }
        public cases_ResultOfInt64? counterMutationIncrease { get; init; }
        public cases_Result? offerMutationRecordQuote { get; init; }
        public cases_Result? offerMutationSecureUpdateOffer { get; init; }
        public cases_ResultOfCreatedStatus? cases_AssignHandlers { get; init; }
        public cases_Result? cases_UnassignHandlers { get; init; }
        public ConvertOfferToApplicationPayload? convertOfferToApplication { get; init; }
        public cases_ResultOfString? agentMutationAgentCreateCase { get; init; }
        public cases_ResultOfCreatedStatus? cases_AssignAgents { get; init; }
        public cases_Result? cases_UnassignAgents { get; init; }
        public cases_AddQuotationPayload? addQuotation { get; init; }
        public cases_AssignAdminToCasePayload? salesAdminMutationAssignAdminToCase { get; init; }
        public cases_ResultOfCreatedStatus? cases_AttachedCommandPermissionMutationCreate { get; init; }
        public cases_Result? cases_AttachedCommandPermissionMutationUpdate { get; init; }
        public cases_Result? cases_AttachedCommandPermissionMutationDelete { get; init; }
        public cases_ResultOfCreatedStatus? cases_AttachedExternalApiCallMutationCreate { get; init; }
        public cases_Result? cases_AttachedExternalApiCallMutationUpdate { get; init; }
        public cases_Result? cases_AttachedExternalApiCallMutationDelete { get; init; }
        public cases_ResultOfCreatedStatus? cases_AttachedFilterMutationCreate { get; init; }
        public cases_Result? cases_AttachedFilterMutationUpdate { get; init; }
        public cases_Result? cases_AttachedFilterMutationDelete { get; init; }
        public cases_ResultOfCreatedStatus? cases_AttachedObjectValidationMutationCreate { get; init; }
        public cases_Result? cases_AttachedObjectValidationMutationUpdate { get; init; }
        public cases_Result? cases_AttachedObjectValidationMutationDelete { get; init; }
        public cases_ResultOfCreatedStatus? cases_AttachedPostHandleValidationMutationCreate { get; init; }
        public cases_Result? cases_AttachedPostHandleValidationMutationUpdate { get; init; }
        public cases_Result? cases_AttachedPostHandleValidationMutationDelete { get; init; }
    }

    public record cases_Counter
    {
        public string? id { get; init; }
        public string? scope { get; init; }
        public ICollection<cases_KeyValuePairOfStringAndInt64>? counters { get; init; }
        public DateTime? createdAt { get; init; }
        public DateTime? lastModifiedAt { get; init; }
        public string? createdById { get; init; }
        public string? lastModifiedById { get; init; }
    }

    public record cases_Broker
    {
        public string? fields { get; init; }
        public string? id { get; init; }
        public string? code { get; init; }
        public string? normalizedCode { get; init; }
        public string? name { get; init; }
        public string? normalizedName { get; init; }
        public string? channel { get; init; }
        public string? group { get; init; }
        public string? description { get; init; }
        public string? contactPerson { get; init; }
        public string? contactPersonTelNo { get; init; }
        public string? contactPersonFaxNo { get; init; }
        public string? contactPersonEmail { get; init; }
        public DateTime? createdAt { get; init; }
        public DateTime? lastModifiedAt { get; init; }
        public string? createdById { get; init; }
        public string? lastModifiedById { get; init; }
    }

    public record ConvertOfferToApplicationPayload
    {
        public string? generatedPolicyId { get; init; }
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_KeyValuePairOfStringAndInt64
    {
        public string? key { get; init; }
        public object? value { get; init; }
    }

    public record cases_Error
    {
        public string? code { get; init; }
        public string? message { get; init; }
    }

    public record cases_AttachedRulesQueryInterfaceOfcasesAttachedCommandPermissionAndcases_AttachedRuleFilter
    {
        public object? count { get; init; }
        public ICollection<cases_casesAttachedCommandPermission?>? list { get; init; }
    }

    public record cases_AssignAdminToCasePayload
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public string? value { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_AddQuotationPayload
    {
        public string? proposalId { get; init; }
        public string? offerId { get; init; }
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_ResultOfString
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public string? value { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_ResultOfInt64
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public object? value { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_Result
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_ResultOfCreatedStatus
    {
        public string? status { get; init; }
        public ICollection<string?>? errors { get; init; }
        public ICollection<cases_Error?>? errors_2 { get; init; }
        public cases_CreatedStatus? value { get; init; }
        public bool? isSuccess { get; init; }
    }

    public record cases_GenericBroker8QueryInterface
    {
        public object? totalCount { get; init; }
        public ICollection<cases_Broker?>? list { get; init; }
    }

    public record cases_GenericCounter8QueryInterface
    {
        public object? totalCount { get; init; }
        public ICollection<cases_Counter?>? list { get; init; }
    }

    public record cases_AttachedRulesQueryInterfaceOfcasesAttachedExternalApiCallAndcases_AttachedRuleFilter
    {
        public object? count { get; init; }
        public ICollection<cases_casesAttachedExternalApiCall?>? list { get; init; }
    }

    public record cases_AttachedRulesQueryInterfaceOfcasesAttachedFilterAndcases_AttachedRuleFilter
    {
        public object? count { get; init; }
        public ICollection<cases_casesAttachedFilter?>? list { get; init; }
    }

    public record cases_AttachedRulesQueryInterfaceOfcasesAttachedObjectValidationAndcases_AttachedRuleFilter
    {
        public object? count { get; init; }
        public ICollection<cases_casesAttachedObjectValidation?>? list { get; init; }
    }

    public record cases_AttachedRulesQueryInterfaceOfcasesAttachedPostHandleValidationAndcases_AttachedRuleFilter
    {
        public object? count { get; init; }
        public ICollection<cases_casesAttachedPostHandleValidation?>? list { get; init; }
    }

    public record cases_casesAttachedPostHandleValidation
    {
        public cases_AttachedRuleAllowAction? action { get; init; }
        public string? eventExpression { get; init; }
        public string? objectExpression { get; init; }
        public int? group { get; init; }
        public string? id { get; init; }
        public object? order { get; init; }
        public string? lang { get; init; }
        public string? langVersion { get; init; }
        public string? description { get; init; }
        public string? userExpression { get; init; }
    }

    public record cases_casesAttachedObjectValidation
    {
        public cases_AttachedRuleAllowAction? action { get; init; }
        public string? objectExpression { get; init; }
        public string? id { get; init; }
        public object? order { get; init; }
        public string? lang { get; init; }
        public string? langVersion { get; init; }
        public string? description { get; init; }
        public string? userExpression { get; init; }
    }

    public record cases_casesAttachedFilter
    {
        public string? objectExpression { get; init; }
        public string? id { get; init; }
        public object? order { get; init; }
        public string? lang { get; init; }
        public string? langVersion { get; init; }
        public string? description { get; init; }
        public string? userExpression { get; init; }
    }

    public record cases_casesAttachedExternalApiCall
    {
        public ICollection<cases_ExternalApiCall>? apiCalls { get; init; }
        public string? forEach { get; init; }
        public string? eventExpression { get; init; }
        public string? objectExpression { get; init; }
        public string? id { get; init; }
        public object? order { get; init; }
        public string? lang { get; init; }
        public string? langVersion { get; init; }
        public string? description { get; init; }
        public string? userExpression { get; init; }
    }

    public record cases_CreatedStatus
    {
        public string? id { get; init; }
        public ICollection<string?>? ids { get; init; }
    }

    public record cases_casesAttachedCommandPermission
    {
        public string? command { get; init; }
        public cases_AttachedRuleAllowAction? action { get; init; }
        public string? commandExpression { get; init; }
        public string? objectExpression { get; init; }
        public string? id { get; init; }
        public object? order { get; init; }
        public string? lang { get; init; }
        public string? langVersion { get; init; }
        public string? description { get; init; }
        public string? userExpression { get; init; }
    }

    public record cases_ExternalApiCall
    {
        public string? url { get; init; }
        public string? method { get; init; }
        public string? stringContent { get; init; }
        public string? contentType { get; init; }
        public ICollection<cases_KeyValuePairOfStringAndString>? headers { get; init; }
        public string? timeout { get; init; }
        public ICollection<string>? beforeCall { get; init; }
        public ICollection<string>? afterCall { get; init; }
        public string? contextSlug { get; init; }
        public string? itemValidationExpression { get; init; }
        public string? expectedResponseExpression { get; init; }
        public string? expectedContentExpression { get; init; }
    }

    public record cases_KeyValuePairOfStringAndString
    {
        public string? key { get; init; }
        public string? value { get; init; }
    }
    #endregion
#nullable restore
}
