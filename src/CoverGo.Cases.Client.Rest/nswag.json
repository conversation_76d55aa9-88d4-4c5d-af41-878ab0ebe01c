﻿{
  "runtime": "Net80",
  "defaultVariables": "Configuration=Debug",
  "documentGenerator": {
    "webApiToOpenApi": {
      "isAspNetCore": true,
      "assemblyPaths": [
        "../CoverGo.Cases.Application/bin/$(Configuration)/net8.0/CoverGo.Cases.Application.dll"
      ],
      "referencePaths": [
        "../CoverGo.Cases.Application/bin/$(Configuration)/net8.0/"
      ],
      "output": null
    }
  },
  "codeGenerators": {
    "openApiToCSharpClient": {
      "clientBaseClass": null,
      "configurationClass": null,
      "generateClientClasses": true,
      "generateClientInterfaces": true,
      "clientBaseInterface": null,
      "injectHttpClient": true,
      "disposeHttpClient": false,
      "protectedMethods": [],
      "generateExceptionClasses": true,
      "exceptionClass": "CasesRestClientException",
      "wrapDtoExceptions": false,
      "useHttpClientCreationMethod": false,
      "httpClientType": "System.Net.Http.HttpClient",
      "useHttpRequestMessageCreationMethod": false,
      "useBaseUrl": false,
      "generateBaseUrlProperty": true,
      "generateSyncMethods": false,
      "generatePrepareRequestAndProcessResponseAsAsyncMethods": false,
      "exposeJsonSerializerSettings": false,
      "clientClassAccessModifier": "public",
      "typeAccessModifier": "public",
      "generateContractsOutput": false,
      "contractsNamespace": "CoverGo.Cases.Client.Rest",
      "contractsOutputFilePath": "./CasesRestClientContracts.cs",
      "parameterDateTimeFormat": "s",
      "parameterDateFormat": "yyyy-MM-dd",
      "generateUpdateJsonSerializerSettingsMethod": true,
      "useRequestAndResponseSerializationSettings": false,
      "serializeTypeInformation": false,
      "queryNullValue": "",
      "className": "CasesRestClient",
      "operationGenerationMode": "SingleClientFromOperationId",
      "additionalNamespaceUsages": [
        "CoverGo.DomainUtils",
        "Newtonsoft.Json.Linq"
      ],
      "additionalContractNamespaceUsages": [],
      "generateOptionalParameters": false,
      "generateJsonMethods": false,
      "enforceFlagEnums": false,
      "parameterArrayType": "System.Collections.Generic.IEnumerable",
      "parameterDictionaryType": "System.Collections.Generic.IDictionary",
      "responseArrayType": "System.Collections.Generic.List",
      "responseDictionaryType": "System.Collections.Generic.Dictionary",
      "wrapResponses": false,
      "wrapResponseMethods": [],
      "generateResponseClasses": false,
      "responseClass": "Result",
      "namespace": "CoverGo.Cases.Client.Rest",
      "requiredPropertiesMustBeDefined": true,
      "dateType": "System.DateTime",
      "jsonConverters": null,
      "anyType": "object",
      "dateTimeType": "System.DateTime",
      "timeType": "System.TimeSpan",
      "timeSpanType": "System.TimeSpan",
      "arrayType": "System.Collections.Generic.List",
      "arrayInstanceType": "System.Collections.Generic.List",
      "dictionaryType": "System.Collections.Generic.Dictionary",
      "dictionaryInstanceType": "System.Collections.Generic.Dictionary",
      "arrayBaseType": "System.Collections.Generic.List",
      "dictionaryBaseType": "System.Collections.Generic.Dictionary",
      "classStyle": "Poco",
      "jsonLibrary": "NewtonsoftJson",
      "generateDefaultValues": true,
      "generateDataAnnotations": false,
      "excludedTypeNames": [
        "EventLog",
        "EventQuery",
        "CurrencyCode",
        "DateRange",
        "FieldsWhere",
        "FieldsWhereCondition",
        "DeleteCommand",
        "RemoveCommand",
        "QueryArguments",
        "OrderBy",
        "OrderByType",
        "Result",
        "CreatedStatus",
        "Error",
        "ScalarValue",
        "KeyScalarValue",
        "SystemObject",
        "Where"
      ],
      "excludedParameterNames": [],
      "handleReferences": true,
      "generateImmutableArrayProperties": false,
      "generateImmutableDictionaryProperties": false,
      "jsonSerializerSettingsTransformationMethod": null,
      "inlineNamedArrays": false,
      "inlineNamedDictionaries": false,
      "inlineNamedTuples": true,
      "inlineNamedAny": false,
      "generateDtoTypes": true,
      "generateOptionalPropertiesAsNullable": false,
      "generateNullableReferenceTypes": true,
      "templateDirectory": null,
      "typeNameGeneratorType": null,
      "propertyNameGeneratorType": null,
      "enumNameGeneratorType": null,
      "serviceHost": null,
      "serviceSchemes": null,
      "output": "./CasesRestClient.cs",
      "newLineBehavior": "Auto"
    }
  }
}
