<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <PackageId>CoverGo.Cases.Client.Rest</PackageId>
    <Company>CoverGo</Company>
    <PackageDescription>CoverGo.Cases service REST client</PackageDescription>
    <RepositoryUrl>https://github.com/CoverGo/Cases</RepositoryUrl>
    <Version>1.0.0</Version>
    <Authors>CoverGo</Authors>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <Description>CoverGo.Cases service REST client</Description>
    <PackageProjectUrl>https://github.com/CoverGo/Cases</PackageProjectUrl>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.DomainUtils" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="NSwag.MSBuild">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <Target Name="NSwag" BeforeTargets="Build">
      <Exec Command="dotnet build ../CoverGo.Cases.Application/CoverGo.Cases.Application.csproj -c $(Configuration)" Condition="'$(BuildApplicationProjectForRestClientGeneration)' == 'true'" />
      <Exec Command="$(NSwagExe_Net80) run nswag.json /variables:Configuration=$(Configuration)" Condition="'$(SonarQubeBuildDirectory)' == '' And Exists('$(NSwagExe_Net80)')" />
      <Exec Command="$(NSwagExe_Net70) run nswag.json /variables:Configuration=$(Configuration)" Condition="'$(SonarQubeBuildDirectory)' == '' And !Exists('$(NSwagExe_Net80)') And Exists('$(NSwagExe_Net70)')" />
  </Target>
</Project>
