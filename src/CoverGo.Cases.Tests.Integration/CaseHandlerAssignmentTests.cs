﻿using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.DomainUtils;
using CoverGo.Proxies.Auth;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration;

public class CaseHandlerAssignmentTests : TestBase
{
    [Fact]
    public async Task GIVEN_existing_case_AND_handler_WHEN_assigning_handler_THEN_success()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string loginId, string entityId) = await CreateHandlerAccount();

        // Act (When)
        var input = new cases_AssignHandlersInput
        {
            caseId = caseId,
            portalUserIds = new List<string>() { loginId }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignHandler(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeTrue();
        Case @case = await QueryCase(caseId);
        @case.Handlers.Should().HaveCount(1);
        @case.Stakeholders.Should().HaveCount(1);
        CaseHandler handler = @case.Handlers!.First();
        handler.EntityId.Should().Be(entityId);
        handler.PortalUserId.Should().Be(loginId);
        handler.StakeholderId.Should().Be(@case.Stakeholders!.First().Id);
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_handler_WHEN_assigning_multiple_handler_THEN_success()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string loginId, string entityId) = await CreateHandlerAccount();
        (string loginId2, string entityId2) = await CreateHandlerAccount();

        // Act (When)
        var input = new cases_AssignHandlersInput
        {
            caseId = caseId,
            portalUserIds = new List<string>() { loginId, loginId2 }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignHandler(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeTrue();
        Case @case = await QueryCase(caseId);
        @case.Handlers.Should().HaveCount(2);
        @case.Stakeholders.Should().HaveCount(2);
        CaseHandler handler = @case.Handlers!.First(a => a.PortalUserId == loginId);
        handler.EntityId.Should().Be(entityId);
        handler.StakeholderId.Should().Be(@case.Stakeholders!.First().Id);

        CaseHandler handler2 = @case.Handlers!.First(a => a.PortalUserId == loginId2);
        handler2.EntityId.Should().Be(entityId2);
        handler2.StakeholderId.Should().Be(@case.Stakeholders!.Last().Id);
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_handler_WHEN_assigning_duplicate_handler_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string loginId, string entityId) = await CreateHandlerAccount();
        var input = new cases_AssignHandlersInput
        {
            caseId = caseId,
            portalUserIds = new List<string>() { loginId }
        };
        cases_ResultOfCreatedStatus assignResultSetup = await AssignHandler(input);
        assignResultSetup.isSuccess.Should().BeTrue();

        // Act (When)
        cases_ResultOfCreatedStatus assignResult = await AssignHandler(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_WHEN_assigning_duplicate_handler_in_same_call_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string loginId, string entityId) = await CreateHandlerAccount();

        // Act (When)
        var input = new cases_AssignHandlersInput
        {
            caseId = caseId,
            portalUserIds = new List<string>() { loginId, loginId }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignHandler(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_assigning_handler_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();

        // Act (When)
        var input = new cases_AssignHandlersInput
        {
            caseId = caseId,
            portalUserIds = new List<string>() { Guid.NewGuid().ToString() }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignHandler(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_handler_WHEN_assigning_handler_THEN_error()
    {
        // Arrange (Given)
        (string loginId, string entityId) = await CreateHandlerAccount();

        // Act (When)
        var input = new cases_AssignHandlersInput
        {
            caseId = Guid.NewGuid().ToString(),
            portalUserIds = new List<string>() { loginId }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignHandler(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_existing_handler_WHEN_unassigning_handler_THEN_success()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string loginId, string entityId) = await CreateHandlerAccount();

        var input = new cases_AssignHandlersInput
        {
            caseId = caseId,
            portalUserIds = new List<string>() { loginId }
        };
        cases_ResultOfCreatedStatus assignResultSetup = await AssignHandler(input);
        assignResultSetup.isSuccess.Should().BeTrue();

        // Act (When)
        var unassignInput = new cases_UnassignHandlersInput { caseId = caseId, portalUserIds = new List<string> { loginId } };
        cases_Result unassignResult = await UnassignHandler(unassignInput);

        // Assert (Then)
        unassignResult.isSuccess.Should().BeTrue();
        Case @case = await QueryCase(caseId);
        @case.Handlers.Should().HaveCount(0);
        @case.Stakeholders.Should().HaveCount(0);
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_unassigning_handler_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();

        // Act (When)
        var unassignInput = new cases_UnassignHandlersInput { caseId = caseId, portalUserIds = new List<string> { Guid.NewGuid().ToString() } };
        cases_Result unassignResult = await UnassignHandler(unassignInput);

        // Assert (Then)
        unassignResult.isSuccess.Should().BeFalse();
        unassignResult.errors.Should().NotBeNullOrEmpty();
    }

    private async Task<string> CreateCase()
    {
        var command = new CreateCaseCommand
        {
            Name = Guid.NewGuid().ToString(),
        };

        ResultOfString result = await CasesRestClient.Case_CreateAsync(TenantId, command);

        return result.Value;
    }

    private async Task<Case> QueryCase(string caseId)
    {
        Case @case = (await CasesRestClient.Case_GetAsync(TenantId, new QueryArgumentsOfCaseWhere { Where = new CaseWhere { Id = caseId } }))!.First();

        return @case;
    }

    private async Task<(string loginId, string entityId)> CreateHandlerAccount()
    {
        string name = CreateNewGuid();
        CreateLoginCommand command = new()
        {
            Username = name,
            ClientId = "admin",
            Password = CreateNewGuid(),
            Email = $"{name}@covergo.com",
            IsEmailConfirmed = true,
            EntityId = CreateNewGuid()
        };

        Result<CreatedStatus> res = await AuthService.CreateLoginAsync(TenantId, command);

        return (res.Value.Id, command.EntityId);
    }

    public async Task<cases_ResultOfCreatedStatus> AssignHandler(cases_AssignHandlersInput input)
    {
        string mutation = new MutationBuilder()
            .cases_AssignHandlers(
                new MutationBuilder.cases_AssignHandlersArgs(input),
                new cases_ResultOfCreatedStatusBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_ResultOfCreatedStatus>(mutation);
    }

    public async Task<cases_Result> UnassignHandler(cases_UnassignHandlersInput input)
    {
        string mutation = new MutationBuilder()
            .cases_UnassignHandlers(
                new MutationBuilder.cases_UnassignHandlersArgs(input),
                new cases_ResultBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_Result>(mutation);
    }
}
