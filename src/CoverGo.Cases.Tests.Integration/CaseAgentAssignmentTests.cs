﻿using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.ChannelManagement.Client;
using FluentAssertions;
using StrawberryShake;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration;

public class CaseAgentAssignmentTests : TestBase
{
    [Fact]
    public async Task GIVEN_existing_case_AND_agent_WHEN_assigning_agent_primary_THEN_success()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();

        // Act (When)
        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.PRIMARY } }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeTrue();
        Case @case = await QueryCase(caseId);
        @case.Agents.Should().HaveCount(1);
        @case.Stakeholders.Should().HaveCount(1);
        CaseAgent agent = @case.Agents!.First();
        agent.Id.Should().Be(agentId);
        agent.RoleType.Should().Be(cases_AgentRoleType.PRIMARY);
        agent.EntityId.Should().Be(entityId);
        agent.PortalUserId.Should().Be(loginId);
        agent.StakeholderId.Should().Be(@case.Stakeholders!.First().Id);
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_agent_WHEN_assigning_multiple_agent_servicing_THEN_success()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();
        (string agentId2, string loginId2, string entityId2) = await CreateAgentAccount();

        // Act (When)
        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> {
                new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.SERVICING },
                new cases_AssignAgentInput() { agentId = agentId2, roleType = cases_AgentRoleType.SERVICING }
            }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeTrue();
        Case @case = await QueryCase(caseId);
        @case.Agents.Should().HaveCount(2);
        @case.Stakeholders.Should().HaveCount(2);
        CaseAgent agent = @case.Agents!.First(a => a.Id == agentId);
        agent.Id.Should().Be(agentId);
        agent.RoleType.Should().Be(cases_AgentRoleType.SERVICING);
        agent.EntityId.Should().Be(entityId);
        agent.PortalUserId.Should().Be(loginId);
        agent.StakeholderId.Should().Be(@case.Stakeholders!.First().Id);

        CaseAgent agent2 = @case.Agents!.First(a => a.Id == agentId2);
        agent2.Id.Should().Be(agentId2);
        agent2.RoleType.Should().Be(cases_AgentRoleType.SERVICING);
        agent2.EntityId.Should().Be(entityId2);
        agent2.PortalUserId.Should().Be(loginId2);
        agent2.StakeholderId.Should().Be(@case.Stakeholders!.Last().Id);
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_assigning_agent_servicing_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();

        // Act (When)
        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = Guid.NewGuid().ToString(), roleType = cases_AgentRoleType.SERVICING } }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_agent_WHEN_assigning_agent_servicing_THEN_error()
    {
        // Arrange (Given)
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();

        // Act (When)
        var input = new cases_AssignAgentsInput
        {
            caseId = Guid.NewGuid().ToString(),
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.SERVICING } }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_existing_primary_agent_WHEN_assigning_agent_primary_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();
        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.PRIMARY } }
        };
        cases_ResultOfCreatedStatus assignResultSetup = await AssignAgent(input);
        assignResultSetup.isSuccess.Should().BeTrue();

        // Act (When)
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_agent_WHEN_assigning_duplicate_in_same_call_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();

        // Act (When)
        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.SERVICING }, new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.SERVICING } }
        };
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_existing_servicing_agent_WHEN_assigning_duplicate_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();
        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.SERVICING } }
        };
        cases_ResultOfCreatedStatus assignResultSetup = await AssignAgent(input);
        assignResultSetup.isSuccess.Should().BeTrue();

        // Act (When)
        cases_ResultOfCreatedStatus assignResult = await AssignAgent(input);

        // Assert (Then)
        assignResult.isSuccess.Should().BeFalse();
        assignResult.errors.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task GIVEN_existing_case_AND_existing_primary_agent_WHEN_unassigning_agent_THEN_success()
    {
        // Arrange (Given)
        string caseId = await CreateCase();
        (string agentId, string loginId, string entityId) = await CreateAgentAccount();

        var input = new cases_AssignAgentsInput
        {
            caseId = caseId,
            agents = new List<cases_AssignAgentInput> { new cases_AssignAgentInput() { agentId = agentId, roleType = cases_AgentRoleType.PRIMARY } }
        };
        cases_ResultOfCreatedStatus assignResultSetup = await AssignAgent(input);
        assignResultSetup.isSuccess.Should().BeTrue();

        // Act (When)
        var unassignInput = new cases_UnassignAgentsInput { caseId = caseId, agentIds = new List<string> { agentId } };
        cases_Result unassignResult = await UnassignAgent(unassignInput);

        // Assert (Then)
        unassignResult.isSuccess.Should().BeTrue();
        Case @case = await QueryCase(caseId);
        @case.Agents.Should().HaveCount(0);
        @case.Stakeholders.Should().HaveCount(0);
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_unassigning_agent_THEN_error()
    {
        // Arrange (Given)
        string caseId = await CreateCase();

        // Act (When)
        var unassignInput = new cases_UnassignAgentsInput { caseId = caseId, agentIds = new List<string> { Guid.NewGuid().ToString() } };
        cases_Result unassignResult = await UnassignAgent(unassignInput);

        // Assert (Then)
        unassignResult.isSuccess.Should().BeFalse();
        unassignResult.errors.Should().NotBeNullOrEmpty();
    }

    private async Task<string> CreateCase()
    {
        var command = new CreateCaseCommand
        {
            Name = Guid.NewGuid().ToString(),
        };

        ResultOfString result = await CasesRestClient.Case_CreateAsync(TenantId, command);

        return result.Value;
    }

    private async Task<Case> QueryCase(string caseId)
    {
        Case @case = (await CasesRestClient.Case_GetAsync(TenantId, new QueryArgumentsOfCaseWhere { Where = new CaseWhere { Id = caseId } }))!.First();

        return @case;
    }

    private async Task<(string agentId, string loginId, string entityId)> CreateAgentAccount()
    {
        CreateSalesChannelInput salesChannelInput = new()
        {
            ChannelType = "SLS",
            ActiveFromDateTime = DateTime.Now,
            HasPortalAccess = true
        };

        IOperationResult<ICreateSalesChannelResult> salesChannel = await ChannelManagementClient.CreateSalesChannel.ExecuteAsync(salesChannelInput);
        salesChannel.EnsureNoErrors();
        string salesChannelId = salesChannel.Data!.CreateSalesChannel!.SalesChannelState!.SalesChannelID;

        CreateDistributorInput distributorInput = new()
        {
            Class = "SLS",
            SalesChannelID = salesChannelId,
            ActiveFromDateTime = DateTime.UtcNow,
            HasPortalAccess = true,
            HasPortalAccessDefault = true,
            UmbrellaPaymentRule = false,
            Party = new OrganizationPartyFieldsInput
            {
                DistributorName = "FTLife",
                AddrLine1 = "test",
                PostCode = "852",
                CountryCode = "HKG"
            },
        };
        IOperationResult<ICreateDistributorResult> createDistributorResult = await ChannelManagementClient.CreateDistributor.ExecuteAsync(distributorInput);
        var distributorId = createDistributorResult.Data!.CreateDistributor.DistributorState!.DistributorId;

        var email = $"{Guid.NewGuid()}@test.com";
        var input = new CreateAgentAccountInput
        {
            CreateAgentInput = new()
            {
                DistributorID = distributorId,
                AgentNumber = Guid.NewGuid().ToString(),
                IsVirtualAgent = false,
                HasAgentNBI = true,
                IsDirectAgent = true,
                Party = new()
                {
                    FirstName = "test",
                    Surname = "test",
                    Salutation = "MR",
                    MainContactEmail = email,
                },
                ActiveFromDateTime = DateTime.UtcNow.AddDays(-1),
                CreditCheckRunningTotal = 2048
            },
            CreateLoginInput = new()
            {
                ClientId = "admin",
                TemplateId = Guid.NewGuid().ToString(),
                Username = email,
                Email = email,
                PermissionGroupIds = new List<string>
                {

                },
                IsEmailConfirmed = false,
                IsPasswordValidationDobRequire = false,
                IgnorePasswordValidation = false,
            }
        };
        IOperationResult<ICreateAgentAccountResult> result = await ChannelManagementClient.CreateAgentAccount.ExecuteAsync(input);
        string agentId = result.Data!.CreateAgentAccount!.CreateAgentAccount!.Agent!.AgentID!;
        string loginId = result.Data.CreateAgentAccount.CreateAgentAccount.Login!.Id!;
        string entityId = result.Data.CreateAgentAccount.CreateAgentAccount.Internal!.Id!;
        return (agentId, loginId, entityId);
    }

    public async Task<cases_ResultOfCreatedStatus> AssignAgent(cases_AssignAgentsInput input)
    {
        string mutation = new MutationBuilder()
            .cases_AssignAgents(
                new MutationBuilder.cases_AssignAgentsArgs(input),
                new cases_ResultOfCreatedStatusBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_ResultOfCreatedStatus>(mutation);
    }

    public async Task<cases_Result> UnassignAgent(cases_UnassignAgentsInput input)
    {
        string mutation = new MutationBuilder()
            .cases_UnassignAgents(
                new MutationBuilder.cases_UnassignAgentsArgs(input),
                new cases_ResultBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_Result>(mutation);
    }
}
