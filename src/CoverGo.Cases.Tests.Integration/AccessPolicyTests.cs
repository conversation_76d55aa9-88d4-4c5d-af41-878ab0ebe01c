﻿using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using AccessPolicy = CoverGo.Cases.Client.Rest.AccessPolicy;

namespace CoverGo.Cases.Tests.Integration;

public class AccessPolicyTests : RestrictedContentBaseTest
{
    #region query

    [Fact]
    public async Task GIVEN_anonymous_user_WHEN_query_and_access_policy_is_missing_THEN_should_not_filter()
    {
        List<Case> cases = await AnonymousUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                Source = RandomTag
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(3);
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().Contain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    public async Task GIVEN_anonymous_user_WHEN_query_and_access_policy_is_standard_THEN_should_return_standard_or_null()
    {
        List<Case> cases = await AnonymousUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Standard }
                }
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(2);
        cases!.Should().NotContain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().Contain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    public async Task GIVEN_anonymous_user_WHEN_query_and_access_policy_is_restricted_THEN_should_return_restricted()
    {
        List<Case> cases = await AnonymousUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Restricted }
                }
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(1);
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().NotContain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().NotContain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    public async Task GIVEN_user_with_permission_WHEN_query_and_access_policy_is_standard_THEN_should_return_standard_or_null()
    {
        List<Case> cases = await FullPermissionUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Standard }
                }
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(2);
        cases!.Should().NotContain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().Contain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    public async Task GIVEN_user_with_permission_WHEN_query_and_access_policy_is_restricted_THEN_should_return_restricted()
    {
        List<Case> cases = await FullPermissionUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Restricted }
                }
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(1);
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().NotContain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().NotContain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    public async Task GIVEN_user_with_permission_WHEN_query_and_access_policy_is_missing_THEN_should_return_standard_or_null()
    {
        List<Case> cases = await FullPermissionUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                Source = RandomTag
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(3);
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().Contain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    [Trait("Category", "Debug")]
    public async Task GIVEN_user_with_no_permission_WHEN_query_and_access_policy_is_missing_THEN_should_return_standard_or_null()
    {
        List<Case> cases = await NoPermissionUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                Source = RandomTag
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(2);
        cases!.Should().NotContain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().Contain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    public async Task GIVEN_user_with_no_permission_WHEN_query_and_access_policy_is_standard_THEN_should_return_standard_or_null()
    {
        List<Case> cases = await NoPermissionUserCaseClient.Case_GetAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Standard }
                }
            }
        });

        cases.Should().NotBeNullOrEmpty();
        cases!.Count.Should().Be(2);
        cases!.Should().NotContain(p => p.AccessPolicy == AccessPolicy.Restricted && p.Name == "restricted");
        cases!.Should().Contain(p => p.AccessPolicy == AccessPolicy.Standard && p.Name == "standard");
        cases!.Should().Contain(p => p.AccessPolicy == null && p.Name == "null");
    }

    [Fact]
    // [Trait("Category", "Debug")]
    public async Task GIVEN_user_with_no_permission_WHEN_query_and_access_policy_is_restricted_THEN_should_throw_exception()
    {
        Func<Task<List<Case>>> act = async () => await NoPermissionUserCaseClient.Case_GetAsync(TenantId,
            new()
            {
                Where = new CaseWhere
                {
                    And = new List<CaseWhere>
                    {
                        new() { Source = RandomTag },
                        new() { AccessPolicy = AccessPolicy.Restricted }
                    }
                }
            });
        await act.Should().ThrowAsync<CasesRestClientException>();
    }

    #endregion

    #region queryids

    [Fact]
    public async Task GIVEN_anonymous_user_WHEN_queryids_and_access_policy_is_missing_THEN_should_not_filter()
    {
        List<string> ids = await AnonymousUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                Source = RandomTag
            }
        });

        ids!.Count.Should().Be(3);
        ids!.Should().Contain(NullCaseId);
        ids!.Should().Contain(StandardCaseId);
        ids!.Should().Contain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_anonymous_user_WHEN_queryids_and_access_policy_is_standard_THEN_should_return_standard_or_null()
    {
        List<string> ids = await AnonymousUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Standard }
                }
            }
        });

        ids!.Count.Should().Be(2);
        ids!.Should().Contain(NullCaseId);
        ids!.Should().Contain(StandardCaseId);
        ids!.Should().NotContain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_anonymous_user_WHEN_queryids_and_access_policy_is_restricted_THEN_should_return_restricted()
    {
        List<string> ids = await AnonymousUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Restricted }
                }
            }
        });

        ids!.Count.Should().Be(1);
        ids!.Should().NotContain(NullCaseId);
        ids!.Should().NotContain(StandardCaseId);
        ids!.Should().Contain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_user_with_permission_WHEN_queryids_and_access_policy_is_standard_THEN_should_return_standard_or_null()
    {
        List<string> ids = await FullPermissionUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Standard }
                }
            }
        });

        ids!.Count.Should().Be(2);
        ids!.Should().Contain(NullCaseId);
        ids!.Should().Contain(StandardCaseId);
        ids!.Should().NotContain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_user_with_permission_WHEN_queryids_and_access_policy_is_restricted_THEN_should_return_restricted()
    {
        List<string> ids = await FullPermissionUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Restricted }
                }
            }
        });

        ids!.Count.Should().Be(1);
        ids!.Should().NotContain(NullCaseId);
        ids!.Should().NotContain(StandardCaseId);
        ids!.Should().Contain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_user_with_permission_WHEN_queryids_and_access_policy_is_missing_THEN_should_return_standard_or_null()
    {
        List<string> ids = await FullPermissionUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                Source = RandomTag
            }
        });

        ids!.Count.Should().Be(3);
        ids!.Should().Contain(NullCaseId);
        ids!.Should().Contain(StandardCaseId);
        ids!.Should().Contain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_user_with_no_permission_WHEN_queryids_and_access_policy_is_missing_THEN_should_return_standard_or_null()
    {
        List<string> ids = await NoPermissionUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                Source = RandomTag
            }
        });

        ids!.Count.Should().Be(2);
        ids!.Should().Contain(NullCaseId);
        ids!.Should().Contain(StandardCaseId);
        ids!.Should().NotContain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_user_with_no_permission_WHEN_queryids_and_access_policy_is_standard_THEN_should_return_standard_or_null()
    {
        List<string> ids = await NoPermissionUserCaseClient.Case_GetIdsAsync(TenantId, new()
        {
            Where = new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = AccessPolicy.Standard }
                }
            }
        });

        ids!.Count.Should().Be(2);
        ids!.Should().Contain(NullCaseId);
        ids!.Should().Contain(StandardCaseId);
        ids!.Should().NotContain(RestrictedCaseId);
    }

    [Fact]
    public async Task GIVEN_user_with_no_permission_WHEN_queryids_and_access_policy_is_restricted_THEN_should_throw_exception()
    {
        Func<Task<List<string>>> act = async () => await NoPermissionUserCaseClient.Case_GetIdsAsync(TenantId,
            new()
            {
                Where = new CaseWhere
                {
                    And = new List<CaseWhere>
                    {
                        new() { Source = RandomTag },
                        new() { AccessPolicy = AccessPolicy.Restricted }
                    }
                }
            });
        await act.Should().ThrowAsync<CasesRestClientException>();
    }

    #endregion

    #region queryProposalIds

    [Theory]
    [InlineData(true, false, null, false)]
    [InlineData(true, false, AccessPolicy.Standard, false)]
    [InlineData(true, false, AccessPolicy.Restricted, false)]
    [InlineData(false, true, null, false)]
    [InlineData(false, true, AccessPolicy.Standard, false)]
    [InlineData(false, true, AccessPolicy.Restricted, false)]
    [InlineData(false, false, null, false)]
    [InlineData(false, false, AccessPolicy.Standard, false)]
    [InlineData(false, false, AccessPolicy.Restricted, true)]
    public async Task GIVEN_users_WHEN_queryProposalIds_THEN_should_authorize(bool isAnonymousUser, bool userHasPermission, AccessPolicy? accessPolicyFilter, bool expectedThrowError)
    {
        CasesRestClient client = isAnonymousUser
            ? AnonymousUserCaseClient
            : userHasPermission
                ? FullPermissionUserCaseClient
                : NoPermissionUserCaseClient;
        CaseWhere caseWhere = accessPolicyFilter == null
            ? new CaseWhere { Source = RandomTag }
            : new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = accessPolicyFilter }
                }
            };
        Func<Task<List<string>>> act = async () => await client.Case_GetProposalIdsAsync(TenantId, new() { Where = caseWhere });
        if (expectedThrowError)
        {
            await act.Should().ThrowAsync<CasesRestClientException>();
        }
        else
        {
            await act.Should().NotThrowAsync();
        }
    }

    #endregion

    #region queryOfferIds

    [Theory]
    [InlineData(true, false, null, false)]
    [InlineData(true, false, AccessPolicy.Standard, false)]
    [InlineData(true, false, AccessPolicy.Restricted, false)]
    [InlineData(false, true, null, false)]
    [InlineData(false, true, AccessPolicy.Standard, false)]
    [InlineData(false, true, AccessPolicy.Restricted, false)]
    [InlineData(false, false, null, false)]
    [InlineData(false, false, AccessPolicy.Standard, false)]
    [InlineData(false, false, AccessPolicy.Restricted, true)]
    public async Task GIVEN_users_WHEN_queryOfferIds_THEN_should_authorize(bool isAnonymousUser, bool userHasPermission, AccessPolicy? accessPolicyFilter, bool expectedThrowError)
    {
        CasesRestClient client = isAnonymousUser
            ? AnonymousUserCaseClient
            : userHasPermission
                ? FullPermissionUserCaseClient
                : NoPermissionUserCaseClient;
        CaseWhere caseWhere = accessPolicyFilter == null
            ? new CaseWhere { Source = RandomTag }
            : new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = accessPolicyFilter }
                }
            };
        Func<Task<List<string>>> act = async () => await client.Case_GetOfferIdsAsync(TenantId, new() { Where = caseWhere });
        if (expectedThrowError)
        {
            await act.Should().ThrowAsync<CasesRestClientException>();
        }
        else
        {
            await act.Should().NotThrowAsync();
        }
    }

    #endregion

    #region queryReport

    [Theory]
    [InlineData(true, false, null, false )]
    [InlineData(true, false, AccessPolicy.Standard, false)]
    [InlineData(true, false, AccessPolicy.Restricted, false)]
    [InlineData(false, true, null, false)]
    [InlineData(false, true, AccessPolicy.Standard, false)]
    [InlineData(false, true, AccessPolicy.Restricted, false)]
    [InlineData(false, false, null, false)]
    [InlineData(false, false, AccessPolicy.Standard, false)]
    [InlineData(false, false, AccessPolicy.Restricted, true)]
    public async Task GIVEN_users_WHEN_queryReport_THEN_should_authorize(bool isAnonymousUser, bool userHasPermission, AccessPolicy? accessPolicyFilter, bool expectedThrowError)
    {
        CasesRestClient client = isAnonymousUser
            ? AnonymousUserCaseClient
            : userHasPermission
                ? FullPermissionUserCaseClient
                : NoPermissionUserCaseClient;
        CaseWhere caseWhere = accessPolicyFilter == null
            ? new CaseWhere { Source = RandomTag }
            : new CaseWhere
            {
                And = new List<CaseWhere>
                {
                    new() { Source = RandomTag },
                    new() { AccessPolicy = accessPolicyFilter }
                }
            };
        Func<Task<List<CasesReport>>> act = async () => await client.Case_GetReportAsync(TenantId, new() { Where = caseWhere });
        if (expectedThrowError)
        {
            await act.Should().ThrowAsync<CasesRestClientException>();
        }
        else
        {
            await act.Should().NotThrowAsync();
        }
    }

    #endregion
}