﻿using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.DomainUtils;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration;

public class CasesUpdateTests
{
    readonly CasesRestClient _client;
    readonly string _tenantId;

    public CasesUpdateTests()
    {
        _tenantId = UserCredentials.Admin.TenantId;
        _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_update_fieldsPatch_THEN_fields_is_updated()
    {
        var command = new CreateCaseCommand { Fields = "{\"oldField\":\"oldValue\"}" };

        ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
        string caseId = createResult!.Value;

        var updateCaseCommand = new UpdateCaseCommand { FieldsPatch = "[{\"op\":\"add\",\"path\":\"newField\",\"value\":\"newValue\"}]" };
        Result updateResult = await _client.Case_UpdateAsync(_tenantId, caseId, updateCaseCommand);
        updateResult.Status.Should().Be("success");

        Case @case = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = new CaseWhere { Id = caseId } })).First();
        @case.Fields.Should().NotBeNull();
        var fieldsJObject = JObject.FromObject(@case.Fields);
        string newField = fieldsJObject["newField"].Value<string>();
        newField.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_update_invalid_fieldsPatch_THEN_invalid_fieldsPatch_error_is_returned()
    {
        var command = new CreateCaseCommand { };

        ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
        string caseId = createResult!.Value;

        var updateCaseCommand = new UpdateCaseCommand { FieldsPatch = "{\"op\":\"add\",\"path\":\"requestForOffers\",\"value\":[{\"label\":1,\"value\":1}]}" };
        Result updateResult = await _client.Case_UpdateAsync(_tenantId, caseId, updateCaseCommand);
        updateResult.Status.Should().Be("failure");
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_update_channelId_THEN_channelId_is_updated()
    {
        var command = new CreateCaseCommand { ChannelId = "Channel1" };

        ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
        string caseId = createResult!.Value;

        var updateCaseCommand = new UpdateCaseCommand { ChannelId = "Channel2", IsChannelIdChanged = true };
        Result updateResult = await _client.Case_UpdateAsync(_tenantId, caseId, updateCaseCommand);
        updateResult.Status.Should().Be("success");

        Case @case = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = new CaseWhere { Id = caseId } })).First();
        @case.Should().NotBeNull();
        @case.ChannelId.Should().Be("Channel2");
    }

    [Fact]
    public async Task GIVEN_existing_case_WHEN_addIssuedPolicy_THEN_issuedPolicyId_is_added()
    {
        var command = new CreateCaseCommand { };

        ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
        string caseId = createResult!.Value;

        string policyId = Guid.NewGuid().ToString();
        var addIssuedPolicyCommand = new AddIssuedPolicyCommand
        {
            PolicyId = policyId,
        };
        Result updateResult = await _client.Case_AddIssuedPolicyAsync(_tenantId, caseId, addIssuedPolicyCommand);
        updateResult.IsSuccess.Should().BeTrue();

        Case @case = (await _client.Case_GetAsync(_tenantId, new QueryArgumentsOfCaseWhere { Where = new CaseWhere { Id = caseId } })).First();
        @case.IssuedPoliciesIds.Should().HaveCount(1);
        @case.IssuedPoliciesIds.First().Should().Be(policyId);
        @case.Status.Should().Be(Domain.CaseStatus.Issued);
    }
}
