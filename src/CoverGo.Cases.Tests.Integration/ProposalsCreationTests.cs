﻿using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.DomainUtils;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration
{
    public class ProposalsCreationTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public ProposalsCreationTests()
        {
            _tenantId = UserCredentials.Admin.TenantId;
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_proposal_WHEN_issue_proposals_for_tenant_dbs_uat_THEN_issuedAt_populated()
        {
            string tenantId = "dbs_uat";
            Proposal proposal = await CreateProposalWithSpecificTenant(tenantId);

            await _client.Proposal_IssueProposalAsync(tenantId, proposal.CaseId, new IssueProposalCommand
            {
                ProposalId = proposal.Id,
                IssuedById = Guid.NewGuid().ToString()
            });

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { proposal.CaseId }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(tenantId, proposal.CaseId, new DeleteCaseCommand { DeletedById = proposal.CaseId });

            cases.Count.Should().Be(1);
            cases[0].Proposals.Count.Should().Be(1);
            cases[0].Proposals.First().IssuedAt.HasValue.Should().Be(true);
        }

        [Fact]
        public async Task GIVEN__filterproposal_WHEN_issue_proposals_for_tenant_dbs_uat_THEN_only_cases_with_issuedAt_populated()
        {
            string tenantId = "dbs_uat";

            Case caseNoProposal = await CreateCase(tenantId);
            Proposal proposal = await CreateProposalWithSpecificTenant(tenantId);

            await _client.Proposal_IssueProposalAsync(tenantId, proposal.CaseId, new IssueProposalCommand
            {
                ProposalId = proposal.Id,
                IssuedById = Guid.NewGuid().ToString()
            });

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Proposal = new ProposalWhere
                    {
                        IssuedAt_gt = DateTime.UtcNow.AddMinutes(-1)
                    }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(tenantId, caseNoProposal.Id, new DeleteCaseCommand { DeletedById = caseNoProposal.Id });
            await _client.Case_DeleteAsync(tenantId, proposal.CaseId, new DeleteCaseCommand { DeletedById = proposal.CaseId });

            cases.Count.Should().Be(1);
            cases[0].Proposals.Count.Should().Be(1);
            cases[0].Proposals.First().IssuedAt.HasValue.Should().Be(true);
        }

        [Fact]
        public async Task GIVEN_existing_case_WHEN_addProposal_THEN_proposal_is_added()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            addResult.IsSuccess.Should().BeTrue();
            var proposalId = addResult.Value;

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.Should().HaveCount(1);
            @case.Proposals.First().Id.Should().Be(proposalId);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_WHEN_updateProposal_THEN_proposal_is_updated()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            string proposalNumber = Guid.NewGuid().ToString();
            UpdateProposalCommand updateCommand = new()
            {
                ProposalId = proposalId,
                ProposalNumber = proposalNumber,
                IsProposalNumberChanged = true
            };
            var updateResult = await _client.Proposal_UpdateProposalAsync(_tenantId, caseId, updateCommand, default);
            updateResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().ProposalNumber.Should().Be(proposalNumber);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_WHEN_issueProposal_THEN_proposal_is_issued()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            IssueProposalCommand issueCommand = new()
            {
                ProposalId = proposalId
            };
            Result? updateResult = await _client.Proposal_IssueProposalAsync(_tenantId, caseId, issueCommand, default);
            updateResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Status.Should().Be("ISSUED");
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_WHEN_rejectProposal_THEN_proposal_is_rejected()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            RejectProposalCommand rejectCommand = new()
            {
                ProposalId = proposalId
            };
            Result? updateResult = await _client.Proposal_RejectProposalAsync(_tenantId, caseId, rejectCommand, default);
            updateResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Status.Should().Be("REJECTED");
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_WHEN_removeProposal_THEN_proposal_is_removed()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            RemoveCommand removeCommand = new()
            {
                Id = proposalId
            };
            Result removeResult = await _client.Proposal_RemoveProposalAsync(_tenantId, caseId, removeCommand, default);
            removeResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.Should().HaveCount(0);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_WHEN_addOfferToProposal_THEN_offer_is_added()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            AddOfferCommand offerCommand = new()
            {
                ProposalId = proposalId,
                OfferNumber = Guid.NewGuid().ToString()
            };
            ResultOfString addOfferResult = await _client.Proposal_AddOfferAsync(_tenantId, caseId, offerCommand, null);
            addOfferResult.IsSuccess.Should().BeTrue();
            string offerId = addOfferResult.Value;

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Basket.Should().HaveCount(1);
            @case.Proposals.First().Basket.First().Id.Should().Be(offerId);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_and_offer_WHEN_update_offer_THEN_offer_is_updated()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            AddOfferCommand offerCommand = new()
            {
                ProposalId = proposalId,
                OfferNumber = Guid.NewGuid().ToString()
            };
            ResultOfString addOfferResult = await _client.Proposal_AddOfferAsync(_tenantId, caseId, offerCommand, null);
            addOfferResult.IsSuccess.Should().BeTrue();
            string offerId = addOfferResult.Value;

            string offerNumber = Guid.NewGuid().ToString();
            UpdateOfferCommand updateOfferCommand = new()
            {
                ProposalId = proposalId,
                OfferId = offerId,
                OfferNumber = offerNumber,
                IsOfferNumberChanged = true
            };
            Result updateResult = await _client.Proposal_UpdateOfferAsync(_tenantId, caseId, updateOfferCommand);
            updateResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Basket.First().OfferNumber.Should().Be(offerNumber);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_and_offer_WHEN_remove_offer_THEN_offer_is_removed()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            AddOfferCommand offerCommand = new()
            {
                ProposalId = proposalId,
                OfferNumber = Guid.NewGuid().ToString()
            };
            ResultOfString addOfferResult = await _client.Proposal_AddOfferAsync(_tenantId, caseId, offerCommand, null);
            addOfferResult.IsSuccess.Should().BeTrue();
            string offerId = addOfferResult.Value;

            RemoveOfferFromProposalCommand removeOfferCommand = new()
            {
                ProposalId = proposalId,
                OfferId = offerId,
            };
            Result removeResult = await _client.Proposal_RemoveOfferAsync(_tenantId, caseId, removeOfferCommand);
            removeResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Basket.Should().HaveCount(0);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_WHEN_addStakeholder_THEN_stakeholder_is_added()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            string entityId = Guid.NewGuid().ToString();
            AddStakeholderCommand addStakeholderCommand = new()
            {
                ProposalId = proposalId,
                EntityId = entityId,
                Type = "agent",
            };
            ResultOfString addStakeholderResult = await _client.Proposal_AddStakeholderOfProposalAsync(_tenantId, caseId, addStakeholderCommand, default);
            addStakeholderResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Stakeholders.Should().HaveCount(1);
            @case.Proposals.First().Stakeholders.First().EntityId.Should().Be(entityId);
        }

        [Fact]
        public async Task GIVEN_existing_case_and_proposal_and_proposal_WHEN_removeStakeholder_THEN_stakeholder_is_removed()
        {
            var command = new CreateCaseCommand { };

            ResultOfString createResult = await _client.Case_CreateAsync(_tenantId, command);
            string caseId = createResult!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = Guid.NewGuid().ToString(),
            };
            ResultOfString addResult = await _client.Proposal_AddProposalAsync(_tenantId, caseId, proposalCommand, null);
            string proposalId = addResult.Value;

            AddStakeholderCommand addStakeholderCommand = new()
            {
                ProposalId = proposalId,
                EntityId = Guid.NewGuid().ToString(),
                Type = "agent",
            };
            ResultOfString addStakeholderResult = await _client.Proposal_AddStakeholderOfProposalAsync(_tenantId, caseId, addStakeholderCommand, default);
            addStakeholderResult.IsSuccess.Should().BeTrue();
            string stakeholderId = addStakeholderResult.Value;

            string entityId = Guid.NewGuid().ToString();
            RemoveStakeholderCommand removeCommand = new()
            {
                Id = stakeholderId,
                ProposalId = proposalId
            };
            Result removeResult = await _client.Proposal_RemoveStakeholderFromProposalAsync(_tenantId, caseId, removeCommand, default);
            removeResult.IsSuccess.Should().BeTrue();

            var arg = new QueryArgumentsOfCaseWhere
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { caseId }
                }
            };

            Case @case = (await _client.Case_GetAsync(_tenantId, arg)).FirstOrDefault();

            @case.Proposals.First().Stakeholders.Should().HaveCount(0);
        }

        public async Task<Case> CreateCase(string tenantId)
        {
            var newCase = new Case
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString(),
                CaseNumber = Guid.NewGuid().ToString()
            };

            var command = new CreateCaseCommand
            {
                Name = newCase.Name,
                Description = newCase.Description,
                CaseNumber = newCase.CaseNumber
            };

            ResultOfString result = await _client.Case_CreateAsync(tenantId, command);
            newCase.Id = result!.Value;

            return newCase;
        }

        [Fact]
        public async Task GIVEN_proposal_WHEN_add_offer_THEN_events_populated()
        {
            string tenantId = "dbs_uat";
            Proposal proposal = await CreateProposalWithSpecificTenant(tenantId);

            var resAddOffer = await _client.Proposal_AddOfferAsync(tenantId, proposal.CaseId, new() { ProposalId = proposal.Id }, null);

            var cases = (await _client.Case_GetAsync(tenantId, new() { Where = new() { Id_in = new() { proposal.CaseId } } }))!;
            cases = cases.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(tenantId, proposal.CaseId, new DeleteCaseCommand { DeletedById = proposal.CaseId });

        }

        public async Task<Proposal> CreateProposalWithSpecificTenant(string tenantId)
        {
            Case newCase = new()
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString()
            };

            Proposal newProposal = new()
            {
                Name = Guid.NewGuid().ToString()
            };

            CreateCaseCommand command = new()
            {
                Name = newCase.Name,
                Description = newCase.Description
            };

            ResultOfString result = await _client.Case_CreateAsync(tenantId, command);
            newCase.Id = result!.Value;

            AddProposalCommand proposalCommand = new()
            {
                Name = newProposal.Name
            };

            ResultOfString addResult = await _client.Proposal_AddProposalAsync(tenantId, newCase.Id, proposalCommand, null);
            newProposal.Id = addResult!.Value;
            newProposal.CaseId = newCase.Id;

            return newProposal;
        }
    }
}
