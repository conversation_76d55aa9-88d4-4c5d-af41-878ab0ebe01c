<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>

    <IsPackable>false</IsPackable>

    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.Gateway.Client" />
    <PackageReference Include="CoverGo.MongoUtils" />
    <PackageReference Include="CoverGo.Policies.Client" />
    <PackageReference Include="CoverGo.Products.Client" />
    <PackageReference Include="CoverGo.ChannelManagement.Client" />
		<PackageReference Include="CoverGo.Reference.Client" />

    <PackageReference Include="CoverGo.Proxies.Auth" />
    <PackageReference Include="GraphQL.Client" />
    <PackageReference Include="GraphQL.Client.Serializer.Newtonsoft" />
    <PackageReference Include="GraphQL.Client.Serializer.SystemTextJson" />
    <PackageReference Include="IdentityModel" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting" />
    <PackageReference Include="JunitXml.TestLogger" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Cases.Client.Rest\CoverGo.Cases.Client.Rest.csproj" />
    <ProjectReference Include="..\CoverGo.Cases.Domain\CoverGo.Cases.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="referenceInputData.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
