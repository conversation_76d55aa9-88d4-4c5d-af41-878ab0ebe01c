﻿using CoverGo.Cases.Client;
using CoverGo.Cases.Domain.OfferStatusPermissions.Bupa;
using CoverGo.Cases.Tests.Integration.Utils;
using FluentAssertions;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration.Offers.Bupa
{
    public class SalesSuperUserTests : TestBase
    {
        [Fact]
        public async Task GIVEN_OfferWithStatusAdded_WHEN_UpdatingStatusToUnderwriterRequested_THEN_StatusIsUpdated()
        {
            // Arrange
            var caseId = await CaseHelper.CreateCase(CasesRestClient, UserCredentials.Admin.TenantId);
            var proposalId = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
            var offerId = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId, BupaOfferStatus.Added);

            var input = new cases_AdminUpdateOfferInput
            {
                status = new cases_SettableOfStringInput { value = BupaOfferStatus.UnderwriterRequested }
            };

            // Act
            var result = await PresalesUserOfferMutations.SecureUpdateOffer(caseId, proposalId, offerId, input);

            // Assert
            result.Should().NotBeNull();
            result.isSuccess.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_OfferWithStatusUnderwriterRequested_WHEN_UpdatingStatusToAdded_THEN_StatusIsUpdated()
        {
            // Arrange
            var caseId = await CaseHelper.CreateCase(CasesRestClient, UserCredentials.Admin.TenantId);
            var proposalId = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
            var offerId = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId, BupaOfferStatus.Added);

            var input = new cases_AdminUpdateOfferInput
            {
                status = new cases_SettableOfStringInput { value = BupaOfferStatus.UnderwriterRequested }
            };

            // Act
            var result = await PresalesUserOfferMutations.SecureUpdateOffer(caseId, proposalId, offerId, input);

            // Assert
            result.Should().NotBeNull();
            result.isSuccess.Should().BeTrue();
        }

        [Fact]
        public async Task GIVEN_OfferWithStatusUnderwriterRequested_WHEN_UpdatingStatusToUnderwriterInProgress_THEN_ErrorIsReturned()
        {
            // Arrange
            var caseId = await CaseHelper.CreateCase(CasesRestClient, UserCredentials.Admin.TenantId);
            var proposalId = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
            var offerId = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId, BupaOfferStatus.UnderwriterRequested);

            var input = new cases_AdminUpdateOfferInput
            {
                status = new cases_SettableOfStringInput { value = BupaOfferStatus.UnderwriterInProgress }
            };

            // Act
            var result = await PresalesUserOfferMutations.SecureUpdateOffer(caseId, proposalId, offerId, input);

            // Assert
            result.Should().NotBeNull();
            result.isSuccess.Should().BeFalse();
            result.errors.Should().HaveCount(1);
        }
    }
}
