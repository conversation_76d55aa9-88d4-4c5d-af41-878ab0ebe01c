using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.DomainUtils;
using FluentAssertions;
using GraphQL.Client.Http;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Xunit;
using Formatting = Newtonsoft.Json.Formatting;

namespace CoverGo.Cases.Tests.Integration.Offers;

public class OfferCrudTests
{
    readonly CasesRestClient _client;
    readonly GraphQLHttpClient _graphqlClient;
    readonly string _tenantId;

    public OfferCrudTests()
    {
        _tenantId = UserCredentials.Admin.TenantId;
        _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        _graphqlClient = Setup.BuildGraphQLHttpClient();
    }

    [Fact]
    public async Task GIVEN_offer_with_fields_WHEN_create_offer_THEN_fields_content_should_be_saved_to_fields2()
    {
        string caseId = await CaseHelper.CreateCase(_client, _tenantId);
        string proposalId = await CaseHelper.AddProposalToCase(_client, _tenantId, caseId);

        var addOfferCommand = new AddOfferCommand
        {
            ProposalId = proposalId,
            Fields = "{\"numberOfInsureds\":100}"
        };
        await _client.Proposal_AddOfferAsync(_tenantId, caseId, addOfferCommand, null);

        var args = new QueryArgumentsOfCaseWhere
        {
            Where = new CaseWhere { Id = caseId }
        };
        Case createdCase = (await _client.Case_GetAsync(_tenantId, args))!.FirstOrDefault();
        Proposal proposal = createdCase!.Proposals!.FirstOrDefault();
        Offer offer = proposal!.Basket!.FirstOrDefault();

        string fields2AsString = JToken.FromObject(offer!.Fields2!).ToString(Formatting.None);

        offer!.Fields.Should().BeNull();
        fields2AsString.Should().Be("{\"numberOfInsureds\":100}");
    }

    [Fact]
    public async Task GIVEN_offer_with_fields_WHEN_update_offer_THEN_fields_content_should_be_saved_to_fields2()
    {
        string seededCaseIdWithFields = await SeedCaseOfferWithFields();

        var args = new QueryArgumentsOfCaseWhere
        {
            Where = new CaseWhere { Id = seededCaseIdWithFields }
        };
        Case originalCase = (await _client.Case_GetAsync(_tenantId, args))!.FirstOrDefault();
        Proposal originalProposal = originalCase!.Proposals!.FirstOrDefault();
        Offer originalOffer = originalProposal!.Basket!.FirstOrDefault();
        originalOffer!.Fields2.Should().BeNull();

        var updateOfferCommand = new UpdateOfferCommand
        {
            ProposalId = originalProposal.Id,
            OfferId = originalOffer.Id,
            Fields = originalOffer.Fields,
            IsFieldsChanged = true
        };
        await _client.Proposal_UpdateOfferAsync(_tenantId, originalCase.Id, updateOfferCommand);
        Case updatedCase = (await _client.Case_GetAsync(_tenantId, args))!.FirstOrDefault();
        Proposal updatedProposal = updatedCase!.Proposals!.FirstOrDefault();
        Offer updatedOffer = updatedProposal!.Basket!.FirstOrDefault();

        string fields2AsString = JToken.FromObject(updatedOffer!.Fields2!).ToString(Formatting.None);

        updatedOffer!.Fields2.Should().NotBeNull();
        fields2AsString.Should().Be(originalOffer.Fields);
    }

    [Fact]
    public async Task GIVEN_offer_WHEN_recordQuote_with_fields_THEN_treeRecords_are_saved_AND_fields_is_updated()
    {
        string caseId = await CaseHelper.CreateCase(_client, _tenantId);
        string proposalId = await CaseHelper.AddProposalToCase(_client, _tenantId, caseId);
        string offerId = await CaseHelper.AddOfferToProposal(_client, _tenantId, caseId, proposalId);

        string fields = "{\\\"test\\\":\\\"value\\\"}";
        string recordId = Guid.NewGuid().ToString();
        string recordQuoteMutation = new MutationBuilder().offerMutationRecordQuote(
            new(
                _tenantId,
                caseId,
                new()
                {
                    proposalId = proposalId,
                    offerId = offerId,
                    productTreeRecords = new List<cases_ProductTreeRecordInput>()
                    {
                        new()
                        {
                            type = "test",
                            recordId = recordId
                        }
                    },
                    fields = fields
                }
                ),
            new cases_ResultBuilder().WithAllFields())
            .Build();

        await _graphqlClient.SendMutationAndEnsureAsync<Result>(recordQuoteMutation);

        Case @case = (await _client.Case_GetAsync(_tenantId, new()
        {
            Where = new CaseWhere
            {
                Id = caseId
            }
        })).First();

        Offer offer = @case.Proposals.First().Basket.First();

        offer.Fields.Should().Be(Regex.Unescape(fields));
        offer.ProductTreeRecords.Should().HaveCount(1);
        offer.ProductTreeRecords.First().Type.Should().Be("test");
        offer.ProductTreeRecords.First().RecordId.Should().Be(recordId);
    }

    private async Task<string> SeedCaseOfferWithFields()
    {
        var seededCaseIdWithFields = "798af5ee-4943-42d6-ab23-6ed7e605a09c";
        var seededOfferId = "fe2b0db8-b7aa-40ad-ad4c-f1283798e0db";
        var seededProposalId = "54b99482-8863-493f-b7ca-7d965d49f6de";
        var seededOfferNumber = "N6WI3Y";
        var seededFields = "{\"numberOfInsureds\":100}";
        CaseSeed caseSeed = new()
        {
            Id = seededCaseIdWithFields,
            CaseNumber = "142d6dde-ac4b-4289-8e87-c4089ca46f41",
            Name = "99e3f4a4-4f14-40bb-8640-080113ec3770",
            Description = "adab1183-a60e-4a05-a43b-1fd3f661382d",
            Proposals = new ProposalSeed[]
                {
                    new()
                    {
                        Id = seededProposalId,
                        CaseId = seededCaseIdWithFields,
                        Name = "a67398dd-4b54-43d3-b414-b7803900cfb6",
                        ProposalNumber = "VZOF1B",
                        TotalPrice = new()
                        {
                            Amount = 0,
                            PaymentFrequency = "OneTime"
                        },
                        RenewalHistory = new(),
                        Basket = new OfferSeed[]
                        {
                            new()
                            {
                                Id = seededOfferId,
                                OfferNumber = seededOfferNumber,
                                Fields = seededFields,
                                Events = new DetailedEventLogSeed[]
                                {
                                    new()
                                    {
                                        Value = new EventLogValueSeed
                                        {
                                            OfferId = seededOfferId,
                                            ProposalId = seededProposalId,
                                            OfferNumber = seededOfferNumber,
                                            Fields = seededFields
                                        },
                                        RelatedId = seededCaseIdWithFields,
                                        Type = "addOffer"
                                    }
                                }
                            }
                        }
                    }
                }
        };

        IMongoDatabase db = Setup.GetMongoDatabase("cases");
        await db.GetCollection<CaseSeed>("covergo-cases")
            .InsertOneAsync(caseSeed);
        await db.GetCollection<CaseEventSeed>("covergo-events")
            .InsertManyAsync(
                new CaseEventSeed[]
                {
                    new()
                    {
                        CaseId = seededCaseIdWithFields,
                        Type = "creation",
                        Values = new BsonDocument
                        {
                            { "name", caseSeed.Name },
                            { "description", caseSeed.Description },
                            { "caseNumber", caseSeed.CaseNumber }
                        }
                    },
                    new()
                    {
                        CaseId = seededCaseIdWithFields,
                        Type = "addProposal",
                        Values = new BsonDocument
                        {
                            { "id", seededProposalId },
                            { "name", caseSeed.Proposals.First().Name },
                            { "proposalNumber", caseSeed.Proposals.First().ProposalNumber }
                        }
                    },
                    new()
                    {
                        CaseId = seededCaseIdWithFields,
                        Type = "addOffer",
                        Values = new BsonDocument
                        {
                            { "offerId", seededOfferId },
                            { "proposalId", seededProposalId },
                            { "offerNumber", seededOfferNumber },
                            { "isPremiumOverridden", false },
                            { "fields", seededFields }
                        }
                    }
                }
            );

        return seededCaseIdWithFields;
    }

    private class CaseSeed
    {
        public string Id { get; set; }
        public string CaseNumber { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public IEnumerable<object> Facts { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> Notes { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> Stakeholders { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> BeneficiaryEligibilities { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> PaymentInfos { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<ProposalSeed> Proposals { get; set; }
    }

    private class ProposalSeed
    {
        public string Id { get; set; }
        public string CaseId { get; set; }
        public string Name { get; set; }
        public string Status { get; set; }
        public string ProposalNumber { get; set; }
        public IEnumerable<object> Notes { get; set; } = Enumerable.Empty<object>();
        public bool IsIssued { get; set; }
        public IEnumerable<OfferSeed> Basket { get; set; } = Enumerable.Empty<OfferSeed>();
        public PremiumSeed TotalPrice { get; set; }
        public IEnumerable<object> PolicyIds { get; set; } = Enumerable.Empty<object>();
        public RenewalHistorySeed RenewalHistory { get; set; }
        public bool IsRejected { get; set; }
    }

    private class OfferSeed
    {
        public string Id { get; set; }
        public string OfferNumber { get; set; }
        public bool IsPremiumOverridden { get; set; }
        public IEnumerable<object> Clauses { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> Jackets { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> Facts { get; set; } = Enumerable.Empty<object>();
        public IEnumerable<object> Commissions { get; set; } = Enumerable.Empty<object>();
        public string Fields { get; set; }
        public IEnumerable<DetailedEventLogSeed> Events { get; set; }
    }

    private class DetailedEventLogSeed
    {
        public string Type { get; set; }
        public string RelatedId { get; set; }
        public EventLogValueSeed Value { get; set; }
    }

    private class EventLogValueSeed
    {
        public string OfferId { get; set; }
        public string ProposalId { get; set; }
        public string OfferNumber { get; set; }
        public string Fields { get; set; }
        public bool IsPremiumOverridden { get; set; }
    }

    private class PremiumSeed
    {
        public int Amount { get; set; }
        public string PaymentFrequency { get; set; }
    }

    private class RenewalHistorySeed
    {
        public int RenewalCount { get; set; }
    }

    private class CaseEventSeed
    {
        public string CaseId { get; set; }
        public string Type { get; set; }
        public BsonDocument Values { get; set; }
    }
}