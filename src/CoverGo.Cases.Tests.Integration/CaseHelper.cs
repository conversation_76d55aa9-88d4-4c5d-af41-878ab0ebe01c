using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.DomainUtils;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JacketCommandBatch = CoverGo.Cases.Client.Rest.JacketCommandBatch;

namespace CoverGo.Cases.Tests.Integration;

public static class CaseHelper
{
    public static async Task<string> CreateCase(CasesRestClient client, string tenantId)
    {
        Case newCase = new()
        {
            Name = Guid.NewGuid().ToString(),
            Description = Guid.NewGuid().ToString(),
            CaseNumber = Guid.NewGuid().ToString()
        };

        CreateCaseCommand command = new()
        {
            Name = newCase.Name,
            Description = newCase.Description,
            CaseNumber = newCase.CaseNumber
        };

        ResultOfString result = await client.Case_CreateAsync(tenantId, command);
        return result!.Value;
    }

    public static async Task<string> AddProposalToCase(CasesRestClient client, string tenantId, string caseId)
    {
        Proposal newProposal = new()
        {
            Name = Guid.NewGuid().ToString()
        };

        AddProposalCommand proposalCommand = new()
        {
            Name = newProposal.Name
        };

        ResultOfString addResult = await client.Proposal_AddProposalAsync(tenantId, caseId, proposalCommand, null);
        return addResult!.Value;
    }

    public static async Task<string> AddOfferToProposal(CasesRestClient client, string tenantId, string caseId, string proposalId, string offerStatus = null)
    {
        AddOfferCommand command = new() { ProposalId = proposalId, Status = offerStatus };

        ResultOfString addResult = await client.Proposal_AddOfferAsync(tenantId, caseId, command, null);
        return addResult!.Value;
    }

    public static async Task<Case> GetCase(CasesRestClient client, string tenantId, string caseId)
    {
        List<Case> cases = await client.Case_GetAsync(tenantId, new QueryArgumentsOfCaseWhere() { Where = new CaseWhere() { Id = caseId } });
        return cases.Single();
    }

    public static async Task<Result> JacketBatch(CasesRestClient client, string tenantId, string caseId, JacketCommandBatch command) => await client.Proposal_JacketBatchAsync(tenantId, caseId, command);

    public static cases_AgentCreateCaseInput MockAgentCreateCaseInput(string agentEntityId = null, string status = null, string holderId = "holderId") =>
        new()
        {
            name = Guid.NewGuid().ToString(),
            componentId = "componentId",
            description = "description",
            fields = "{ \\\"testField\\\": \\\"testValue\\\" }",
            fieldsSchemaId = "fieldsSchemaId",
            holderId = holderId,
            status = status,
            insuredIds = new List<string> { "insuredId" },
            otherHolderIds = new List<string> { "otherHolderId" },
            assignedAgentEntityId = agentEntityId,
            source = "source"
        };
}