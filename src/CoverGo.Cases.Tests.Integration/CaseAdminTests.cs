﻿using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Domain.OfferStatusPermissions.Bupa;
using CoverGo.Cases.Tests.Integration.Utils;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration;

public class CaseAdminTests : TestBase
{
    [Fact]
    public async Task GIVEN_input_WHEN_adminCreateCase_THEN_case_is_created()
    {
        string agentEntityId = Guid.NewGuid().ToString();
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput(agentEntityId);

        cases_ResultOfString result = await AdminCreatorMutations.SumbitCase(submitCaseInput);
        string caseId = result.value;
        caseId.Should().NotBeNullOrEmpty();
        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        @case.Stakeholders.Should().HaveCount(1);
        @case.Stakeholders.First().EntityId.Should().Be(agentEntityId);
    }


    [Fact]
    public async Task GIVEN_input_WHEN_adminCreateCase_without_agentId_THEN_error_is_returned()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();

        cases_ResultOfString result = await AdminCreatorMutations.SumbitCase(submitCaseInput);
        result.errors_2.First().code.Should().Be("NO_AGENT_ASSIGNED");
        result.status.Should().Be("failure");
    }

    [Fact]
    public async Task GIVEN_input_WHEN_proposalApproverCreateCase_without_agentId_THEN_error_is_returned()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();

        cases_ResultOfString result = await AdminApproverMutations.SumbitCase(submitCaseInput);
        result.errors_2.First().code.Should().Be("NOT_AN_AGENT_AND_NOT_A_PROPOSAL_CREATOR");
        result.status.Should().Be("failure");
    }

    [Fact]
    public async Task GIVEN_case_WHEN_admin_assigns_self_to_case_THEN_admin_is_assigned()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();

        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);
        caseId.Should().NotBeNullOrEmpty();

        var result = await SystemAdminCreatorMutations.AssignAdminToCase(new cases_AssignAdminToCaseInput { caseId = caseId});
        result.errors.Should().BeNullOrEmpty();
        result.isSuccess.Should().BeTrue();

        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        Stakeholder adminStakeHolder = @case.Stakeholders.FirstOrDefault(s => s.Type == "System Admin");
        adminStakeHolder.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_case_WHEN_admin_assigns_self_to_case_THEN_only_admin_can_add_quotation()
    {
        string agentEntityId = Guid.NewGuid().ToString();
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput(agentEntityId);

        //Admin A create case
        var caseId = await AdminCreatorMutations.SumbitCaseAndReturnId(submitCaseInput);
        caseId.Should().NotBeNullOrEmpty();


        cases_AddQuotationInput quotationInput = GetQuotation(caseId);

        //Admin A cannot add quotation
        var addQuotationResult = await AdminCreatorMutations.AddQuotation(quotationInput);
        addQuotationResult.isSuccess.Should().BeFalse();

        //Admin B cannot add quotation
        var addQuotationResult2 = await SystemAdminCreatorMutations.AddQuotation(quotationInput);
        addQuotationResult2.isSuccess.Should().BeFalse();

        //Admin B assing case to self
        var assignToBResult = await SystemAdminCreatorMutations.AssignAdminToCase(
            new cases_AssignAdminToCaseInput { caseId = caseId });
        assignToBResult.isSuccess.Should().BeTrue();

        //Admin B can add quotation
        var addQuotationResult3 = await SystemAdminCreatorMutations.AddQuotation(quotationInput);
        addQuotationResult3.isSuccess.Should().BeTrue();
    }

    private static cases_AddQuotationInput GetQuotation(string caseId) =>
            new cases_AddQuotationInput
            {
                caseId = caseId,
                offer = new cases_QuotationOfferInput
                {
                    status = BupaOfferStatus.Added,
                    productId = new cases_ProductIdInput
                    {
                        plan = "plan A",
                        type = "type A",
                        version = "1"
                    }
                }
            };
}
