﻿using CoverGo.Cases.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;

namespace CoverGo.Cases.Tests.Integration.Utils;

public class AgentMutations
{
    private readonly GraphQLHttpClient _graphQlClient;

    public AgentMutations(GraphQLHttpClient client)
    {
        _graphQlClient = client;
    }

    public async Task<string> SumbitCaseAndReturnId(cases_AgentCreateCaseInput input) =>
        (await SumbitCase(input)).value;

    public async Task<cases_ResultOfString> SumbitCase(cases_AgentCreateCaseInput input)
    {
        string mutation = new MutationBuilder()
            .agentMutationAgentCreateCase(
                new MutationBuilder.agentMutationAgentCreateCaseArgs(input),
                new cases_ResultOfStringBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_ResultOfString>(mutation);
    }

    public async Task<cases_Result> ConvertOfferToApplication(string caseId, string proposalId)
    {
        string mutation = new MutationBuilder()
            .convertOfferToApplication(
                new MutationBuilder.convertOfferToApplicationArgs(new ConvertOfferToApplicationInput()
                {
                    caseId = caseId,
                    proposalId = proposalId 
                }),
                new ConvertOfferToApplicationPayloadBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_Result>(mutation);
    }

    public async Task<cases_AssignAdminToCasePayload> AssignAdminToCase(cases_AssignAdminToCaseInput input)
    {
        string mutation = new MutationBuilder()
            .salesAdminMutationAssignAdminToCase(
                new MutationBuilder.salesAdminMutationAssignAdminToCaseArgs(input),
                new cases_AssignAdminToCasePayloadBuilder().WithAllFields()
            )
            .Build();

        return await _graphQlClient.SendMutationAndEnsureAsync<cases_AssignAdminToCasePayload>(mutation);
    }
}
