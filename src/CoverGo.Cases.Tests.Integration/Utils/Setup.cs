﻿using CoverGo.Configuration;
using CoverGo.GraphQL.Client;
using CoverGo.MongoUtils;
using CoverGo.Proxies.Auth;
using GraphQL.Client.Abstractions.Websocket;
using GraphQL.Client.Http;
using GraphQL.Client.Serializer.Newtonsoft;
using IdentityModel.Client;
using Microsoft.IdentityModel.Tokens;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Driver;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;

namespace CoverGo.Cases.Tests.Integration.Utils;

static class Setup
{
    internal static readonly Lazy<Config> Config = new(() => Utils.Config.Load());

    public static readonly CoverGoAuthService AuthService = new(BuildAuthHttpClient());

    public static HttpClient BuildHttpClient(string tenantId, Config config = null)
        => new() { BaseAddress = new Uri($"{(config ?? Config.Value).CasesUrl}") };

    public static HttpClient BuildProductsHttpClient(string tenantId, Config config = null)
      => new() { BaseAddress = new Uri($"{(config ?? Config.Value).ProductsUrl}") };

    public static HttpClient BuildPoliciesHttpClient(string tenantId, Config config = null)
      => new() { BaseAddress = new Uri($"{(config ?? Config.Value).PoliciesUrl}") };

    public static HttpClient BuildAppHttpClient(string tenantId, string accessToken, Config config = null) =>
        new()
        {
            BaseAddress = new UriBuilder((config ?? Config.Value).AuthUrl) { Path = tenantId + "/" }.Uri,
            DefaultRequestHeaders = { { "Authorization", "Bearer " + accessToken } },
        };

    public static GraphQLHttpClient BuildGraphQLHttpClient(Config config = null)
        => new GraphQLHttpClient(new UriBuilder((config ?? Config.Value).CasesUrl) { Path = "/graphql" }.Uri, new NewtonsoftJsonSerializer());

    public static async Task<CoverGoGraphQlClient> BuildGatewayGraphQLHttpClient(Config config = null)
    {
        var client = new CoverGoGraphQlClient(new UriBuilder((config ?? Config.Value).GatewayUrl) { Path = "/graphql" }.ToString());
        await client.Authorize();
        return client;
    }

    public static HttpClient BuildAuthHttpClient(Config config = null)
        => new() { BaseAddress = new Uri($"{(config ?? Config.Value).AuthUrl}") };

    public static GraphQLHttpClient BuildCasesGraphQlClient(string accessToken) =>
        BuildGraphQlClient(Config.Value.CasesUrl, accessToken, new NewtonsoftJsonSerializer());

    public static GraphQLHttpClient BuildGatewayGraphQlClient(string accessToken) =>
        BuildGraphQlClient(Config.Value.GatewayUrl, accessToken, new NewtonsoftJsonSerializer());

    private static GraphQLHttpClient BuildGraphQlClient(
        string baseUrl,
        string accessToken,
        IGraphQLWebsocketJsonSerializer serializer)
    {
        var client = new GraphQLHttpClient(new UriBuilder(baseUrl) { Path = "/graphql" }.Uri, serializer);
        client.HttpClient.DefaultRequestHeaders.Authorization = new("Bearer", accessToken);
        return client;
    }

    public static IMongoDatabase GetMongoDatabase(string databaseName)
    {
        var conventionPack = new ConventionPack
        {
            new CamelCaseElementNameConvention(),
            new EnumRepresentationConvention(BsonType.String),
            new IgnoreExtraElementsConvention(true),
            new IgnoreIfNullConvention(true)
        };
        ConventionRegistry.Register("all", conventionPack, t => true);
        var dbConfig = new DbConfig()
        {
            ProviderId = "mongoDb",
            ConnectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ??
                "****************************************"
        };
        MongoClient mongoClient = MongoTools.GetOrAddMongoClient(dbConfig);
        return mongoClient.GetDatabase(databaseName);
    }

    public static async Task<string> GetAccessToken(string tenantId = null, string clientId = null, string username = null, string password = null)
    {
        var client = new HttpClient();

        TokenResponse response = await client.RequestPasswordTokenAsync(new()
        {
            Address = new UriBuilder(Config.Value.AuthUrl) { Path = $"{tenantId ?? UserCredentials.Admin.TenantId}/connect/token" }.ToString(),
            ClientId = clientId ?? UserCredentials.Admin.ClientId,
            UserName = username ?? UserCredentials.Admin.UserName,
            Password = password ?? UserCredentials.Admin.Password,
            Scope = "custom_profile offline_access",
        });
        return response.AccessToken;
    }

    public static ClaimsPrincipal ValidateAccessToken(string accessToken)
    {
        TokenValidationParameters parameters = new()
        {
            SignatureValidator = (t, p) => new JwtSecurityToken(t),
            ValidateAudience = false,
            ValidateIssuer = false
        };
        JwtSecurityTokenHandler handler = new();
        ClaimsPrincipal claimsPrincipal = handler.ValidateToken(
            accessToken,
            parameters,
            out SecurityToken _);

        return claimsPrincipal;
    }
}
