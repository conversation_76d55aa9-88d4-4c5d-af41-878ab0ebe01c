﻿using CoverGo.Cases.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;

namespace CoverGo.Cases.Tests.Integration.Utils
{
    public class OfferMutations
    {
        private readonly GraphQLHttpClient _graphQlClient;

        public OfferMutations(GraphQLHttpClient client)
        {
            _graphQlClient = client;
        }

        public async Task<cases_Result> SecureUpdateOffer(string caseId, string proposalId, string offerId, cases_AdminUpdateOfferInput input)
        {
            string mutation = new MutationBuilder()
                .offerMutationSecureUpdateOffer(
                    new MutationBuilder.offerMutationSecureUpdateOfferArgs(caseId, proposalId, offerId, input),
                    new cases_ResultBuilder().WithAllFields()
                )
                .Build();

            return await _graphQlClient.SendMutationAndEnsureAsync<cases_Result>(mutation);
        }
    }
}
