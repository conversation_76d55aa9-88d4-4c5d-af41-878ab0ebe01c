using GraphQL;
using GraphQL.Client.Http;
using Newtonsoft.Json.Linq;

using System;
using System.Linq;
using System.Threading.Tasks;

namespace CoverGo.Cases.Tests.Integration.Utils;

static class GraphQlHttpClientExtensions
{
    public static async Task<TResponse> SendMutationAndEnsureAsync<TResponse>(
        this GraphQLHttpClient client,
        string mutation) where TResponse : class
    {
        GraphQLResponse<JToken> response = await client.SendMutationAsync<JToken>(new GraphQLHttpRequest(mutation));
        Validate(response);
        return response.Data.First?.First?.ToObject<TResponse>() ?? throw new("Cannot deserialize result");
    }

    public static async Task<TResponse> SendQueryAndEnsureAsync<TResponse>(this GraphQLHttpClient client, string query) where TResponse : class
    {
        GraphQLResponse<JToken> response = await client.SendQueryAsync<JToken>(new GraphQLHttpRequest(query));
        Validate(response);
        return response.Data.First?.First?.ToObject<TResponse>() ?? throw new("Cannot deserialize result");
    }

    static void Validate<TResponse>(GraphQLResponse<TResponse> response)
    {
        if (response.Errors?.Any() == true)
        {
            var error = response.Errors.First();
            var exceptionMessage = error.Message;
            if (error?.Extensions?.TryGetValue("message", out var extraMessage) == true)
            {
                exceptionMessage += Environment.NewLine + extraMessage;
            }
            throw new(exceptionMessage);
        }
    }
}