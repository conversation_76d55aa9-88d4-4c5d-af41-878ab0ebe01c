﻿
using CoverGo.Gateway.Client;
using GraphQL.Client.Http;
using System.Threading.Tasks;

namespace CoverGo.Cases.Tests.Integration.Utils
{
    public class PoliciesQuery 
    {

        private readonly GraphQLHttpClient _client;

        public PoliciesQuery(GraphQLHttpClient client)
        {
            _client = client;
        }

        public Task<policies> GetPolicies(int limit, int skip, policyWhereInput input)
        {
            string query = new covergoQueryBuilder()
               .policies(new covergoQueryBuilder.policiesArgs(limit, skip, null,input), new policiesBuilder()
                   .list(new policyBuilder().id()))
               .Build();

            return _client.SendQueryAsync<policies>(query);
        }
    }
}
