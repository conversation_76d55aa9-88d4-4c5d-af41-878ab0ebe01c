using GraphQL.Client.Http;
using System;
using System.Threading.Tasks;
using CoverGo.Cases.Client;
using System.Linq;
using System.Collections.Generic;

namespace CoverGo.Cases.Tests.Integration.Utils;

public sealed class Brokers
{
    private readonly GraphQLHttpClient _graphQlClient;
    private readonly DateTime _today;

    public Brokers(GraphQLHttpClient client)
    {
        _today = DateTime.UtcNow.Date;
        _graphQlClient = client;
    }

    public async Task<string> Create(cases_BrokerUpsertInput input)
    {
        string mutation = new MutationBuilder()
            .brokersMutationCreate(
                new MutationBuilder.brokersMutationCreateArgs(input),
                new cases_ResultOfCreatedStatusBuilder().WithAllFields()
            )
            .Build();

        return (await _graphQlClient.SendMutationAndEnsureAsync<cases_ResultOfCreatedStatus>(mutation))
            .value
            .id;
    }

    public async Task<ICollection<cases_Error>> CreateWithError(cases_BrokerUpsertInput input)
    {
        string mutation = new MutationBuilder()
            .brokersMutationCreate(
                new MutationBuilder.brokersMutationCreateArgs(input),
                new cases_ResultOfCreatedStatusBuilder().WithAllFields()
            )
            .Build();

        return (await _graphQlClient.SendMutationAndEnsureAsync<cases_ResultOfCreatedStatus>(mutation)).errors_2;
    }

    public async Task<cases_Broker> Read(string brokerId)
    {
        string query = new QueryBuilder()
            .brokersQuery(
                new QueryBuilder.brokersQueryArgs(
                    new cases_GenericBrokerQueryInput
                    {
                        where = new cases_GenericBrokerFilterInput
                        {
                            where = new cases_BrokerFilterInput
                            {
                                id = brokerId,
                            }
                        }
                    }
                ),
                new cases_GenericBroker8QueryInterfaceBuilder()
                    .list(
                        new cases_BrokerBuilder().WithAllFields()
                    )
            )
            .Build();

        var brokers = await _graphQlClient.SendQueryAndEnsureAsync<cases_GenericBroker8QueryInterface>(query);

        return brokers.list?.FirstOrDefault();
    }

    public async Task<cases_Broker> NameContains(string nameContains)
    {
        string query = new QueryBuilder()
            .brokersQuery(
                new QueryBuilder.brokersQueryArgs(
                    new cases_GenericBrokerQueryInput
                    {
                        where = new cases_GenericBrokerFilterInput
                        {
                            where = new cases_BrokerFilterInput
                            {
                                name_contains = nameContains
                            }
                        }
                    }
                ),
                new cases_GenericBroker8QueryInterfaceBuilder()
                    .list(
                        new cases_BrokerBuilder().WithAllFields()
                    )
            )
            .Build();

        var brokers = await _graphQlClient.SendQueryAndEnsureAsync<cases_GenericBroker8QueryInterface>(query);

        return brokers.list?.FirstOrDefault();
    }

    public async Task<cases_Broker> NameIn(string name)
    {
        string query = new QueryBuilder()
            .brokersQuery(
                new QueryBuilder.brokersQueryArgs(
                    new cases_GenericBrokerQueryInput
                    {
                        where = new cases_GenericBrokerFilterInput
                        {
                            where = new cases_BrokerFilterInput
                            {
                                name_in = new List<string> { name }
                            }
                        }
                    }
                ),
                new cases_GenericBroker8QueryInterfaceBuilder()
                    .list(
                        new cases_BrokerBuilder().WithAllFields()
                    )
            )
            .Build();

        var brokers = await _graphQlClient.SendQueryAndEnsureAsync<cases_GenericBroker8QueryInterface>(query);

        return brokers.list?.FirstOrDefault();
    }

    public async Task<cases_Broker> GetByContactPersonPart(string contactPersonPart)
    {
        string query = new QueryBuilder()
            .brokersQuery(
                new QueryBuilder.brokersQueryArgs(
                    new cases_GenericBrokerQueryInput
                    {
                        where = new cases_GenericBrokerFilterInput
                        {
                            where = new cases_BrokerFilterInput
                            {
                                contactPerson_contains = contactPersonPart,
                            }
                        }
                    }
                ),
                new cases_GenericBroker8QueryInterfaceBuilder()
                    .list(
                        new cases_BrokerBuilder().WithAllFields()
                    )
            )
            .Build();

        var brokers = await _graphQlClient.SendQueryAndEnsureAsync<cases_GenericBroker8QueryInterface>(query);

        return brokers.list?.FirstOrDefault();
    }

    public Task<cases_Result> Update(cases_BrokerUpsertInput input)
    {
        string mutation = new MutationBuilder()
            .brokersMutationUpdate(
                new MutationBuilder.brokersMutationUpdateArgs(input),
                new cases_ResultBuilder().WithAllFields()
            )
            .Build();

        return _graphQlClient.SendMutationAndEnsureAsync<cases_Result>(mutation);
    }

    public Task<cases_Result> Delete(string brokerId)
    {
        string mutation = new MutationBuilder()
            .brokersMutationDelete(
                new MutationBuilder.brokersMutationDeleteArgs(
                    new cases_BrokerUpsertInput { id = brokerId }
                ),
                new cases_ResultBuilder().WithAllFields()
            )
            .Build();

        return _graphQlClient.SendMutationAndEnsureAsync<cases_Result>(mutation);
    }
}
