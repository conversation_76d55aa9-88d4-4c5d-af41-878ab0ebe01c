﻿using System.Diagnostics.CodeAnalysis;

namespace CoverGo.Cases.Tests.Integration.Utils;

[SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "User credentials should be accessible for integration tests for creating own copies")]
public class UserCredentials
{
    public string TenantId { get; private init; }
    public string ClientId { get; private init; }
    public string UserName { get; private init; }
    public string Password { get; private init; }

    public static UserCredentials Admin => new()
    {
        ClientId = "admin",
        Password = "V9K&KobcZO3",
        UserName = "<EMAIL>",
        TenantId = "covergo"
    };
}