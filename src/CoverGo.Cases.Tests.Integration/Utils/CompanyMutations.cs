﻿
using CoverGo.Gateway.Client;
using GraphQL.Client.Abstractions;
using GraphQL.Client.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoverGo.Cases.Tests.Integration.Utils
{
    public class CompanyMutations
    {
        private readonly GraphQLHttpClient _client;

        public CompanyMutations(GraphQLHttpClient client) 
        {
            _client = client;
        }

        public Task<string> CreateCompany(createCompanyInput input) 
        {
            string mutation = new covergoMutationBuilder()
               .createCompany(new covergoMutationBuilder.createCompanyArgs(input), new createdStatusResultBuilder()
                   .WithAllFields())
               .Build();

            return _client.CreateAndReturnId(mutation);
        }
    }
}
