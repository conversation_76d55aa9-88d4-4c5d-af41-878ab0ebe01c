﻿using System;
using Microsoft.Extensions.Configuration;

namespace CoverGo.Cases.Tests.Integration.Utils;

public class Config
{
    public string AuthUrl { get; set; }
    public string CasesUrl { get; set; }
    public string ProductsUrl { get; set; }
    public string GatewayUrl { get; set; }
    public string PoliciesUrl { get; set; }
    public string ReferenceUrl { get; set; }
    public string ChannelManagementUrl { get; set; }

    public static readonly Config Local = new()
    {
        AuthUrl = "http://localhost:60000",
        CasesUrl = "http://localhost:60600",
        ProductsUrl = "http://localhost:60020/",
        GatewayUrl = "http://localhost:60060",
        PoliciesUrl = "http://localhost:60050/",
        ReferenceUrl = "http://localhost:61910/",
        ChannelManagementUrl = "http://localhost:64483"
    };

    private const string EnvironmentVariablePrefix = "CASES_INTEGRATION_TEST-";

    public static Config Load(string prefix = EnvironmentVariablePrefix)
    {
        var cfg = new Config();
        new ConfigurationBuilder().AddEnvironmentVariables(prefix).Build().Bind(cfg);

        return string.IsNullOrWhiteSpace(cfg.CasesUrl) ? Local : cfg;
    }
}
