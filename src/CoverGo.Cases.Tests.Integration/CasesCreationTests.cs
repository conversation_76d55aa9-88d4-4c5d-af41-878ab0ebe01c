﻿using CoverGo.Cases.Client.Rest;
using FluentAssertions;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using CoverGo.Cases.Tests.Integration.Utils;

namespace CoverGo.Cases.Tests.Integration
{
    public class CasesCreationTests
    {
        readonly CasesRestClient _client;
        readonly string _tenantId;

        public CasesCreationTests()
        {
            _tenantId = UserCredentials.Admin.TenantId;
            _client = new CasesRestClient(Setup.BuildHttpClient(_tenantId));
        }

        [Fact]
        public async Task GIVEN_tenant_fubon_WHEN_create_case_THEN_case_should_have_customised_caseNumber()
        {
            string tenantId = "fubon_uat";
            IMongoDatabase db = Setup.GetMongoDatabase("cases");
            await db.GetCollection<BsonDocument>($"{tenantId}-refgen")
                .InsertOneAsync(new()
                {
                    { "type", "createCase" },
                    { "format", "{0}{1}" },
                    { "arguments", new BsonArray()
                        {
                            new BsonDocument()
                            {
                                { "order", 0 },
                                { "format", "D7" },
                                { "type", "incrementor" },
                                { "currentIncrement", 7999999 }
                            },
                            new BsonDocument()
                            {
                                { "order", 1 },
                                { "format", "D1" },
                                { "type", "remainderOfFirstSevenDigitsSumDividedByTen" }
                            }
                        }
                    }
                });

            string[] expectedCaseNumber = new string[]
            {
                "80000008",
                "80000019",
                "80000020",
                "80000031",
                "80000053",
                "80000075"
            };

            Case case1 = await CreateCaseWithSpecificTenant(tenantId);
            Case case2 = await CreateCaseWithSpecificTenant(tenantId);
            Case case3 = await CreateCaseWithSpecificTenant(tenantId);
            Case case4 = await CreateCaseWithSpecificTenant(tenantId);
            Case case5 = await CreateCaseWithSpecificTenant(tenantId);
            Case case6 = await CreateCaseWithSpecificTenant(tenantId);

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { case1.Id, case2.Id, case3.Id, case4.Id, case5.Id, case6.Id }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(tenantId, arg);
            var orderedCases = resp!.OrderBy(c => c.CreatedAt).ToList();

            for (int i = 0; i < orderedCases.Count; i++)
            {
                orderedCases[i].CaseNumber.Should().Be(expectedCaseNumber[i]);
                orderedCases[i].ChannelId.Should().Be("ChannelId1");
            }
        }

        [Fact]
        public async Task GIVEN_tenant_walaa_WHEN_create_case_THEN_case_should_have_custom_case_number()
        {
            string tenantId = "walaa_dev";
            IMongoDatabase db = Setup.GetMongoDatabase("cases");
            await db.GetCollection<BsonDocument>($"{tenantId}-refgen")
                .InsertOneAsync(new()
                {
                    { "type", "createCase" },
                    { "format", "Q-{0}-{1}-{2}" },
                    { "arguments", new BsonArray
                        {
                            new BsonDocument
                            {
                                { "order", 0 },
                                { "type", "currentDate" },
                                { "format", "yy" }
                            },
                            new BsonDocument
                            {
                                { "order", 1 },
                                { "type", "dictionary" },
                                { "jsonPath", "$.fields" },
                                { "expectedType", "JObjectString" },
                                { "format", "productTypeId" },
                                { "dictionary", new BsonDocument { { "gm", "GM" }, { "im", "IM" } } }
                            },
                            new BsonDocument
                            {
                                { "order", 2 },
                                { "type", "positionalCounter" },
                                { "format", "D7" }
                            }
                        }
                    }
                });

            var dateTimeYear = DateTime.UtcNow.ToString("yy");
            string[] expectedCaseNumbers = new string[]
            {
                $"Q-{dateTimeYear}-GM-0000001",
                $"Q-{dateTimeYear}-IM-0000001",
                $"Q-{dateTimeYear}-GM-0000002",
                $"Q-{dateTimeYear}-IM-0000002"
            };

            Case[] createdCases = new [] {
                await CreateCaseWithSpecificTenant(tenantId, fields: "{\"productTypeId\":\"gm\"}"),
                await CreateCaseWithSpecificTenant(tenantId, fields: "{\"productTypeId\":\"im\"}"),
                await CreateCaseWithSpecificTenant(tenantId, fields: "{\"productTypeId\":\"gm\"}"),
                await CreateCaseWithSpecificTenant(tenantId, fields: "{\"productTypeId\":\"im\"}")
            };

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = createdCases.Select(c => c.Id!).ToList()
                }
            };

            List<Case>? cases = await _client.Case_GetAsync(tenantId, arg);
            cases.Should().NotBeNull();
            cases!.OrderBy(c => c.CreatedAt).Select(c => c.CaseNumber).Should().BeEquivalentTo(expectedCaseNumbers);
        }

        [Fact]
        public async Task GIVEN_empty_caseNumber_WHEN_creating_case_THEN_case_created()
        {
            string tenantId = "dbs_uat";
            Case case1 = await CreateCaseWithSpecificTenant(tenantId);
            Case case2 = await CreateCaseWithSpecificTenant(tenantId);
            Case case3 = await CreateCaseWithSpecificTenant(tenantId);

            QueryArgumentsOfCaseWhere arg = new()
            {
                Where = new CaseWhere
                {
                    Id_in = new List<string> { case1.Id, case2.Id, case3.Id }
                }
            };

            List<Case> resp = await _client.Case_GetAsync(tenantId, arg);
            var cases = resp!.OrderBy(c => c.CreatedAt).ToList();

            await _client.Case_DeleteAsync(tenantId, case1.Id, new DeleteCaseCommand { DeletedById = case1.Id });
            await _client.Case_DeleteAsync(tenantId, case2.Id, new DeleteCaseCommand { DeletedById = case2.Id });
            await _client.Case_DeleteAsync(tenantId, case3.Id, new DeleteCaseCommand { DeletedById = case3.Id });

            string currentDate = DateTime.Now.ToString("yyMMdd");

            cases.Count.Should().Be(3);
        }

        public async Task<Case> CreateCaseWithSpecificTenant(string tenantId, string? fields = null)
        {
            var newCase = new Case
            {
                Name = Guid.NewGuid().ToString(),
                Description = Guid.NewGuid().ToString(),
                ChannelId = "ChannelId1"
            };

            var command = new CreateCaseCommand
            {
                Name = newCase.Name,
                Description = newCase.Description,
                ChannelId = newCase.ChannelId,
                Fields = fields
            };

            ResultOfString result = await _client.Case_CreateAsync(tenantId, command);
            newCase.Id = result!.Value;

            return newCase;
        }
    }
}
