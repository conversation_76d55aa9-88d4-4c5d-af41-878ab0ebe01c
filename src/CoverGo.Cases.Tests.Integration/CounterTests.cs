using System.Linq;
using System.Threading.Tasks;
using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using FluentAssertions;
using Xunit;

namespace CoverGo.Cases.Tests.Integration
{
    public class CounterTests : TestBase
    {
        [Fact]
        public async Task GIVEN_counter_WHEN_increase_THEN_increased_by_one()
        {
            var counterKey = CreateNewGuid();
            var scope = CreateNewGuid();

            var counter1 = await IncreaseCounter(scope, counterKey);
            await IncreaseCounter(scope, counterKey);
            var counter2 = await QueryCounter(scope, counterKey);

            counter1.Should().Be(1);
            counter2.Should().Be(counter1 + 1);
        }

        [Fact]
        public async Task GIVEN_two_counter_keys_WHEN_increase_one_THEN_another_unchanged()
        {
            var scope = CreateNewGuid();
            var key1 = CreateNewGuid();
            var key2 = CreateNewGuid();

            var counter1 = await IncreaseCounter(scope, key1);

            await IncreaseCounter(scope, key2);

            var counter2 = await QueryCounter(scope, key1);

            counter2.Should().Be(counter1);
        }

        [Fact]
        public async Task GIVEN_two_scopes_WHEN_increase_one_THEN_another_unchanged()
        {
            var key = CreateNewGuid();
            var scope1 = CreateNewGuid();
            var scope2 = CreateNewGuid();

            var counter1 = await IncreaseCounter(scope1, key);

            await IncreaseCounter(scope2, key);

            var counter2 = await QueryCounter(scope1, key);

            counter2.Should().Be(counter1);
        }

        private async Task<long> IncreaseCounter(string scope, string key)
        {
            var input = new Client.cases_IncreaseCounterCommandInput
            {
                counterKey = key,
                scope = scope,
            };

            var mutation = new MutationBuilder()
                .counterMutationIncrease(
                    new MutationBuilder.counterMutationIncreaseArgs(input),
                    new cases_ResultOfInt64Builder().WithAllFields()
                )
                .Build();

            return (long)(await _graphQlClient.SendMutationAndEnsureAsync<cases_ResultOfInt64>(mutation)).value;
        }

        private async Task<long> QueryCounter(string scope, string key)
        {
            var input = new Client.cases_GenericCounterQueryInput
            {
                where = new cases_GenericCounterFilterInput
                {
                    where = new cases_CounterFilterInput
                    {
                        scope = scope
                    }
                }
            };

            var query = new QueryBuilder()
                .countersQuery(
                    new QueryBuilder.countersQueryArgs(input),
                    new cases_GenericCounter8QueryInterfaceBuilder().WithAllFields()
                )
                .Build();

            return (long)(await _graphQlClient.SendQueryAndEnsureAsync<cases_GenericCounter8QueryInterface>(query))
                .list
                .First()
                .counters
                .First(c => c.key == key)
                .value;
        }
    }
}