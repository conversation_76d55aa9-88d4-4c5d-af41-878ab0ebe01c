﻿using CoverGo.Cases.Client;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Domain.OfferStatusPermissions.Bupa;
using CoverGo.Cases.Tests.Integration.Utils;
using FluentAssertions;
using Newtonsoft.Json.Linq;
using StrawberryShake;
using System;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace CoverGo.Cases.Tests.Integration;

public class CaseAgentTests : TestBase
{
    [Fact]
    public async Task GIVEN_caseInput_WHEN_agentSubmitCase_THEN_case_with_provided_values_and_submitted_state_is_created()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();

        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);
        caseId.Should().NotBeNullOrEmpty();
        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        @case.Id.Should().NotBeNullOrEmpty();
        @case.Status.Should().Be("REQUEST_SUBMITTED");
        @case.Name.Should().Be(submitCaseInput.name);
        @case.ComponentId.Should().Be(submitCaseInput.componentId);
        @case.Description.Should().Be(submitCaseInput.description);
        @case.Fields.ToString().Should().BeEquivalentTo(JToken.Parse("{ \"testField\": \"testValue\" }").ToString());
        @case.FieldsSchemaId.Should().Be(submitCaseInput.fieldsSchemaId);
        @case.HolderId.Should().Be(submitCaseInput.holderId);
        @case.InsuredIds.Should().BeEquivalentTo(submitCaseInput.insuredIds);
        @case.OtherHolderIds.Should().BeEquivalentTo(submitCaseInput.otherHolderIds);
        @case.Source.Should().Be(submitCaseInput.source);
        @case.Stakeholders.Should().HaveCount(1);
    }

    [Fact]
    public async Task GIVEN_caseInput_WHEN_agentSubmitCase_with_custom_status_THEN_case_with_provided_values_and_submitted_state_is_created()
    {
        string customStatus = "customStatus";
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput(null, customStatus);

        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);
        caseId.Should().NotBeNullOrEmpty();
        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        @case.Id.Should().NotBeNullOrEmpty();
        @case.Status.Should().Be(customStatus);
        @case.Name.Should().Be(submitCaseInput.name);
        @case.ComponentId.Should().Be(submitCaseInput.componentId);
        @case.Description.Should().Be(submitCaseInput.description);
        @case.Fields.ToString().Should().BeEquivalentTo(JToken.Parse("{ \"testField\": \"testValue\" }").ToString());
        @case.FieldsSchemaId.Should().Be(submitCaseInput.fieldsSchemaId);
        @case.HolderId.Should().Be(submitCaseInput.holderId);
        @case.InsuredIds.Should().BeEquivalentTo(submitCaseInput.insuredIds);
        @case.OtherHolderIds.Should().BeEquivalentTo(submitCaseInput.otherHolderIds);
        @case.Source.Should().Be(submitCaseInput.source);
        @case.Stakeholders.Should().HaveCount(1);
    }


    [Fact]
    public async Task GIVEN_caseInput_WHEN_agentSubmitCase_and_set_agentEntityId_THEN_error_is_returned()
    {
        string agentEntityId = Guid.NewGuid().ToString();
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput(agentEntityId);

        cases_ResultOfString result = await AgentMutations.SumbitCase(submitCaseInput);
        result.errors_2.First().code.Should().Be("CANNOT_ASSIGN_AGENT");
        result.status.Should().Be("failure");
    }

    [Fact]
    public async Task GIVEN_case_with_multiple_proposals_created_not_agent_WHEN_convert_offer_to_application_THEN_other_proposal_is_rejected()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();
        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);


        var assingAdminResult = await AgentMutations.AssignAdminToCase(new cases_AssignAdminToCaseInput { caseId = caseId, adminEntityId = Guid.NewGuid().ToString() });

        var proposalId1 = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId1 = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId1, BupaOfferStatus.Added);

        var proposalId2 = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId2 = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId2, BupaOfferStatus.Added);

        var result = await AgentMutations.ConvertOfferToApplication(caseId, proposalId1);
        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        @case.Proposals.Should().HaveCount(2);
        @case.Status.Should().BeEquivalentTo("OFFER_ACCEPTED");
        @case.Proposals.First(p => p.Id == proposalId1).Status.Should().BeEquivalentTo(Domain.Constants.ProposalStatus.OFFER_ACCEPTED);
        @case.Proposals.First(p => p.Id != proposalId1).Status.Should().BeEquivalentTo(Domain.Constants.ProposalStatus.OFFER_REJECTED);
    }

    [Fact]
    public async Task GIVEN_case_with_convertedOffer_WHEN_update_case_status_THEN_case_status_is_updated()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();
        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);


        var assingAdminResult = await AgentMutations.AssignAdminToCase(new cases_AssignAdminToCaseInput { caseId = caseId, adminEntityId = Guid.NewGuid().ToString() });

        var proposalId1 = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId1 = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId1, BupaOfferStatus.Added);

        var proposalId2 = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId2 = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId2, BupaOfferStatus.Added);

        var result = await AgentMutations.ConvertOfferToApplication(caseId, proposalId1);

        DomainUtils.Result? updateCaseResult = await CasesRestClient.Case_UpdateAsync(TenantId, caseId, new UpdateCaseCommand { Status = "ISSUED", IsStatusChanged = true });
        updateCaseResult.IsSuccess.Should().BeTrue();

        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        @case.Status.Should().BeEquivalentTo("ISSUED");
    }

    [Fact]
    public async Task GIVEN_case_WHEN_agent_handler_assigs_admin_to_case_THEN_admin_is_assigned()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();

        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);
        caseId.Should().NotBeNullOrEmpty();

        var result = await AgentMutations.AssignAdminToCase(new cases_AssignAdminToCaseInput { caseId = caseId, adminEntityId = Guid.NewGuid().ToString() });
        result.errors.Should().BeNullOrEmpty();
        result.isSuccess.Should().BeTrue();

        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        Stakeholder adminStakeHolder = @case.Stakeholders.FirstOrDefault(s => s.Type == "System Admin");
        adminStakeHolder.Should().NotBeNull();
    }

    [Fact]
    public async Task GIVEN_case_with_multiple_proposals_created_not_agent_WHEN_convert_offer_to_application_THEN_cases_stakeholder_is_copied_into_policies_stakeholder()
    {
        cases_AgentCreateCaseInput submitCaseInput = CaseHelper.MockAgentCreateCaseInput();
        string caseId = await AgentMutations.SumbitCaseAndReturnId(submitCaseInput);

        var assingAdminResult = await AgentMutations.AssignAdminToCase(new cases_AssignAdminToCaseInput { caseId = caseId, adminEntityId = Guid.NewGuid().ToString() });

        var proposalId1 = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId1 = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId1, BupaOfferStatus.Added);

        var proposalId2 = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId2 = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId2, BupaOfferStatus.Added);

        var result = await AgentMutations.ConvertOfferToApplication(caseId, proposalId1);

        Case @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        var policyId = @case.Proposals.First(p => p.Id == proposalId1).PolicyIds.First();
        var policiesResult = await PoliciesClient.Policy_GetPoliciesAsync(UserCredentials.Admin.TenantId, new Policies.Client.QueryArgumentsOfPolicyWhere
        {
            Where = new Policies.Client.PolicyWhere
            {
                Id = policyId,
            }
        });
        var policy = policiesResult.First();
        policy.Stakeholders.First().EntityId.Should().Be(@case.Stakeholders.First().EntityId);
        var policiesAgentResult = await PoliciesAgentClient.GetPolicies(10, 0, new Gateway.Client.policyWhereInput
        {
            id = policyId,
        });

        policiesAgentResult.list.Should().HaveCount(1);
        policiesAgentResult.list.First().id.Should().Be(policyId);
    }

    [Fact]
    public async Task GIVEN_case_created_by_agent_WHEN_convert_offer_to_application_THEN_policy_should_have_distributorID()
    {
        var resCreateAgent = await SetupAgent();
        var entityID = resCreateAgent.EntityId;
        var distributorID = resCreateAgent.DistributorID;

        string caseId = await AgentMutations.SumbitCaseAndReturnId(CaseHelper.MockAgentCreateCaseInput());
        var @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        var agentStakeholders = @case.Stakeholders?.FirstOrDefault(s => s.Type == "Agent");
        if (agentStakeholders != null)
        {
            var res = await CasesRestClient.Case_RemoveStakeholderFromCaseAsync(UserCredentials.Admin.TenantId, caseId, new() { Id = agentStakeholders.Id });
            res.IsSuccess.Should().BeTrue();
        }
        var resAddStakehoder = await CasesRestClient.Case_AddStakeholderOfCaseAsync(UserCredentials.Admin.TenantId, caseId, new() { EntityId = entityID, Type = "Agent" });
        resAddStakehoder.IsSuccess.Should().BeTrue();

        var assingAdminResult = await AgentMutations.AssignAdminToCase(new() { caseId = caseId, adminEntityId = entityID });

        var proposalId = await CaseHelper.AddProposalToCase(CasesRestClient, UserCredentials.Admin.TenantId, caseId);
        var offerId = await CaseHelper.AddOfferToProposal(CasesRestClient, UserCredentials.Admin.TenantId, caseId, proposalId, BupaOfferStatus.Added);

        var result = await AgentMutations.ConvertOfferToApplication(caseId, proposalId);

        @case = await CaseHelper.GetCase(CasesRestClient, TenantId, caseId);
        var policyId = @case.Proposals!.First(p => p.Id == proposalId).PolicyIds!.First();
        var policiesResult = await PoliciesClient.Policy_GetPoliciesAsync(UserCredentials.Admin.TenantId, new() { Where = new() { Id = policyId } });

        var policy = policiesResult!.First();
        policy.PolicyCommission!.DistributorID.Should().Be(distributorID);
    }

    async Task<CreateAgentResult> SetupAgent()
    {
        var updateClientResult = await AppClient.UpdateAsync("admin", new() { RedirectUris = new[] { "https://covergo.com/redirect-uri" }, IsRedirectUrisChanged = true });
        updateClientResult.Result.IsSuccess.Should().BeTrue();

        var resCreateSalesChannel = await ChannelManagementClient.CreateSalesChannel.ExecuteAsync(new()
        {
            ActiveFromDateTime = DateTimeOffset.Now.AddDays(-1),
            ChannelType = "SLS", // SCTYP
            HasPortalAccess = true,
        });
        resCreateSalesChannel.EnsureNoErrors();
        var salesChannelID = resCreateSalesChannel.Data?.CreateSalesChannel?.SalesChannelState?.SalesChannelID!;

        var resCreateDistributor = await ChannelManagementClient.CreateDistributor.ExecuteAsync(new()
        {
            SalesChannelID = salesChannelID!,
            Class = "SLS",// DCLS
            ActiveFromDateTime = DateTimeOffset.Now.AddDays(-1),
            HasPortalAccess = true,
            HasPortalAccessDefault = true,
            UmbrellaPaymentRule = false,
            Party = new() { DistributorName = Guid.NewGuid().ToString("N"), AddrLine1 = Guid.NewGuid().ToString("N"), PostCode = "852", CountryCode = "HKG" },
        });
        resCreateDistributor.EnsureNoErrors();
        var distributorID = resCreateDistributor.Data?.CreateDistributor?.DistributorState?.DistributorId!;

        var email = $"{Guid.NewGuid():N}@test.com";
        var resCreateAgent = await ChannelManagementClient.CreateAgentAccount.ExecuteAsync(new()
        {
            CreateAgentInput = new()
            {
                DistributorID = distributorID,
                AgentNumber = Guid.NewGuid().ToString(),
                IsVirtualAgent = false,
                HasAgentNBI = true,
                Party = new()
                {
                    FirstName = "test",
                    Surname = "test",
                    Salutation = "MR", // TTL
                    AddrLine1 = "test",
                    PostCode = "test",
                    CountryCode = "HKG", // CRY
                    MainContactEmail = email,
                },
                ActiveFromDateTime = DateTimeOffset.UtcNow.AddDays(-1),
                CreditCheckRunningTotal = 2048,
            },
            CreateLoginInput = new()
            {
                ClientId = "admin",
                TemplateId = Guid.NewGuid().ToString(),
                Username = email,
                Email = email,
                PermissionGroupIds = Array.Empty<string>(),
                IsEmailConfirmed = false,
                IsPasswordValidationDobRequire = false,
                IgnorePasswordValidation = false,
            }
        });
        resCreateAgent.EnsureNoErrors();
        var res = resCreateAgent.Data?.CreateAgentAccount?.CreateAgentAccount!;
        var loginId = res.Login?.Id!;
        var entityId = res.Internal?.Id!;
        var agentId = res.Agent?.AgentID!;
        return new CreateAgentResult(salesChannelID, distributorID, email, loginId, entityId, agentId);
    }
    record CreateAgentResult(string SalesChannelID, string DistributorID, string Email, string LoginId, string EntityId, string AgentID);
}
