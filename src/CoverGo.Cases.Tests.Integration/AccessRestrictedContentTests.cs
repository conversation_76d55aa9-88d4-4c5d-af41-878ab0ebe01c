﻿using System;
using System.Threading.Tasks;
using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.DomainUtils;
using FluentAssertions;
using Xunit;

namespace CoverGo.Cases.Tests.Integration
{
    public class AccessRestrictedContentTests : RestrictedContentBaseTest
    {
        [Fact]
        public async Task
            GIVEN_user_with_full_access_to_restricted_content_WHEN_update_restricted_case_THEN_no_exception_is_thrown()
        {
            //Arrange
            string? restrictedCaseId = RestrictedCaseId;
            var updateCaseCommand = new UpdateCaseCommand
            {
                FieldsPatch = "[{\"op\":\"add\",\"path\":\"newField\",\"value\":\"newValue\"}]"
            };

            // Act
            Result? updateResult =
                await FullPermissionUserCaseClient?.Case_UpdateAsync(TenantId, restrictedCaseId, updateCaseCommand)!;

            // Assert
            updateResult?.Status.Should().Be("success");
        }

        [Fact]
        public async Task
            GIVEN_user_with_no_full_access_to_restricted_content_WHEN_update_restricted_case_THEN_exception_is_thrown()
        {
            //Arrange
            string? restrictedCaseId = RestrictedCaseId;
            var updateCaseCommand = new UpdateCaseCommand
            {
                FieldsPatch = "[{\"op\":\"add\",\"path\":\"newField\",\"value\":\"newValue\"}]"
            };

            // Act
            Func<Task<Result?>> updateAction = async () =>
                await NoPermissionUserCaseClient?.Case_UpdateAsync(TenantId, restrictedCaseId, updateCaseCommand)!;

            // Assert
            await updateAction.Should().ThrowAsync<CasesRestClientException>();
        }

        [Fact]
        public async Task
            GIVEN_user_with_full_access_to_restricted_content_WHEN_delete_restricted_case_THEN_no_exception_is_thrown()
        {
            // Arrange
            string? restrictedCaseId = RestrictedCaseId; // Assuming RestrictedCaseId holds the ID of a restricted case
            var deleteCaseCommand = new DeleteCaseCommand(); // Populate with necessary details if required

            // Act
            Result? deleteResult =
                await FullPermissionUserCaseClient?.Case_DeleteAsync(TenantId, restrictedCaseId, deleteCaseCommand)!;

            // Assert
            deleteResult?.Status.Should().Be("success");
        }

        [Fact]
        public async Task
            GIVEN_user_with_no_full_access_to_restricted_content_WHEN_delete_restricted_case_THEN_exception_is_thrown()
        {
            // Arrange
            string? restrictedCaseId = RestrictedCaseId; // Assuming RestrictedCaseId holds the ID of a restricted case
            var deleteCaseCommand = new DeleteCaseCommand(); // Populate with necessary details if required

            // Act
            Func<Task<Result?>> deleteAction = async () =>
                await NoPermissionUserCaseClient?.Case_DeleteAsync(TenantId, restrictedCaseId, deleteCaseCommand)!;

            // Assert
            await deleteAction.Should().ThrowAsync<CasesRestClientException>();
        }
        
        [Fact]
        public async Task GIVEN_user_with_full_access_to_restricted_content_WHEN_generate_policies_from_restricted_case_THEN_no_exception_is_thrown()
        {
            // Arrange
            string? restrictedCaseId = RestrictedCaseId;
            var generatePoliciesCommand = new GeneratePoliciesFromProposalCommand();
            string accessToken = await Setup.GetAccessToken();
            
            // Act
            Func<Task<ResultOfCreatedStatus?>> generatePoliciesAction = async () =>
                await FullPermissionUserCaseClient?.Case_GeneratePoliciesAsync(TenantId, restrictedCaseId, generatePoliciesCommand, accessToken)!;

            // Assert
            await generatePoliciesAction.Should().NotThrowAsync();
            ResultOfCreatedStatus? generatePoliciesResult = await generatePoliciesAction();
            generatePoliciesResult.Should().NotBeNull();
            generatePoliciesResult?.Status.Should().Be("failure");
        }
        
        [Fact]
        public async Task GIVEN_user_with_no_full_access_to_restricted_content_WHEN_generate_policies_from_restricted_case_THEN_exception_is_thrown()
        {
            // Arrange
            string? restrictedCaseId = RestrictedCaseId;
            var generatePoliciesCommand = new GeneratePoliciesFromProposalCommand();
            string accessToken = await Setup.GetAccessToken();

            // Act
            Func<Task<ResultOfCreatedStatus?>> generatePoliciesAction = async () =>
                await NoPermissionUserCaseClient?.Case_GeneratePoliciesAsync(TenantId, restrictedCaseId, generatePoliciesCommand, accessToken)!;

            // Assert
            await generatePoliciesAction.Should().ThrowAsync<CasesRestClientException>();
        }
        
        [Fact]
        public async Task GIVEN_user_with_full_access_to_restricted_content_WHEN_add_issued_policy_to_restricted_case_THEN_no_exception_is_thrown()
        {
            // Arrange
            string? restrictedCaseId = RestrictedCaseId; // Assuming this holds the ID of a restricted case
            var addIssuedPolicyCommand = new AddIssuedPolicyCommand(); // Populate with necessary details

            // Act
            Func<Task<Result?>> addIssuedPolicyAction = async () =>
                await FullPermissionUserCaseClient?.Case_AddIssuedPolicyAsync(TenantId, restrictedCaseId, addIssuedPolicyCommand)!;

            // Assert
            await addIssuedPolicyAction.Should().NotThrowAsync();
            Result? addIssuedPolicyResult = await addIssuedPolicyAction();
            addIssuedPolicyResult.Should().NotBeNull();
            addIssuedPolicyResult?.Status.Should().Be("success");
        }

        [Fact]
        public async Task
            GIVEN_user_with_no_full_access_to_restricted_content_WHEN_add_issued_policy_to_restricted_case_THEN_exception_is_thrown()
        {
            // Arrange
            string? restrictedCaseId = RestrictedCaseId; // Assuming this holds the ID of a restricted case
            var addIssuedPolicyCommand = new AddIssuedPolicyCommand(); // Populate with necessary details

            // Act
            Func<Task<Result?>> addIssuedPolicyAction = async () =>
                await NoPermissionUserCaseClient?.Case_AddIssuedPolicyAsync(TenantId, restrictedCaseId,
                    addIssuedPolicyCommand)!;

            // Assert
            await addIssuedPolicyAction.Should().ThrowAsync<CasesRestClientException>();
        }
        
        [Fact]
        public async Task GIVEN_user_with_full_access_to_restricted_content_WHEN_add_stakeholder_to_case_THEN_no_exception_is_thrown()
        {
            // Arrange
            string? caseId = RestrictedCaseId;
            var addStakeholderCommand = new AddStakeholderCommand();

            // Act
            Func<Task<ResultOfString?>> addStakeholderAction = async () =>
                await FullPermissionUserCaseClient?.Case_AddStakeholderOfCaseAsync(TenantId, caseId, addStakeholderCommand)!;

            // Assert
            await addStakeholderAction.Should().NotThrowAsync();
            ResultOfString? addStakeholderResult = await addStakeholderAction();
            addStakeholderResult.Should().NotBeNull();
            addStakeholderResult?.Status.Should().Be("success");
        }
        
        [Fact]
        public async Task GIVEN_user_with_no_full_access_to_restricted_content_WHEN_add_stakeholder_to_case_THEN_exception_is_thrown()
        {
            // Arrange
            string? caseId = RestrictedCaseId;
            var addStakeholderCommand = new AddStakeholderCommand();

            // Act
            Func<Task<ResultOfString?>> addStakeholderAction = async () =>
                await NoPermissionUserCaseClient?.Case_AddStakeholderOfCaseAsync(TenantId, caseId, addStakeholderCommand)!;

            // Assert
            await addStakeholderAction.Should().ThrowAsync<CasesRestClientException>();
        }
        
        [Fact]
        public async Task GIVEN_user_with_full_access_to_restricted_content_WHEN_update_stakeholder_of_case_THEN_no_exception_is_thrown()
        {
            // Arrange
            string? caseId = RestrictedCaseId; 
            var updateStakeholderCommand = new UpdateStakeholderCommand();

            // Act
            Func<Task<Result?>> updateStakeholderAction = async () =>
                await FullPermissionUserCaseClient?.Case_UpdateStakeholderOfCaseAsync(TenantId, caseId, updateStakeholderCommand)!;

            // Assert
            await updateStakeholderAction.Should().NotThrowAsync();
            Result? updateStakeholderResult = await updateStakeholderAction();
            updateStakeholderResult.Should().NotBeNull();
            updateStakeholderResult?.Status.Should().Be("success");
        }
        
        [Fact]
        public async Task GIVEN_user_with_no_full_access_to_restricted_content_WHEN_update_stakeholder_of_case_THEN_exception_is_thrown()
        {
            // Arrange
            string? caseId = RestrictedCaseId; 
            var updateStakeholderCommand = new UpdateStakeholderCommand();

            // Act
            Func<Task<Result?>> updateStakeholderAction = async () =>
                await NoPermissionUserCaseClient?.Case_UpdateStakeholderOfCaseAsync(TenantId, caseId, updateStakeholderCommand)!;

            // Assert
            await updateStakeholderAction.Should().ThrowAsync<CasesRestClientException>();
        }
        
        [Fact]
        public async Task GIVEN_user_with_full_access_WHEN_remove_stakeholder_from_case_THEN_no_exception_is_thrown()
        {
            // Arrange
            string? caseId = RestrictedCaseId;
            var removeStakeholderCommand = new RemoveStakeholderCommand();

            // Act
            Func<Task<Result?>> removeStakeholderAction = async () =>
                await FullPermissionUserCaseClient?.Case_RemoveStakeholderFromCaseAsync(TenantId, caseId, removeStakeholderCommand)!;

            // Assert
            await removeStakeholderAction.Should().NotThrowAsync();
            Result? result = await removeStakeholderAction();
            result.Should().NotBeNull();
            result?.Status.Should().Be("success");
        }
        
        [Fact]
        public async Task GIVEN_user_with_no_full_access_WHEN_remove_stakeholder_from_case_THEN_exception_is_thrown()
        {
            // Arrange
            string? caseId = RestrictedCaseId;
            var removeStakeholderCommand = new RemoveStakeholderCommand();

            // Act
            Func<Task<Result?>> removeStakeholderAction = async () =>
                await NoPermissionUserCaseClient?.Case_RemoveStakeholderFromCaseAsync(TenantId, caseId, removeStakeholderCommand)!;

            // Assert
            await removeStakeholderAction.Should().ThrowAsync<CasesRestClientException>();
        }
    }
}