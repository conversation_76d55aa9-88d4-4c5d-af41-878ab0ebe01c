﻿using CoverGo.Cases.Client.Rest;
using CoverGo.Cases.Tests.Integration.Utils;
using CoverGo.GraphQL.Client;
using GraphQL;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using AccessPolicy = CoverGo.Cases.Client.Rest.AccessPolicy;

namespace CoverGo.Cases.Tests.Integration;

public abstract class RestrictedContentBaseTest : IAsyncLifetime
{
    protected const string TenantId = "covergo";
    protected readonly string RandomTag = Guid.NewGuid().ToString();
    protected CasesRestClient? AnonymousUserCaseClient;
    protected CasesRestClient? FullPermissionUserCaseClient;
    protected CasesRestClient? NoPermissionUserCaseClient;
    private CoverGoGraphQlClient? _gatewayClient;
    protected string? NullCaseId;
    protected string? StandardCaseId;
    protected string? RestrictedCaseId;

    public async Task InitializeAsync()
    {
        _gatewayClient = await Setup.BuildGatewayGraphQLHttpClient();
        AnonymousUserCaseClient = new CasesRestClient(Setup.BuildHttpClient(TenantId));
        FullPermissionUserCaseClient = await CreateUserCasesClient(("accessRestrictedContent", "full"));
        NoPermissionUserCaseClient = await CreateUserCasesClient();

        string nullAccessPolicyCompany = await CreateCompany("null", null);
        string standardAccessPolicyCompany = await CreateCompany("standard", AccessPolicy.Standard);
        string restrictedAccessPolicyCompany = await CreateCompany("restricted", AccessPolicy.Restricted);

        NullCaseId = (await AnonymousUserCaseClient.Case_CreateAsync(TenantId, new CreateCaseCommand
        {
            Name = "null",
            Source = RandomTag,
            HolderId = nullAccessPolicyCompany
        }))?.Value;
        
        StandardCaseId = (await AnonymousUserCaseClient.Case_CreateAsync(TenantId, new CreateCaseCommand
        {
            Name = "standard",
            Source = RandomTag,
            HolderId = standardAccessPolicyCompany
        }))?.Value;
        
        RestrictedCaseId = (await AnonymousUserCaseClient.Case_CreateAsync(TenantId, new CreateCaseCommand
        {
            Name = "restricted",
            Source = RandomTag,
            HolderId = restrictedAccessPolicyCompany
        }))?.Value;
    }
    private async Task<CasesRestClient> CreateUserCasesClient(params (string permission, string value)[] permissions)
    {
        var loginInfo = new
        {
            tenantId = "covergo",
            clientId = "admin",
            entityId = Guid.NewGuid().ToString("N"),
            username = Guid.NewGuid().ToString("N"),
            password = Guid.NewGuid().ToString("N"),
            email = $"{Guid.NewGuid():N}@covergo.com"
        };

        string loginId = await CreateLogin(loginInfo.clientId, loginInfo.entityId, loginInfo.username, loginInfo.password, loginInfo.email);
        foreach ((string permission, string value) in permissions)
        {
            await AddPermission(loginId, permission, value);
        }

        string userToken = await Setup.GetAccessToken(loginInfo.tenantId, loginInfo.clientId, loginInfo.username, loginInfo.password);
        HttpClient httpClient = Setup.BuildHttpClient(loginInfo.tenantId);
        httpClient.DefaultRequestHeaders.Authorization = new("Bearer", userToken);
        return new CasesRestClient(httpClient);

        async Task<string> CreateLogin(string clientId, string entityId, string username, string password, string email)
        {
            string mutation = $"mutation {{createLogin(createLoginInput: {{clientId: \"{clientId}\", entityId: \"{entityId}\", username: \"{username}\", password: \"{password}\", email: \"{email}\", isEmailConfirmed: true}}){{status,createdStatus{{id}}}}}}";
            GraphQLResponse<JObject> response = await _gatewayClient.SendMutationAsync<JObject>(new GraphQLRequest(mutation));
            return response.Errors != null && response.Errors.Any() ? string.Empty : response.Data["createLogin"]["createdStatus"]["id"].ToString();
        }

        async Task AddPermission(string loginId, string permission, string value)
        {
            string mutation = $@"mutation {{
              addTargettedPermission(
                loginId: ""{loginId}""
                addTargettedPermissionInput: {{ type: ""{permission}"", value: ""{value}"" }}
              ) {{
                status
                errors
                errors_2 {{
                  code
                  message
                }}
              }}
            }}
            ";
            GraphQLResponse<JObject> r = await _gatewayClient.SendMutationAsync<JObject>(new GraphQLRequest(mutation));
        }
    }

    private async Task<string> CreateCompany(string internalCode, AccessPolicy? accessPolicy)
    {
        string mutation = accessPolicy == null
            ? $@"mutation {{
                  createCompany(
                    createCompanyInput: {{ registrationNumber: ""{internalCode}"" }}
                  ) {{
                    createdStatus {{
                      id
                    }}
                  }}
                }}
                "
            : $@"mutation {{
                  createCompany(
                    createCompanyInput: {{ accessPolicy: {accessPolicy}, registrationNumber: ""{internalCode}"" }}
                  ) {{
                    createdStatus {{
                      id
                    }}
                  }}
                }}
                ";
        GraphQLResponse<JObject> response = await _gatewayClient.SendMutationAsync<JObject>(new GraphQLRequest(mutation));
        return response.Errors != null && response.Errors.Any() ? string.Empty : response.Data["createCompany"]["createdStatus"]["id"].ToString();
    }

    public Task DisposeAsync() => Task.CompletedTask;
}