using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Infrastructure.Common;
using Moq;
using Newtonsoft.Json.Linq;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-30011")]
public class RichTextFieldTests
{
    [Fact]
    public async Task ParseCheckboxGroupFieldTest()
    {
        var memberSchema = $$"""
        {
          "properties": {
            "testRiskTextBox": {
              "type": "richTextEditor",
              "meta": {
                "label": "testRiskTextBox",
                "component": "QuillEditor",
                "required": false,
                "fieldType": "richTextEditor",
                "validations": "",
                "order": 20,
                "config": true,
                "condition": "",
                "propertyName": "testRiskTextBox"
              }
            }
          }
        }
        """;
        var memberDataSchemaObject = JObject.Parse(memberSchema);
        var parsedMemberDataSchema = DataSchemaParser.Parse(memberDataSchemaObject).ToList();
        // Act (When)

        var fieldsDefinitions = await CustomFieldsDefinitionsUtils.ToCustomFieldDefinitions([.. parsedMemberDataSchema.Select(it => (it.Key, it.Value))], Mock.Of<IReferenceDataOptionsRepository>());

        fieldsDefinitions[0].Type.Should().BeOfType<RichTextFieldType>();
        var checkboxFieldType = fieldsDefinitions[0].Type as RichTextFieldType;
        checkboxFieldType.Should().NotBeNull();
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    [InlineData(234)]
    [InlineData(999d)]
    public void ValidCheckboxGroupFieldTypeError(object? value)
    {
        // Arrange (Given)
        var type = new RichTextFieldType();

        // Act (When)
        var result = type.ValidateField(value, "richTextFieldType");

        // Assert (Then)
        result.Should().HaveCount(1);
        result[0].Message.Should().Be("Invalid type passed");
    }

    [Theory]
    [InlineData("text1")]
    [InlineData("text2")]
    [InlineData(null)]
    public void ValidCheckboxGroupFieldType(object? value)
    {
        // Arrange (Given)
        var type = new RichTextFieldType();

        // Act (When)
        var result = type.ValidateField(value, "richTextFieldType");

        // Assert (Then)
        result.Should().HaveCount(0);
    }
}
