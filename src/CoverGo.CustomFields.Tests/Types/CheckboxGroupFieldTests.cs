using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Infrastructure.Common;
using Moq;
using Newtonsoft.Json.Linq;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-30010")]
public class CheckboxGroupFieldTests
{
    [Fact]
    public async Task ParseCheckboxGroupFieldTest()
    {
        var memberSchema = $$"""
        {
          "properties": {

            "test": {
              "type": "group",
              "meta": {
                "label": "Test",
                "component": "JCheckboxGroup",
                "required": false,
                "fieldType": "checkboxGroup",
                "validations": "",
                "order": 23,
                "config": true,
                "condition": "",
                "options": [
                  { "key": "175758598046554333", "name": "1", "value": "1" },
                  { "key": "175758600751354160", "name": "2", "value": "2" },
                  { "key": "175758601045093672", "name": "3", "value": "4" }
                ],
                "propertyName": "test",
                "helpText": "ss"
              }
            }
          }
        }
        """;
        var memberDataSchemaObject = JObject.Parse(memberSchema);
        var parsedMemberDataSchema = DataSchemaParser.Parse(memberDataSchemaObject).ToList();
        // Act (When)

        var fieldsDefinitions = await CustomFieldsDefinitionsUtils.ToCustomFieldDefinitions([.. parsedMemberDataSchema.Select(it => (it.Key, it.Value))], Mock.Of<IReferenceDataOptionsRepository>());

        fieldsDefinitions[0].Type.Should().BeOfType<CheckboxGroupFieldType>();
        var checkboxFieldType = fieldsDefinitions[0].Type as CheckboxGroupFieldType;
        checkboxFieldType.Should().NotBeNull();
        checkboxFieldType!.Options.Should().NotBeNull();
        checkboxFieldType!.Options.Should().Contain(c => c!.Value!.ToString()!.Equals("1"));
        checkboxFieldType!.Options.Should().Contain(c => c!.Value!.ToString()!.Equals("2"));
        checkboxFieldType!.Options.Should().Contain(c => c!.Value!.ToString()!.Equals("4"));
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    [InlineData(234)]
    [InlineData(999d)]
    public void ValidCheckboxGroupFieldTypeError(object? value)
    {
        // Arrange (Given)
        var type = new CheckboxGroupFieldType();

        // Act (When)
        var result = type.ValidateField(value, "checkboxGroupField");

        // Assert (Then)
        result.Should().HaveCount(1);
        result[0].Message.Should().Be("Invalid type passed");
    }

    [Theory]
    [InlineData("text1")]
    [InlineData("text2")]
    [InlineData(null)]
    public void ValidCheckboxGroupFieldType(object? value)
    {
        // Arrange (Given)
        var type = new CheckboxGroupFieldType();

        // Act (When)
        var result = type.ValidateField(value, "checkboxGroupField");

        // Assert (Then)
        result.Should().HaveCount(0);
    }
}
