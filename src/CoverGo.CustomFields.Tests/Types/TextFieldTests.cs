using System.Text.Json;

using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[<PERSON>rait("Ticket", "CH-1644")]
[<PERSON>rait("Ticket", "CH-1646")]
public class TextFieldTests
{
    [Theory]
    [InlineData("Hello")]
    [InlineData("""
        Hello
        There
        """)]
    [InlineData(null)]
    public void ValidTextOptions(object? value)
    {
        // Arrange (Given)
        var type = new TextFieldType();

        // Act (When)
        var result = type.ValidateField(value, "textField");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(1)]
    [InlineData(true)]
    public void InvalidTextOptions(object? value)
    {
        // Arrange (Given)
        var type = new TextFieldType();

        // Act (When)
        var result = type.ValidateField(value, "textField");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "textField");
    }

    [Fact]
    public void JsonElemenentConvertsToText()
    {
        // Arrange (Given)
        var type = new TextFieldType();
        var doc = JsonDocument.Parse("\"2022-01-01\"");

        // Act (When)
        var result = type.TransformField(doc.RootElement);

        // Assert (Then)
        result.Should().BeOfType<string>().Subject.Should().Be("2022-01-01");
    }
}
