using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1646")]
public class OptionsFieldTests
{
    public static IEnumerable<object?[]> ValidOptionPassedData =>
        [
            [1, new NumberFieldType() { Options = new HashSet<SchemaOption?> { new("1", 1), new("2", 2), new("3", 3) } }],
            ["1", new TextFieldType() { Options = new HashSet<SchemaOption?> { new("1", "1"), new("2", "2"), new("3", "3") } }],
        ];

    [Theory]
    [MemberData(nameof(ValidOptionPassedData))]
    public void ValidOptionPassed(object? value, IOptionsFieldType optionsFieldType)
    {
        // Arrange (Given)
        var type = optionsFieldType;

        // Act (When)
        var result = type.IsValidOption(value);

        // Assert (Then)
        result.Should().BeTrue();
    }

    public static IEnumerable<object?[]> InvalidOptionPassedData =>
        [
            [123, new NumberFieldType() { Options = new HashSet<SchemaOption?> { new("1", 1), new("2", 2), new("3", 3) } }],
            ["123", new TextFieldType() { Options = new HashSet<SchemaOption?> { new("1", "1"), new("2", "2"), new("3", "3") } }],
        ];

    [Theory]
    [MemberData(nameof(InvalidOptionPassedData))]
    public void InvalidOptionPassed(object? value, IOptionsFieldType optionsFieldType)
    {
        // Arrange (Given)
        var type = optionsFieldType;

        // Act (When)
        var result = type.IsValidOption(value);

        // Assert (Then)
        result.Should().BeFalse();
    }

    public static IEnumerable<object?[]> OptionsAreNotSetData =>
    [
        [1, new NumberFieldType() { Options = null }],
        ["1", new TextFieldType() { Options = null }],
    ];

    [Theory]
    [MemberData(nameof(OptionsAreNotSetData))]
    public void OptionsAreNotSet(object? value, IOptionsFieldType optionsFieldType)
    {
        // Arrange (Given)
        var type = optionsFieldType;

        // Act (When)
        var result = type.IsValidOption(value);

        // Assert (Then)
        result.Should().BeTrue();
    }

    [Fact]
    public void CustomFieldDefinitionNoErrorsWithValidOption()
    {
        // Arrange (Given)
        var type = new CustomFieldDefinition(
            "field",
            new TextFieldType() { Options = new HashSet<SchemaOption?> { new("1", "1"), new("2", "2"), new("3", "3") } },
            []);

        // Act (When)
        var result = type.ValidateField("1");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Fact]
    public void CustomFieldDefinitionReturnsOptionsError()
    {
        // Arrange (Given)
        var type = new CustomFieldDefinition(
            "field",
            new TextFieldType() { Options = new HashSet<SchemaOption?> { new("1", "1"), new("2", "2"), new("3", "3") } },
            []);

        // Act (When)
        var result = type.ValidateField("123");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "field");
    }

    [Fact]
    public void CustomFieldDefinitionAllowsNullOption()
    {
        // Arrange (Given)
        var type = new CustomFieldDefinition(
            "field",
            new TextFieldType() { Options = new HashSet<SchemaOption?> { new("1", "1"), new("2", "2"), new("3", "3") } },
            []);

        // Act (When)
        var result = type.ValidateField(null);

        // Assert (Then)
        result.Should().BeEmpty();
    }
}

