using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.ProposalMembers;
using Moq;
using Newtonsoft.Json.Linq;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Infrastructure.Common;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
public class FormularFieldTests
{
    [Fact]
    public async Task ShouldCalculateTheValue()
    {
        var member = new ProposalMemberAggregate()
        {
            Id = "1",
            OpportunityId = "1",
            OfferId = "1",
            ProposalId = "1",
            Fields = [
                new CustomField<ProposalMemberAggregate>("givenName", "John"),
                new CustomField<ProposalMemberAggregate>("surname", "Doe"),
            ]
        };
        var schema = $$"""
        {
            "properties": {
                "fullName": {
                    "type": "string",
                    "meta": {
                        "label": "Name",
                        "component": "FormulaEditor",
                        "required": false,
                        "fieldType": "dynamic",
                        "validations": "",
                        "order": 3,
                        "config": false,
                        "condition": "",
                        "propertyName": "fullName",
                        "formula": [
                            {
                                "id": "4237842307410131",
                                "name": "formula",
                                "children": [
                                    {
                                        "id": "9187187513787671",
                                        "label": "Data",
                                        "name": "data",
                                        "componentName": "NodeData",
                                        "children": [],
                                        "props": {
                                            "path": "givenName"
                                        },
                                        "editor": {
                                            "componentName": "NodeDataEditor"
                                        }
                                    },
                                    {
                                        "id": "27769427526562285",
                                        "label": "Data",
                                        "name": "data",
                                        "componentName": "NodeData",
                                        "children": [],
                                        "props": {
                                            "path": "surname"
                                        },
                                        "editor": {
                                            "componentName": "NodeDataEditor"
                                        }
                                    },
                                    {
                                        "id": "9658360429862378",
                                        "label": "Value",
                                        "name": "value",
                                        "componentName": "NodeValue",
                                        "children": [],
                                        "props": {
                                            "dataType": "number",
                                            "value": " "
                                        },
                                        "editor": {
                                            "componentName": "NodeValueEditor"
                                        }
                                    }
                                ],
                                "label": "Formula",
                                "componentName": "NodeFormula",
                                "props": {
                                    "formulaName": "JOIN"
                                },
                                "editor": {
                                    "componentName": "NodeFormulaEditor"
                                }
                            }
                        ]
                    }
                }
            }
        }
        """;

        // Act (When)
        var fields = DataSchemaParser.Parse(JObject.Parse(schema));
        var fieldsDefinitions = await CustomFieldsDefinitionsUtils.ToCustomFieldDefinitions([.. fields.Select(it => (it.Key, it.Value))], Mock.Of<IReferenceDataOptionsRepository>());

        // Assert (Then)
        fieldsDefinitions[0].Type.Should().BeOfType<FormulaFieldType>();
        var formulaFieldType = fieldsDefinitions[0].Type as FormulaFieldType;
        var value = formulaFieldType!.Func.Calculate(member.Fields.Select(it => (it.Key, it.Value)).ToDictionary(it => it.Key, it => it.Value));
        value.Should().Be("John Doe");
    }
}
