using System.Text.Json;

using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1646")]
public class BooleanFieldTests
{
    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    [InlineData(null)]
    public void ValidBooleanOptions(object? value)
    {
        // Arrange (Given)
        var type = new BooleanFieldType();

        // Act (When)
        var result = type.ValidateField(value, "boolField");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(1)]
    [InlineData("123")]
    public void InvalidBooleanOptions(object? value)
    {
        // Arrange (Given)
        var type = new BooleanFieldType();

        // Act (When)
        var result = type.ValidateField(value, "boolField");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "boolField");
    }

    [Fact]
    public void JsonElemenentConvertsToBoolean()
    {
        // Arrange (Given)
        var type = new BooleanFieldType();
        var doc = JsonDocument.Parse("true");

        // Act (When)
        var result = type.TransformField(doc.RootElement);

        // Assert (Then)
        result.Should().BeOfType<bool>().Subject.Should().BeTrue();
    }
}

