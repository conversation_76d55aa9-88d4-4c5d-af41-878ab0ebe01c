using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1646")]
public class ObjectFieldTests
{
    public static TheoryData<object?> ValidObjectOptionsData => new()
    {
        new Dictionary<string, object?> { },
        new Dictionary<string, object?> { ["money"] = 123 },
        null
    };

    [Theory]
    [MemberData(nameof(ValidObjectOptionsData))]
    public void ValidObjectOptions(object? value)
    {
        // Arrange (Given)
        var type = new ObjectFieldType([new CustomFieldDefinition("money", new NumberFieldType(), [])]);

        // Act (When)
        var result = type.ValidateField(value, "objectField");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(true)]
    [InlineData("123")]
    [InlineData(123)]
    public void InvalidObjectOptions(object? value)
    {
        // Arrange (Given)
        var type = new ObjectFieldType([]);

        // Act (When)
        var result = type.ValidateField(value, "objectField");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "objectField");
    }

    // Rule: ExtraFieldsAreNotAllowed
    [Fact]
    public void ValidateFieldReturnsErrorsWithExtraFields()
    {
        // Arrange (Given)
        var type = new ObjectFieldType([]);
        var value = new Dictionary<string, object?> { ["money"] = 123, ["currency"] = "CAD" };

        // Act (When)
        var result = type.ValidateField(value, "objectField");

        // Assert (Then)
        result.Should().HaveCount(2);
    }

    // Rule: ValidatesInnerFields
    [Fact]
    public void ValidateFieldReturnsInnerErrors()
    {
        // Arrange (Given)
        var type = new ObjectFieldType(
            [
                new CustomFieldDefinition("money", new NumberFieldType(), []),
                new CustomFieldDefinition("currency", new TextFieldType(), [])
            ]);
        var value = new Dictionary<string, object?> { ["money"] = true, ["currency"] = 123 };

        // Act (When)
        var result = type.ValidateField(value, "objectField");

        // Assert (Then)
        result.Should().BeEquivalentTo(
            [
                new SchemaFieldValidationError("money", "Invalid type passed"),
                new SchemaFieldValidationError("currency", "Invalid type passed")
            ]);
    }
}

