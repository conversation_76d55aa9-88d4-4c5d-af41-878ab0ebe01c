using System.Text.Json;

using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1646")]
public class NumberFieldTests
{
    public static IEnumerable<object?[]> ValidNumberOptionsData =>
        [
            ["123"],
            [123],
            [123L],
            [123.0f],
            [123.0d],
            [123M],
            [123u],
            [123uL],
            [null],
        ];

    [Theory]
    [MemberData(nameof(ValidNumberOptionsData))]
    public void ValidNumberOptions(object? value)
    {
        // Arrange (Given)
        var type = new NumberFieldType();

        // Act (When)
        var result = type.ValidateField(value, "numberField");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(true)]
    public void InvalidNumberOptions(object? value)
    {
        // Arrange (Given)
        var type = new DescriptionFieldType();

        // Act (When)
        var result = type.ValidateField(value, "numberField");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "numberField");
    }

    [Fact]
    public void JsonElemenentConvertsToNumber()
    {
        // Arrange (Given)
        var type = new NumberFieldType();
        var doc = JsonDocument.Parse("200");

        // Act (When)
        var result = type.TransformField(doc.RootElement);

        // Assert (Then)
        result.Should().BeOfType<decimal>().Subject.Should().Be(200);
    }
}
