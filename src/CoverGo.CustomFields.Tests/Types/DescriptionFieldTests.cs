using System.Text.Json;

using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[<PERSON>rai<PERSON>("Ticket", "CH-1644")]
[<PERSON>rai<PERSON>("Ticket", "CH-1646")]
public class DescriptionFieldTests
{
    [Theory]
    [InlineData("Hello")]
    [InlineData("""
        Hello
        There
        """)]
    [InlineData(null)]
    public void ValidDescriptionOptions(object? value)
    {
        // Arrange (Given)
        var type = new DescriptionFieldType();

        // Act (When)
        var result = type.ValidateField(value, "descriptionField");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(1)]
    [InlineData(true)]
    public void InvalidDescriptionOptions(object? value)
    {
        // Arrange (Given)
        var type = new DescriptionFieldType();

        // Act (When)
        var result = type.ValidateField(value, "descriptionField");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "descriptionField");
    }

    [Fact]
    public void JsonElemenentConvertsToDescription()
    {
        // Arrange (Given)
        var type = new DescriptionFieldType();
        var doc = JsonDocument.Parse("\"2022-01-01\"");

        // Act (When)
        var result = type.TransformField(doc.RootElement);

        // Assert (Then)
        result.Should().BeOfType<string>().Subject.Should().Be("2022-01-01");
    }
}
