using System.Globalization;
using System.Text.Json;

using CoverGo.BuildingBlocks.CustomFields.Types;

namespace CoverGo.CustomFields.Tests.Types;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[Trait("Ticket", "CH-1646")]
public class DateFieldTests
{
    public static IEnumerable<object?[]> ValidDateOptionsData =>
    [
        [DateOnly.Parse("2022-01-01", CultureInfo.InvariantCulture)],
        [null],
    ];

    [Theory]
    [MemberData(nameof(ValidDateOptionsData))]
    public void ValidDateOptions(object? value)
    {
        // Arrange (Given)
        var type = new DateFieldType();

        // Act (When)
        var result = type.ValidateField(value, "dateField");

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(1)]
    [InlineData("123")]
    [InlineData(true)]
    public void InvalidDateOptions(object? value)
    {
        // Arrange (Given)
        var type = new DateFieldType();

        // Act (When)
        var result = type.ValidateField(value, "dateField");

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "dateField");
    }

    [Fact]
    public void JsonElemenentConvertsToDate()
    {
        // Arrange (Given)
        var type = new DateFieldType();
        var doc = JsonDocument.Parse("\"2022-01-01\"");

        // Act (When)
        var result = type.TransformField(doc.RootElement);

        // Assert (Then)
        result.Should().BeOfType<DateOnly>().Subject.Should().Be(DateOnly.Parse("2022-01-01", CultureInfo.InvariantCulture));
    }

    [Fact]
    [Trait("Ticket", "CH-20173")]
    public void GetFieldValueStringNull()
    {
        // Arrange (Given)
        var type = new DateFieldType();

        // Act (When)
        var result = type.GetFieldValueString(null, null);

        // Assert (Then)
        result.Should().BeNull();
    }

    [Fact]
    [Trait("Ticket", "CH-20173")]
    public void GetFieldValueStringDateFormat()
    {
        // Arrange (Given)
        var type = new DateFieldType();

        // Act (When)
        var result = type.GetFieldValueString("2024-01-12", null);

        // Assert (Then)
        result.Should().Be("2024-01-12");
    }
}
