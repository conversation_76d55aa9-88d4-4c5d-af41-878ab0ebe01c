using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-7749")]
public class CoreFieldsTests
{
    public class CoreFieldEntity
    {
        public string? CoreField { get; set; }

        public CustomFields<CoreFieldEntity> Fields { get; set; } = [];
    }

    [Fact]
    public void CoreFieldsCanBeValid()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "coreField",
                    new DescriptionFieldType(),
                    [new RequiredValidator()]),
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    []),
            ]);
        var entity = new CoreFieldEntity()
        {
            CoreField = "123",
            Fields = [
                new CustomField<CoreFieldEntity>("notCoreField", null)
            ],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            coreFields: [
                ("coreField", entity.CoreField)
            ]);

        // Assert (Then)
        errors.Should().BeEmpty();
    }

    [Fact]
    public void CoreFieldsCanBeInvalid()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "coreField",
                    new DescriptionFieldType(),
                    [new RequiredValidator()]),
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    []),
            ]);
        var entity = new CoreFieldEntity()
        {
            CoreField = null,
            Fields = [
                new CustomField<CoreFieldEntity>("notCoreField", null)
            ],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            coreFields: [
                ("coreField", entity.CoreField)
            ]);

        // Assert (Then)
        errors.Should().BeEquivalentTo(new List<SchemaValidationError>()
        {
            new SchemaFieldValidationError("coreField", "Field is required")
        });
    }

    [Fact]
    public void CoreFieldsMustNotBeSuppliedInDynamicFields()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "coreField",
                    new DescriptionFieldType(),
                    [new RequiredValidator()]),
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    []),
            ]);
        var entity = new CoreFieldEntity()
        {
            CoreField = "123",
            Fields = [
                new CustomField<CoreFieldEntity>("coreField", null),
                new CustomField<CoreFieldEntity>("notCoreField", null)
            ],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            coreFields: [
                ("coreField", entity.CoreField)
            ]);

        // Assert (Then)
        errors.Should().BeEquivalentTo(new List<SchemaValidationError>()
        {
            new SchemaFieldValidationError("coreField", "Core field coreField must not be passed in the Fields")
        });
    }

    [Fact]
    public void CoreFieldsMustNotBeSuppliedWhenNotInDefinitions()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    []),
            ]);
        var entity = new CoreFieldEntity()
        {
            CoreField = "123",
            Fields = [
                new CustomField<CoreFieldEntity>("notCoreField", null)
            ],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            coreFields: [
                ("coreField", entity.CoreField)
            ]);

        // Assert (Then)
        errors.Should().BeEquivalentTo(new List<SchemaValidationError>()
        {
            new SchemaFieldValidationError("coreField", "No extra fields allowed")
        });
    }

    [Fact]
    public void CoreFieldsCanBeNotSuppliedWhenNotInDefinitions()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    []),
            ]);
        var entity = new CoreFieldEntity()
        {
            CoreField = null,
            Fields = [
                new CustomField<CoreFieldEntity>("notCoreField", null)
            ],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            coreFields: [
                ("coreField", entity.CoreField)
            ]);

        // Assert (Then)
        errors.Should().BeEmpty();
    }
}
