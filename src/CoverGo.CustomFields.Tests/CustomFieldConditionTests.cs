using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests;

[Trait("Category", "Unit")]
public class CustomFieldConditionTests
{
    [Trait("Ticket", "CH-21883")]
    [Theory]
    [InlineData(null, null)]
    [InlineData(" $get(memberType).value===employee  ", true)]
    [InlineData(" $get(memberType).value == 'employee' ", true)]
    [InlineData(" $get(health).value===true ", true)]
    [InlineData(" $get(drugs).value===false ", false)]
    [InlineData("  $get(sameResidenceAsThePolicyholder).value == false ", true)]
    [InlineData(
            "$get(planSelected).value === blue || $get(planSelected).value === azure || $get(planId).value === blue || $get(planId).value === azure",
            true)]
    [InlineData(
            "$get(planSelected).value === aurora || $get(planSelected).value === azure || $get(planId).value === blue || $get(planId).value === vista",
            false)]
    [InlineData(
            "$get(planSelected).value === blue && $get(planId).value === azure",
            true)]
    [InlineData(
            "$get(planSelected).value === blue && $get(planId).value === vista",
            false)]
    public void EvaluateTests(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "employee"},
            {"health", true},
            {"drugs", true},
            {"sameResidenceAsThePolicyholder", false},
            {"planSelected", "blue"},
            {"planId", "azure"}
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }

    [Trait("Ticket", "CH-21883")]
    [Fact]
    public void GIVEN_required_field_and_true_condition_WHEN_validate_missing_field_THEN_required_error()
    {
        // Arrange (Given)
        var type = new ObjectFieldType(
            [
                new CustomFieldDefinition("money", new NumberFieldType(), []),
                new CustomFieldDefinition("currency", new TextFieldType(), [new RequiredValidator()], Condition:new CustomFieldCondition("$get(money).value == 123"))
            ]);
        var value = new Dictionary<string, object?> { ["money"] = 123 };

        // Act (When)
        var result = type.ValidateField(value, "objectField");

        // Assert (Then)
        result.Should().BeEquivalentTo(
            [
                new SchemaFieldValidationError("currency", "Field is required")
            ]);
    }

    [Trait("Ticket", "CH-21883")]
    [Fact]
    public void GIVEN_field_and_false_condition_WHEN_validate_field_THEN_extra_error()
    {
        // Arrange (Given)
        var type = new ObjectFieldType(
            [
                new CustomFieldDefinition("money", new NumberFieldType(), []),
                new CustomFieldDefinition("currency", new TextFieldType(), [new RequiredValidator()], Condition:new CustomFieldCondition("$get(money).value == \"123\""))
            ]);
        var value = new Dictionary<string, object?> { ["money"] = 123, ["currency"] = "VND" };

        // Act (When)
        var result = type.ValidateField(value, "objectField");

        // Assert (Then)
        result.Should().BeEquivalentTo(
            [
                new SchemaFieldValidationError("currency", "No extra fields allowed")
            ]);
    }

    [Trait("Ticket", "CH-30093")]
    [Theory]
    [InlineData(
        "$get(memberType).value == 'dependent' && $get(sameResidenceAsThePolicyholder).value == false",
        false)]
    public void ShouldEvaluateConditionsLazily(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "employee"},
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }

    [Trait("Ticket", "CH-30093")]
    [Theory]
    [InlineData(
        "$get(sameResidenceAsThePolicyholder).value == false",
        false)]
    [InlineData(
        "$get(sameResidenceAsThePolicyholder).value == true",
        false)]
    public void ShouldTreatMissingConditionsAsUndefined(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "employee"},
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }


    [Trait("Ticket", "CH-30093")]
    [Theory]
    [InlineData(
        "$get(memberType).value == 'employee' || ($get(memberType).value == 'dependent' && $get(sameResidenceAsThePolicyholder).value == false)",
        true)]
    public void ShouldEvaluateFirstOr(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "employee"}
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }

    [Trait("Ticket", "CH-30093")]
    [Theory]
    [InlineData(
        "$get(memberType).value == 'employee' || ($get(memberType).value == 'dependent' && $get(sameResidenceAsThePolicyholder).value == false)",
        true)]
    public void ShouldEvaluateSecondOr(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "dependent"},
            {"sameResidenceAsThePolicyholder", false}
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }

    [Trait("Ticket", "CH-30093")]
    [Theory]
    [InlineData(
        "$get(memberType).value == 'employee' || ($get(memberType).value == 'dependent' && $get(sameResidenceAsThePolicyholder).value == false)",
        false)]
    public void ShouldEvaluateAllOrToFalse(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "dependent"},
            {"sameResidenceAsThePolicyholder", true}
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }

    [Trait("Ticket", "CH-30093")]
    [Theory]
    [InlineData(
        "($get(memberType).value == 'employee' && $get(health).value == true) || ($get(memberType).value == 'dependent' && ($get(sameResidenceAsThePolicyholder).value == false || $get(health).value == false))",
        true)]
    [InlineData(
        "($get(memberType).value == 'contractor' || $get(memberType).value == 'employee') && ($get(health).value == true && ($get(drugs).value == false || $get(planSelected).value == 'premium'))",
        false)]
    public void ShouldHandleComplexNestedExpressions(string? condition, bool? expected)
    {
        // Arrange (Given)
        var contextFields = new Dictionary<string, object?>{
            {"memberType", "dependent"},
            {"health", false},
            {"drugs", true},
            {"sameResidenceAsThePolicyholder", false},
            {"planSelected", "basic"}
        };
        var customFieldCondition = string.IsNullOrWhiteSpace(condition) ? null : new CustomFieldCondition(condition);

        // Act (When)
        var result = customFieldCondition?.Evaluate(contextFields);

        // Assert (Then)
        result.Should().Be(expected);
    }
}
