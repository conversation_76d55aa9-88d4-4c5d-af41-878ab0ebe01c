using CoverGo.BuildingBlocks.CustomFields.ExpressionParser;

namespace CoverGo.CustomFields.Tests.ExpressionParser;

public class LexerTests
{
    [Theory]
    [Trait("Ticket", "CH-31032")]
    [MemberData(nameof(TokenizeData))]
    public void TestTokenize(string input, Token[] expectedTokens)
    {
        // Arrange (Given)
        var lexer = new Lexer(input);

        // Act (When)
        var tokens = lexer.Tokenize();

        // Assert (Then)
        tokens.Should().BeEquivalentTo(expectedTokens);
    }
    public static TheoryData<string, Token[]> TokenizeData() => new()
    {
        { "'abc'", [ new(TokenType.String, "abc", 0), new(TokenType.EndOfFile, "", 5) ] },
        { "\"abc\"", [ new(TokenType.String, "abc", 0), new(TokenType.EndOfFile, "", 5) ] },
        { "123", [ new(TokenType.Number, "123", 0), new(TokenType.EndOfFile, "", 3) ] },
        { "true", [ new(TokenType.Boolean, "true", 0), new(TokenType.EndOfFile, "", 4) ] },
        { "false", [ new(TokenType.Boolean, "false", 0), new(TokenType.EndOfFile, "", 5) ] },

        { "a == '123'", [ new(TokenType.Identifier, "a", 0), new(TokenType.Equals, "==", 2), new(TokenType.String, "123", 5), new(TokenType.EndOfFile, "", 10) ] },
        { "a === '123'", [ new(TokenType.Identifier, "a", 0), new(TokenType.StrictEquals, "===", 2), new(TokenType.String, "123", 6), new(TokenType.EndOfFile, "", 11) ] },
        { "a != '123'", [ new(TokenType.Identifier, "a", 0), new(TokenType.NotEquals, "!=", 2), new(TokenType.String, "123", 5), new(TokenType.EndOfFile, "", 10) ] },
        {"a !== '123'", [ new(TokenType.Identifier, "a", 0), new(TokenType.NotStrictEquals, "!==", 2), new(TokenType.String, "123", 6), new(TokenType.EndOfFile, "", 11) ] },
        {"!a", [ new(TokenType.Not, "!", 0), new(TokenType.Identifier, "a", 1), new(TokenType.EndOfFile, "", 2) ] },
        {"a || b", [ new(TokenType.Identifier, "a", 0), new(TokenType.LogicalOr, "||", 2), new(TokenType.Identifier, "b", 5), new(TokenType.EndOfFile, "", 6) ] },
        {"a && b", [ new(TokenType.Identifier, "a", 0), new(TokenType.LogicalAnd, "&&", 2), new(TokenType.Identifier, "b", 5), new(TokenType.EndOfFile, "", 6) ] },
        {"a < b", [ new(TokenType.Identifier, "a", 0), new(TokenType.LessThan, "<", 2), new(TokenType.Identifier, "b", 4), new(TokenType.EndOfFile, "", 5) ] },
        {"a <= b", [ new(TokenType.Identifier, "a", 0), new(TokenType.LessThanOrEqual, "<=", 2), new(TokenType.Identifier, "b", 5), new(TokenType.EndOfFile, "", 6) ] },
        {"a > b", [ new(TokenType.Identifier, "a", 0), new(TokenType.GreaterThan, ">", 2), new(TokenType.Identifier, "b", 4), new(TokenType.EndOfFile, "", 5) ] },
        {"a >= b", [ new(TokenType.Identifier, "a", 0), new(TokenType.GreaterThanOrEqual, ">=", 2), new(TokenType.Identifier, "b", 5), new(TokenType.EndOfFile, "", 6) ] },

        {"(a)", [ new(TokenType.LeftParen, "(", 0), new(TokenType.Identifier, "a", 1), new(TokenType.RightParen, ")", 2), new(TokenType.EndOfFile, "", 3) ] },
        {"a.b", [ new(TokenType.Identifier, "a", 0), new(TokenType.Dot, ".", 1), new(TokenType.Identifier, "b", 2), new(TokenType.EndOfFile, "", 3) ] },
        {"$get(a).value", [ new(TokenType.Get, "$get", 0), new(TokenType.LeftParen, "(", 4), new(TokenType.Identifier, "a", 5), new(TokenType.RightParen, ")", 6), new(TokenType.Dot, ".", 7), new(TokenType.Value, "value", 8), new(TokenType.EndOfFile, "", 13) ] },
    };
}
