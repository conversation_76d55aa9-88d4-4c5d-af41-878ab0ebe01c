using CoverGo.BuildingBlocks.CustomFields.ExpressionParser;

namespace CoverGo.CustomFields.Tests.ExpressionParser;

public class ExpressionEvaluatorTests
{
    [Theory]
    [Trait("Ticket", "CH-31032")]
    [MemberData(nameof(EvaluateData))]
    public void TestEvaluate(string input, Dictionary<string, object?> context, bool expected)
    {
        // Arrange (Given)
        var expression = new Parser(new Lexer(input).Tokenize()).Parse();

        // Act (When)
        var result = new ExpressionEvaluator(context).Evaluate(expression);

        // Assert (Then)
        result.Should().Be(expected);
    }
    public static TheoryData<string, Dictionary<string, object?>, bool> EvaluateData => new()
    {
        { "true", [], true },
        { "false", [], false },
        { "$get(a).value", new(){ { "a", true } }, true },
        { "$get(a).value", new(){ { "a", false } }, false },
        { "1 == 1", [], true },
        { "1 == 2", [], false },
        { "1 != 2", [], true },
        { "1 != 1", [], false },
        { "$get(a).value === 1", new(){ { "a", 1 } }, true },
        { "$get(a).value === 2", new(){ { "a", 1 } }, false },
        { "$get(a).value == 1", new(){ { "a", 1 } }, true },
        { "$get(a).value == 2", new(){ { "a", 1 } }, false },
        { "$get(a).value !== 1", new(){ { "a", 1 } }, false },
        { "$get(a).value !== 1", new(){ { "a", 2 } }, true },
        { "$get(a).value != 1", new(){ { "a", 1 } }, false },
        { "$get(a).value != 1", new(){ { "a", 2 } }, true },
        { "$get(a).value < 1", new(){ { "a", 0 } }, true },
        { "$get(a).value <= 1", new(){ { "a", 1 } }, true },
        { "$get(a).value > 1", new(){ { "a", 2 } }, true },
        { "$get(a).value >= 1", new(){ { "a", 1 } }, true },
        { "$get(a).value < '1'", new(){ { "a", "0" } }, true },
        { "$get(a).value <= '1'", new(){ { "a", "1" } }, true },
        { "$get(a).value > '1'", new(){ { "a", "2" } }, true },
        { "$get(a).value >= '1'", new(){ { "a", "1" } }, true },
        { "true || false", [], true },
        { "false || false", [], false },
        { "true && false", [], false },
        { "true && true", [], true },
    };

}
