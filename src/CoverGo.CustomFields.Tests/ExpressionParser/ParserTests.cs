using CoverGo.BuildingBlocks.CustomFields.ExpressionParser;
using CoverGo.BuildingBlocks.CustomFields.ExpressionParser.Ast;

namespace CoverGo.CustomFields.Tests.ExpressionParser;

public class ParserTests
{
    [Theory]
    [Trait("Ticket", "CH-31032")]
    [MemberData(nameof(ParseData))]
    public void TestParse(string input, ExpressionNode expected)
    {
        // Arrange (Given)
        var parser = new Parser(new Lexer(input).Tokenize());

        // Act (When)
        var expression = parser.Parse();

        // Assert (Then)
        expression.Should().Be(expected);
    }
    public static TheoryData<string, ExpressionNode> ParseData() => new() {
        { "'123'", new LiteralNode("123") },
        { "\"123\"", new LiteralNode("123") },
        { "123", new LiteralNode(123) },
        { "true", new LiteralNode(true) },
        { "false", new LiteralNode(false) },
        { "a", new LiteralNode("a") },

        { "a == '123'", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.Equals, new LiteralNode("123")) },
        { "a === '123'", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.StrictEquals, new LiteralNode("123")) },
        { "a != '123'", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.NotEquals, new LiteralNode("123")) },
        { "a !== '123'", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.NotStrictEquals, new LiteralNode("123")) },
        { "a || b", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.LogicalOr, new LiteralNode("b")) },
        { "a && b", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.LogicalAnd, new LiteralNode("b")) },
        { "a < b", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.LessThan, new LiteralNode("b")) },
        { "a <= b", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.LessThanOrEqual, new LiteralNode("b")) },
        { "a > b", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.GreaterThan, new LiteralNode("b")) },
        { "a >= b", new BinaryExpressionNode(new LiteralNode("a"), BinaryOperator.GreaterThanOrEqual, new LiteralNode("b")) },
        { "$get(a).value", new FieldAccessNode("a") },
    };

}
