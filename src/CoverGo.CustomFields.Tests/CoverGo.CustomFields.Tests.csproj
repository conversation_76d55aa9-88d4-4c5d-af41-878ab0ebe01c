<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CoverGo.BuildingBlocks.Bootstrapper" />
    <PackageReference Include="CoverGo.BuildingBlocks.DataAccess.Mongo" />
    <PackageReference Include="CoverGo.BuildingBlocks.CustomFields" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\CoverGo.Quotation.Infrastructure\CoverGo.Quotation.Infrastructure.csproj" />
    <ProjectReference Include="..\CoverGo.Quotation.Infrastructure.MongoDbExtensions\CoverGo.Quotation.Infrastructure.MongoDbExtensions.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="FluentAssertions" />
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="testsettings.json" />
    <None Remove="testsettings.*.json" />
    <Content Include="testsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="testsettings.*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <DependentUpon>testsettings.json</DependentUpon>
    </Content>
  </ItemGroup>

</Project>
