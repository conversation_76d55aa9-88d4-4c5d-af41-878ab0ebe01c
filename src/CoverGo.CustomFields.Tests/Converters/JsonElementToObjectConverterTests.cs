using System.Text.Json;

using CoverGo.BuildingBlocks.CustomFields.Converters;

namespace CoverGo.CustomFields.Tests.Converters;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-10784")]
public sealed class JsonElementToObjectConverterTests
{
    [Fact]
    public void ConvertsNestedObjectsToDictionary()
    {
        // Arrange (Given)
        var jsonElement = JsonDocument.Parse("""
            {
                "Hello": "there",
                "There": {
                    "Value": "Hello"
                }
            }
            """).RootElement;

        // Act (When)
        var result = jsonElement.ToObject();

        // Assert (Then)
        result.Should().BeEquivalentTo(new Dictionary<string, object?>
        {
            ["Hello"] = "there",
            ["There"] = new Dictionary<string, object?>
            {
                ["Value"] = "Hello",
            },
        });
    }

    [Fact]
    public void ConvertsNestedArrayToList()
    {
        // Arrange (Given)
        var jsonElement = JsonDocument.Parse("""
            [
                "123",
                "345"
            ]
            """).RootElement;

        // Act (When)
        var result = jsonElement.ToObject();

        // Assert (Then)
        result.Should().BeEquivalentTo(new[]
        {
            "123",
            "345",
        });
    }
}
