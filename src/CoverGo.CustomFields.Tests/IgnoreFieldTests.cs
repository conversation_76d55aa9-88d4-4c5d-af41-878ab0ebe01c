using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-7749")]
public class IgnoreFieldTests
{
    public class IgnoreFieldEntity
    {
        public string? CoreField { get; set; }

        public CustomFields<IgnoreFieldEntity> Fields { get; set; } = [];
    }

    [Fact]
    public void IgnoredFieldsAreNotIncludedInValidations()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    [new RequiredValidator()])
            ]);
        var entity = new IgnoreFieldEntity()
        {
            CoreField = null,
            Fields = [],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            ignoreFields: ["notCoreField"]);

        // Assert (Then)
        errors.Should().BeEmpty();
    }

    [Fact]
    public void IgnoredFieldsMustNotExistInFields()
    {
        // Arrange (Given)
        var fieldDefinitions = new CustomFieldDefinitions(
            [
                new CustomFieldDefinition(
                    "notCoreField",
                    new DescriptionFieldType(),
                    [new RequiredValidator()])
            ]);
        var entity = new IgnoreFieldEntity()
        {
            CoreField = "123",
            Fields = [
                new CustomField<IgnoreFieldEntity>("notCoreField", "123")
            ],
        };

        // Act (When)
        var errors = fieldDefinitions.ValidateFields(
            entity.Fields,
            ignoreFields: ["notCoreField"]);

        // Assert (Then)
        errors.Should().BeEquivalentTo(new List<SchemaValidationError>()
        {
            new SchemaFieldValidationError("notCoreField", "No extra fields allowed")
        });
    }
}
