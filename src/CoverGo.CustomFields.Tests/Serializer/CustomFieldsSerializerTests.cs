using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Bootstrapper.Configuration;
using CoverGo.BuildingBlocks.DataAccess.Mongo;
using CoverGo.BuildingBlocks.DataAccess.Mongo.Configuration;
using CoverGo.BuildingBlocks.Domain.Core.Audit;
using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.BuildingBlocks.CustomFields.Serializers;
using CoverGo.CustomFields.Tests.Support;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Infrastructure.MongoDbExtensions;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;
using Moq;
using CoverGo.BuildingBlocks.CustomFields;

namespace CoverGo.CustomFields.Tests.Serializers;

[Trait("IntegrationType", "Database")]
[Trait("Category", "Integration")]
[Trait("Ticket", "CH-15910")]
public class CustomFieldsSerializerTests : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private string _collectionName;

    /// <summary>
    /// Current document.
    /// </summary>
    public record CustomFieldsSerializerTestsDocument : IAggregateRoot<Guid>
    {
        public Guid Id { get; init; } = Guid.NewGuid();

        public EntityAuditInfo EntityAuditInfo { get; set; } = default!;

        public CustomFields<CustomFieldsSerializerTestsDocument>? Fields { get; set; }

        public IReadOnlyCollection<IDomainEvent> DomainEvents => [];

        public void ClearDomainEvents() { }
    }

    /// <summary>
    /// A way to save document via an old way.
    /// </summary>
    public record CustomFieldsSerializerTestsDocumentWithArray : IAggregateRoot<Guid>
    {
        public Guid Id { get; init; } = Guid.NewGuid();

        public EntityAuditInfo EntityAuditInfo { get; set; } = default!;

        public IEnumerable<CustomField<CustomFieldsSerializerTestsDocument>>? Fields { get; set; }

        public IReadOnlyCollection<IDomainEvent> DomainEvents => [];

        public void ClearDomainEvents() { }
    }

    /// <summary>
    /// A way to get document via a new way.
    /// </summary>
    public record CustomFieldsSerializerTestsDocumentWithDictionary : IAggregateRoot<Guid>
    {
        public Guid Id { get; init; } = Guid.NewGuid();

        public EntityAuditInfo EntityAuditInfo { get; set; } = default!;

        public Dictionary<string, object>? Fields { get; set; }

        public IReadOnlyCollection<IDomainEvent> DomainEvents => [];

        public void ClearDomainEvents() { }
    }

    public CustomFieldsSerializerTests()
    {
        var configuration = TestConfigFactory.Create();
        var services = new ServiceCollection();
        var dbConfig = configuration.GetConfiguration<MongoDatabaseConfiguration>();
        BsonSerializerEx.TryRegisterGenericSerializerDefinition(typeof(CustomFields<>), typeof(CustomFieldsSerializer<>));
        services.AddLogging();
        services.AddMongoDb(options =>
        {
            options.ConnectionString = dbConfig.ConnectionString;
            options.DatabaseName = dbConfig.DatabaseName;
            options.UseTransactions = dbConfig.UseTransactions;
        });

        _collectionName = "custom-fields-serializers" + Guid.NewGuid().ToString();
        services.AddMongoRepository<CustomFieldsSerializerTestsDocument, Guid>(collectionName: _collectionName);
        services.AddMongoRepository<CustomFieldsSerializerTestsDocumentWithArray, Guid>(collectionName: _collectionName);
        services.AddMongoRepository<CustomFieldsSerializerTestsDocumentWithDictionary, Guid>(collectionName: _collectionName);
        services.AddSingleton(new TenantId("covergo"));
        services.AddSingleton(Mock.Of<IMediator>());
        services.AddSingleton<ITenantProvider>(new ValueTenantProvider(new("covergo")));
        var userContextProviderMock = new Mock<IUserContextProvider>();
        services.AddSingleton(userContextProviderMock.Object);

        var httpContextAccessor = new Mock<IHttpContextAccessor>();
        httpContextAccessor.Setup(x => x.HttpContext).Returns(new DefaultHttpContext());
        services.AddScoped<IHttpContextAccessor>(_ => httpContextAccessor.Object);

        _serviceProvider = services.BuildServiceProvider();
    }

    public static TheoryData<CustomFields<CustomFieldsSerializerTestsDocument>?> CustomFieldSamples =>
        new()
        {
            null,
            new CustomFields<CustomFieldsSerializerTestsDocument>([]),
            new CustomFields<CustomFieldsSerializerTestsDocument>([
                new("email", "<EMAIL>")]),
            new CustomFields<CustomFieldsSerializerTestsDocument>([
                new("email", "<EMAIL>"),
                new("number", 15.3M),
                new("bool", true),
                new("null", null),
                new("array", new object?[] { }),
                new("obj", new Dictionary<string, object?>
                {
                    ["N"] = 15,
                    ["B"] = true,
                    ["NU"] = null,
                    ["AR"] = new string[] { },
                    ["OBJ"] = new Dictionary<string, object?>()
                })]),
        };

    [Theory]
    [MemberData(nameof(CustomFieldSamples))]
    public async Task DeserializesFromArrayFormat(CustomFields<CustomFieldsSerializerTestsDocument>? customFields)
    {
        using var scope = _serviceProvider.CreateScope();
        var oldRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocumentWithArray, Guid>>();
        var newRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocument, Guid>>();

        // Arrange (Given)
        var oldDoc = new CustomFieldsSerializerTestsDocumentWithArray
        {
            Fields = customFields
        };
        await oldRepository.InsertAsync(oldDoc, default);

        // Act (When)

        var savedDoc = await newRepository.GetByIdAsync(oldDoc.Id, default);

        // Assert (Then)
        savedDoc.Fields!.Should().BeEquivalentTo(customFields);
    }

    [Theory]
    [MemberData(nameof(CustomFieldSamples))]
    public async Task DeserializesFromObjectFormat(CustomFields<CustomFieldsSerializerTestsDocument>? customFields)
    {
        using var scope = _serviceProvider.CreateScope();
        var newRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocument, Guid>>();

        // Arrange (Given)
        var newDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = customFields
        };
        await newRepository.InsertAsync(newDoc, default);

        // Act (When)
        var savedDoc = await newRepository.GetByIdAsync(newDoc.Id, default);

        // Assert (Then)
        savedDoc.Fields!.Should().BeEquivalentTo(customFields);
    }

    [Theory]
    [MemberData(nameof(CustomFieldSamples))]
    public async Task SerializesAsObjectFormat(CustomFields<CustomFieldsSerializerTestsDocument>? customFields)
    {
        using var scope = _serviceProvider.CreateScope();
        var newRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocument, Guid>>();
        var testRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocumentWithDictionary, Guid>>();

        // Arrange (Given)
        var newDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = customFields
        };
        await newRepository.InsertAsync(newDoc, default);

        // Act (When)
        var savedDoc = await testRepository.GetByIdAsync(newDoc.Id, default);

        // Assert (Then)
        savedDoc.Fields.Should().BeEquivalentTo(customFields);
    }

    [Fact]
    public async Task CustomFieldUniqueIndex()
    {
        // Arrange (Given)
        using var scope = _serviceProvider.CreateScope();
        var collection = _serviceProvider.GetRequiredService<IMongoCollection<CustomFieldsSerializerTestsDocument>>();
        await GivenUniqueIndex(collection);
        var newRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocument, Guid>>();
        var existingDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = [new CustomField<CustomFieldsSerializerTestsDocument>("email", "<EMAIL>")]
        };
        await newRepository.InsertAsync(existingDoc, default);

        // Act (When)
        var duplicateDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = [new CustomField<CustomFieldsSerializerTestsDocument>("email", "<EMAIL>")]
        };
        var action = async () => await newRepository.InsertAsync(duplicateDoc, default);

        // Assert (Then)

        (await action.Should().ThrowAsync<MongoWriteException>()).And.WriteError.Category.Should().Be(ServerErrorCategory.DuplicateKey);
    }

    [Fact]
    public async Task CustomFieldUniqueIndexNullValue()
    {
        // Arrange (Given)
        using var scope = _serviceProvider.CreateScope();
        var collection = _serviceProvider.GetRequiredService<IMongoCollection<CustomFieldsSerializerTestsDocument>>();
        await GivenUniqueIndex(collection);
        var newRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocument, Guid>>();
        var existingDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = [new CustomField<CustomFieldsSerializerTestsDocument>("email", null)]
        };
        await newRepository.InsertAsync(existingDoc, default);

        // Act (When)
        var duplicateDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = [new CustomField<CustomFieldsSerializerTestsDocument>("email", null)]
        };
        var action = async () => await newRepository.InsertAsync(duplicateDoc, default);

        // Assert (Then)
        await action.Invoke();
    }

    [Fact]
    public async Task CustomFieldUniqueIndexNoValue()
    {
        // Arrange (Given)
        using var scope = _serviceProvider.CreateScope();
        var collection = _serviceProvider.GetRequiredService<IMongoCollection<CustomFieldsSerializerTestsDocument>>();
        await GivenUniqueIndex(collection);
        var newRepository = _serviceProvider.GetRequiredService<IRepository<CustomFieldsSerializerTestsDocument, Guid>>();
        var existingDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = []
        };
        await newRepository.InsertAsync(existingDoc, default);

        // Act (When)
        var duplicateDoc = new CustomFieldsSerializerTestsDocument
        {
            Fields = []
        };
        var action = async () => await newRepository.InsertAsync(duplicateDoc, default);

        // Assert (Then)
        await action.Invoke();
    }

    public void Dispose()
    {
        using var scope = _serviceProvider.CreateScope();
        var db = _serviceProvider.GetRequiredService<IMongoDatabase>();
        db.DropCollection(_collectionName);
    }

    private static async Task GivenUniqueIndex(IMongoCollection<CustomFieldsSerializerTestsDocument> collection) => await collection.Indexes.CreateOneAsync(new CreateIndexModel<CustomFieldsSerializerTestsDocument>(
        Builders<CustomFieldsSerializerTestsDocument>.IndexKeys
            .Ascending("fields.email"),
        new CreateIndexOptions<CustomFieldsSerializerTestsDocument>()
        {
            Name = "FieldsEmail_Unique",
            Unique = true,
            PartialFilterExpression =
                Builders<CustomFieldsSerializerTestsDocument>.Filter.And(
                    Builders<CustomFieldsSerializerTestsDocument>.Filter.Exists("fields.email"),
                    Builders<CustomFieldsSerializerTestsDocument>.Filter.Type("fields.email", MongoDB.Bson.BsonType.String)),
        }));
}
