using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Types;
using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests.Validators;

[Trait("Category", "Unit")]
[Trait("Ticket", "CH-1644")]
[<PERSON>rai<PERSON>("Ticket", "CH-1646")]
public class ValidatorTests
{
    [Fact]
    public void ValidationsAreRunDuringValidateField()
    {
        // Arrange (Given)
        var type = new CustomFieldDefinition(
            "field",
            new TextFieldType() { Options = null },
            [new RequiredValidator()]);

        // Act (When)
        var result = type.ValidateField(null);

        // Assert (Then)
        result.Should().ContainSingle(it => it.Path == "field");
    }
}
