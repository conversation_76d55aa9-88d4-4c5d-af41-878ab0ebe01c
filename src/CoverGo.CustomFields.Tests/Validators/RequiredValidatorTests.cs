using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests.Validators;

[<PERSON>rai<PERSON>("Category", "Unit")]
[<PERSON>rai<PERSON>("Ticket", "CH-1644")]
[<PERSON>rai<PERSON>("Ticket", "CH-1646")]
public class RequiredValidatorTests
{
    [Theory]
    [InlineData("hello")]
    [InlineData(5)]
    public void ValidatePassedField(object? input)
    {
        // Arrange (Given)
        var validator = new RequiredValidator();

        // Act (When)
        var result = validator.ValidateField(input);

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public void ValidateNotPassedField(object? input)
    {
        // Arrange (Given)
        var validator = new RequiredValidator();

        // Act (When)
        var result = validator.ValidateField(input);

        // Assert (Then)
        result.Should().NotBeEmpty();
    }
}
