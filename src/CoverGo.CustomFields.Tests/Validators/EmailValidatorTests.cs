using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests.Validators;

[<PERSON>rai<PERSON>("Category", "Unit")]
[<PERSON>rai<PERSON>("Ticket", "CH-1644")]
[<PERSON>rai<PERSON>("Ticket", "CH-1646")]
public class EmailValidatorTests
{
    [Theory]
    [InlineData("<EMAIL>")]
    public void ValidateValidEmail(string emailInput)
    {
        // Arrange (Given)
        var validator = new EmailValidator();

        // Act (When)
        var result = validator.ValidateField(emailInput);

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData("emailgmail.com")]
    public void ValidateInvalidEmail(string emailInput)
    {
        // Arrange (Given)
        var validator = new EmailValidator();

        // Act (When)
        var result = validator.ValidateField(emailInput);

        // Assert (Then)
        result.Should().NotBeEmpty();
    }
}
