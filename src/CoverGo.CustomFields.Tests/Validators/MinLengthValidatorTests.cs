using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests.Validators;

[<PERSON>rai<PERSON>("Category", "Unit")]
[<PERSON>rai<PERSON>("Ticket", "CH-1644")]
[<PERSON>rai<PERSON>("Ticket", "CH-1646")]
public class MinLengthValidatorTests
{
    [Theory]
    [InlineData(1, "abc")]
    [InlineData(3, "abc")]
    [InlineData(10, null)]
    public void ValidateMoreThanMinLength(uint maxLength, string? input)
    {
        // Arrange (Given)
        var validator = new MinLengthValidator() { Amount = maxLength };

        // Act (When)
        var result = validator.ValidateField(input);

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(10, "abc")]
    [InlineData(10, "")]

    public void ValidateLessThanMinLength(uint maxLength, string? input)
    {
        // Arrange (Given)
        var validator = new MinLengthValidator() { Amount = maxLength };

        // Act (When)
        var result = validator.ValidateField(input);

        // Assert (Then)
        result.Should().NotBeEmpty();
    }
}
