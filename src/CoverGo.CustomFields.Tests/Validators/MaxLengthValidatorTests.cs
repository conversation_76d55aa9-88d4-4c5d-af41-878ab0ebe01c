using CoverGo.BuildingBlocks.CustomFields.Validators;

namespace CoverGo.CustomFields.Tests.Validators;

[<PERSON><PERSON><PERSON>("Category", "Unit")]
[<PERSON>rai<PERSON>("Ticket", "CH-1644")]
[<PERSON>rai<PERSON>("Ticket", "CH-1646")]
public class MaxLengthValidatorTests
{
    [Theory]
    [InlineData(10, "abc")]
    [InlineData(3, "abc")]
    [InlineData(10, "")]
    [InlineData(10, null)]
    public void ValidateLessThanMaxLength(uint maxLength, string? input)
    {
        // Arrange (Given)
        var validator = new MaxLengthValidator() { Amount = maxLength };

        // Act (When)
        var result = validator.ValidateField(input);

        // Assert (Then)
        result.Should().BeEmpty();
    }

    [Theory]
    [InlineData(1, "abc")]
    public void ValidateMoreThanMaxLength(uint maxLength, string? input)
    {
        // Arrange (Given)
        var validator = new MaxLengthValidator() { Amount = maxLength };

        // Act (When)
        var result = validator.ValidateField(input);

        // Assert (Then)
        result.Should().NotBeEmpty();
    }
}
