using CoverGo.Cases.Application.VersionBridge.IntegrationEvents;
using CoverGo.Cases.Application.VersionBridge.IntegrationEvents.Cases;
using CoverGo.Cases.Application.VersionBridge.Interfaces;
using CoverGo.Cases.Domain;
using CoverGo.Cases.Infrastructure.Adapters.Mongo;

namespace CoverGo.Cases.Infrastructure.Decorators.RepositoryDecorators;

public class MongoDbOfferRepositoryDecorator : MongoDbOfferRepository, IRepositoryDecorator<MongoDbOfferRepository>
{
    private readonly MongoDbOfferRepository _decorated;
    private readonly MessageSender _messageSender;
    public MongoDbOfferRepositoryDecorator(MongoDbOfferRepository decorated, MessageSender messageSender)
    {
        _decorated = decorated;
        _messageSender = messageSender;
    }
    
    public new Task<IEnumerable<Offer>> GetByProposalIdAsync(
        string tenantId,
        string proposalId,
        CancellationToken cancellationToken = default)
        => _decorated.GetByProposalIdAsync(tenantId, proposalId, cancellationToken);

    public new Task<IEnumerable<Offer>> GetAsync(
        string tenantId,
        OfferWhere where,
        CancellationToken cancellationToken = default)
        => _decorated.GetAsync(tenantId, where, cancellationToken);
    
    public new Task UpsertAsync(string tenantId, string id, Offer offer, CancellationToken cancellationToken)
    {
        Task result =_decorated.UpsertAsync(tenantId, id, offer, cancellationToken);
        
        _ = _messageSender.PublishEventAsync(new CaseOfferRepositoryEvent
        {
            Operation = RepositoryOperation.Upsert,
            UpsertedPayload = [offer]
        });
        
        return result;
    }

    public new Task DeleteAsync(string tenantId, string caseId, CancellationToken cancellationToken)
    {
        Task result = _decorated.DeleteAsync(tenantId, caseId, cancellationToken);
        
        _ = _messageSender.PublishEventAsync(new CaseOfferRepositoryEvent
        {
            Operation = RepositoryOperation.Delete,
            DeletedPayload = [caseId]
        });
        
        return result;
    }

    public MongoDbOfferRepository Original => _decorated;
}