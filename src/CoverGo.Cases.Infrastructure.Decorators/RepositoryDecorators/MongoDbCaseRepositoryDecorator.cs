﻿using CoverGo.Cases.Application.VersionBridge.IntegrationEvents;
using CoverGo.Cases.Application.VersionBridge.IntegrationEvents.Cases;
using CoverGo.Cases.Application.VersionBridge.Interfaces;
using CoverGo.Cases.Domain;
using CoverGo.DomainUtils;
using CoverGo.Cases.Infrastructure.Adapters.Mongo;
using Newtonsoft.Json.Linq;


namespace CoverGo.Cases.Infrastructure.Decorators.RepositoryDecorators;

public class MongoDbCaseRepositoryDecorator : MongoDbCaseRepository, IRepositoryDecorator<ICaseRepository>
{
    private readonly MongoDbCaseRepository _decorated;
    private readonly MessageSender _messageSender;

    public MongoDbCaseRepositoryDecorator(MongoDbCaseRepository decorated, MongoDbOfferRepository offerRepository, MessageSender messageSender) : base(offerRepository)
    {
        _decorated = decorated;
        _messageSender = messageSender;
    }
    public new Task<IEnumerable<Case>> GetAsync(
        string tenantId,
        CaseWhere where,
        OrderBy orderBy = null,
        int? skip = null,
        int? first = null,
        CancellationToken cancellationToken = default)
        => _decorated.GetAsync(tenantId, where, orderBy, skip, first, cancellationToken);

    public new Task<IEnumerable<JToken>> GetReportAsync(
        string tenantId,
        CaseWhere where,
        OrderBy orderBy = null,
        int? skip = null,
        int? first = null,
        CancellationToken cancellationToken = default)
        => _decorated.GetReportAsync(tenantId, where, orderBy, skip, first, cancellationToken);

    public new Task<IEnumerable<string>> GetIdsAsync(
        string tenantId,
        CaseWhere where,
        OrderBy orderBy,
        int? skip,
        int? first,
        CancellationToken cancellationToken = default)
        => _decorated.GetIdsAsync(tenantId, where, orderBy, skip, first, cancellationToken);

    public new IEnumerable<string> GetIds(string tenantId, CaseWhere where, OrderBy orderBy, int? skip, int? first)
        => _decorated.GetIds(tenantId, where, orderBy, skip, first);

    public new Task<IEnumerable<string>> GetProposalIdsAsync(
        string tenantId,
        CaseWhere where,
        OrderBy orderBy,
        int? skip,
        int? first,
        CancellationToken cancellationToken = default)
        => _decorated.GetProposalIdsAsync(tenantId, where, orderBy, skip, first, cancellationToken);

    public new Task<IEnumerable<string>> GetOfferIdsAsync(
        string tenantId,
        CaseWhere where,
        OrderBy orderBy,
        int? skip,
        int? first,
        CancellationToken cancellationToken = default)
        => _decorated.GetOfferIdsAsync(tenantId, where, orderBy, skip, first, cancellationToken);

    public new Task<long> GetTotalCountAsync(string tenantId, CaseWhere where, CancellationToken cancellationToken)
        => _decorated.GetTotalCountAsync(tenantId, where, cancellationToken);

    public override Task UpsertAsync(string tenantId, string id, Case @case, CancellationToken cancellationToken)
    {
        Task result = _decorated.UpsertAsync(tenantId, id, @case, cancellationToken);
        
        _ = _messageSender.PublishEventAsync(new CaseRepositoryEvent
        {
            Operation = RepositoryOperation.Upsert, 
            UpsertedPayload = [@case]
        });
        
        return result;
    }

    public override Task DeleteAsync(string tenantId, string id, CancellationToken cancellationToken)
    {
        Task result = _decorated.DeleteAsync(tenantId, id, cancellationToken);
        
        _ = _messageSender.PublishEventAsync(new CaseRepositoryEvent
        {
            Operation = RepositoryOperation.Delete,
            DeletedPayload = [id]
        });
        
        return result;
    }

    public ICaseRepository Original => _decorated;
}