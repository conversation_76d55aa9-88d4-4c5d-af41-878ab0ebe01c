using CoverGo.BuildingBlocks.MessageBus.Abstractions;
using CoverGo.BuildingBlocks.MessageBus.Contracts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CoverGo.Cases.Infrastructure.Decorators;

public class MessageSender
{
    private readonly IBus _bus;
    private readonly ILogger<MessageSender> _logger;

    public MessageSender(IBus bus, ILogger<MessageSender> logger)
    {
        _bus = bus;
        _logger = logger;
    }
    public async Task PublishEventAsync<TEvent>(TEvent @event) where TEvent : IntegrationEvent
    {
        try
        {
            _logger.LogInformation("Publishing integration event {EventType} with ID {EventId}: {@Event}",
                typeof(TEvent).Name, @event.MessageId, @event);
            
            await _bus.PublishEventAsync(@event,
                new SendOptions
                {
                    SkipInterceptors = true,
                    SkipOutbox = true
                }
            );
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error publishing integration event {EventType} with ID {EventId}: {@Event}",
                typeof(TEvent).Name, @event.MessageId, @event);
            throw;
        }
    }
}