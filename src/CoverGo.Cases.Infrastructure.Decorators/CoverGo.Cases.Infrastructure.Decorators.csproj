<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\CoverGo.Cases.Application.VersionBridge\CoverGo.Cases.Application.VersionBridge.csproj" />
      <ProjectReference Include="..\CoverGo.Cases.Domain\CoverGo.Cases.Domain.csproj" />
      <ProjectReference Include="..\CoverGo.Cases.Infrasctructure\CoverGo.Cases.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="CoverGo.BuildingBlocks.MessageBus.Dapr" />
    </ItemGroup>

</Project>
