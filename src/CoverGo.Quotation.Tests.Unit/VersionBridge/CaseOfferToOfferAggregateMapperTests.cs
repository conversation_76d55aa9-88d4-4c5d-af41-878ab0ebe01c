using CoverGo.Cases.Domain;
using CoverGo.Quotation.Application.VersionBridge.Mappers;
using CoverGo.Quotation.Domain.Offers;
using FluentAssertions;
using Xunit;

namespace CoverGo.Quotation.Tests.Unit.VersionBridge;

public class CaseOfferToOfferAggregateMapperTests
{
    private readonly CaseOfferToOfferAggregateMapper _mapper = new();

    [Fact]
    public void ToOfferAggregate_WithValidCaseOffer_ShouldMapAllProperties()
    {
        // Arrange
        var caseOffer = new Offer
        {
            Id = "offer-123",
            CaseId = "case-456",
            Status = "Added",
            OfferNumber = "OFF-001",
            PolicyNumber = "POL-789",
            ProductId = new CoverGo.Cases.Domain.ProductId
            {
                Plan = "medical",
                Type = "gm",
                Version = "1.0"
            },
            StartDate = new DateTime(2024, 1, 1),
            EndDate = new DateTime(2024, 12, 31),
            Premium = new CoverGo.Cases.Domain.Premium
            {
                Amount = 1500.00m,
                CurrencyCode = CoverGo.Cases.Domain.CurrencyCode.USD,
                OriginalPrice = 1200.00m
            }
        };

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Value.Should().Be("offer-123");
        result.OpportunityId.Value.Should().Be("case-456");
        result.Status.Should().Be(OfferStatus.Draft);
        result.InternalCode.Should().Be("OFF-001");
        result.LegacyPolicyId!.Value.Should().Be("POL-789");
        result.LegacyOfferId!.Value.Should().Be("offer-123");
        result.ProductVersionId.ProductId.Plan.Should().Be("medical");
        result.ProductVersionId.ProductId.Type.Should().Be("gm");
        result.ProductVersionId.Version.Should().Be("1.0");
        result.PolicyDetails.Should().NotBeNull();
        result.PolicyDetails!.StartDate.Should().Be(new DateOnly(2024, 1, 1));
        result.PolicyDetails.EndDate.Should().Be(new DateOnly(2024, 12, 31));
        result.Pricing.Should().NotBeNull();
        result.Pricing!.Totals.Total.Amount.Should().Be(1500.00m);
        result.Pricing.Summary.Net.Amount.Should().Be(1200.00m);
    }

    [Theory]
    [InlineData("Added", OfferStatus.Draft)]
    [InlineData("Sent", OfferStatus.Issued)]
    [InlineData("Accepted", OfferStatus.Accepted)]
    [InlineData("Rejected", OfferStatus.Rejected)]
    [InlineData("Lapsed", OfferStatus.Expired)]
    [InlineData("UnderwriterRequested", OfferStatus.Draft)]
    [InlineData("UnderwriterInProgress", OfferStatus.Draft)]
    [InlineData("UnderwriterApproved", OfferStatus.Draft)]
    [InlineData("UnderwriterRejected", OfferStatus.Rejected)]
    [InlineData("NotProceeding", OfferStatus.Rejected)]
    [InlineData("DRAFT", OfferStatus.Draft)]
    [InlineData("ISSUED", OfferStatus.Issued)]
    [InlineData("ACCEPTED", OfferStatus.Accepted)]
    [InlineData("REJECTED", OfferStatus.Rejected)]
    [InlineData("EXPIRED", OfferStatus.Expired)]
    [InlineData("Expired", OfferStatus.Expired)]
    [InlineData("UnknownStatus", OfferStatus.Draft)] // Default fallback
    public void ToOfferAggregate_WithDifferentStatuses_ShouldMapCorrectly(string inputStatus, OfferStatus expectedStatus)
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Status = inputStatus;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Status.Should().Be(expectedStatus);
    }

    [Fact]
    public void ToOfferAggregate_WithNullId_ShouldReturnNull()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Id = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullCaseId_ShouldReturnNull()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.CaseId = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullProductId_ShouldReturnNull()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.ProductId = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullDates_ShouldHaveNullPolicyDetails()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.StartDate = null;
        caseOffer.EndDate = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.PolicyDetails.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullPolicyNumber_ShouldHaveNullLegacyPolicyId()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.PolicyNumber = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.LegacyPolicyId.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithPremium_ShouldMapPricingCorrectly()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Premium = new CoverGo.Cases.Domain.Premium
        {
            Amount = 1000.50m,
            CurrencyCode = CoverGo.Cases.Domain.CurrencyCode.USD,
            OriginalPrice = 900.00m,
            AppliedDiscounts = new List<Discount>
            {
                new Discount { OriginalPrice = 900.00m, NewPrice = 850.00m }
            },
            Loadings = new List<Loading>
            {
                new Loading { OriginalPrice = 850.00m, NewPrice = 900.00m }
            },
            AppliedTaxes = new List<Tax>
            {
                new Tax { OriginalPrice = 900.00m, NewPrice = 1000.50m }
            }
        };

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Pricing.Should().NotBeNull();

        // Verify Summary
        result.Pricing!.Summary.Net.Amount.Should().Be(900.00m);
        result.Pricing.Summary.Net.CurrencyCode.Value.Should().Be("USD");
        result.Pricing.Summary.Gross.Amount.Should().Be(900.00m); // Total - Taxes
        result.Pricing.Summary.Taxes.Amount.Should().Be(100.50m);
        result.Pricing.Summary.GrossWithTaxes!.Amount.Should().Be(1000.50m);
        result.Pricing.Summary.Discounts.Amount.Should().Be(50.00m); // Absolute value of discount
        result.Pricing.Summary.Loading.Amount.Should().Be(50.00m);

        // Verify Totals
        result.Pricing.Totals.Total.Amount.Should().Be(1000.50m);
        result.Pricing.Totals.Premiums.Amount.Should().Be(900.00m);
        result.Pricing.Totals.Taxes.Amount.Should().Be(100.50m);
        result.Pricing.Totals.Discounts.Amount.Should().Be(50.00m);
        result.Pricing.Totals.Loadings.Amount.Should().Be(50.00m);

        // Verify other properties
        result.Pricing.ModalFactor.Should().Be(1.0m);
        result.Pricing.Commissions.PrimaryAgentCommission.Amount.Should().Be(0m);
        result.Pricing.Commissions.SecondaryAgentCommission.Amount.Should().Be(0m);
    }

    [Fact]
    public void ToOfferAggregate_WithNullPremium_ShouldHaveNullPricing()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Premium = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Pricing.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithPremiumWithoutAmount_ShouldHaveNullPricing()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Premium = new CoverGo.Cases.Domain.Premium
        {
            Amount = null,
            CurrencyCode = CoverGo.Cases.Domain.CurrencyCode.USD
        };

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Pricing.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithPremiumWithoutCurrency_ShouldDefaultToUSD()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Premium = new CoverGo.Cases.Domain.Premium
        {
            Amount = 500.00m,
            CurrencyCode = null
        };

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Pricing.Should().NotBeNull();
        result.Pricing!.Summary.Net.CurrencyCode.Value.Should().Be("USD");
        result.Pricing.Totals.Total.CurrencyCode.Value.Should().Be("USD");
    }

    private static Offer CreateValidCaseOffer()
    {
        return new Offer
        {
            Id = "offer-123",
            CaseId = "case-456",
            Status = "Added",
            OfferNumber = "OFF-001",
            PolicyNumber = "POL-789",
            ProductId = new CoverGo.Cases.Domain.ProductId
            {
                Plan = "medical",
                Type = "gm",
                Version = "1.0"
            },
            StartDate = new DateTime(2024, 1, 1),
            EndDate = new DateTime(2024, 12, 31),
            Premium = new CoverGo.Cases.Domain.Premium
            {
                Amount = 1200.00m,
                CurrencyCode = CoverGo.Cases.Domain.CurrencyCode.USD,
                OriginalPrice = 1000.00m
            }
        };
    }
}
