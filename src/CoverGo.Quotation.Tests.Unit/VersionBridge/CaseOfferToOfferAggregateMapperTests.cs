using CoverGo.Cases.Domain;
using CoverGo.Quotation.Application.VersionBridge.Mappers;
using CoverGo.Quotation.Domain.Offers;
using FluentAssertions;
using Xunit;

namespace CoverGo.Quotation.Tests.Unit.VersionBridge;

public class CaseOfferToOfferAggregateMapperTests
{
    private readonly CaseOfferToOfferAggregateMapper _mapper = new();

    [Fact]
    public void ToOfferAggregate_WithValidCaseOffer_ShouldMapAllProperties()
    {
        // Arrange
        var caseOffer = new Offer
        {
            Id = "offer-123",
            CaseId = "case-456",
            Status = "Added",
            OfferNumber = "OFF-001",
            PolicyNumber = "POL-789",
            ProductId = new CoverGo.Cases.Domain.ProductId
            {
                Plan = "medical",
                Type = "gm",
                Version = "1.0"
            },
            StartDate = new DateTime(2024, 1, 1),
            EndDate = new DateTime(2024, 12, 31)
        };

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Value.Should().Be("offer-123");
        result.OpportunityId.Value.Should().Be("case-456");
        result.Status.Should().Be(OfferStatus.Draft);
        result.InternalCode.Should().Be("OFF-001");
        result.LegacyPolicyId!.Value.Should().Be("POL-789");
        result.LegacyOfferId!.Value.Should().Be("offer-123");
        result.ProductVersionId.ProductId.Plan.Should().Be("medical");
        result.ProductVersionId.ProductId.Type.Should().Be("gm");
        result.ProductVersionId.Version.Should().Be("1.0");
        result.PolicyDetails.Should().NotBeNull();
        result.PolicyDetails!.StartDate.Should().Be(new DateOnly(2024, 1, 1));
        result.PolicyDetails.EndDate.Should().Be(new DateOnly(2024, 12, 31));
    }

    [Theory]
    [InlineData("Added", OfferStatus.Draft)]
    [InlineData("Sent", OfferStatus.Issued)]
    [InlineData("Accepted", OfferStatus.Accepted)]
    [InlineData("Rejected", OfferStatus.Rejected)]
    [InlineData("Lapsed", OfferStatus.Expired)]
    [InlineData("UnderwriterRequested", OfferStatus.Draft)]
    [InlineData("UnderwriterInProgress", OfferStatus.Draft)]
    [InlineData("UnderwriterApproved", OfferStatus.Draft)]
    [InlineData("UnderwriterRejected", OfferStatus.Rejected)]
    [InlineData("NotProceeding", OfferStatus.Rejected)]
    [InlineData("DRAFT", OfferStatus.Draft)]
    [InlineData("ISSUED", OfferStatus.Issued)]
    [InlineData("ACCEPTED", OfferStatus.Accepted)]
    [InlineData("REJECTED", OfferStatus.Rejected)]
    [InlineData("EXPIRED", OfferStatus.Expired)]
    [InlineData("Expired", OfferStatus.Expired)]
    [InlineData("UnknownStatus", OfferStatus.Draft)] // Default fallback
    public void ToOfferAggregate_WithDifferentStatuses_ShouldMapCorrectly(string inputStatus, OfferStatus expectedStatus)
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Status = inputStatus;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.Status.Should().Be(expectedStatus);
    }

    [Fact]
    public void ToOfferAggregate_WithNullId_ShouldReturnNull()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.Id = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullCaseId_ShouldReturnNull()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.CaseId = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullProductId_ShouldReturnNull()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.ProductId = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullDates_ShouldHaveNullPolicyDetails()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.StartDate = null;
        caseOffer.EndDate = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.PolicyDetails.Should().BeNull();
    }

    [Fact]
    public void ToOfferAggregate_WithNullPolicyNumber_ShouldHaveNullLegacyPolicyId()
    {
        // Arrange
        var caseOffer = CreateValidCaseOffer();
        caseOffer.PolicyNumber = null;

        // Act
        var result = _mapper.ToOfferAggregate(caseOffer);

        // Assert
        result.Should().NotBeNull();
        result!.LegacyPolicyId.Should().BeNull();
    }

    private static Offer CreateValidCaseOffer()
    {
        return new Offer
        {
            Id = "offer-123",
            CaseId = "case-456",
            Status = "Added",
            OfferNumber = "OFF-001",
            PolicyNumber = "POL-789",
            ProductId = new CoverGo.Cases.Domain.ProductId
            {
                Plan = "medical",
                Type = "gm",
                Version = "1.0"
            },
            StartDate = new DateTime(2024, 1, 1),
            EndDate = new DateTime(2024, 12, 31)
        };
    }
}
