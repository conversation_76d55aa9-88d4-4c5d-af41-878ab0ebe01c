using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Members.Validators;

namespace CoverGo.Quotation.Application.OfferMembers.Validators;

public interface IOfferMemberValidator : IMemberValidator
{
}

public class OfferMemberValidator : MemberValidator, IOfferMemberValidator
{
    public new void ValidateFields(IEnumerable<CustomField> fields)
    {
        MemberValidator.ValidateFields(fields);
    }
}
