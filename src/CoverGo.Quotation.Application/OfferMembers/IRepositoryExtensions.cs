using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.OfferMembers;

public static class IRepositoryExtensions
{
    public static Task<IEnumerable<OfferMemberAggregate>> FindByOffer(this IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> repository, ValueObjectId<OfferAggregate> offerId, CancellationToken cancellationToken) => repository.FindAllByAsync(x => x.OfferId == offerId && x.EntityAuditInfo.DeletedAt == null, cancellationToken);
    public static async Task<ProposalUnderwritingCase?> FindByProposal(this IRepository<ProposalUnderwritingCase, string> repository, ValueObjectId<ProposalAggregate> proposalId, CancellationToken cancellationToken) => (await repository.FindAllByAsync(uw => uw.ProposalId == proposalId, cancellationToken)).FirstOrDefault();
    public static async Task<ProposalAggregate?> FindByPolicy(this IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> repository, string policyId, CancellationToken cancellationToken) => (await repository.FindAllByAsync(x => x.Policies != null && x.Policies.Any(p => p.Id == policyId), cancellationToken)).FirstOrDefault();
    public static async Task<T?> FindById<T>(this IRepository<T, ValueObjectId<T>> repository, ValueObjectId<T> id, CancellationToken cancellationToken) where T : AggregateBaseGenericId<ValueObjectId<T>> => (await repository.FindAllByAsync(x => x.Id == id, cancellationToken)).FirstOrDefault();
}
