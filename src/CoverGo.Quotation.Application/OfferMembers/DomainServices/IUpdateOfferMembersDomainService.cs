using CoverGo.Quotation.Application.OfferMembers.Contracts;

namespace CoverGo.Quotation.Application.OfferMembers.DomainServices;

public interface IUpdateOfferMembersDomainService
{
    /// <summary>
    /// Loads the offer, members, and updates them with the provided inputs
    /// </summary>
    Task<UpdateOfferMembersResult> UpdateOfferMembers(
        string offerId,
        IReadOnlyList<OfferMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default);
}
