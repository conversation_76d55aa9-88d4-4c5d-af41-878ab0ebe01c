using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.OfferMembers.DomainServices;

/// <summary>
/// Repository for accessing product information related to benefit mirroring
/// </summary>
public interface IBenefitMirroringProductRepository
{
    /// <summary>
    /// Checks if the product has benefit mirroring enabled
    /// </summary>
    Task<bool> CheckProductHasBenefitMirroring(ProductVersionId productVersionId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets the benefit field names from the product's pricing script
    /// </summary>
    Task<HashSet<string>> GetProductBenefitFields(ProductVersionId productVersionId, CancellationToken cancellationToken = default);
} 