using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.OfferMembers.DomainServices;

public class UpdateOfferMembersResult
{
    public required OfferAggregate Offer { get; init; }
    public required List<UpdatedMemberResult> UpdatedMembers { get; init; }

    public record UpdatedMemberResult(OfferMemberAggregate Member, OfferMemberAggregate? Primary);

    public List<MemberUpdated<OfferMemberAggregate>> ToMemberUpdatedEvents()
    {
        return [ ..UpdatedMembers.Select(updatedMember => new MemberUpdated<OfferMemberAggregate>
        {
            MemberHolder = Offer,
            UpdatedMember = updatedMember.Member,
            DependentOfValue = updatedMember.Primary
        })];
    }
}
