using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.OfferMembers.DomainServices;

public interface IBenefitMirroringDomainService
{
    /// <summary>
    /// Returns mirrored values from primary member if conditions are met (product supports it, and primary member exists).
    /// Returns the original values if conditions are not met.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    Task<(string? planId, CustomFields<OfferMemberAggregate> fields)> GetMirroredBenefitsFromPrimary(
        OfferAggregate offer,
        OfferMemberAggregate? primaryMember,
        string? requestedPlanId,
        CustomFields<OfferMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates all dependents of a primary member to have the same planId and benefit fields.
    /// Returns the list of updated dependent members.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    Task<List<OfferMemberAggregate>> UpdateAllDependentsFromPrimary(
        OfferAggregate offer,
        OfferMemberAggregate primaryMember,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets primary first, then returns mirrored values for a dependent member update.
    /// Returns the original values if conditions are not met.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    Task<(string? planId, CustomFields<OfferMemberAggregate> fields)> GetMirroredBenefitsForDependent(
        OfferAggregate offer,
        OfferMemberAggregate dependentMember,
        string? requestedPlanId,
        CustomFields<OfferMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Handles the entire bulk update process including both dependent mirroring and primary syncing.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    Task HandleBulkMemberUpdates(
        UpdateOfferMembersResult result,
        IReadOnlyList<OfferMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default);
}
