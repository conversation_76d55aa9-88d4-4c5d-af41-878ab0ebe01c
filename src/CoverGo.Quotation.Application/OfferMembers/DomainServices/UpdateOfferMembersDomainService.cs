using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.ProposalMembers;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.OfferMembers.DomainServices;

public class UpdateOfferMembersDomainService(
    IPlanIdValidator planIdValidator,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IMemberRepository<OfferMemberAggregate> memberRepository,
    IIndividualRepository individualRepository)
    : IUpdateOfferMembersDomainService
{
    /// <summary>
    /// Loads the offer, members, and updates them with the provided inputs
    /// </summary>
    public async Task<UpdateOfferMembersResult> UpdateOfferMembers(
        string offerId,
        IReadOnlyList<OfferMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default)
    {
        var offer = await offerRepository.GetByIdAsync(new ValueObjectId<OfferAggregate>(offerId), cancellationToken);
        var memberIds = memberUpdates.Select(m => m.Id).ToList();
        var membersList = await memberRepository.FindAllAsync(memberIds, cancellationToken);
        await offerRelationshipLoader.LoadPrimaryMemberIds(offer, cancellationToken);
        await offerRelationshipLoader.LoadProductVersion(offer, cancellationToken);

        var membersById = membersList.ToDictionary(m => m.Id, m => m);

        await planIdValidator.ValidatePlanIds(
            [.. memberUpdates
                .Where(it => it.PlanId != null)
                .Select(it => it.PlanId!.Value is null ? null : new PlanId(it.PlanId.Value))],
            offer.ProductVersionId,
            cancellationToken);

        // Track which members will need to be updated in the offer
        var membersToUpdate = memberUpdates
            .Select(update => !membersById.TryGetValue(update.Id, out var member) ? throw new EntityNotFoundException(update.Id) : member)
            .ToList();

        var updatedResults = new List<(OfferMemberAggregate Member, OfferMemberAggregate? Primary)>();
        foreach (var update in memberUpdates)
        {
            var member = membersToUpdate.First(m => m.Id.Equals(update.Id));

            var result = await UpdateSingleMember(
                member,
                update.ClassName,
                update.PlanId,
                update.DependentOf,
                update.Fields,
                update.IndividualId,
                cancellationToken);

            updatedResults.Add(result);
        }

        // Convert tuples to UpdatedMemberResult objects
        var updatedMemberResults = updatedResults.Select(
            r => new UpdateOfferMembersResult.UpdatedMemberResult(r.Member, r.Primary)).ToList();

        var updatedMembers = updatedResults.Select(m => m.Member).ToList();
        offer.UpdateMembers(updatedMembers);
        return new UpdateOfferMembersResult
        {
            Offer = offer,
            UpdatedMembers = updatedMemberResults
        };
    }

    private async Task<(OfferMemberAggregate UpdatedMember, OfferMemberAggregate? Primary)> UpdateSingleMember(
        OfferMemberAggregate offerMember,
        SettableOfNullable<string?>? className,
        SettableOfNullable<Id?>? planId,
        SettableOfNullable<Id?>? dependentOf,
        Settable<IReadOnlyList<FieldInput>>? fields,
        SettableOfNullable<Id?>? individualId,
        CancellationToken cancellationToken)
    {
        if (className != null)
        {
            offerMember.UpdateClass(className.Value ?? null);
        }

        OfferMemberAggregate? primary = null;

        if (dependentOf != null)
        {
            var newDependentOf = dependentOf.Value;
            if (newDependentOf is not null)
            {
                await memberRepository.LoadDependents(offerMember, cancellationToken);
                primary = await memberRepository.GetByIdAsync(newDependentOf.Value, cancellationToken);
                offerMember.SetDependentOf(primary);
            }
            else
            {
                offerMember.BecomePrimary();
            }
        }
        else if (offerMember.DependentOf is not null)
        {
            primary = await memberRepository.GetByIdAsync(offerMember.DependentOf, cancellationToken);
        }

        // Apply basic updates without benefit mirroring
        if (planId != null)
        {
            var newPlanId = planId.Value?.Value ?? null;
            offerMember.UpdatePlanId(newPlanId);
        }

        if (fields != null)
        {
            var customFields = fields.Value.ToCustomFields<OfferMemberAggregate>();
            offerMember.UpdateFields(customFields);
        }

        if (individualId != null)
        {
            string? newIndividualId = individualId.Value?.Value;

            // Validate the individual ID if provided
            if (newIndividualId is not null)
            {
                await individualRepository.GetByIdAsync(newIndividualId, cancellationToken);
            }

            offerMember.UpdateIndividualId(newIndividualId);
        }

        return (offerMember, primary);
    }
}
