using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.OfferMembers.DomainServices;

public class BenefitMirroringDomainService(
    IBenefitMirroringProductRepository benefitMirroringProductRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IClientRepository clientRepository,
    IMemberRepository<OfferMemberAggregate> memberRepository,
    ILogger<BenefitMirroringDomainService> logger) : IBenefitMirroringDomainService
{
    private async Task<bool> HasBenefitMirroringEnabledAsync(OfferAggregate offer, CancellationToken cancellationToken = default)
    {
        try
        {
            return await benefitMirroringProductRepository.CheckProductHasBenefitMirroring(
                offer.ProductVersionId,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to check benefit mirroring configuration for offer {OfferId}. Assuming disabled.",
                offer.Id);
            return false;
        }
    }

    private async Task<(string? PlanId, CustomFields<OfferMemberAggregate> Fields)> MirrorBenefitFields(
        OfferAggregate offer,
        OfferMemberAggregate primaryMember,
        string? requestedPlanId,
        CustomFields<OfferMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Load necessary relationships
            await offerRelationshipLoader.LoadOpportunity(offer, cancellationToken);

            // Get the client from the opportunity
            ClientAggregate? client = null;
            if (offer.Opportunity?.ClientId is not null)
            {
                client = await clientRepository.GetByIdAsync(new ValueObjectId<ClientAggregate>(offer.Opportunity.ClientId.Value), cancellationToken);
            }

            if (client is null)
            {
                logger.LogWarning("Cannot apply benefit mirroring for offer {OfferId}. Client not found.", offer.Id);
                return (requestedPlanId, requestedFields);
            }

            // Get benefit fields from product - repository handles parsing inputSchema metadata
            var benefitFields = await benefitMirroringProductRepository.GetProductBenefitFields(
                offer.ProductVersionId,
                cancellationToken);

            // Mirror plan ID from primary member
            var mirroredPlanId = primaryMember.PlanId?.Value ?? requestedPlanId;

            // Mirror benefit fields from primary member
            var mirroredFields = MirrorBenefitFieldsInternal(primaryMember, requestedFields, benefitFields);

            logger.LogInformation(
                "Applied benefit mirroring for offer {OfferId}. Primary member: {PrimaryMemberId}, Mirrored plan: {MirroredPlanId}, Benefit fields: {BenefitFields}",
                offer.Id,
                primaryMember.Id,
                mirroredPlanId,
                string.Join(", ", benefitFields));

            return (mirroredPlanId, mirroredFields);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to apply benefit mirroring for offer {OfferId}. Falling back to original values.",
                offer.Id);

            // Return original values if mirroring fails
            return (requestedPlanId, requestedFields);
        }
    }

    private static CustomFields<OfferMemberAggregate> MirrorBenefitFieldsInternal(
        OfferMemberAggregate primaryMember,
        CustomFields<OfferMemberAggregate> requestedFields,
        HashSet<string> benefitFields)
    {
        var mirroredFields = new List<CustomField<OfferMemberAggregate>>(requestedFields);

        // Mirror benefit fields from primary member
        foreach (var benefitFieldName in benefitFields)
        {
            var primaryField = primaryMember.Fields.FirstOrDefault(f => f.Key == benefitFieldName);
            if (primaryField is not null)
            {
                // Remove any existing field with the same name from requested fields
                mirroredFields.RemoveAll(f => f.Key == benefitFieldName);

                // Add the mirrored field
                mirroredFields.Add(new CustomField<OfferMemberAggregate>(
                    primaryField.Key,
                    primaryField.Value));
            }
        }

        return new CustomFields<OfferMemberAggregate>(mirroredFields);
    }

    /// <summary>
    /// Returns mirrored values from primary member if conditions are met (product supports it, and primary member exists).
    /// Returns the original values if conditions are not met.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    /// <param name="offer">The offer being processed</param>
    /// <param name="primaryMember">The primary member to mirror from (null if not a dependent)</param>
    /// <param name="requestedPlanId">The originally requested plan ID</param>
    /// <param name="requestedFields">The originally requested fields</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tuple of (final plan ID, final fields) after applying mirroring if applicable</returns>
    public async Task<(string? planId, CustomFields<OfferMemberAggregate> fields)> GetMirroredBenefitsFromPrimary(
        OfferAggregate offer,
        OfferMemberAggregate? primaryMember,
        string? requestedPlanId,
        CustomFields<OfferMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default)
    {
        // If no primary member, return original values (not a dependent)
        if (primaryMember is null)
        {
            return (requestedPlanId, requestedFields);
        }

        // Check if product supports benefit mirroring
        var productHasBenefitMirroring = await HasBenefitMirroringEnabledAsync(offer, cancellationToken);
        if (!productHasBenefitMirroring)
        {
            return (requestedPlanId, requestedFields);
        }

        // Apply benefit mirroring
        return await MirrorBenefitFields(
            offer,
            primaryMember,
            requestedPlanId,
            requestedFields,
            cancellationToken);
    }

    /// <summary>
    /// Updates all dependents of a primary member to have the same planId and benefit fields.
    /// Returns the list of updated dependent members.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    public async Task<List<OfferMemberAggregate>> UpdateAllDependentsFromPrimary(
        OfferAggregate offer,
        OfferMemberAggregate primaryMember,
        CancellationToken cancellationToken = default)
    {
        var updatedDependents = new List<OfferMemberAggregate>();

        // Check if benefit mirroring should be applied
        if (!await ShouldApplyBenefitMirroring(offer, primaryMember, cancellationToken))
        {
            return updatedDependents;
        }

        try
        {
            // Find all dependents of this primary member
            var dependents = await memberRepository.FindDependentsByPrimaryIdAsync(
                primaryMember.Id,
                cancellationToken);

            if (!dependents.Any())
            {
                return updatedDependents;
            }

            // Get benefit fields from product
            var benefitFields = await benefitMirroringProductRepository.GetProductBenefitFields(
                offer.ProductVersionId,
                cancellationToken);

            // Update each dependent
            foreach (var dependent in dependents)
            {
                // Mirror plan ID from primary
                var originalPlanId = dependent.PlanId?.Value;
                var mirroredPlanId = primaryMember.PlanId?.Value;

                if (originalPlanId != mirroredPlanId)
                {
                    dependent.UpdatePlanId(mirroredPlanId);
                }

                // Mirror benefit fields from primary
                var originalFields = dependent.Fields;
                var mirroredFields = MirrorBenefitFieldsInternal(primaryMember, originalFields, benefitFields);

                if (!AreFieldsEqual(originalFields, mirroredFields))
                {
                    dependent.UpdateFields(mirroredFields);
                }

                updatedDependents.Add(dependent);
            }

            logger.LogInformation(
                "Synced {DependentCount} dependents from primary member {PrimaryMemberId} for offer {OfferId}. Benefit fields: {BenefitFields}",
                dependents.Count,
                primaryMember.Id,
                offer.Id,
                string.Join(", ", benefitFields));
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to sync dependents from primary member {PrimaryMemberId} for offer {OfferId}. Continuing without sync.",
                primaryMember.Id,
                offer.Id);
        }

        return updatedDependents;
    }

    /// <summary>
    /// Gets primary first, then returns mirrored values for a dependent member update.
    /// Returns the original values if conditions are not met.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    public async Task<(string? planId, CustomFields<OfferMemberAggregate> fields)> GetMirroredBenefitsForDependent(
        OfferAggregate offer,
        OfferMemberAggregate dependentMember,
        string? requestedPlanId,
        CustomFields<OfferMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default)
    {
        // If not a dependent, return original values
        if (dependentMember.DependentOf is null)
        {
            return (requestedPlanId, requestedFields);
        }

        try
        {
            // Get the primary member
            var primaryMember = await memberRepository.GetByIdAsync(dependentMember.DependentOf, cancellationToken);

            // Apply benefit mirroring using the existing method
            return await GetMirroredBenefitsFromPrimary(
                offer,
                primaryMember,
                requestedPlanId,
                requestedFields,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to apply benefit mirroring for dependent member {DependentMemberId} for offer {OfferId}. Falling back to original values.",
                dependentMember.Id,
                offer.Id);

            // Return original values if mirroring fails
            return (requestedPlanId, requestedFields);
        }
    }

    /// <summary>
    /// Handles the entire bulk update process including both dependent mirroring and primary syncing.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    public async Task HandleBulkMemberUpdates(
        UpdateOfferMembersResult result,
        IReadOnlyList<OfferMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default)
    {
        var offer = result.Offer;

        // Check if product supports benefit mirroring - early return if not
        var productHasBenefitMirroring = await HasBenefitMirroringEnabledAsync(offer, cancellationToken);
        if (!productHasBenefitMirroring)
        {
            return;
        }

        var updatedMembersById = result.UpdatedMembers.ToDictionary(m => m.Member.Id, m => m.Member);
        var memberUpdatesByIdString = memberUpdates.ToDictionary(u => u.Id.Value, u => u);

        // Step 1: Apply benefit mirroring for dependent member updates
        await ApplyBenefitMirroringForDependentUpdates(
            result,
            updatedMembersById,
            memberUpdatesByIdString,
            offer,
            cancellationToken);

        // Step 2: Sync dependents from primary member updates
        var allSyncedDependents = await SyncDependentsFromPrimaryUpdates(
            result,
            memberUpdatesByIdString,
            offer,
            cancellationToken);

        // Step 3: Add synced dependents to the result if they weren't already updated
        await AddSyncedDependentsToResult(result, allSyncedDependents, cancellationToken);
    }

    private async Task ApplyBenefitMirroringForDependentUpdates(
        UpdateOfferMembersResult result,
        Dictionary<ValueObjectId<OfferMemberAggregate>, OfferMemberAggregate> updatedMembersById,
        Dictionary<string, OfferMemberUpdateInput> memberUpdatesByIdString,
        OfferAggregate offer,
        CancellationToken cancellationToken)
    {
        var dependentUpdates = result.UpdatedMembers
            .Where(m => m.Member.DependentOf is not null &&
                       memberUpdatesByIdString.ContainsKey(m.Member.Id.Value) &&
                       (memberUpdatesByIdString[m.Member.Id.Value].PlanId != null ||
                        memberUpdatesByIdString[m.Member.Id.Value].Fields != null))
            .ToList();

        foreach (var dependentUpdate in dependentUpdates)
        {
            var dependent = dependentUpdate.Member;
            var update = memberUpdatesByIdString[dependent.Id.Value];

            var requestedPlanId = update.PlanId?.Value?.Value;
            var requestedFields = update.Fields?.Value.ToCustomFields<OfferMemberAggregate>() ?? dependent.Fields;

            // Get the primary member
            var primaryMember = updatedMembersById.TryGetValue(dependent.DependentOf!, out var primary)
                ? primary
                : await memberRepository.GetByIdAsync(dependent.DependentOf!, cancellationToken);

            // Apply benefit mirroring
            var (mirroredPlanId, mirroredFields) = await GetMirroredBenefitsFromPrimary(
                offer,
                primaryMember,
                requestedPlanId,
                requestedFields,
                cancellationToken);

            // Update with mirrored values
            if (update.PlanId != null)
            {
                dependent.UpdatePlanId(mirroredPlanId);
            }

            if (update.Fields != null)
            {
                dependent.UpdateFields(mirroredFields);
            }
        }
    }

    private async Task<List<OfferMemberAggregate>> SyncDependentsFromPrimaryUpdates(
        UpdateOfferMembersResult result,
        Dictionary<string, OfferMemberUpdateInput> memberUpdatesByIdString,
        OfferAggregate offer,
        CancellationToken cancellationToken)
    {
        var primaryUpdates = result.UpdatedMembers
            .Where(m => m.Member.DependentOf is null &&
                       memberUpdatesByIdString.ContainsKey(m.Member.Id.Value) &&
                       (memberUpdatesByIdString[m.Member.Id.Value].PlanId != null ||
                        memberUpdatesByIdString[m.Member.Id.Value].Fields != null))
            .ToList();

        var allSyncedDependents = new List<OfferMemberAggregate>();
        foreach (var primaryUpdate in primaryUpdates)
        {
            var primaryMember = primaryUpdate.Member;
            var syncedDependents = await UpdateAllDependentsFromPrimary(
                offer,
                primaryMember,
                cancellationToken);

            allSyncedDependents.AddRange(syncedDependents);
        }

        return allSyncedDependents;
    }

    private async Task AddSyncedDependentsToResult(
        UpdateOfferMembersResult result,
        List<OfferMemberAggregate> allSyncedDependents,
        CancellationToken cancellationToken)
    {
        if (allSyncedDependents.Any())
        {
            foreach (var syncedDependent in allSyncedDependents)
            {
                if (!result.UpdatedMembers.Any(m => m.Member.Id == syncedDependent.Id))
                {
                    // Find the primary member for this dependent
                    var primaryMember = syncedDependent.DependentOf is not null
                        ? await memberRepository.GetByIdAsync(syncedDependent.DependentOf, cancellationToken)
                        : null;

                    result.UpdatedMembers.Add(new UpdateOfferMembersResult.UpdatedMemberResult(syncedDependent, primaryMember));
                }
            }
        }
    }

    private async Task<bool> ShouldApplyBenefitMirroring(
        OfferAggregate offer,
        OfferMemberAggregate? primaryMember,
        CancellationToken cancellationToken = default)
    {
        // If no primary member, don't apply
        if (primaryMember is null)
        {
            return false;
        }

        // Check if product supports benefit mirroring
        var productHasBenefitMirroring = await HasBenefitMirroringEnabledAsync(offer, cancellationToken);
        if (!productHasBenefitMirroring)
        {
            return false;
        }

        return true;
    }

    private static bool AreFieldsEqual(
        CustomFields<OfferMemberAggregate> fields1,
        CustomFields<OfferMemberAggregate> fields2)
    {
        if (fields1.Count != fields2.Count)
        {
            return false;
        }

        var fields1Dict = fields1.ToDictionary(f => f.Key, f => f.Value);
        var fields2Dict = fields2.ToDictionary(f => f.Key, f => f.Value);

        foreach (var (key, value) in fields1Dict)
        {
            if (!fields2Dict.TryGetValue(key, out var otherValue))
            {
                return false;
            }

            // Handle null values properly
            if (value is null && otherValue is null)
            {
                continue;
            }

            if (value is null || otherValue is null)
            {
                return false;
            }

            if (!value.Equals(otherValue))
            {
                return false;
            }
        }

        return true;
    }
}
