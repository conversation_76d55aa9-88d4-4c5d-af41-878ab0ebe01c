using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.HealthQuestionnaires;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.OfferMembers.Contracts;

public record OfferMember : IMember<CustomFieldDto<OfferMember>>
{
    public required AuditInfo AuditInfo { get; set; }
    public required Id Id { get; init; }
    public ValueObjectId<Member>? MemberId { get; init; }
    public Id? DependentOf { get; init; }
    public string? Class { get; init; }
    public string? PlanId { get; init; }
    public ValueObjectId<LegacyPolicyMember>? LegacyPolicyMemberId { get; init; }
    public ValueObjectId<HealthQuestionnaireResponse>? HealthQuestionnaireResponseId { get; init; }
    public MemberPricing? Pricing { get; init; }
    public required IEnumerable<CustomFieldDto<OfferMember>>? Fields { get; init; }
    public required Underwriting.Contracts.MemberUnderwriting MemberUnderwriting { get; init; }
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public IEnumerable<MemberDocument>? Documents { get; init; }
    public Id? DocumentFolderId { get; init; }
    public ValueObjectId<Individual>? IndividualId { get; init; }
}
