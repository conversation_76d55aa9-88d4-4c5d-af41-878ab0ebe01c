namespace CoverGo.Quotation.Application.OfferMembers.Contracts;
public record CensusSummary
{
    //Will be class, though leaving it generic to be configurable as per requirement.
    public required string GroupingField { get; init; }
    public required IEnumerable<GroupedEntry> GroupedEntries { get; init; }
    public required IEnumerable<GroupTotal> GroupTotals { get; init; }

}

public record GroupedEntry
{
    public required string GroupKey { get; init; }
    public required IEnumerable<SubGroupCount> SubGroupCounts { get; init; }
}

public record SubGroupCount
{
    public required string MemberSubGroup { get; init; }
    public required int Count { get; init; }
}

public record GroupTotal
{
    public required string GroupKey { get; init; }
    public required int TotalCount { get; init; }

}
