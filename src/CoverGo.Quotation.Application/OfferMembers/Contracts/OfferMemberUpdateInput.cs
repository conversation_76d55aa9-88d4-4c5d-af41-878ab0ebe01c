using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.OfferMembers.Contracts;

public record OfferMemberUpdateInput
{
    public required ValueObjectId<OfferMemberAggregate> Id { get; init; }
    public SettableOfNullable<Id?>? PlanId { get; init; }
    public SettableOfNullable<string?>? ClassName { get; init; }
    public SettableOfNullable<Id?>? DependentOf { get; init; }
    public Settable<IReadOnlyList<FieldInput>>? Fields { get; init; }
    public SettableOfNullable<Id?>? IndividualId { get; init; }
}

