using CoverGo.Quotation.Application.Common;

namespace CoverGo.Quotation.Application.OfferMembers.Contracts;

public record OfferMemberInput
{
    public Id? PlanId { get; init; }
    public string? ClassName { get; init; }
    public Id? DependentOf { get; init; }
    public Id? MemberId { get; init; }
    public Id? IndividualId { get; init; }
    private FieldsInput _fields = [];
    public IReadOnlyList<FieldInput> Fields { get => _fields; init => _fields = [.. value]; }

    public List<OfferMemberInput>? Dependents { get; init; }

    public Id? LegacyPolicyId { get; init; }
    public Id? LegacyPolicyMemberId { get; init; }
}
