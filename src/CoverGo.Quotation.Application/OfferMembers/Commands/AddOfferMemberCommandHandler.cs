using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Commands;
using CoverGo.Quotation.Application.Members.Helpers;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.ProposalMembers;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Underwriting;

using MediatR;

namespace CoverGo.Quotation.Application.OfferMembers.Commands;

public record AddOfferMemberCommand : IAddMemberCommand, ICommand<OfferMember>
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }

    public Id? PlanId { get; init; }
    public string? ClassName { get; init; }
    public Id? DependentOf { get; init; }
    public Id? MemberId { get; init; }
    public ValueObjectId<ClientAggregate>? IndividualId { get; init; }
    private FieldsInput _fields = [];
    public IReadOnlyList<FieldInput> Fields { get => _fields; init => _fields = [.. value]; }

    public Id? LegacyPolicyId { get; init; }
    public Id? LegacyPolicyMemberId { get; init; }
}

public class AddOfferMemberCommandHandler(
    IMapper mapper,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IPlanIdValidator planIdValidator,
    IIndividualRepository individualRepository,
    IAggregateLock aggregateLock,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId,
    IMediator mediator) : ICommandHandler<AddOfferMemberCommand, OfferMember>
{
    public async Task<OfferMember> Handle(AddOfferMemberCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);

        var customFields = request.Fields.ToCustomFields<OfferMemberAggregate>();

        var offer = await offerRepository.GetByIdAsync(request.OfferId.Value, cancellationToken);

        if (request.IndividualId is not null)
        {
            await individualRepository.GetByIdAsync(request.IndividualId.Value, cancellationToken);
        }
        // Use the helper to get the member ID with fallback to individual's internal code
        var memberId = await MemberIdHelper.GetMemberIdWithIndividualFallback(
            request.IndividualId?.Value,
            request.MemberId?.Value,
            individualRepository,
            cancellationToken);

        OfferMemberAggregate? primary = null;
        if (request.DependentOf is not null)
        {
            primary = await offerMemberRepository.GetByIdAsync(request.DependentOf.Value, cancellationToken);
        }

        // Initialize with requested values
        var planId = request.PlanId?.Value;
        var fields = customFields;

        // Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring if applicable
            (planId, fields) = await benefitMirroringDomainService.GetMirroredBenefitsFromPrimary(
                offer,
                primary,
                planId,
                fields,
                cancellationToken);
        }

        var offerMemberAggregate = new OfferMemberAggregate
        {
            OpportunityId = offer.OpportunityId,
            Id = Guid.NewGuid().ToString(),
            OfferId = offer.Id,
            MemberId = memberId,
            PlanId = planId,
            Class = request.ClassName,
            Fields = fields,
            LegacyPolicyId = request.LegacyPolicyId?.Value,
            LegacyPolicyMemberId = request.LegacyPolicyMemberId?.Value,
            MemberUnderwriting = MemberUnderwriting.Pending(),
            IndividualId = request.IndividualId?.Value,
            DocumentFolderId = new DocumentFolderId(Guid.NewGuid().ToString()),
        };

        await planIdValidator.ValidatePlanId(offerMemberAggregate.PlanId, offer.ProductVersionId, cancellationToken);
        
        if (primary is not null)
        {
            offerMemberAggregate.SetDependentOf(primary);
        }

        await offerRelationshipLoader.LoadPrimaryMemberIds(offer, cancellationToken);
        offer.AddMembers([offerMemberAggregate]);

        await offerMemberRepository.InsertAsync(offerMemberAggregate, cancellationToken);

        await mediator.Publish(
            new MemberAdded<OfferMemberAggregate>
            {
                AddedMember = offerMemberAggregate,
                DependentOfValue = primary,
                MemberHolder = offer,
            },
            cancellationToken);

        offerMemberAggregate = await offerMemberRepository.GetByIdAsync(offerMemberAggregate.Id, cancellationToken);

        return mapper.Map<OfferMember>(offerMemberAggregate);
    }
}
