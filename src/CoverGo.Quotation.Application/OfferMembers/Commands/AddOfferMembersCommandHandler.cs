using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Helpers;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.Validators;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Opportunities.Commands;
using CoverGo.Quotation.Application.ProposalMembers;
using CoverGo.Quotation.Application.Quotes.Extensions;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.HealthQuestionnaires;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Underwriting;
using MediatR;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;

namespace CoverGo.Quotation.Application.OfferMembers.Commands;

public interface IAddOfferMembersCommand
{
    Id OfferId { get; }
    IEnumerable<OfferMemberInput> OfferMembers { get; }
}

public record AddOfferMembersCommand(Id OfferId, IEnumerable<OfferMemberInput> OfferMembers) : ICommand<List<OfferMember>>, IAddOfferMembersCommand;

public record AddRenewalOfferMembersCommand(Id OfferId, IEnumerable<OfferMemberInput> OfferMembers) : ICommand<List<OfferMemberAggregate>>, IAddOfferMembersCommand;

public class AddOfferMembersCommandHandler(
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IPlanIdValidator planIdValidator,
    IOfferMemberValidator offerMemberValidator,
    IIndividualRepository individualRepository,
    IProductVersionRepository productVersionRepository,
    IBenefitDefinitionsRepository benefitDefinitionsRepository,
    IUserContextProvider userContext,
    IMediator mediator,
    IAggregateLock aggregateLock,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId,
    IMapper mapper) :
    ICommandHandler<AddOfferMembersCommand, List<OfferMember>>,
    ICommandHandler<AddRenewalOfferMembersCommand, List<OfferMemberAggregate>>
{
    public async Task<List<OfferMember>> Handle(AddOfferMembersCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);
        var membersBatch = await HandleInternal(request, cancellationToken: cancellationToken);
        await mediator.Publish(
            MembersAdded<OfferMemberAggregate>.For(membersBatch), cancellationToken);
        return mapper.Map<List<OfferMember>>(membersBatch);
    }

    public async Task<List<OfferMemberAggregate>> Handle(AddRenewalOfferMembersCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);
        var userId = userContext.GetUserId();
        return await HandleInternal(request, async (member, productVersion, _) =>
        {
            if (member is RenewalOfferMemberInput renewalMember)
            {
                var underwriting = MemberUnderwriting.Accepted();
                if (renewalMember.MemberUnderwriting is { } memberUnderwriting)
                {
                    List<Id> benefitBusinessIds = memberUnderwriting.BenefitsUnderwritings?.Select(u => u.ProductBenefitId).ToList() ?? [];
                    List<BenefitDefinition> benefitDefinitions = benefitBusinessIds.Any() ? await benefitDefinitionsRepository.GetByIdsAsync(benefitBusinessIds, _) : [];
                    underwriting = MemberUnderwriting.Accepted(memberUnderwriting.ToDomain(productVersion.Currency!, benefitDefinitions, userId!));
                }

                var healthQuestionnaireResponse = default(HealthQuestionnaireResponse);
                if (renewalMember.HealthQuestionnaireResponseId is Id { Value: string } id)
                {
                    healthQuestionnaireResponse = new HealthQuestionnaireResponse { Id = id.Value, Status = HealthQuestionnaireResponseStatus.Published };
                }

                return (underwriting, healthQuestionnaireResponse);
            }

            return (default, default);
        }, cancellationToken);
    }

    private async Task<List<OfferMemberAggregate>> HandleInternal(
        IAddOfferMembersCommand request,
        Func<OfferMemberInput, ProductVersion, CancellationToken, Task<(MemberUnderwriting? underwriting, HealthQuestionnaireResponse? healthQuestionnaireResponse)>>? moreInfo = default,
        CancellationToken cancellationToken = default)
    {
        var customFields = request.OfferMembers.SelectMany(m => m.Fields.ToCustomFields<OfferMemberAggregate>());
        offerMemberValidator.ValidateFields(customFields);
        var offer = await offerRepository.GetByIdAsync(request.OfferId.Value, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);
        var primaryOfferMembersIds = request.OfferMembers
            .Where(m => m.DependentOf != null)
            .Select(m => new ValueObjectId<OfferMemberAggregate>(m.DependentOf!.Value))
            .Distinct()
            .ToList();
        var primaryOfferMembers = await offerMemberRepository.GetAllByIdsAsync(primaryOfferMembersIds, cancellationToken);

        // Preload all individuals in a batch to avoid N+1 problem
        var individualIds = request.OfferMembers
            .Where(m => m.IndividualId != null)
            .Select(m => m.IndividualId!.Value)
            .Distinct()
            .ToHashSet();

        var _ = await individualRepository.GetByIdsAsync(individualIds, cancellationToken);

        var membersBatch = new List<OfferMemberAggregate>();
        foreach (var member in request.OfferMembers)
        {
            var (underwriting, healthQuestionnaireResponse) = default((MemberUnderwriting, HealthQuestionnaireResponse));
            if (moreInfo is not null)
            {
                (underwriting, healthQuestionnaireResponse) = await moreInfo.Invoke(member, productVersion, cancellationToken);
            }
            var offerMemberAggr = await CreateOfferMember(
                member,
                offer,
                member.DependentOf is null ? null : primaryOfferMembers.Single(it => it.Id.Value == member.DependentOf.Value),
                underwriting,
                healthQuestionnaireResponse,
                cancellationToken: cancellationToken);
            membersBatch.Add(offerMemberAggr);

            List<OfferMemberInput>? dependents = member is RenewalOfferMemberInput renewalMember ? renewalMember.Dependents?.OfType<OfferMemberInput>()?.ToList() : member.Dependents;
            if (member.DependentOf == null && dependents?.Count > 0)
            {
                foreach (var dependent in dependents)
                {
                    if (moreInfo is not null)
                    {
                        (underwriting, healthQuestionnaireResponse) = await moreInfo.Invoke(dependent, productVersion, cancellationToken);
                    }
                    var dependentMember = await CreateOfferMember(
                        dependent,
                        offer,
                        offerMemberAggr,
                        underwriting,
                        healthQuestionnaireResponse,
                        cancellationToken: cancellationToken);
                    membersBatch.Add(dependentMember);
                }
            }
        }

        var planIds = membersBatch.Select(it => it.PlanId).Distinct().ToList();
        foreach (var planId in planIds)
        {
            await planIdValidator.ValidatePlanId(planId, offer.ProductVersionId, cancellationToken);
        }

        await offerRelationshipLoader.LoadPrimaryMemberIds(offer, cancellationToken);
        offer.AddMembers(membersBatch);
        await offerMemberRepository.InsertBatchAsync(membersBatch, cancellationToken);
        foreach (var member in membersBatch)
        {
            await mediator.Publish(new OfferMemberAddedDomainEvent
            {
                OfferId = offer.Id.Value,
                OfferMemberId = member.Id.Value,
            }, cancellationToken);
        }

        return membersBatch;
    }

    private async Task<OfferMemberAggregate> CreateOfferMember(
        OfferMemberInput input,
        OfferAggregate offer,
        OfferMemberAggregate? parent,
        MemberUnderwriting? underwriting = default,
        HealthQuestionnaireResponse? healthQuestionnaireResponse = default,
        CancellationToken cancellationToken = default)
    {
        var memberId = await MemberIdHelper.GetMemberIdWithIndividualFallback(
            input.IndividualId?.Value,
            input.MemberId?.Value,
            individualRepository,
            cancellationToken);

        var customFields = input.Fields.ToCustomFields<OfferMemberAggregate>();
        var planId = input.PlanId?.Value;
        var fields = customFields;

        // Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring if applicable
            (planId, fields) = await benefitMirroringDomainService.GetMirroredBenefitsFromPrimary(
                offer,
                parent,
                planId,
                fields,
                cancellationToken);
        }

        var offerMember = new OfferMemberAggregate()
        {
            OpportunityId = offer.OpportunityId,
            Id = Guid.NewGuid().ToString(),
            OfferId = offer.Id,
            MemberId = memberId,
            IndividualId = input.IndividualId?.Value,
            PlanId = planId,
            Class = input.ClassName,
            Fields = fields,
            LegacyPolicyId = input.LegacyPolicyId?.Value,
            LegacyPolicyMemberId = input.LegacyPolicyMemberId?.Value,
            MemberUnderwriting = underwriting ?? MemberUnderwriting.Pending(),
            HealthQuestionnaireResponse = healthQuestionnaireResponse,
            DocumentFolderId = new DocumentFolderId(Guid.NewGuid().ToString()),
        };

        offerMember.SetDependentOf(parent);

        return offerMember;
    }
}
