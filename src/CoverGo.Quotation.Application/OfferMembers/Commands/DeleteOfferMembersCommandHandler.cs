using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;

using MediatR;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.OfferMembers.Commands;

public record DeleteOfferMembersCommand(ValueObjectId<OfferAggregate> OfferId, IEnumerable<Id> OfferMemberIds) : ICommand<List<OfferMember>>;

public class DeleteOfferMembersCommandHandler(
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepo,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IMediator mediator,
    IMapper mapper,
    IAggregateLock aggregateLock,
    ILogger<DeleteOfferMembersCommandHandler> logger) : ICommandHandler<DeleteOfferMembersCommand, List<OfferMember>>
{
    public async Task<List<OfferMember>> Handle(DeleteOfferMembersCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);
        logger.LogInformation("Deleting offer members {@OfferMemberIds} from offer with id {OfferId}", request.OfferMemberIds, request.OfferId.Value);
        OfferAggregate offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);

        var idsToMatch = request.OfferMemberIds.Select(x => new ValueObjectId<OfferMemberAggregate>(x)).ToList();

        var offerMembersToDelete = (await offerMemberRepo.FindAllByAsync(x => x.OfferId == offer.Id && (idsToMatch.Contains(x.Id) || idsToMatch.Contains(x.DependentOf!)), cancellationToken)).ToList();

        logger.LogInformation("Matched {Count} offer members to delete", offerMembersToDelete.Count);

        await offerRelationshipLoader.LoadPrimaryMemberIds(offer, cancellationToken);
        offer.DeleteMembers(offerMembersToDelete);
        await offerMemberRepo.DeleteBatchAsync(offerMembersToDelete.Select(x => x.Id).ToList(), cancellationToken);

        await mediator.Publish(
            MembersDeleted<OfferMemberAggregate>.For(offer, [.. offerMembersToDelete]),
            cancellationToken);

        return mapper.Map<List<OfferMember>>(offerMembersToDelete);
    }
}

