using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.OfferMembers;

namespace CoverGo.Quotation.Application.OfferMembers.Commands;

public record UpdateOfferMemberLegacyPolicyMemberIdCommand : ICommand<OfferMember>
{
    public required ValueObjectId<OfferMemberAggregate> OfferMemberId { get; init; }

    public required ValueObjectId<LegacyPolicyMember> LegacyPolicyMemberId { get; init; }

    public required ValueObjectId<LegacyPolicy> LegacyPolicyId { get; init; }
}

public class UpdateOfferMemberLegacyPolicyMemberIdCommandHandler(
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> repository,
    IMapper mapper) : ICommandHandler<UpdateOfferMemberLegacyPolicyMemberIdCommand, OfferMember>
{
    public async Task<OfferMember> Handle(UpdateOfferMemberLegacyPolicyMemberIdCommand request, CancellationToken cancellationToken)
    {
        var offerMember = await repository.GetByIdAsync(request.OfferMemberId, cancellationToken);
        offerMember.LegacyPolicyMemberId = request.LegacyPolicyMemberId;
        offerMember.LegacyPolicyId = request.LegacyPolicyId;
        offerMember = await repository.UpdateAsync(offerMember, cancellationToken);
        return mapper.Map<OfferMember>(offerMember);
    }
}
