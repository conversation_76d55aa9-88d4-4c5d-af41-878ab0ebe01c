using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.OfferMembers.Commands.Documents;

public record RemoveOfferMemberDocumentCommand: ICommand<OfferMember>
{
    public required ValueObjectId<OfferMemberAggregate> OfferMemberId { get; init; }
    public required string DocumentKey { get; init; }
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
}

public class RemoveOfferMemberDocumentCommandHandler(
    IMapper mapper,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IAggregateLock aggregateLock)
    : ICommandHandler<RemoveOfferMemberDocumentCommand, OfferMember>
{
    public async Task<OfferMember> Handle(RemoveOfferMemberDocumentCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.ToString(), cancellationToken);

        OfferMemberAggregate offerMember = await offerMemberRepository.GetByIdAsync(request.OfferMemberId, cancellationToken);
        offerMember.RemoveDocument(request.DocumentKey);
        var savedMember = await offerMemberRepository.UpdateAsync(offerMember, cancellationToken);

        return mapper.Map<OfferMember>(savedMember);
    }
}
