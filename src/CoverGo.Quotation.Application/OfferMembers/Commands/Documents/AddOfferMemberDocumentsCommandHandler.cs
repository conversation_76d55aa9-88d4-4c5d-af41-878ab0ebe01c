using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.OfferMembers.Commands.Documents;

public record AddOfferMemberDocumentsCommand: ICommand<OfferMember>
{
    public required ValueObjectId<OfferMemberAggregate> OfferMemberId { get; init; }
    public required IEnumerable<MemberDocumentInput> Documents { get; init; } = Enumerable.Empty<MemberDocumentInput>();
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public required Id DocumentFolderId { get; init; }
}

public class AddOfferMemberDocumentsCommandHandler(
    IMapper mapper,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IAggregateLock aggregateLock,
    IUserContextProvider userContextProvider) : ICommandHandler<AddOfferMemberDocumentsCommand, OfferMember>
{
    public async Task<OfferMember> Handle(AddOfferMemberDocumentsCommand request, CancellationToken cancellationToken)
    {
        var loginId = userContextProvider.GetUserId();
        ArgumentNullException.ThrowIfNullOrEmpty(loginId);
        IEnumerable<MemberDocument> docs = request.Documents.Select(t => t.ToDomain());

        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.ToString(), cancellationToken);

        OfferMemberAggregate offerMember = await offerMemberRepository.GetByIdAsync(request.OfferMemberId, cancellationToken);
        var documentFolderId = new DocumentFolderId(request.DocumentFolderId);
        if (offerMember.ShouldMigrateDocumentFolderId(documentFolderId))
        {
            offerMember.MigrateDocumentFolderId(documentFolderId);
        }

        offerMember.UploadDocuments(docs, loginId);
        var savedMember = await offerMemberRepository.UpdateAsync(offerMember, cancellationToken);

        return mapper.Map<OfferMember>(savedMember);
    }
}

