using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using MediatR;

namespace CoverGo.Quotation.Application.OfferMembers.Commands;

public record UpdateOfferMembersCommand(
    Id OfferId,
    IReadOnlyList<OfferMemberUpdateInput> Members) : ICommand<List<OfferMember>>;

public class UpdateOfferMembersCommandHandler(
    IMediator mediator,
    IMemberRepository<OfferMemberAggregate> offerMemberRepository,
    IUpdateOfferMembersDomainService updateOfferMembersDomainService,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IAggregateLock aggregateLock,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId,
    IMapper mapper)
    : ICommandHandler<UpdateOfferMembersCommand, List<OfferMember>>
{
    public async Task<List<OfferMember>> Handle(UpdateOfferMembersCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);

        // Step 1: Execute the basic updates through the domain service
        var result = await updateOfferMembersDomainService.UpdateOfferMembers(
            request.OfferId,
            request.Members,
            cancellationToken);

        // Step 2: Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring for all updated members
            await benefitMirroringDomainService.HandleBulkMemberUpdates(
                result,
                request.Members,
                cancellationToken);
        }

        // Step 3: Save all updated members
        await offerMemberRepository.UpdateBatchAsync([ ..result.UpdatedMembers.Select(it => it.Member)], cancellationToken);
        
        // Step 4: Publish events
        await mediator.Publish(
            new MembersUpdated<OfferMemberAggregate>
            {
                Updates = result.ToMemberUpdatedEvents(),
                MemberHolderId = request.OfferId.Value,
            },
            cancellationToken);
        
        // Publish individual domain events for each updated member
        foreach (var member in result.UpdatedMembers)
        {
            await mediator.Publish(new OfferMemberUpdatedDomainEvent
            {
                OfferId = request.OfferId.Value,
                OfferMemberId = member.Member.Id.Value,
            }, cancellationToken);
        }
        
        return [.. result.UpdatedMembers.Select(member => mapper.Map<OfferMember>(member.Member))];
    }
}
