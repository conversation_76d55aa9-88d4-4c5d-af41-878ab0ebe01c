using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Commands;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;
using CoverGo.Quotation.Application.OfferMembers.Events;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.SettableValues;

using MediatR;

namespace CoverGo.Quotation.Application.OfferMembers.Commands;

public record UpdateOfferMemberCommand : IUpdateMemberCommand<OfferMemberAggregate>, ICommand<OfferMember>
{
    public ValueObjectId<OfferAggregate>? OfferId { get; init; }
    public required ValueObjectId<OfferMemberAggregate> Id { get; init; }
    public SettableOfNullable<Id?>? IndividualId { get; init; }
    public SettableOfNullable<Id?>? PlanId { get; init; }
    public SettableOfNullable<string?>? ClassName { get; init; }
    public SettableOfNullable<Id?>? DependentOf { get; init; }
    public Settable<IReadOnlyList<FieldInput>>? Fields { get; init; }
}

// ReSharper disable once UnusedType.Global
public class UpdateOfferMemberCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IMemberRepository<OfferMemberAggregate> offerMemberRepository,
    IAggregateLock aggregateLock,
    IUpdateOfferMembersDomainService updateOfferMembersDomainService,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId)
    : ICommandHandler<UpdateOfferMemberCommand, OfferMember>
{
    public async Task<OfferMember> Handle(UpdateOfferMemberCommand request, CancellationToken cancellationToken)
    {
        var offerIdInputted = request.OfferId is { Value: string };
        if (offerIdInputted)
        {
            await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId!.Value, cancellationToken);
        }
        var offerMember = await offerMemberRepository.GetByIdAsync(request.Id.Value, cancellationToken);
        if (!offerIdInputted)
        {
            await aggregateLock.TakeLockAsync<OfferAggregate>(offerMember.OfferId.Value, cancellationToken);
        }

        var updateInput = new OfferMemberUpdateInput
        {
            Id = request.Id,
            PlanId = request.PlanId,
            ClassName = request.ClassName,
            DependentOf = request.DependentOf,
            Fields = request.Fields,
            IndividualId = request.IndividualId,
        };

        // Use the domain service to handle the basic update
        var result = await updateOfferMembersDomainService.UpdateOfferMembers(
            offerMember.OfferId.Value,
            [updateInput],
            cancellationToken);

        // Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring for all updated members
            await benefitMirroringDomainService.HandleBulkMemberUpdates(
                result,
                [updateInput],
                cancellationToken);
        }

        // Save all updated members (including those affected by benefit mirroring)
        await offerMemberRepository.UpdateBatchAsync(
            [.. result.UpdatedMembers.Select(it => it.Member)],
            cancellationToken);

        // Publish MemberUpdated events for all updated members
        var memberUpdatedEvents = result.ToMemberUpdatedEvents();
        foreach (var memberUpdatedEvent in memberUpdatedEvents)
        {
            await mediator.Publish(memberUpdatedEvent, cancellationToken);
        }

        // Publish event for benefit mirroring to handle additional updates
        await mediator.Publish(new OfferMemberUpdatedForBenefitMirroring(result), cancellationToken);

        // Find the originally requested member from the result
        var requestedMember = result.UpdatedMembers.FirstOrDefault(m => m.Member.Id == request.Id);
        if (requestedMember == null)
        {
            throw new InvalidOperationException($"Could not find updated member with ID {request.Id}");
        }

        return mapper.Map<OfferMember>(requestedMember.Member);
    }
}
