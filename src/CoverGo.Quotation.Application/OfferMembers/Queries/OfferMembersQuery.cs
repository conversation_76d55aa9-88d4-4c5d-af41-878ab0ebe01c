using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.OfferMembers.Queries;

public class OfferMembersWhere
{
    public IReadOnlyCollection<OfferMembersWhere>? Or { get; set; }
    public IReadOnlyCollection<OfferMembersWhere>? And { get; set; }
    public IdWhere<OfferMemberAggregate>? Id { get; set; }
    public IdWhere<Member>? MemberId { get; set; }
    public IdWhere<OpportunityAggregate>? OpportunityId { get; set; }
    public IdWhere<OfferAggregate>? OfferId { get; set; }
    public FieldsWhere? Fields { get; set; }
}

public class OfferMembersQuery : PagedQuery<OfferMembersWhere>, IQuery<PagedResult<OfferMember>> { }
