using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.OfferMembers;

public record LegacyPolicyMemberQuery(
    ValueObjectId<LegacyPolicy> LegacyPolicyId,
    ValueObjectId<LegacyPolicyMember> LegacyPolicyMemberId,
    ValueObjectId<OfferAggregate> OfferId) : IQuery<OfferMember>;
