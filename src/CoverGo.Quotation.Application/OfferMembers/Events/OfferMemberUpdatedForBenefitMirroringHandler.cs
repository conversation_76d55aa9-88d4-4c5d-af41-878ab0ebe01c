using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;
using CoverGo.Quotation.Domain.OfferMembers;
using MediatR;

namespace CoverGo.Quotation.Application.OfferMembers.Events;

/// <summary>
/// Handler for benefit mirroring logic when an offer member is updated
/// </summary>
public class OfferMemberUpdatedForBenefitMirroringHandler(
    IMemberRepository<OfferMemberAggregate> offerMemberRepository,
    IMediator mediator)
    : INotificationHandler<OfferMemberUpdatedForBenefitMirroring>
{
    public async Task Handle(OfferMemberUpdatedForBenefitMirroring notification, CancellationToken cancellationToken)
    {
        var result = notification.Result;
        
        // If there are multiple updated members (due to benefit mirroring), save the additional ones
        // The command handler already saved the originally requested member
        if (result.UpdatedMembers.Count > 1)
        {
            // Get all members that need to be saved (excluding the originally requested one)
            var additionalMembersToUpdate = result.UpdatedMembers
                .Skip(1) // Skip the first one which was the originally requested member
                .Select(m => m.Member)
                .ToList();

            if (additionalMembersToUpdate.Any())
            {
                // Save each additional member individually to ensure proper persistence
                foreach (var member in additionalMembersToUpdate)
                {
                    await offerMemberRepository.UpdateAsync(member, cancellationToken);
                }
            }
        }

        // Publish events for all updated members
        var memberUpdatedEvents = result.ToMemberUpdatedEvents();
        foreach (var memberUpdatedEvent in memberUpdatedEvents)
        {
            await mediator.Publish(memberUpdatedEvent, cancellationToken);
        }
    }
} 