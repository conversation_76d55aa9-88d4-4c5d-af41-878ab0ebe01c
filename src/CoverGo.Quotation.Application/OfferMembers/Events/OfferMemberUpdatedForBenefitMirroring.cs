using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;
using MediatR;

namespace CoverGo.Quotation.Application.OfferMembers.Events;

/// <summary>
/// Event published when an offer member is updated and benefit mirroring logic needs to be applied
/// </summary>
public record OfferMemberUpdatedForBenefitMirroring(UpdateOfferMembersResult Result) : INotification; 