using CoverGo.Quotation.Application.OfferMembers.Ports;
using CoverGo.Quotation.Application.Offers.Ports;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Quotation.Application.Offers;

public static class OfferApplicationDI
{
    public static IServiceCollection AddOffersApplication(
        this IServiceCollection services)
    {
        services.AddScoped<IOfferRelationshipLoader, OfferRelationshipLoader>();
        services.AddScoped<IOfferMemberRelationshipLoader, OfferMemberRelationshipLoader>();
        services.AddScoped<IOfferUnderwritingUpdater, OfferUnderwritingUpdater>();
        services.AddScoped<IOfferPricingUpdater, OfferPricingUpdater>();
        return services;
    }
}
