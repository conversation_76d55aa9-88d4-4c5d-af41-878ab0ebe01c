using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Pricing;
using CoverGo.Quotation.Application.ChannelManagement;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Offers;

public interface IOfferPricingUpdater
{
    Task UpdateOfferPricing(ValueObjectId<OfferAggregate> offerId, CancellationToken cancellationToken);
}

public class OfferPricingUpdater(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IMemberRepository<OfferMemberAggregate> offerMembersRepository,
    IPremiums premiums,
    IChannelManagementAdapter channelManagementAdapter,
    ILogger<OfferPricingUpdater> logger) : IOfferPricingUpdater
{
    public async Task UpdateOfferPricing(ValueObjectId<OfferAggregate> offerId, CancellationToken cancellationToken)
    {
        using var _ = logger.BeginScope(new Dictionary<string, object?>
        {
            { "OfferId", offerId.Value }
        });
        var offer = await offerRepository.GetByIdAsync(offerId, cancellationToken);
        await offerRelationshipLoader.LoadOpportunity(offer, cancellationToken);
        await offerRelationshipLoader.LoadProductVersion(offer, cancellationToken);
        await offerRelationshipLoader.LoadMembers(offer, cancellationToken);

        if (!offer.CanGetPremium())
        {
            logger.LogInformation("The offer {OfferId} does not have policy details yet, not-rejected members, or billing information, skipping pricing update. {@OfferPolicyDetails}. {@OfferBillingInformation}. {@OfferMembers}",
                offerId.Value, offer.PolicyDetails, offer.BillingInformation, offer.Members.Select(it => (it.DateOfBirth, it.MemberUnderwriting.Status, it.PlanId)).ToList());
            return;
        }
        var quote = OfferToQuoteMapper.BuildQuote(offer);

        var tCommission = channelManagementAdapter.GetCommissionPlan(
            offer.ProductVersionId,
            offer.Opportunity.DistributorId.Value,
            offer.PolicyDetails!.StartDate.ToDateTime(TimeOnly.MinValue),
            cancellationToken);

        var premiumResult = await premiums.CalculatePremiumFor(
            quote,
            new DistributorId(quote.DistributorId),
            quote.ProductVersion,
            OfferToQuoteMapper.BuildQuoteMembers(offer),
            cancellationToken);

        foreach (var (key, pricing) in premiumResult.Members.MemberPremiums)
            offer.Members.Single(it => it.Id.Value == key).Pricing = pricing;

        offer.UpdateCommissionPlan(await tCommission);
        await offerRepository.UpdateAsync(
            offer with
            {
                Pricing = premiumResult.QuotationPricing,
                BillingPlan = premiumResult.BillingPlan,
            }, cancellationToken);

        await offerMembersRepository.UpdateBatchAsync(offer.Members, cancellationToken);
    }
}
