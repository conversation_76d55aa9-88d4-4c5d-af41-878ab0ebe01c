using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.HealthQuestionnaires;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Underwriting.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.Offers;

public interface IUnderwritingRequestOfferDataCollector
{
    Task<UnderwritingRequest> For(ValueObjectId<OfferAggregate> offerId, CancellationToken ct = default);
}

public sealed class UnderwritingRequestOfferDataCollector(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IHealthQuestionnaireResponseRepository healthQuestionnaireRepository) : IUnderwritingRequestOfferDataCollector
{
    public async Task<UnderwritingRequest> For(ValueObjectId<OfferAggregate> offerId, CancellationToken ct = default)
    {
        var offer = await offerRepository.GetByIdAsync(offerId, ct);
        await offerRelationshipLoader.LoadMembers(offer, ct);
        await offerRelationshipLoader.LoadProductVersion(offer, ct);

        var request = new UnderwritingRequest
        {
            ProductVersion = offer.ProductVersion,
            PolicyStartDate = offer.PolicyDetails?.StartDate,
            PolicyEndDate = offer.PolicyDetails?.EndDate,
            PolicyFields = offer.PolicyDetails?.Fields.ToDictionary(x => x.Key, x => x.Value) ?? [],
            MembersWithQuestionnaireResponses = [],
        };
        if (!offer.Members.Any(x => (x as IUnderwritableMember).CanRunAutoUnderwriting())) return request;

        var hcQuestionnaireResponses = (await healthQuestionnaireRepository.GetByIdsAsync(
            offer.Members.Where(x => (x as IUnderwritableMember).ShouldSendHealthQuestionnaireResponse()).Select(x => x.HealthQuestionnaireResponse!.Id.Value).ToList()
            , ct))
            .Where(response => !response.IsDraft)
            .ToDictionary(response => response.Id);

        return request with
        {
            MembersWithQuestionnaireResponses = offer.Members
                .Select(x =>
                (
                    x as IUnderwritableMember,
                    x.HealthQuestionnaireResponse?.Id?.Value is string id ? hcQuestionnaireResponses.GetValueOrDefault(id) : null
                ))
                .ToList(),
        };
    }
}
