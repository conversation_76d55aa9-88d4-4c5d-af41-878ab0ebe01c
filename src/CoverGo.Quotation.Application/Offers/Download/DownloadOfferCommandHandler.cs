using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Common.PdfTemplates;

namespace CoverGo.Quotation.Application.Offers.Download;

public class DownloadOfferCommandHandler(
    IOfferRenderingService offerRenderingService,
    IPdfTemplateRenderer pdfTemplateRenderer
) : ICommandHandler<DownloadOfferCommand, byte[]>
{
    public async Task<byte[]> Handle(DownloadOfferCommand request, CancellationToken cancellationToken)
    {
        var contentJsonString = await offerRenderingService.GetDocumentData(request.OfferId, cancellationToken);
        return await pdfTemplateRenderer.Render(
            request.TemplateId ?? "PDF_POLICY_QUOTE_GM_V2",
            request.TemplateType ?? TemplateType.Wkhtmltopdf,
            contentJsonString,
            cancellationToken);
    }
}
