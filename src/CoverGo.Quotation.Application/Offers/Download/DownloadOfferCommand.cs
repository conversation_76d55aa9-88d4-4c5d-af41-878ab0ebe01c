using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Download;

public class DownloadOfferCommand : ICommand<byte[]>
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public string? TemplateId { get; init; }
    public TemplateType? TemplateType { get; init; }
}
