using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using Newtonsoft.Json.Linq;

namespace CoverGo.Quotation.Application.Offers.Download;

public interface IOfferRenderingService
{
    Task<string> GetDocumentData(
        ValueObjectId<OfferAggregate> offerId,
        CancellationToken cancellationToken
    );
    Task<JToken> GetDocumentJToken(
        ValueObjectId<OfferAggregate> offerId,
        CancellationToken cancellationToken
    );
}
