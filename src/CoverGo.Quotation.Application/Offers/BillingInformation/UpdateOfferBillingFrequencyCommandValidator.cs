﻿using FluentValidation;

namespace CoverGo.Quotation.Application.Offers.BillingInformation;

public class UpdateOfferBillingFrequencyCommandValidator : AbstractValidator<UpdateOfferBillingFrequencyCommand>
{
    public UpdateOfferBillingFrequencyCommandValidator(IBillingConfigurationRepository billingFrequencyRepository)
    {
        RuleFor(x => x.BillingFrequency).MustAsync(async (x, ct) =>
        {
            var billingFrequencies = billingFrequencyRepository.GetBillingFrequencies(ct);

            return (await billingFrequencies).Contains(x);
        }).WithMessage("Invalid billing frequency");
    }
}
