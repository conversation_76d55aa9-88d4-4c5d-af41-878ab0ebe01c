using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.Billing;

using MediatR;

namespace CoverGo.Quotation.Application.Offers.BillingInformation;

public record UpdateOfferBillingFrequencyCommand(ValueObjectId<OfferAggregate> OfferId, string BillingFrequency, string? BillingPricingDateBasis) : ICommand<Offer>;

public class UpdateOfferBillingFrequencyCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IMediator mediator,
    IAggregateLock aggregateLock,
    IMapper mapper) : ICommandHandler<UpdateOfferBillingFrequencyCommand, Offer>
{
    public async Task<Offer> Handle(UpdateOfferBillingFrequencyCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);

        var offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);

        if (offer is null)
            throw new OfferNotFoundException("Offer not found");

        offer.UpdateBillingFrequency(request.BillingFrequency);
        if(!string.IsNullOrWhiteSpace(request.BillingPricingDateBasis))
        {
            offer.UpdateBillingYearPriceDateBasis(request.BillingPricingDateBasis);
        }
        await offerRepository.UpdateAsync(offer, cancellationToken);

        await mediator.Publish(new OfferBillingFrequencyUpdated(offer), cancellationToken);

        offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);

        return mapper.Map<Offer>(offer);
    }
}
