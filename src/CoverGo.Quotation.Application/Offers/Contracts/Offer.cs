using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Pricing.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Pricing;
using CoverGo.Quotation.Domain.ChannelManagement;
using InsuredGroup = CoverGo.Quotation.Application.Offers.Headcount.InsuredGroup;

namespace CoverGo.Quotation.Application.Offers.Contracts;

public class Offer
{
    public required ValueObjectId<OfferAggregate> Id { get; init; }
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public Id? LegacyOfferId { get; init; }
    public ValueObjectId<LegacyPolicy>? LegacyPolicyId { get; init; }
    public required BillingInfo BillingInfo { get; init; }
    public QuotationPricing? Pricing { get; init; }
    public BillingPlan? BillingPlan { get; init; }
    public required IList<IBenefitClass> Classes { get; init; }
    public required ProductVersionId ProductVersionId { get; init; }
    public PolicyDetails? PolicyDetails { get; init; }
    public required AuditInfo AuditInfo { get; init; }
    public required OfferStatus Status { get; init; }
    public List<InsuredGroup>? InsuredGroups { get; init; }
    public DateTime? ExpirationDate { get; init; }
    public OfferVersion? Version { get; init; }
    public string? InternalCode { get; init; }
    public string? RejectedReason { get; init; }
    public IReadOnlyList<Tax>? TaxOverrides { get; init; }
    public IReadOnlyList<QuotationRiskCarrierContract>? RiskCarrierContracts { get; init; }
    public QuotationCommissionPlan? CommissionPlan { get; init; }
}
