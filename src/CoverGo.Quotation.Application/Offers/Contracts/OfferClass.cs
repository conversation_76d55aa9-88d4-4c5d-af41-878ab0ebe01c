using System.Text.Json;

using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Contracts;

public interface IBenefitClass
{
    string Name { get; }
    BenefitSelection? BenefitSelection { get; }
}

public record OfferClass(string Name, BenefitSelection? BenefitSelection) : IBenefitClass
{
    public static OfferClass FromDomain(Domain.Offers.IBenefitClass benefitClass)
    {
        return new OfferClass(
            benefitClass.Name,
            benefitClass.BenefitSelection != null
                ? new(
                benefitClass.BenefitSelection.PlanId,
                benefitClass.BenefitSelection.PlanFields
                    .Select(it => new CustomFieldDto<Plan>(
                        it.Key,
                        JsonDocument.Parse(JsonSerializer.Serialize(it.Value)).RootElement))
                    .ToList())
                : null);
    }
}

public record ProposalClass(string Name, BenefitSelection? BenefitSelection) : IBenefitClass
{
    public static ProposalClass FromDomain(Domain.Offers.IBenefitClass benefitClass)
    {
        return new ProposalClass(
            benefitClass.Name,
            benefitClass.BenefitSelection != null
                ? new(
                benefitClass.BenefitSelection.PlanId,
                benefitClass.BenefitSelection.PlanFields
                    .Select(it => new CustomFieldDto<Plan>(
                        it.Key,
                        JsonDocument.Parse(JsonSerializer.Serialize(it.Value)).RootElement))
                    .ToList())
                : null);
    }
}
