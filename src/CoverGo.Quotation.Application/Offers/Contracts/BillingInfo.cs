using CoverGo.Quotation.Application.Common;

namespace CoverGo.Quotation.Application.Offers.Contracts;

public record LocalizedString
{
    //mnemonic
    public required string Value { get; set; }
}

public enum BillingYearMode
{
    CalendarYear, // Set up if PolicyDate end date is 31 Dec.
    PolicyYear
}
public record BillingInfo
{
    public required LocalizedString? BillingFrequency { get; init; }
    public required LocalizedString? BillingPricingDateBasis { get; init; }
    public required BillingYearMode? BillingYearMode { get; init; }
    public required Id? PayorId { get; init; }   
}
