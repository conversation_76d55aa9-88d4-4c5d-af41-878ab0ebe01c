using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Underwriting.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Underwriting;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Offers;

public interface IOfferUnderwritingUpdater
{
    Task UpdateUnderwriting(ValueObjectId<OfferAggregate> offerId, CancellationToken ct);
    Task UpdateUnderwritingHealthQuestionnaire(ValueObjectId<OfferAggregate> offerId, CancellationToken ct);
}

public class OfferUnderwritingUpdater(IUnderwritingRequestOfferDataCollector dataCollector, IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository, IUnderwritings underwritings, ILogger<OfferUnderwritingDispatcher> logger) : IOfferUnderwritingUpdater
{
    private async Task UpdateUnderwritingInternal(ValueObjectId<OfferAggregate> offerId,
        Func<OfferMemberAggregate, MemberUnderwriting, bool> updateMemberUnderwritingFunc, CancellationToken ct)
    {
        using var _ = logger.BeginScope(new Dictionary<string, object?>
            {
                { "OfferId", offerId.Value }
            });

        logger.LogInformation("Collecting data for offer");
        var requestData = await dataCollector.For(offerId, ct);

        logger.LogInformation("Calculating members underwriting");
        var membersUnderwriting = await underwritings.CalculateMembersUnderwriting(requestData, ct);

        var updatedMembers = requestData.MembersWithQuestionnaireResponses.Select(x => (OfferMemberAggregate)x.Item1).Where(member =>
        {
            if (!membersUnderwriting.TryGetValue(member.Id.Value, out MemberUnderwriting? underwriting)) return false;
            return updateMemberUnderwritingFunc(member, underwriting);
        }).ToList();

        if (updatedMembers.Count > 0)
        {
            logger.LogInformation("Updating {Count} members underwriting", updatedMembers.Count);
            await offerMemberRepository.UpdateBatchAsync([.. updatedMembers], ct);
        }

        logger.LogInformation("Updating members underwriting completed");
    }

    public async Task UpdateUnderwriting(ValueObjectId<OfferAggregate> offerId, CancellationToken ct)
    {
        await UpdateUnderwritingInternal(offerId, (member, underwriting) =>
        {
            member.UpdateUnderwriting(underwriting);
            return true;
        }, ct);
    }

    public async Task UpdateUnderwritingHealthQuestionnaire(ValueObjectId<OfferAggregate> offerId, CancellationToken ct)
    {
        await UpdateUnderwritingInternal(offerId, (member, underwriting) =>
        {
            return member.UpdateUnderwritingHealthQuestionnaire(underwriting?.HealthQuestionnaire);
        }, ct);
    }
}
