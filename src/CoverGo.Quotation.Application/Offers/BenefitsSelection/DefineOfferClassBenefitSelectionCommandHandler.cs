using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

using MediatR;

namespace CoverGo.Quotation.Application.Offers.BenefitsSelection;

public record DefineOfferClassBenefitSelectionCommand(
    ValueObjectId<OfferAggregate> OfferId,
    string Class,
    Id PlanId,
    List<FieldInput> PlanFields) : ICommand<Offer>;

public class DefineOfferClassBenefitSelectionCommandHandler(
    IMediator mediator,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IMapper mapper) : ICommandHandler<DefineOfferClassBenefitSelectionCommand, Offer>
{
    public async Task<Offer> Handle(DefineOfferClassBenefitSelectionCommand request, CancellationToken cancellationToken)
    {
        var className = new ClassName(request.Class);

        OfferAggregate offer = await offerRepository.GetByIdAsync(request.OfferId.Value, cancellationToken);

        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);

        //TODO Leverage future B.B library an extract domain events from the aggregate
        offer.DefineOfferClassBenefitSelection(
            className,
            new(request.PlanId.Value, [.. request.PlanFields.Select(it => new CustomField<Plan>(it.Key, it.Value))]),
            productVersion);

        await offerRepository.UpdateAsync(offer, cancellationToken);

        await mediator.Publish(new OfferClassBenefitSelectionDefined(offer, className, productVersion), cancellationToken);

        return mapper.Map<Offer>(offer);
    }
}
