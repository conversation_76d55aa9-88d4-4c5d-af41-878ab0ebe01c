using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.BenefitsSelection;

/// <summary>
/// We only need an anticorruption layer.
/// No need to use full blown BB.
/// </summary>
public interface IProductVersionRepository
{
    public Task<ProductVersion> GetByIdAsync(ProductVersionId id, CancellationToken cancellationToken = default);
    public Task<IReadOnlyList<ProductAttachedDocument>> GetProductAttachedDocuments(ProductVersionId id, CancellationToken cancellationToken = default);
}

public delegate IProductVersionRepository BuildProductVersionRepository(string key);

public static class ProductVersionRepositories
{
    public const string Lite = "lite";
}
