﻿using CoverGo.Quotation.Domain.Offers;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace CoverGo.Quotation.Application.Offers.BenefitsSelection;

/// <summary>
/// Decorator for IProductVersionRepository that caches individuals to prevent redundant calls
/// </summary>
public class CachingProductVersionRepositoryDecorator : IProductVersionRepository
{
    private readonly IProductVersionRepository _decorated;
    private readonly ConcurrentDictionary<string, ProductVersion> _cache = new();

    public CachingProductVersionRepositoryDecorator(IProductVersionRepository decorated)
    {
        _decorated = decorated;
    }

    public async Task<ProductVersion> GetByIdAsync(ProductVersionId id, CancellationToken cancellationToken = default)
    {
        if (_cache.TryGetValue(id.ToString(), out ProductVersion? cachedProductVersion))
        {
            return cachedProductVersion;
        }

        var productVersion = await _decorated.GetByIdAsync(id, cancellationToken);
        _cache.TryAdd(id.ToString(), productVersion);
        return productVersion;
    }

    public Task<IReadOnlyList<ProductAttachedDocument>> GetProductAttachedDocuments(ProductVersionId id, CancellationToken cancellationToken = default) =>
        _decorated.GetProductAttachedDocuments(id, cancellationToken);
}
