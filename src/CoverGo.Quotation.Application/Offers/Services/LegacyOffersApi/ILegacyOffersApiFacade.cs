using System.Text.Json;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Services.LegacyOffersApi;

public interface ILegacyOffersApiFacade
{
    Task<ExistingClient?> GetExistingClient(string clientId, CancellationToken cancellationToken);
    Task<string> CreateIndividualClient(
        IndividualClientInput client,
        CancellationToken cancellationToken
    );
    Task<string> CreateGroupClient(GroupClientInput client, CancellationToken cancellationToken);
}

public record DynamicField
{
    public required string Name { get; init; }
    public required string Value { get; init; }
}

public record ExistingClient
{
    public required string Id { get; init; }
    public required JsonElement DynamicFields { get; init; }
}

public enum ClientType
{
    Individual,
    Group
}
