using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Services.LegacyOffersApi.Contracts;

public record Product
{
    public string? Fields { get; init; }

    public string? Name { get; init; }

    public ProductVersionId? ProductVersionId { get; init; }

    public string? LifecycleStage { get; init; }

    public string? Representation { get; init; }

    public ICollection<Script>? Scripts { get; init; }

    public string? ProductType { get; init; }
}


