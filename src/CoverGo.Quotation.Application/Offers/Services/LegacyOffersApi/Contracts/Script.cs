namespace CoverGo.Quotation.Application.Offers.Services.LegacyOffersApi.Contracts;

public record Script
{
    public required string Id { get; init; }
    public required string Name { get; init; }
    public string? InputSchema { get; init; }
    public string? OutputSchema { get; init; }
    public string? SourceCode { get; init; }
    public string? ReferenceSourceCodeUrl { get; init; }
    public string? ReferenceSourceCode { get; init; }
    public string? ExternalTableDataUrl { get; init; }
    public ICollection<string>? ExternalTableDataUrls { get; init; }
    public ScriptTypeEnum? Type { get; init; }
}
