using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Offers.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IOfferRelationshipLoader
{
    public Task LoadProductVersion(OfferAggregate aggregate, CancellationToken cancellationToken = default);
    public Task LoadOpportunity(OfferAggregate aggregate, CancellationToken cancellationToken = default);
    public Task LoadMembers(OfferAggregate aggregate, CancellationToken cancellationToken = default);
    public Task LoadPrimaryMemberIds(OfferAggregate aggregate, CancellationToken cancellationToken = default);
}

public sealed class OfferRelationshipLoader(
    ILogger<OfferRelationshipLoader> logger,
    IProductVersionRepository productVersionRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository) : IOfferRelationshipLoader
{
    public async Task LoadProductVersion(OfferAggregate aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.ProductVersion = await productVersionRepository.GetByIdAsync(aggregate.ProductVersionId, cancellationToken);
        logger.LogInformation("Loaded product version {ProductVersion} for offer {OfferId}", aggregate.ProductVersion, aggregate.Id);
    }

    public async Task LoadOpportunity(OfferAggregate aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.Opportunity = await opportunityRepository.GetByIdAsync(aggregate.OpportunityId, cancellationToken);
        logger.LogInformation("Loaded opportunity for offer {OfferId}", aggregate.Id);
    }

    public async Task LoadMembers(OfferAggregate aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.Members = [.. await offerMemberRepository.FindAllByAsync(it => it.OfferId == aggregate.Id, cancellationToken)];
        logger.LogInformation("Loaded {Count} members for offer {OfferId}", aggregate.Members.Count, aggregate.Id);
    }

    public async Task LoadPrimaryMemberIds(OfferAggregate aggregate, CancellationToken cancellationToken = default)
    {
        var primaryMembers = (await offerMemberRepository.FindAllByAsync(it => it.OfferId == aggregate.Id, cancellationToken))
                                                         .Where(m => m.DependentOf == null || m.MemberType == Domain.Members.MemberType.Primary);

        aggregate.PrimaryMemberIds = [.. primaryMembers.Select(it => it.Id.Value)];
        logger.LogInformation("Loaded primary member ids for offer {OfferId}: {@PrimaryMemberIds}", aggregate.Id, aggregate.PrimaryMemberIds);
    }
}
