using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;

namespace CoverGo.Quotation.Application.OfferMembers.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IOfferMemberRelationshipLoader
{
    public Task LoadPrimary(OfferMemberAggregate proposalMemberAggregate, CancellationToken cancellationToken = default);
}

public sealed class OfferMemberRelationshipLoader(
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository) : IOfferMemberRelationshipLoader
{
    public async Task LoadPrimary(OfferMemberAggregate offerMemberAggregate, CancellationToken cancellationToken = default)
    {
        if (offerMemberAggregate is { DependentOf: not null })
        {
            offerMemberAggregate.DependentOfValue = await offerMemberRepository.GetByIdAsync(offerMemberAggregate.DependentOf, cancellationToken);
        }
    }
}
