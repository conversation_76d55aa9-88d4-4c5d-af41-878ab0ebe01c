namespace CoverGo.Quotation.Application.Offers.Headcount;

public class InsuredGroupInput
{
    public PrimaryInsuredGroupInput? PrimaryInsuredGroup { get; set; }

    public DependentInsuredGroupInput? DependentInsuredGroup { get; set; }

    public InsuredGroupInputBase GetInsuredGroup() => PrimaryInsuredGroup ?? (InsuredGroupInputBase?)DependentInsuredGroup ?? throw new NotSupportedException("Missing value. Can't occur in prod.");
}
