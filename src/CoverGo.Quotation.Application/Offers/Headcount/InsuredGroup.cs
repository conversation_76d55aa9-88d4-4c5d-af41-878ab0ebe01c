using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Members;

namespace CoverGo.Quotation.Application.Offers.Headcount;

public record InsuredGroup
{
    public string? Class { get; set; }

    public Id? PlanId { get; set; }

    public virtual MemberType MemberType { get; }

    public long Count { get; set; }

    public List<CustomFieldDto<InsuredGroup>>? Fields { get; set; }
}
