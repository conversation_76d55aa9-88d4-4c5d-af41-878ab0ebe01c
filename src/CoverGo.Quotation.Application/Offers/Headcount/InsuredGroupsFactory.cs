using System.Diagnostics.CodeAnalysis;

using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.ProposalMembers;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Headcount;

public interface IInsuredGroupsFactory
{
    [return: NotNullIfNotNull(nameof(input))]
    public Task<InsuredGroups?> Create(
        List<InsuredGroupInput>? input,
        ProductVersionId productVersionId,
        CancellationToken cancellation = default);
}

public class InsuredGroupsFactory(
    IPlanIdValidator planIdValidator) : IInsuredGroupsFactory
{
    public async Task<InsuredGroups?> Create(
        List<InsuredGroupInput>? input,
        ProductVersionId productVersionId,
        CancellationToken cancellation = default)
    {
        if (input is null)
        {
            return null;
        }

        // Validate fields via Domain from Product. Insured Fields in Product Tree.
        var plans = input.Select<InsuredGroupInput, PlanId?>(it => it.GetInsuredGroup().PlanId?.Value).ToList();
        await planIdValidator.ValidatePlanIds(plans, productVersionId, cancellation);

        return [..input
            .Select<InsuredGroupInput, Domain.Offers.InsuredGroup>(it =>
                it.GetInsuredGroup() switch
                {
                    PrimaryInsuredGroupInput pg => new Domain.Offers.PrimaryInsuredGroup()
                    {
                        Count = pg.Count,
                        Class = pg.Class,
                        PlanId = pg.PlanId?.Value,
                        Fields = pg.Fields?.ToCustomFields<Domain.Offers.InsuredGroup>(),
                    },
                    DependentInsuredGroupInput dg => new Domain.Offers.DependentInsuredGroup()
                    {
                        Count = dg.Count,
                        Class = dg.Class,
                        PlanId = dg.PlanId?.Value,
                        Fields = dg.Fields?.ToCustomFields<Domain.Offers.InsuredGroup>(),
                        RelationshipToPrimary = dg.RelationshipToPrimary,
                    },
                    _ => throw new NotImplementedException("Not supported input. Can't occur in reality."),
                })];
    }
}
