using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;

using MediatR;

namespace CoverGo.Quotation.Application.Offers;

public class OfferUnderwritingDispatcher(
    IOfferUnderwritingCanRun uwPreConditionsChecker,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IOfferUnderwritingUpdater offerUnderwritingUpdater,
    IMediator mediator) :
    INotificationHandler<OfferClassBenefitSelectionDefined>,
    INotificationHandler<MemberAdded<OfferMemberAggregate>>,
    INotificationHandler<MembersAdded<OfferMemberAggregate>>,
    INotificationHandler<MemberUpdated<OfferMemberAggregate>>,
    INotificationHandler<MembersUpdated<OfferMemberAggregate>>,
    INotificationHandler<MembersDeleted<OfferMemberAggregate>>,
    INotificationHandler<HealthQuestionnaireResponsePublished<OfferMemberAggregate>>
{
    public async Task Handle(MemberAdded<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.AddedMember.OfferId, cancellationToken);
    }

    public async Task Handle(MembersAdded<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        var sampleOfferMember = await offerMemberRepository.FindByIdAsync(notification.Ids.First(), cancellationToken);
        await HandleUnderwriting(sampleOfferMember.OfferId, cancellationToken);
    }

    public async Task Handle(OfferClassBenefitSelectionDefined notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.Offer.Id, cancellationToken);
    }

    public async Task Handle(MemberUpdated<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.UpdatedMember.OfferId, cancellationToken);
    }

    public async Task Handle(MembersDeleted<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.DeletedMembers[0].OfferId, cancellationToken);
    }

    public async Task Handle(HealthQuestionnaireResponsePublished<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.Member.OfferId, cancellationToken);
    }

    public async Task Handle(MembersUpdated<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.MemberHolderId, cancellationToken);
    }

    private async Task HandleUnderwriting(ValueObjectId<OfferAggregate> offerId, CancellationToken cancellationToken)
    {
        if (await uwPreConditionsChecker.CanRunAutoUnderwriting(offerId, cancellationToken))
        {
            await offerUnderwritingUpdater.UpdateUnderwriting(offerId, cancellationToken);
            await mediator.Publish(new OfferUnderwritingUpdatedDomainEvent { OfferId = offerId }, cancellationToken);
            return;
        }

        await offerUnderwritingUpdater.UpdateUnderwritingHealthQuestionnaire(offerId, cancellationToken);
        await mediator.Publish(new OfferUnderwritingSkippedDomainEvent { OfferId = offerId }, cancellationToken);
    }
}
