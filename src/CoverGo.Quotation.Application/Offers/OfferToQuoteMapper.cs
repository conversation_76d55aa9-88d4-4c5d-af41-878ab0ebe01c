using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Offers;

public static class OfferToQuoteMapper
{
    public static Quote BuildQuote(OfferAggregate offer)
    {
        return new Quote
        {
            Id = offer.Id.Value,
            ProductVersionId = offer.ProductVersion.Id,
            ProductVersion = offer.ProductVersion,
            DistributorId = offer.Opportunity.DistributorId.Value,
            PrimaryAgentId = offer.Opportunity.PrimaryAgentId.Value,
            SecondaryAgentId = offer.Opportunity.SecondaryAgentId?.Value,
            BillingInformation = offer.BillingInformation,
            PolicyDetails = offer.PolicyDetails!,
            ClientId = offer.Opportunity.ClientId?.Value ?? "",
            TaxOverrides = offer.TaxOverrides,
        };
    }

    public static List<QuoteMember> BuildQuoteMembers(OfferAggregate offer)
    {
        var primaries = offer.Members
            .Where(it => it.DependentOf is null)
            .Select(it => BuildQuoteMember(it, offer))
            .ToDictionary(it => it.Id, it => it);

        var dependents = offer.Members
            .Where(it => it.DependentOf is not null)
            .Select(it => BuildQuoteMember(it, offer, primaries))
            .ToList();

        return [.. primaries.Values, .. dependents];
    }

    public static QuoteMember BuildQuoteMember(
        OfferMemberAggregate offerMember,
        OfferAggregate offer,
        Dictionary<string, QuoteMember>? primaries = null)
    {
        return new QuoteMember()
        {
            // Note that those ids don't exist in DB
            Id = offerMember.Id.Value,
            QuoteId = offerMember.OfferId.Value,

            Fields = [.. offerMember.Fields.Select(it => new CustomField<QuoteMember>(it.Key, it.Value))],
            HealthQuestionnaireResponseId = offerMember.HealthQuestionnaireResponse?.Id.Value,
            MemberId = offerMember.MemberId?.Value,
            MemberUnderwriting = offerMember.MemberUnderwriting,
            Pricing = offerMember.Pricing,
            States = [
                new QuoteMemberState(
                    offer.PolicyDetails!.StartDate,
                    offer.PolicyDetails!.EndDate,
                    offerMember.PlanId?.Value,
                    offerMember.Class?.Value,
                    offerMember.DependentOf is null
                        ? null
                        : new QuoteMemberDependency { DependentOfId = offerMember.DependentOf.Value, DependentOf = primaries?[offerMember.DependentOf.Value] })
                {
                    Fields = [.. offerMember.Fields.Select(it => new CustomField<QuoteMemberState>(it.Key, it.Value))],
                }
            ],
        };
    }
}
