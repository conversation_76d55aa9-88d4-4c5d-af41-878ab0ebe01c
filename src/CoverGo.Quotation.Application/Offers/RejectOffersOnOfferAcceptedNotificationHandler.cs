using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Domain.Opportunities;

using MediatR;

using OfferStatus = CoverGo.Quotation.Domain.Offers.OfferStatus;

namespace CoverGo.Quotation.Application.Offers;

// ReSharper disable once UnusedType.Global
public class RejectOffersOnOfferAcceptedNotificationHandler : INotificationHandler<OfferAcceptedDomainEvent>
{
    private readonly IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> _offerAggregateRepository;

    public RejectOffersOnOfferAcceptedNotificationHandler(IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerAggregateRepository)
    {
        _offerAggregateRepository = offerAggregateRepository;
    }

    public async Task Handle(OfferAcceptedDomainEvent notification, CancellationToken cancellationToken)
    {
        var acceptedOffer = notification.Offer;
        var acceptedOfferId = acceptedOffer.Id;
        var opportunityId = acceptedOffer.OpportunityId;

        List<OfferAggregate> offersToReject = await GetOffersToReject(cancellationToken, opportunityId, acceptedOfferId);

        if (offersToReject.Count > 0)
        {
            var reason = $"Offer {acceptedOffer.Version?.ToString()} Accepted";
            offersToReject.ForEach(offer => offer.Reject(reason));
            await _offerAggregateRepository.UpdateBatchAsync(offersToReject.ToList(), cancellationToken);
        }
    }

    private async Task<List<OfferAggregate>> GetOffersToReject(CancellationToken cancellationToken, ValueObjectId<OpportunityAggregate> opportunityId, ValueObjectId<OfferAggregate> acceptedOfferId)
    {
        var rejectableOfferStatus = new[] { OfferStatus.Draft, OfferStatus.Issued };
        var offersToReject = (await _offerAggregateRepository.FindAllByAsync(
            offer => offer.OpportunityId == opportunityId && offer.Id != acceptedOfferId  && rejectableOfferStatus.Contains(offer.Status),
            cancellationToken)).ToList();

        return offersToReject;
    }
}
