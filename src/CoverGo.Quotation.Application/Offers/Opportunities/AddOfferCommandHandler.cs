using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.BillingInformation;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Opportunities.Ports;
using CoverGo.Quotation.Application.Products;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Domain.Opportunities;

using MediatR;
using MongoDB.Driver;

namespace CoverGo.Quotation.Application.Offers.Opportunities;

public class AddOfferCommand : ICommand<Offer>
{
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; set; }

    /// <summary>
    /// Optional productVersionId that will be used instead of a custom product version generated from a product of an opportunity.
    /// </summary>
    public ProductVersionId? ProductVersionId { get; set; }
    public VersionPart? VersionIncrement { get; set; }
    public bool? SkipCloning { get; init; }
}

/// <summary>
/// This one works with quotation db.
/// Later on we can move the one used by Api layer to use this one. And move old code to the infra layer.
/// </summary>
public class AddOfferCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOpportunityRepository opportunityRepository,
    BuildProductVersionRepository buildProductVersionRepository,
    IBillingConfigurationRepository referenceDataRepository,
    ITaxReferenceRepository taxReferenceRepository,
    IProductRiskCarrierContractRepository productRiskCarrierContractRepository,
    IMultiTenantFeatureManager tenantFeatureManager,
    TenantId tenantId,
    IMapper mapper,
    IMediator mediator) : ICommandHandler<AddOfferCommand, Offer>
{
    public async Task<Offer> Handle(AddOfferCommand request, CancellationToken cancellationToken)
    {
        var opportunity = await opportunityRepository.GetByIdAsync(request.OpportunityId, cancellationToken);

        // Used for validation that product exists
        var productVersionRepository = buildProductVersionRepository(ProductVersionRepositories.Lite);

        var productVersion = await productVersionRepository.GetByIdAsync(request.ProductVersionId ?? opportunity.ProductVersionId, cancellationToken);

        var taxDetailsTask = taxReferenceRepository.GetCustomTaxRateDetails(productVersion.TaxConfiguration);
        var riskCarrierContractsTask = productRiskCarrierContractRepository.GetByIdAsync(productVersion.Id, cancellationToken);

        await Task.WhenAll(taxDetailsTask, riskCarrierContractsTask);

        var taxDetails = await taxDetailsTask;
        var riskCarrierContracts = await riskCarrierContractsTask;

        await opportunityRepository.LoadOffers(opportunity, cancellationToken);
        await opportunityRepository.LoadSource(opportunity, cancellationToken);

        var useLegacyProductLifecycle = await tenantFeatureManager.IsEnabled("UseLegacyProductLifecycle", tenantId.Value);
        var productVersionPostfix = useLegacyProductLifecycle ? DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString() : null;
        var newOffer = opportunity.AddOffer(
            request.ProductVersionId,
            request.VersionIncrement,
            productVersionPostfix,
            taxDetails?.Select(it => it.ToDomain(productVersion.Currency!)).ToList(),
            riskCarrierContracts?.ToList());

        var billingFrequencies = await referenceDataRepository.GetBillingFrequencies(cancellationToken);
        var billingYearPriceDateBasis = await referenceDataRepository.GetBillingYearPriceDateOptions(cancellationToken);

        if (billingFrequencies.Count() == 1)
        {
            newOffer.UpdateBillingFrequency(billingFrequencies.Single());
        }

        newOffer.UpdateBillingYearPriceDateBasis(billingYearPriceDateBasis.Single());

        await offerRepository.InsertAsync(newOffer, cancellationToken);
        await mediator.Publish(new OfferCreatedDomainEvent()
        {
            Opportunity = opportunity,
            Offer = newOffer,
            UseLegacyProductLifecycle = useLegacyProductLifecycle,
            SkipCloning = request.SkipCloning == true
        }, cancellationToken);
        return mapper.Map<Offer>(newOffer);
    }
}
