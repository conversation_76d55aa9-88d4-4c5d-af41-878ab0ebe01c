using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Opportunities;

public class UpdateOfferClassesCommand : ICommand<OfferAggregate>
{
    public required ValueObjectId<OfferAggregate> OfferId { get; set; }

    public required List<ClassName> ClassNames { get; set; }
}

/// <summary>
/// This one works with quotation db.
/// Shouldn't be exposed to an API.
/// </summary>
public class UpdateOfferClassesCommandHandler : ICommandHandler<UpdateOfferClassesCommand, OfferAggregate>
{
    private readonly IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> _repository;

    public UpdateOfferClassesCommandHandler(
        IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> repository)
    {
        _repository = repository;
    }

    public async Task<OfferAggregate> Handle(UpdateOfferClassesCommand request, CancellationToken cancellationToken)
    {
        var offer = await _repository.GetByIdAsync(request.OfferId, cancellationToken);
        offer.UpdateClasses(new(request.ClassNames));
        await _repository.UpdateAsync(offer, cancellationToken);
        return offer;
    }
}
