using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.Services.LegacyOffersApi;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.DomainEvents;

using MediatR;

namespace CoverGo.Quotation.Application.Offers.Opportunities;

public class UpdateOpportunityClientCommand : ICommand<Opportunity>
{
    public required Id OpportunityId { get; set; }
    public required Id ClientId { get; set; }
}

public class UpdateOpportunityClientCommandHandler(
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    ILegacyOffersApiFacade legacyOffersApiFacade,
    IMediator mediator,
    IMapper mapper
) : ICommandHandler<UpdateOpportunityClientCommand, Opportunity>
{
    public async Task<Opportunity> Handle(
        UpdateOpportunityClientCommand request,
        CancellationToken cancellationToken)
    {
        OpportunityAggregate opportunity = await opportunityRepository.GetByIdAsync(
            request.OpportunityId.Value,
            cancellationToken
        );

        ExistingClient? client = await legacyOffersApiFacade.GetExistingClient(
            request.ClientId,
            cancellationToken
        );

        if (client == null)
            throw new InvalidOperationException($"Client '{request.ClientId}' not found");

        opportunity.ClientId = new ClientId(request.ClientId);
        await opportunityRepository.UpdateAsync(opportunity, cancellationToken);
        await mediator.Publish(
            new OpportunityClientUpdated
            {
                ClientId = opportunity.ClientId,
                OpportunityId = opportunity.Id
            },
            cancellationToken
        );

        return mapper.Map<Opportunity>(opportunity);
    }
}
