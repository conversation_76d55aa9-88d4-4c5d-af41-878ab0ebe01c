using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Members.Services;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Opportunities.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Offers.Opportunities;

public class DuplicateOfferCommand : ICommand<Offer>
{
    public required ValueObjectId<OfferAggregate> SourceOfferId { get; set; }

    public ProductVersionId? ProductVersionId { get; set; }

    public VersionPart? VersionIncrement { get; set; }

    public bool? SkipCloning { get; init; }
}

public class DuplicateOfferCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IOpportunityRepository opportunityRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    IMemberDocumentService memberDocumentService,
    TenantId tenantId,
    IMapper mapper,
    IMediator mediator) : ICommandHandler<DuplicateOfferCommand, Offer>
{
    public async Task<Offer> Handle(DuplicateOfferCommand request, CancellationToken cancellationToken)
    {
        // Load source offer with all related data
        var sourceOffer = await offerRepository.GetByIdAsync(request.SourceOfferId, cancellationToken);
        await offerRelationshipLoader.LoadOpportunity(sourceOffer, cancellationToken);

        var opportunity = sourceOffer.Opportunity ?? await opportunityRepository.GetByIdAsync(sourceOffer.OpportunityId, cancellationToken);

        // Load opportunity relationships
        await Task.WhenAll(
            offerRelationshipLoader.LoadMembers(sourceOffer, cancellationToken),
            opportunityRepository.LoadOffers(opportunity, cancellationToken),
            opportunityRepository.LoadSource(opportunity, cancellationToken)
        );

        // Step 1: Create new offer using domain service
        var targetProductVersionId = request.ProductVersionId ?? sourceOffer.ProductVersionId;
        var newOffer = opportunity.DuplicateOffer(
            sourceOffer,
            targetProductVersionId,
            request.VersionIncrement);

        // Save the new offer
        _ = await offerRepository.InsertAsync(newOffer, cancellationToken);

        // Publish domain events
        var useLegacyProductLifecycle = await multiTenantFeatureManager.IsEnabled("UseLegacyProductLifecycle", tenantId.Value);
        await mediator.Publish(new OfferCreatedDomainEvent()
        {
            Opportunity = opportunity,
            Offer = newOffer,
            UseLegacyProductLifecycle = useLegacyProductLifecycle,
            SkipCloning = request.SkipCloning == true
        }, cancellationToken);

        // Step 3: Duplicate offer members using domain logic
        if (sourceOffer.Members?.Count > 0)
        {
            var newOfferMembers = sourceOffer.DuplicateOfferMembers(newOffer);

            // Handle document duplication for all members asynchronously
            await DuplicateMemberDocumentsAsync(newOfferMembers, cancellationToken);

            // Add members to offer and save them
            newOffer.AddMembers(newOfferMembers);
            await offerMemberRepository.InsertBatchAsync(newOfferMembers, cancellationToken);

            // Publish member added events
            List<Task> publishTasks = new();
            foreach (var member in newOfferMembers)
            {
                publishTasks.Add(mediator.Publish(
                    new MemberAdded<OfferMemberAggregate>
                    {
                        AddedMember = member,
                        DependentOfValue = member.DependentOfValue,
                        MemberHolder = newOffer,
                    },
                    cancellationToken));
            }

            await Task.WhenAll(publishTasks);
        }

        await mediator.Publish(new OfferDuplicatedDomainEvent()
        {
            Opportunity = opportunity,
            SourceOffer = sourceOffer,
            DuplicatedOffer = newOffer,
            UseLegacyProductLifecycle = useLegacyProductLifecycle,
            SkipCloning = request.SkipCloning == true
        }, cancellationToken);

        return mapper.Map<Offer>(newOffer);
    }

    private async Task DuplicateMemberDocumentsAsync(
        IList<OfferMemberAggregate> offerMembers,
        CancellationToken cancellationToken)
    {
        var documentDuplicationTasks = new List<Task>();

        for (int i = 0; i < offerMembers.Count; i++)
        {
            var member = offerMembers[i];
            if (member.Documents is null || member.Documents.Count == 0 || member.DocumentFolderId is null)
                continue;

            var task = DuplicateDocumentsForMemberAsync(member, i, offerMembers, cancellationToken);
            documentDuplicationTasks.Add(task);
        }

        await Task.WhenAll(documentDuplicationTasks);
    }

    private async Task DuplicateDocumentsForMemberAsync(
        OfferMemberAggregate member,
        int memberIndex,
        IList<OfferMemberAggregate> offerMembers,
        CancellationToken cancellationToken)
    {
        var sourceDocuments = member.Documents!;
        var sourceDocumentFolderId = member.DocumentFolderId!;
        var newDocumentFolderId = new DocumentFolderId(Guid.NewGuid().ToString());
        var newDocuments = await memberDocumentService.DuplicateMemberDocuments(
            sourceDocuments,
            sourceDocumentFolderId,
            newDocumentFolderId,
            cancellationToken);
        var updatedMember = member with
        {
            DocumentFolderId = newDocumentFolderId,
            Documents = newDocuments
        };
        offerMembers[memberIndex] = updatedMember;
    }
}
