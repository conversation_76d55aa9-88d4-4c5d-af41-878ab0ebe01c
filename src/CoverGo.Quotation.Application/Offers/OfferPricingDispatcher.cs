using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Quotation.Application.Opportunities.Commands;
using CoverGo.Quotation.Domain.Offers.Billing;
using CoverGo.Quotation.Domain.Offers.DomainEvents;

using MediatR;

namespace CoverGo.Quotation.Application.Offers;

public class OfferPricingDispatcher(IOfferPricingUpdater updater) :
    ICommandHandler<RenewalOpportunityOfferCreated>,
    INotificationHandler<OfferBillingFrequencyUpdated>,
    INotificationHandler<OfferUnderwritingUpdatedDomainEvent>,
    INotificationHandler<PolicyDetailsUpdated>,
    INotificationHandler<OfferUnderwritingSkippedDomainEvent>,
    INotificationHandler<OfferAgentAssignedDomainEvent>,
    INotificationHandler<OfferTaxOverridesUpdatedDomainEvent>
{
    public async Task Handle(OfferBillingFrequencyUpdated notification, CancellationToken cancellationToken)
    {
        await updater.UpdateOfferPricing(notification.Offer.Id, cancellationToken);
    }

    public async Task Handle(OfferUnderwritingUpdatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateOfferPricing(notification.OfferId, cancellationToken);
    }

    public async Task Handle(PolicyDetailsUpdated notification, CancellationToken cancellationToken)
    {
        await updater.UpdateOfferPricing(notification.OfferId, cancellationToken);
    }

    public async Task Handle(OfferUnderwritingSkippedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateOfferPricing(notification.OfferId, cancellationToken);
    }

    public Task Handle(OfferAgentAssignedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateOfferPricing(notification.OfferId, cancellationToken);
    public Task Handle(RenewalOpportunityOfferCreated request, CancellationToken cancellationToken) => updater.UpdateOfferPricing(request.OfferId, cancellationToken);
    public Task Handle(OfferTaxOverridesUpdatedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateOfferPricing(notification.OfferId, cancellationToken);
}
