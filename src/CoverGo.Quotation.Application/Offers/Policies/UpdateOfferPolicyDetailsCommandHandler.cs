using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Offers.Policies;

public record UpdateOfferPolicyDetailsCommand(ValueObjectId<OfferAggregate> OfferId, DateOnly StartDate, DateOnly? EndDate, List<FieldInput> Fields)
    : ICommand<Offer>;

public class UpdateOfferPolicyDetailsCommandHandler(
    IMapper mapper,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IAggregateLock aggregateLock,
    IMediator mediator)
    : ICommandHandler<UpdateOfferPolicyDetailsCommand, Offer>
{
    public async Task<Offer> Handle(UpdateOfferPolicyDetailsCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);

        var offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);

        var policyFields = request.Fields.Select(x => new PolicyField(x.Key, x.Value)).ToList();

        var policyDetails = request.EndDate is null ?
            Domain.Offers.PolicyDetails.OneYearLong(request.StartDate, policyFields) :
            new Domain.Offers.PolicyDetails(request.StartDate, request.EndDate.Value, policyFields);

        offer.UpdatePolicyDetails(policyDetails);
        await offerRepository.UpdateAsync(offer, cancellationToken);

        await mediator.Publish(new PolicyDetailsUpdated { OfferId = offer.Id }, cancellationToken);
        return mapper.Map<Offer>(offer);
    }
}
