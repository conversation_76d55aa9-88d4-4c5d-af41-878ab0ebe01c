using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.Offers.Send
{
    public record SendOffersCommand : ICommand<bool>
    {
        public required string To { get; set; }
        public string[]? Cc { get; set; }
        public required string ReplyTo { get; set; }
        public required string Subject { get; set; }
        public required string MessageBody { get; set; }
        public bool? IsSendBuyFlowLink { get; set; }
        public required ValueObjectId<OpportunityAggregate> OpportunityId { get; set; }
        public List<SendOfferAttachedDocumentInfo>? AttachedDocuments { get; set; }
    }

    public record SendRenewalOffersCommand : SendOffersCommand;

    public class SendOfferAttachedDocumentInfo
    {
        public required ValueObjectId<OfferAggregate> OfferId { get; set; }
        public required string TemplateId { get; set; }
        public required TemplateType TemplateType { get; set; }
        public required string LogicalId { get; set; }
        public required string Name { get; set; }
    }
}
