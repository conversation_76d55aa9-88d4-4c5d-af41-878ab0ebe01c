using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Proposals;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Domain.Opportunities;
using MediatR;
using Microsoft.Extensions.Logging;
using OfferAggregate = CoverGo.Quotation.Domain.Offers.OfferAggregate;

namespace CoverGo.Quotation.Application.Offers.Send;

public class AutoSendOfferHandler(
    IProductVersionRepository productVersionRepository,
    IClientRepository clientRepository,
    IAgentRepository agentRepository,
    IRenewalConfiguration renewalConfiguration,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IMediator mediator,
    ILogger<AutoSendOfferHandler> logger) :
    INotificationHandler<RenewalOfferSettledDomainEvent>
{
    public async Task Handle(RenewalOfferSettledDomainEvent request, CancellationToken cancellationToken)
    {
        var renewalConfig = await renewalConfiguration.GetRenewalConfiguration(cancellationToken);
        if (!renewalConfig.AutoSendOffer) return;

        var offerId = request.OfferId;
        var offer = await offerRepository.GetByIdAsync(offerId, cancellationToken);
        if (!offer.CanSendOffer()) return;
        var pProduct = productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);
        var pAttachedDocuments = productVersionRepository.GetProductAttachedDocuments(offer.ProductVersionId, cancellationToken);
        var pOpportunity = opportunityRepository.GetByIdAsync(offer.OpportunityId, cancellationToken);
        var opportunity = await pOpportunity;
        var clientId = opportunity.ClientId?.Value;
        if (clientId == null)
        {
            logger.LogWarning("Cannot send offer {OfferId} of {OpportunityId}. ClientId is null.", offerId, offer.OpportunityId);
            return;
        }
        var pClient = clientRepository.FindByIdAsync(clientId, cancellationToken);
        var pAgent = agentRepository.FindByIdAsync(opportunity.PrimaryAgentId.Value, cancellationToken);

        var client = await pClient;
        var clientEmail = client?.Email;
        if (string.IsNullOrEmpty(clientEmail))
        {
            logger.LogWarning("Cannot send offer {OfferId} of {ClientId}. Client email not found.", offerId, opportunity.ClientId);
            return;
        }

        var product = await pProduct;
        var agent = await pAgent;
        var attachedDocuments = (await pAttachedDocuments)
            .Where(d => d.Category == Domain.Offers.ProductAttachedDocumentCategory.Offer)
            .Select(d => new SendOfferAttachedDocumentInfo
            {
                TemplateId = d.Id,
                LogicalId = d.LogicalId,
                TemplateType = d.Type.ToTemplateType(),
                Name = $"{d.Name} {offer.Version?.Major ?? 1}.{offer.Version?.Minor ?? 0}.{offer.Version?.Patch ?? 0}",
                OfferId = offer.Id.Value,
            })
            .ToList();

        var command = new SendRenewalOffersCommand
        {
            To = clientEmail,
            ReplyTo = !string.IsNullOrEmpty(agent?.Email) ? agent.Email : DefaultReplyTo,
            Cc = DefaultCc,
            Subject = $"{product.Name} - Offers",
            MessageBody = DefaultBody,
            IsSendBuyFlowLink = opportunity.BuyFlowType == BuyFlowType.RedirectBuyFlow && DefaultIsSendBuyFlowLink,
            OpportunityId = offer.OpportunityId,
            AttachedDocuments = attachedDocuments,
        };
        await mediator.Send(command, cancellationToken);
    }

    static readonly string[] DefaultCc = [];
    const string DefaultReplyTo = "<EMAIL>";
    const string DefaultBody = "";
    const bool DefaultIsSendBuyFlowLink = false;
}
