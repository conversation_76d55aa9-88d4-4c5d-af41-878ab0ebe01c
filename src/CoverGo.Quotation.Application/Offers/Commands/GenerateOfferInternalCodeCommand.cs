using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.OfferIdGeneration;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Commands;

public record GenerateOfferInternalCodeCommand : ICommand<Offer>
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
}

public class GenerateOfferInternalCodeCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IMapper mapper,
    IOfferIdGenerator offerIdGenerator,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId,
    IAggregateLock aggregateLock) : ICommandHandler<GenerateOfferInternalCodeCommand, Offer>
{
    public async Task<Offer> Handle(GenerateOfferInternalCodeCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);
        var offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);

        bool useOfferIdGeneration = await multiTenantFeatureManager.IsEnabled("UseOfferIdGeneration", tenantId.Value);
        if (useOfferIdGeneration)
        {
            offer.InternalCode = await offerIdGenerator.Generate(cancellationToken);
            await offerRepository.Patch(offer.Id).Set(o => o.InternalCode, offer.InternalCode).ExecuteAsync(cancellationToken);
        }

        return mapper.Map<Offer>(offer);
    }
}
