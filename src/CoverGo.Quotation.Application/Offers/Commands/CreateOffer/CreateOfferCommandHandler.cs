using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.Headcount;
using CoverGo.Quotation.Application.Offers.Services.LegacyOffersApi;
using CoverGo.Quotation.Application.Opportunities.Commands;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using MediatR;

namespace CoverGo.Quotation.Application.Offers.Commands.CreateOffer;

public interface ICreateOfferCommand
{
    public CreateOfferOpportunityInput? Opportunity { get; }
    List<InsuredGroupInput>? InsuredGroups { get; }
    PolicyDetailsInput? PolicyDetails { get; }
    string? BillingFrequency { get; }
}

public record CreateOfferCommand : ICommand<OfferResponse>, ICreateOfferCommand
{
    public Id? ClientId { get; set; }
    public ClientInput? Client { get; set; }
    public required CreateOfferOpportunityInput Opportunity { get; set; }
    public List<InsuredGroupInput>? InsuredGroups { get; set; }
    public PolicyDetailsInput? PolicyDetails { get; set; }
    public string? BillingFrequency { get; set; }
}

public record CreateRenewalOfferCommand : ICommand<OfferAggregate>, ICreateOfferCommand
{
    public required RenewalOpportunity Renewal { get; set; }
    public List<InsuredGroupInput>? InsuredGroups { get; init; }
    public PolicyDetailsInput? PolicyDetails { get; init; }
    public string? BillingFrequency { get; init; }
    public CreateOfferOpportunityInput? Opportunity { get; init; }
}

/// <summary>
/// Command that allows to create an opportunity and modify created offer.
/// </summary>
public class CreateOfferCommandHandler(
    ILegacyOffersApiFacade legacyOffersApiFacade,
    IMediator mediator,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IInsuredGroupsFactory insuredGroupsFactory
) : ICommandHandler<CreateOfferCommand, OfferResponse>, ICommandHandler<CreateRenewalOfferCommand, OfferAggregate>
{
    public async Task<OfferResponse> Handle(
        CreateOfferCommand command,
        CancellationToken cancellationToken)
    {
        var client = await GetOrCreateClientIfAvailable(command, cancellationToken);
        var opportunity = await mediator.Send(
            new CreateOpportunityCommand
            {
                ClientId = client is null ? null : new Id(client.Id),
                DistributorId = command.Opportunity.DistributorId,
                PrimaryAgentId = command.Opportunity.PrimaryAgentId,
                SecondaryAgentId = command.Opportunity.SecondaryAgentId,
                SalesChannelId = command.Opportunity.SalesChannelId,
                ProductVersionId = command.Opportunity.ProductVersionId,
                BuyFlowType = command.Opportunity.BuyFlowType,
                Source = command.Opportunity.Source,
            },
            cancellationToken
        );
        var productVersionTask = productVersionRepository.GetByIdAsync(command.Opportunity.ProductVersionId, cancellationToken);

        var insuredGroupsTask = insuredGroupsFactory.Create(
            command.InsuredGroups,
            command.Opportunity.ProductVersionId,
            cancellationToken
        );
        var offer = await HandleInternal(command, opportunity.Id, insuredGroupsTask, productVersionTask, cancellationToken);
        return new OfferResponse() { Id = offer.Id.Value, Client = client };
    }

    public Task<OfferAggregate> Handle(CreateRenewalOfferCommand request, CancellationToken cancellationToken)
    => HandleInternal(request, request.Renewal.Id, cancellationToken: cancellationToken);

    private async Task<OfferAggregate> HandleInternal(
        ICreateOfferCommand command,
        ValueObjectId<OpportunityAggregate> opportunityId,
        Task<InsuredGroups?>? insuredGroupsTask = null,
        Task<ProductVersion>? productVersionTask = null,
        CancellationToken cancellationToken = default)
    {
        var offer = (
            await offerRepository.FindAllByAsync(
                it => it.OpportunityId == opportunityId,
                cancellationToken)
        ).First();

        if (command.BillingFrequency is not null)
        {
            offer.UpdateBillingFrequency(command.BillingFrequency);
        }

        if (command.PolicyDetails is not null)
        {
            var policyFields = command.PolicyDetails.Fields.Select(x => new PolicyField(x.Key, x.Value)).ToList();

            offer.UpdatePolicyDetails(
                command.PolicyDetails.EndDate is null ?
                Domain.Offers.PolicyDetails.OneYearLong(command.PolicyDetails.StartDate, policyFields) :
                new Domain.Offers.PolicyDetails(command.PolicyDetails.StartDate, command.PolicyDetails.EndDate.Value, policyFields));
        }

        if (insuredGroupsTask is not null && productVersionTask is not null)
        {
            var insuredGroups = await insuredGroupsTask;
            var productVersion = await productVersionTask;
            offer.UpdateInsuredGroups(insuredGroups, productVersion);
        }

        return await offerRepository.UpdateAsync(offer, cancellationToken);
    }

    private async Task<Client?> GetOrCreateClientIfAvailable(
        CreateOfferCommand command,
        CancellationToken cancellationToken
    )
    {
        if (command.Client is not null)
        {
            return await CreateClient(command.Client, cancellationToken);
        }

        if (command.ClientId is not null)
        {
            ExistingClient? existing = await legacyOffersApiFacade.GetExistingClient(
                command.ClientId,
                cancellationToken
            );

            if (existing is null) throw new InvalidOperationException($"Client '{command.ClientId}' not found");

            return command.Opportunity.ProductVersionId.ProductId.Type switch
            {
                ProductTypes.GM
                    => new GroupClient
                    {
                        Id = command.ClientId,
                        DynamicFields = existing.DynamicFields
                    },
                ProductTypes.IM or ProductTypes.PET
                    => new IndividualClient
                    {
                        Id = command.ClientId,
                        DynamicFields = existing.DynamicFields
                    },
                _
                    => throw new NotSupportedException(
                        $"Unsupported product type: '{command.Opportunity.ProductVersionId.ProductId.Type}'"
                    ),
            };
        }

        return null;
    }

    private async Task<Client> CreateClient(ClientInput client, CancellationToken cancellationToken)
    {
        if (client.IndividualClient is not null)
        {
            string clientId = await legacyOffersApiFacade.CreateIndividualClient(
                client.IndividualClient,
                cancellationToken
            );

            return new IndividualClient()
            {
                Id = clientId,
                DynamicFields = client.IndividualClient.DynamicFields
            };
        }

        if (client.GroupClient is not null)
        {
            string clientId = await legacyOffersApiFacade.CreateGroupClient(
                client.GroupClient,
                cancellationToken
            );

            return new GroupClient()
            {
                Id = clientId,
                DynamicFields = client.GroupClient.DynamicFields
            };
        }

        throw new InvalidOperationException("Client not specified");
    }
}
