using CoverGo.Quotation.Application.Offers.BillingInformation;
using FluentValidation;

namespace CoverGo.Quotation.Application.Offers.Commands.CreateOffer;

// ReSharper disable once UnusedType.Global
public sealed class CreateOfferCommandValidator : AbstractValidator<CreateOfferCommand>
{
    public CreateOfferCommandValidator(IBillingConfigurationRepository billingFrequencyRepository)
    {
        RuleFor(x => x.Client)
            .Must(x => !(x?.GroupClient is not null && x?.IndividualClient is not null))
            .WithMessage("GroupClient and IndividualClient should not be set at the same time.");

        RuleFor(x => x)
            .Must(x => !(x.Client is not null && x.ClientId is not null))
            .WithMessage("ClientId and ClientId should not be set at the same time.");

        RuleFor(x => x.BillingFrequency)
            .MustAsync(
                async (x, ct) =>
                {
                    if (x is null)
                    {
                        return true;
                    }

                    Task<IEnumerable<string>> billingFrequencies =
                        billingFrequencyRepository.GetBillingFrequencies(ct);

                    return (await billingFrequencies).Contains(x);
                }
            );
    }
}
