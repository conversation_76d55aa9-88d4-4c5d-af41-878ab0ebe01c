using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.Offers.Commands.CreateOffer;

public class CreateOfferOpportunityInput
{
    public required Id SalesChannelId { get; init; }
    public required Id DistributorId { get; init; }
    public required Id PrimaryAgentId { get; init; }
    public required Id? SecondaryAgentId { get; init; }
    public required ProductVersionId ProductVersionId { get; init; }
    public BuyFlowType? BuyFlowType { get; set; }
    public OpportunitySource? Source { get; set; }
}
