using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities.DomainEvents;

using MediatR;

namespace CoverGo.Quotation.Application.Offers.Commands;

public class OpportunityClientUpdatedNotificationHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offersRepository
) : INotificationHandler<OpportunityClientUpdated>
{
    public async Task Handle(
        OpportunityClientUpdated notification,
        CancellationToken cancellationToken
    )
    {
        var offers = (
            await offersRepository.FindAllByAsync(
                x => x.OpportunityId == notification.OpportunityId,
                cancellationToken
            )
        ).ToList();

        foreach (OfferAggregate offer in offers)
        {
            if (offer.BillingInformation == null)
            {
                offer.BillingInformation = new Domain.Offers.Billing.BillingInformation();
            }

            offer.BillingInformation.PayorId = notification.ClientId;
        }

        await offersRepository.UpdateBatchAsync(offers, cancellationToken);
    }
}
