using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Commands;

public record UpdateOfferTaxOverridesCommand : ICommand<Offer>
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public IReadOnlyList<TaxInput>? TaxOverrides { get; init; }
}

public class UpdateOfferTaxOverridesCommandHandler(
    IAggregateLock aggregateLock,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IMapper mapper) : ICommandHandler<UpdateOfferTaxOverridesCommand, Offer>
{
    public async Task<Offer> Handle(UpdateOfferTaxOverridesCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(request.OfferId.Value, cancellationToken);
        var offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);
        await offerRelationshipLoader.LoadProductVersion(offer, cancellationToken);

        var taxOverrides = request.TaxOverrides?
            .Select(input => input.ToDomain(offer.ProductVersion.Currency!))
            .ToList();

        offer.UpdateTaxOverrides(taxOverrides, offer.ProductVersion);

        await offerRepository.UpdateAsync(offer, cancellationToken);

        return mapper.Map<Offer>(offer);
    }
}
