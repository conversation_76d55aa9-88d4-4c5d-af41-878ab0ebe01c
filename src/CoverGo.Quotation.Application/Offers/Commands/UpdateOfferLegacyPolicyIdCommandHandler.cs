using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Commands;

public class UpdateOfferLegacyPolicyIdCommand : ICommand<Offer>
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public required ValueObjectId<LegacyPolicy> LegacyPolicyId { get; init; }
}

/// <summary>
/// Shouldn't be exposed an as API.
/// </summary>
public sealed class UpdateOfferLegacyPolicyIdCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> repository,
    IMapper mapper) : ICommandHandler<UpdateOfferLegacyPolicyIdCommand, Offer>
{
    public async Task<Offer> Handle(UpdateOfferLegacyPolicyIdCommand request, CancellationToken cancellationToken)
    {
        var offer = await repository.GetByIdAsync(request.OfferId, cancellationToken);
        offer.LegacyPolicyId = request.LegacyPolicyId;
        offer = await repository.UpdateAsync(offer, cancellationToken);

        var result = mapper.Map<Offer>(offer);
        return result;
    }
}
