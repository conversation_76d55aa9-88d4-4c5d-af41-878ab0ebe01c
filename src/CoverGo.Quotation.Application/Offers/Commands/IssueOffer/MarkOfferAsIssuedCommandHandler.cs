using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.OfferMembers;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.OfferIdGeneration;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using MediatR;

namespace CoverGo.Quotation.Application.Offers.Commands.IssueOffer
{
    public class MarkOfferAsIssuedCommand : ICommand<Offer>
    {
        public required ValueObjectId<OfferAggregate> Id { get; set; }
    }

    public class MarkOfferAsIssuedCommandHandler(
        IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
        IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
        IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
        IProductVersionRepository productVersionRepository,
        IMapper mapper,
        IMediator mediator,
        IMultiTenantFeatureManager multiTenantFeatureManager,
        TenantId tenantId,
        IOfferIdLockRepository offerIdLockRepository,
        ILockManager lockManager,
        IAggregateLock aggregateLock
    ) : ICommandHandler<MarkOfferAsIssuedCommand, Offer>
    {
        public async Task<Offer> Handle(
            MarkOfferAsIssuedCommand request,
            CancellationToken cancellationToken)
        {
            await aggregateLock.TakeLockAsync<OfferAggregate>(request.Id.Value, cancellationToken);
            bool useOfferIdGeneration = await multiTenantFeatureManager.IsEnabled("UseOfferIdGeneration", tenantId.Value);
            if (useOfferIdGeneration)
            {
                var (mongoLock, acquire) = await offerIdLockRepository.TakeLockAsync(cancellationToken);
                lockManager.RegisterLock(new RegisteredLock(mongoLock, acquire));
            }
            var utcNow = DateTime.UtcNow;
            var offerAggregate = await offerRepository.GetByIdAsync(request.Id, cancellationToken);

            var opportunity = await opportunityRepository.GetByIdAsync(offerAggregate.OpportunityId, cancellationToken);
            var offerMembers = await offerMemberRepository.FindByOffer(offerAggregate.Id, cancellationToken);
            var productVersion = await productVersionRepository.GetByIdAsync(offerAggregate.ProductVersionId, cancellationToken);

            offerAggregate.MarkAsIssued(offerMembers.Count(), productVersion, opportunity, utcNow);

            await offerRepository.UpdateAsync(offerAggregate, cancellationToken);
            if (opportunity is RenewalOpportunity) await mediator.Publish(new RenewalOfferSettledDomainEvent { OfferId = offerAggregate.Id }, cancellationToken);

            return mapper.Map<Offer>(offerAggregate);
        }
    }
}
