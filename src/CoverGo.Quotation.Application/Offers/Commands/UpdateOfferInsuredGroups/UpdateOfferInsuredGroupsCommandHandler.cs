using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Offers.Headcount;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Commands.UpdateOfferInsuredGroups;

public sealed record UpdateOfferInsuredGroupsCommand(ValueObjectId<OfferAggregate> OfferId, List<InsuredGroupInput> InsuredGroups) : ICommand<Offer>;

// ReSharper disable once UnusedType.Global
public class UpdateOfferInsuredGroupsCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IInsuredGroupsFactory insuredGroupsFactory,
    IMapper mapper) : ICommandHandler<UpdateOfferInsuredGroupsCommand, Offer>
{
    public async Task<Offer> Handle(UpdateOfferInsuredGroupsCommand command, CancellationToken cancellationToken)
    {
        var offer = await offerRepository.GetByIdAsync(command.OfferId, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);
        var insuredGroups = await insuredGroupsFactory.Create(command.InsuredGroups, offer.ProductVersionId, cancellationToken);
        offer.UpdateInsuredGroups(insuredGroups, productVersion);
        offer = await offerRepository.UpdateAsync(offer, cancellationToken);
        var result = mapper.Map<Offer>(offer);
        return result;
    }
}
