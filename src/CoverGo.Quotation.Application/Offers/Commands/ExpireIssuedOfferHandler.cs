using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Commands;

public record ExpireIssuedOfferCommand : ICommand
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
}

public class ExpireIssuedOfferHandler(IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository) : ICommandHandler<ExpireIssuedOfferCommand>
{
    public async Task Handle(ExpireIssuedOfferCommand request, CancellationToken cancellationToken)
    {
        var offer = await offerRepository.GetByIdAsync(request.OfferId, cancellationToken);

        offer.Expire(DateTime.UtcNow);

        await offerRepository.UpdateAsync(offer, cancellationToken);
    }
}
