
using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Offers.Commands.RejectOffer;

public class RejectOfferCommand : ICommand<Offer>
{
    public required ValueObjectId<OfferAggregate> Id { get; set; }
    public string? RejectedReason { get; set; }
}

public class RejectOfferCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IAggregateLock aggregateLock,
    IMapper mapper) : ICommandHandler<RejectOfferCommand, Offer>
{
    public async Task<Offer> Handle(RejectOfferCommand command, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(command.Id.Value, cancellationToken);
        OfferAggregate offerAggregate = await offerRepository.GetByIdAsync(command.Id, cancellationToken);
        offerAggregate.Reject(command.RejectedReason);
        offerAggregate = await offerRepository.UpdateAsync(offerAggregate, cancellationToken);
        return mapper.Map<Offer>(offerAggregate);
    }
}
