using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using MediatR;

namespace CoverGo.Quotation.Application.Offers.Commands.AcceptOffer;

public class AcceptOfferCommand : ICommand<Offer>
{
    public required ValueObjectId<OfferAggregate> Id { get; set; }
}

public class AcceptOfferCommandHandler(
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IMediator mediator,
    IAggregateLock aggregateLock,
    IMapper mapper) : ICommandHandler<AcceptOfferCommand, Offer>
{
    public async Task<Offer> Handle(AcceptOfferCommand command, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<OfferAggregate>(command.Id.Value, cancellationToken);
        OfferAggregate offerAggregate = await offerRepository.GetByIdAsync(command.Id, cancellationToken);
        var expirationDate = offerAggregate.ExpirationDate;
        offerAggregate.Accept();
        offerAggregate = await offerRepository.UpdateAsync(offerAggregate, cancellationToken);
        var opportunity = await opportunityRepository.GetByIdAsync(offerAggregate.OpportunityId, cancellationToken);
        await mediator.Publish(new OfferAcceptedDomainEvent {
            Offer = offerAggregate,
            ExpirationDate = expirationDate,
            AutoApproveProposal = opportunity is RenewalOpportunity {} }, cancellationToken);
        return mapper.Map<Offer>(offerAggregate);
    }
}
