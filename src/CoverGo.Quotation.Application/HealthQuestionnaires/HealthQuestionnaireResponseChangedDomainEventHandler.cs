using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.ProposalMembers;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.HealthQuestionnaires;

public sealed class HealthQuestionnaireResponseChangedDomainEventHandler(
    ILogger<HealthQuestionnaireResponseChangedDomainEventHandler> logger,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository
) : INotificationHandler<HealthQuestionnaireResponseChangedDomainEvent<OfferMemberAggregate>>,
    INotificationHandler<HealthQuestionnaireResponseChangedDomainEvent<ProposalMemberAggregate>>
{
    public async Task Handle(HealthQuestionnaireResponseChangedDomainEvent<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling HealthQuestionnaireResponseChangedDomainEvent for OfferMember with ID: {OfferMemberId}", notification.Member.Id);
        var offerMemberId = notification.Member.Id;
        var proposalMember = (await proposalMemberRepository.FindAllByAsync(p => p.OfferMemberId == offerMemberId, cancellationToken)).FirstOrDefault();
        if (proposalMember is not null)
        {
            proposalMember.UpdateHealthQuestionnaireResponse(notification.Response, shouldPublishChangedEvent: false);
            await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);
            logger.LogInformation("Updated HealthQuestionnaireResponse for ProposalMember with ID: {ProposalMemberId}", proposalMember.Id);
        }
    }

    public async Task Handle(HealthQuestionnaireResponseChangedDomainEvent<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling HealthQuestionnaireResponseChangedDomainEvent for ProposalMember with ID: {ProposalMemberId}", notification.Member.Id);
        var offerMemberId = notification.Member.OfferMemberId;
        if (offerMemberId is not null)
        {
            var offerMember = await offerMemberRepository.GetByIdAsync(offerMemberId.Value, cancellationToken);
            offerMember.UpdateHealthQuestionnaireResponse(notification.Response, shouldPublishChangedEvent: false);
            await offerMemberRepository.UpdateAsync(offerMember, cancellationToken);
            logger.LogInformation("Updated HealthQuestionnaireResponse for OfferMember with ID: {OfferMemberId}", offerMember.Id);
        }
    }
}
