namespace CoverGo.Quotation.Application.HealthQuestionnaires;

public class QuestionnaireVersion
{
    public required string Id { get; init; }
    public int Version { get; init; }
    public DateTime CreatedAt { get; init; }
    public DateTime UpdatedAt { get; init; }
    public required IEnumerable<Question> Questions { get; init; }
}

public abstract class Question
{
    public required int Id { get; init; }
    public required string Body { get; init; }
    public string? Description { get; init; }
    public QuestionEnabler? QuestionEnabler { get; init; }
}

public sealed class BooleanQuestion : Question
{
}

public sealed class TextQuestion : Question
{
}

public sealed class OptionsQuestion : Question
{
    public required IEnumerable<Option> Options { get; init; }
}

public sealed class MultiOptionsQuestion : Question
{
    public required IEnumerable<Option> Options { get; init; }
}

public sealed class NumericQuestion : Question
{
}

public sealed class DateQuestion : Question
{
}

public sealed class FileQuestion : Question
{
}

public sealed class Option
{
    public required string Label { get; init; }
    public required string Value { get; init; }
}

public abstract class QuestionEnabler
{
}

public abstract class EnablingQuestion : QuestionEnabler
{
    public required int QuestionId { get; init; }
}

public abstract class EnablingMemberDataCondition : QuestionEnabler
{
    public required string FieldName { get; init; }
}

public class OptionsEnablingMemberDataCondition : EnablingMemberDataCondition
{
    public OptionMatchConditionType Condition { get; init; }
    public required string Value { get; init; }
}

public class NumericEnablingMemberDataCondition : EnablingMemberDataCondition
{
    public NumericMatchConditionType Condition { get; init; }
    public required decimal Value { get; init; }
}

public sealed class BooleanEnablingQuestion : EnablingQuestion
{
    public BooleanMatchConditionType Condition { get; init; }
    public required bool Value { get; init; }
}

public sealed class OptionEnablingQuestion : EnablingQuestion
{
    public OptionMatchConditionType Condition { get; init; }
    public required string Value { get; init; }
}
public sealed class TextEnablingQuestion : EnablingQuestion
{
    public TextMatchConditionType Condition { get; init; }
    public required string Value { get; init; }
}

public sealed class MultiOptionsEnablingQuestion : EnablingQuestion
{
    public MultiOptionsMatchConditionType Condition { get; init; }
    public required List<string> Values { get; init; }
}

public sealed class NumericEnablingQuestion : EnablingQuestion
{
    public NumericMatchConditionType Condition { get; init; }
    public required decimal Value { get; init; }
}

public enum TextMatchConditionType
{
    CONTAINS,
    EQUALS
}

public enum BooleanMatchConditionType
{
    EQUALS,
    NOT_EQUALS
}

public enum OptionMatchConditionType
{
    NOT_EQUALS,
    EQUALS
}

public enum MultiOptionsMatchConditionType
{
    NOT_EQUALS,
    EQUALS,
    CONTAINS
}

public enum NumericMatchConditionType
{
    EQUALS,
    NOT_EQUALS,
    LESS_THAN,
    LESS_THAN_OR_EQUAL,
    GREATER_THAN,
    GREATER_THAN_OR_EQUAL
}

public class HealthQuestionnaireResponse
{
    public required string Id { get; init; }
    public required string QuestionnaireId { get; init; }
    public required string QuestionnaireVersionId { get; init; }
    public required IResponseContext Context { get; init; }
    public required DateTime CreatedAt { get; init; }
    public required DateTime UpdatedAt { get; init; }
    public DateTime? PublishedAt { get; init; }
    public string? PublishedById { get; init; }
    public required bool IsDraft { get; init; }
    public required IEnumerable<QuestionAnswer> Answers { get; init; }
    public QuestionnaireVersion? QuestionnaireVersion { get; init; }
}


public abstract record QuestionAnswer
{
    public required int QuestionId { get; init; }
}

public sealed record BooleanQuestionAnswer : QuestionAnswer
{
    public required bool Value { get; init; }
}

public sealed record TextQuestionAnswer : QuestionAnswer
{
    public required string Value { get; init; }
}

public sealed record OptionsQuestionAnswer : QuestionAnswer
{
    public required string Value { get; init; }
}

public sealed record MultiOptionsQuestionAnswer : QuestionAnswer
{
    public required IEnumerable<string> Values { get; init; }
}

public sealed record NumericQuestionAnswer : QuestionAnswer
{
    public required decimal Value { get; init; }
}

public sealed record DateQuestionAnswer : QuestionAnswer
{
    public required DateOnly Value { get; init; }
}

public sealed record FileQuestionAnswer : QuestionAnswer
{
    public required string Path { get; init; }
}

public interface IResponseContext
{
}

public record InsuredResponseContext : IResponseContext
{
    public required string PolicyId { get; init; }
    public required string PolicyMemberId { get; init; }
}

public record OfferMemberResponseContext : IResponseContext
{
    public required string OfferId { get; init; }
    public required string OfferMemberId { get; init; }
}

public record ProposalMemberResponseContext : IResponseContext
{
    public required string ProposalId { get; init; }
    public required string ProposalMemberId { get; init; }
}
