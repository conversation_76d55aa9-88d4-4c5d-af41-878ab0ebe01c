using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.HealthQuestionnaires;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using DomainHealthQuestionnaireResponse = CoverGo.Quotation.Domain.HealthQuestionnaires.HealthQuestionnaireResponse;

namespace CoverGo.Quotation.Application.HealthQuestionnaires.Commands;

public sealed record UpdateResponseIdCommand(ValueObjectId<HealthQuestionnaireResponse> ResponseId) : ICommand
{
    public bool IsPublished { get; init; }
}

// ReSharper disable once UnusedType.Global
public sealed class UpdateResponseIdCommandHandler(
    IAggregateLock aggregateLock,
    IHealthQuestionnaireResponseRepository responseRepository,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository) : ICommandHandler<UpdateResponseIdCommand>
{
    public async Task Handle(UpdateResponseIdCommand command, CancellationToken cancellationToken)
    {
        var response = await responseRepository.GetByIdAsync(command.ResponseId.Value, cancellationToken);

        var domainHealthQuestionnaireResponse = new DomainHealthQuestionnaireResponse
        {
            Id = response.Id,
            Status = command.IsPublished ? HealthQuestionnaireResponseStatus.Published : HealthQuestionnaireResponseStatus.Submitted
        };

        switch (response.Context)
        {
            case OfferMemberResponseContext offerMemberResponseContext:
                await aggregateLock.TakeLockAsync<OfferAggregate>(offerMemberResponseContext.OfferId, cancellationToken);
                await SetOfferMemberResponseAsync(domainHealthQuestionnaireResponse, offerMemberResponseContext.OfferMemberId, cancellationToken);
                break;
            case ProposalMemberResponseContext proposalMemberResponseContext:
                await aggregateLock.TakeLockAsync<ProposalAggregate>(proposalMemberResponseContext.ProposalId, cancellationToken);
                await SetProposalMemberResponseAsync(domainHealthQuestionnaireResponse, proposalMemberResponseContext.ProposalMemberId, cancellationToken);
                break;
        }
    }

    private async Task SetProposalMemberResponseAsync(DomainHealthQuestionnaireResponse response, ValueObjectId<ProposalMemberAggregate> proposalMemberId, CancellationToken cancellationToken)
    {
        var proposalMember = await proposalMemberRepository.GetByIdAsync(proposalMemberId, cancellationToken);
        proposalMember.UpdateHealthQuestionnaireResponse(response);
        await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);
    }

    private async Task SetOfferMemberResponseAsync(DomainHealthQuestionnaireResponse response, ValueObjectId<OfferMemberAggregate> offerMemberId, CancellationToken cancellationToken)
    {
        var offerMember = await offerMemberRepository.GetByIdAsync(offerMemberId, cancellationToken);
        offerMember.UpdateHealthQuestionnaireResponse(response);
        await offerMemberRepository.UpdateAsync(offerMember, cancellationToken);
    }
}
