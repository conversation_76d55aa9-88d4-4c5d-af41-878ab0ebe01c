using CoverGo.Quotation.Domain.Members;

namespace CoverGo.Quotation.Application.Members.Services;

public interface IMemberDocumentService
{
    Task<List<MemberDocument>> GetDocumentsForPathAsync(string prefix,
                                                        IEnumerable<MemberDocument> memberDocuments,
                                                        CancellationToken cancellationToken);

    Task<List<MemberDocument>> DuplicateMemberDocuments(
        IEnumerable<MemberDocument> sourceMemberDocuments,
        DocumentFolderId sourceMemberDocumentFolderId,
        DocumentFolderId targetMemberDocumentFolderId,
        CancellationToken cancellationToken);
}
