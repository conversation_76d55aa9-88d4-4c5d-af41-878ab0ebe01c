using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.Members.Commands;

/// <summary>
/// Reasons for partial updates:
/// 1. You can't override new properties with null if they are optional in different consumers.
/// 2. You don't have to break contract by making some field required each time your model has new properties.
/// </summary>
public interface IUpdateMemberCommand<TMember>
{
    public ValueObjectId<TMember> Id { get; init; }

    public SettableOfNullable<Id?>? PlanId { get; init; }
    public SettableOfNullable<string?>? ClassName { get; init; }
    public SettableOfNullable<Id?>? DependentOf { get; init; }
    public Settable<IReadOnlyList<FieldInput>>? Fields { get; init; }
}
