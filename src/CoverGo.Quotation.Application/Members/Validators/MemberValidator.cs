using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Domain.Members.Exceptions;

namespace CoverGo.Quotation.Application.Members.Validators;

public interface IMemberValidator
{
    void ValidateFields(IEnumerable<CustomField> fields);
}

public abstract class MemberValidator
{
    public const string DUPLICATED_EMAIL_ERROR_MESSAGE = "This email is already in-used by another member. Please use another one.";
    public const string DUPLICATED_FIELD_VALUES = "Duplicated {0}(s): {1}.";

    protected static void ValidateFields(IEnumerable<CustomField> fields)
    {
        var emails = fields
            .Where(f => f.Key.Equals(CustomField.EMAIL, StringComparison.OrdinalIgnoreCase))
            .Select(f => f.Value)
            .OfType<string>()
            .Where(email => !string.IsNullOrWhiteSpace(email))
            .ToArray();
        if (emails.Any())
        {
            var distinct = emails.DistinctBy(e => e.Trim().ToUpper());
            if (distinct.Count() != emails.Count())
            {
                throw new UniqueFieldException(DUPLICATED_EMAIL_ERROR_MESSAGE);
            }
        }
    }

    public static IEnumerable<string> Errors(IEnumerable<CustomField> fields)
    {
        List<string> list = [
            DuplicatedError(fields, CustomField.EMAIL),
            DuplicatedError(fields, CustomField.STAFF_NO),
            DuplicatedError(fields, CustomField.PASSPORT_NO),
            DuplicatedError(fields, CustomField.HK_ID)
        ];
        return list.Where(error => !string.IsNullOrWhiteSpace(error));
    }

    private static string DuplicatedError(IEnumerable<CustomField> fields, string field)
    {
        var duplicatedValues = fields
            .Where(f => f.Key.Equals(field, StringComparison.OrdinalIgnoreCase))
            .Select(f => f.Value)
            .OfType<string>()
            .Where(value => !string.IsNullOrWhiteSpace(value))
            .GroupBy(value => value, StringComparer.OrdinalIgnoreCase)
            .Select(g => (Value:g.Key, Count:g.Count()))
            .Where(g => g.Count > 1)
            .Select(g => g.Value);
        if (duplicatedValues.Any())
        {
            const string separator = ", ";
            return string.Format(DUPLICATED_FIELD_VALUES, field, string.Join(separator, duplicatedValues));
        }

        return string.Empty;
    }
}
