using CoverGo.BuildingBlocks.Application.Core.Exceptions;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Members.Validators;

public interface IPlanIdValidator
{
    public Task ValidatePlanId(PlanId? planId, ProductVersionId productVersionId, CancellationToken cancellationToken = default);

    public Task ValidatePlanIds(List<PlanId?> planIds, ProductVersionId productVersionId, CancellationToken cancellationToken = default);
}

public class PlanIdValidator(
    IProductVersionRepository productVersionRepository) : IPlanIdValidator
{
    public Task ValidatePlanId(PlanId? planId, ProductVersionId productVersionId, CancellationToken cancellationToken = default)
    {
        return ValidatePlanIds([planId], productVersionId, cancellationToken);
    }

    public async Task ValidatePlanIds(List<PlanId?> planIds, ProductVersionId productVersionId, CancellationToken cancellationToken = default)
    {
        var plans = planIds.Where(it => it != null).Cast<PlanId>().ToList();

        if (plans.Count == 0)
        {
            return;
        }

        var product = await productVersionRepository.GetByIdAsync(productVersionId, cancellationToken);
        var missingPlans = plans.Where(it => !product.ContainsPlan(it)).ToList();
        if (missingPlans.Count == 1)
        {
            throw new EntityNotFoundException($"Plan with id={missingPlans[0]} not found.");
        }
        else if (missingPlans.Count > 1)
        {
            throw new AggregateException(missingPlans.Select(it => new EntityNotFoundException($"Plan with id={it} not found.")));
        }
    }
}
