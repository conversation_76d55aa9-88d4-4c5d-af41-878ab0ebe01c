using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.SettableValues;

using LoadingCondition = CoverGo.Quotation.Domain.Loadings.LoadingCondition;
using LoadingConditionDto = CoverGo.Quotation.Application.Loadings.Contracts.LoadingCondition;
// ReSharper disable ClassNeverInstantiated.Global

namespace CoverGo.Quotation.Application.Members.Contracts;

public record MemberLoadingUpdateInput(MemberScaleFactorLoadingUpdateInput? ScaleFactorLoading, MemberFixedAmountLoadingUpdateInput? FixedAmountLoading)
{
    public MemberLoadingUpdateInputBase GetMemberLoadingUpdateInput()
    {
        if (ScaleFactorLoading is not null)
        {
            return ScaleFactorLoading;
        }
        if (FixedAmountLoading is not null)
        {
            return FixedAmountLoading;
        }
        throw new NotSupportedException("Member loading not supported");
    }

    public Domain.Members.MemberLoading GetUpdatedLoading(
        Domain.Members.MemberLoading loadingToUpdate,
        CurrencyCode currencyCode)
    {
        var loadingUpdate = GetMemberLoadingUpdateInput();

        string name = loadingUpdate.Name is not null ? loadingUpdate.Name.Value! : loadingToUpdate.Name;
        LoadingCondition? condition = loadingUpdate.Condition != null ?
            loadingUpdate.Condition.Value is not null ? new LoadingCondition(loadingUpdate.Condition.Value.Value, loadingUpdate.Condition.Value.IsCustom) : null
            : loadingToUpdate.Condition;
        string? remark = loadingUpdate.Remark != null ? loadingUpdate.Remark.Value : loadingToUpdate.Remark;

        if (loadingToUpdate is Domain.Members.MemberScaleFactorLoading scaleFactorLoadingToUpdate)
        {
            if (loadingUpdate is MemberScaleFactorLoadingUpdateInput scaleFactorLoadingUpdate)
            {
                return new Domain.Members.MemberScaleFactorLoading()
                {
                    Id = loadingToUpdate.Id,
                    Name = name,
                    Condition = condition,
                    Remark = remark,
                    Source = loadingToUpdate.Source,
                    Factor = scaleFactorLoadingUpdate.Factor?.Value ?? scaleFactorLoadingToUpdate.Factor,
                    CreatedAt = loadingToUpdate.CreatedAt,
                    CreatedBy = loadingToUpdate.CreatedBy
                };
            }

            if (loadingUpdate is MemberFixedAmountLoadingUpdateInput fixedAmountLoadingUpdate && fixedAmountLoadingUpdate.Amount is not null)
            {
                return new Domain.Members.MemberFixedAmountLoading()
                {
                    Id = loadingToUpdate.Id,
                    Name = name,
                    Condition = condition,
                    Remark = remark,
                    Source = loadingToUpdate.Source,
                    Amount = new(currencyCode, fixedAmountLoadingUpdate.Amount.Value),
                    CreatedAt = loadingToUpdate.CreatedAt,
                    CreatedBy = loadingToUpdate.CreatedBy
                };
            }

            throw new LoadingTypeCannotBeChangedException("Member loading type cannot be changed from scale factor to fixed amount without value");
        }

        if (loadingToUpdate is Domain.Members.MemberFixedAmountLoading fixedAmountLoadingToUpdate)
        {
            if (loadingUpdate is MemberFixedAmountLoadingUpdateInput fixedAmountLoadingUpdate)
            {
                return new Domain.Members.MemberFixedAmountLoading()
                {
                    Id = loadingToUpdate.Id,
                    Name = name,
                    Condition = condition,
                    Remark = remark,
                    Source = loadingToUpdate.Source,
                    Amount = fixedAmountLoadingUpdate.Amount is not null ?
                        fixedAmountLoadingToUpdate.Amount with { Amount = fixedAmountLoadingUpdate.Amount.Value }
                        : fixedAmountLoadingToUpdate.Amount,
                    CreatedAt = loadingToUpdate.CreatedAt,
                    CreatedBy = loadingToUpdate.CreatedBy
                };
            }

            if (loadingUpdate is MemberScaleFactorLoadingUpdateInput scaleFactorLoadingUpdate && scaleFactorLoadingUpdate.Factor is not null)
            {
                return new Domain.Members.MemberScaleFactorLoading()
                {
                    Id = loadingToUpdate.Id,
                    Name = name,
                    Condition = condition,
                    Remark = remark,
                    Source = loadingToUpdate.Source,
                    Factor = scaleFactorLoadingUpdate.Factor.Value,
                    CreatedAt = loadingToUpdate.CreatedAt,
                    CreatedBy = loadingToUpdate.CreatedBy
                };
            }

            throw new LoadingTypeCannotBeChangedException("Member loading type cannot be changed from fixed amount to scale factor without value");
        }

        throw new NotSupportedException("Member loading type not supported");
    }
}

public abstract record MemberLoadingUpdateInputBase
{
    public required ValueObjectId<Domain.Members.MemberLoading> Id { get; init; }
    public SettableOfNullable<string>? Name { get; init; }
    public SettableOfNullable<LoadingConditionDto?>? Condition { get; init; }
    public SettableOfNullable<string?>? Remark { get; init; }
}

public record MemberScaleFactorLoadingUpdateInput : MemberLoadingUpdateInputBase
{
    public required SettableOfNullable<decimal>? Factor { get; init; }
}

public record MemberFixedAmountLoadingUpdateInput : MemberLoadingUpdateInputBase
{
    public required SettableOfNullable<decimal>? Amount { get; init; }
}
