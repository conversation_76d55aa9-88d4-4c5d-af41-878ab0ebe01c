using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Members;

namespace CoverGo.Quotation.Application.Members.Contracts;

public interface IMember<TField>
{
    public AuditInfo AuditInfo { get; set; }
    public Id Id { get; init; }
    public ValueObjectId<Member>? MemberId { get; init; }
    public Id? DependentOf { get; init; }
    public string? Class { get; init; }
    public string? PlanId { get; init; }
    public ValueObjectId<LegacyPolicyMember>? LegacyPolicyMemberId { get; init; }
    public MemberPricing? Pricing { get; init; }
    public IEnumerable<TField>? Fields { get; init; }
    public Underwriting.Contracts.MemberUnderwriting MemberUnderwriting { get; init; }
    public IEnumerable<MemberDocument>? Documents { get; init; }
    public Id? DocumentFolderId { get; init; }
}
