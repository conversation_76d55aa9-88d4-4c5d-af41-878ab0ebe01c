﻿using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Application.Users;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Members.Contracts;

public abstract record MemberLoading
{
    public required Id Id { get; init; }
    public required string Name { get; init; }
    public required LoadingSource Source { get; init; }
    public LoadingCondition? Condition { get; init; }
    public string? Remark { get; init; }
    public required DateTime CreatedAt { get; init; }
    public required QuotationUser CreatedBy { get; init; }
}

public record MemberScaleFactorLoading : MemberLoading
{
    public required decimal Factor { get; init; }
}

public record MemberFixedAmountLoading : MemberLoading
{
    public required Money? Amount { get; init; }
}
