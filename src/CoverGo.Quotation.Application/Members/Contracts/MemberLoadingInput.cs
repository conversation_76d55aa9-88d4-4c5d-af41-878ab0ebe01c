// ReSharper disable ClassNeverInstantiated.Global

using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Members.Contracts;

public record MemberLoadingInput(MemberScaleFactorLoadingInput? ScaleFactorLoading, MemberFixedAmountLoadingInput? FixedAmountLoading)
{
    public Domain.Members.MemberLoading GetLoading(
        CurrencyCode currencyCode,
        DateTime createdAt,
        string createdBy)
    {
        if (ScaleFactorLoading is not null)
        {
            return new Domain.Members.MemberScaleFactorLoading
            {
                Id = Guid.NewGuid().ToString(),
                Name = ScaleFactorLoading.Name,
                Factor = ScaleFactorLoading.Factor,
                Condition = ScaleFactorLoading.Condition is not null ?
                    new Domain.Loadings.LoadingCondition(ScaleFactorLoading.Condition.Value, ScaleFactorLoading.Condition.IsCustom) : null,
                Remark = ScaleFactorLoading.Remark,
                Source = Domain.Underwriting.LoadingSource.Manual,
                CreatedAt = createdAt,
                CreatedBy = createdBy,
            };
        }

        if (FixedAmountLoading is not null)
        {
            return new Domain.Members.MemberFixedAmountLoading
            {
                Id = Guid.NewGuid().ToString(),
                Name = FixedAmountLoading.Name,
                Amount = new Money(currencyCode, FixedAmountLoading.Amount),
                Condition = FixedAmountLoading.Condition is not null ?
                    new Domain.Loadings.LoadingCondition(FixedAmountLoading.Condition.Value, FixedAmountLoading.Condition.IsCustom) : null,
                Remark = FixedAmountLoading.Remark,
                Source = Domain.Underwriting.LoadingSource.Manual,
                CreatedAt = createdAt,
                CreatedBy = createdBy,
            };
        }

        throw new NotSupportedException("Member loading type not supported");
    }
}

public abstract record MemberLoadingInputBase
{
    public required string Name { get; init; }
    public LoadingCondition? Condition { get; init; }
    public string? Remark { get; init; }
}

public record MemberScaleFactorLoadingInput : MemberLoadingInputBase
{
    public required decimal Factor { get; init; }
}

public record MemberFixedAmountLoadingInput : MemberLoadingInputBase
{
    public required decimal Amount { get; init; }
}
