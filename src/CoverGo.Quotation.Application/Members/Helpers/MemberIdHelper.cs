using CoverGo.Quotation.Application.Quotes.Ports;

namespace CoverGo.Quotation.Application.Members.Helpers;

/// <summary>
/// Helper methods for working with Member IDs across different handlers
/// </summary>
public static class MemberIdHelper
{
    /// <summary>
    /// Gets the member ID from either the provided memberId or falls back to individual's InternalCode if memberId is null
    /// This ensures consistent behavior across all member creation handlers
    /// </summary>
    public static async Task<string?> GetMemberIdWithIndividualFallback(
        string? individualId,
        string? memberId,
        IIndividualRepository individualRepository,
        CancellationToken cancellationToken)
    {
        if (memberId != null)
            return memberId;

        if (individualId != null)
        {
            var client = await individualRepository.GetByIdAsync(individualId, cancellationToken);
            return client.InternalCode;
        }

        return null;
    }
}
