using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;

namespace CoverGo.Quotation.Application.Members.Ports;

public interface IMemberRepository<TMemberAggregate> : IRepository<TMemberAggregate, ValueObjectId<TMemberAggregate>>
    where TMemberAggregate : MemberAggregateBase<TMemberAggregate>
{
    /// <summary>
    /// BB lacks navigation properties, change tracking, state storage - so we have to eager load dependents on our own.
    /// </summary>
    /// <param name="memberAggregate"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task LoadDependents(TMemberAggregate memberAggregate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Finds all dependents of a primary member by their primary member ID.
    /// </summary>
    /// <param name="primaryMemberId">The ID of the primary member</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of dependent members</returns>
    public Task<List<TMemberAggregate>> FindDependentsByPrimaryIdAsync(
        ValueObjectId<TMemberAggregate> primaryMemberId, 
        CancellationToken cancellationToken = default);
}
