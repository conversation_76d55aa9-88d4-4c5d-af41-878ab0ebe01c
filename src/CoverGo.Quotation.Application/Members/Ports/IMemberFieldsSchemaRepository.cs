using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Members.Ports;

/// <summary>
/// Responsible for fetch member schemas
/// </summary>
public interface IMemberFieldsSchemaRepository
{
    /// <summary>
    /// Fetches schema for member fields
    /// </summary>
    /// <param name="client"></param>
    /// <param name="productVersion"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task<MemberFieldsSchema> GetSchema(
        ClientAggregate client,
        ProductVersion productVersion,
        CancellationToken cancellationToken = default);
}
