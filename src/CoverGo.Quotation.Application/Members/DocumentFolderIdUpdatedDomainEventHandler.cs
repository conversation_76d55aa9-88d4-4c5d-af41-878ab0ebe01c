using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.ProposalMembers;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.HealthQuestionnaires;

public sealed class DocumentFolderIdUpdatedDomainEventHandler(
    ILogger<DocumentFolderIdUpdatedDomainEventHandler> logger,
    IRepository<OfferMemberAggregate, ValueObjectId<OfferMemberAggregate>> offerMemberRepository,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository
) : INotificationHandler<DocumentFolderIdUpdatedDomainEvent<OfferMemberAggregate>>,
    INotificationHandler<DocumentFolderIdUpdatedDomainEvent<ProposalMemberAggregate>>
{
    public async Task Handle(DocumentFolderIdUpdatedDomainEvent<OfferMemberAggregate> notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling DocumentFolderIdUpdatedDomainEvent for OfferMember with ID: {OfferMemberId}", notification.Member.Id);
        var offerMemberId = notification.Member.Id;
        var documentFolderId = notification.DocumentFolderId;

        var proposalMember = await proposalMemberRepository.FindByOneAsync(t => t.OfferMemberId == offerMemberId, cancellationToken);
        if (proposalMember is not null
        && proposalMember.ShouldMigrateDocumentFolderId(documentFolderId))
        {
            proposalMember.MigrateDocumentFolderId(documentFolderId);
            await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);
        }
    }

    public async Task Handle(DocumentFolderIdUpdatedDomainEvent<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling DocumentFolderIdUpdatedDomainEvent for ProposalMember with ID: {ProposalMemberId}", notification.Member.Id);
        var offerMemberId = notification.Member.OfferMemberId;
        var documentFolderId = notification.DocumentFolderId;

        if (offerMemberId is not null)
        {
            var offerMember = await offerMemberRepository.GetByIdAsync(offerMemberId, cancellationToken);
            if (offerMember is not null
            && offerMember.ShouldMigrateDocumentFolderId(documentFolderId))
            {
                offerMember.MigrateDocumentFolderId(documentFolderId);
                await offerMemberRepository.UpdateAsync(offerMember, cancellationToken);
            }
        }
    }
}
