using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Loadings;

namespace CoverGo.Quotation.Application.Loadings.Contracts;

public record ProposalLoadingInput(ProposalScaleFactorLoadingInput? ScaleFactorLoading, ProposalFixedAmountLoadingInput? FixedAmountLoading)
{
    public Domain.Loadings.ProposalLoading ToDomain(
        CurrencyCode currencyCode,
        string contextUserId
    )
    {
        if (FixedAmountLoading is { } fixedAmountLoading) return fixedAmountLoading.ToDomain(currencyCode, contextUserId);
        if (ScaleFactorLoading is { } scaleFactorLoading) return scaleFactorLoading.ToDomain(contextUserId);
        throw new NotSupportedException("Proposal loading type not supported");
    }
}

public abstract record ProposalLoadingInputBase
{
    public LoadingCondition? Condition { get; init; }
    public string? Remark { get; init; }
    public string? BenefitId { get; init; }
    public string? BenefitName { get; init; }
}

public record ProposalScaleFactorLoadingInput : ProposalLoadingInputBase
{
    public required decimal Factor { get; init; }

    public Domain.Loadings.ProposalScaleFactorLoading ToDomain(string contextUserId) =>
        new()
        {
            Id = new ProposalLoadingId(Guid.NewGuid().ToString()),
            Factor = Factor,
            Condition = Condition is not null
                ? new Domain.Loadings.LoadingCondition(Condition.Value, Condition.IsCustom)
                : null,
            BenefitId = BenefitId,
            BenefitName = BenefitName,
            Remark = Remark,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = contextUserId
        };
}

public record ProposalFixedAmountLoadingInput : ProposalLoadingInputBase
{
    public required decimal Amount { get; init; }

    public Domain.Loadings.ProposalFixedAmountLoading ToDomain(CurrencyCode currencyCode, string contextUserId) =>
        new()
        {
            Id = new ProposalLoadingId(Guid.NewGuid().ToString()),
            Amount = new Money(currencyCode, Amount),
            BenefitId = BenefitId,
            BenefitName = BenefitName,
            Condition = Condition is not null ? new Domain.Loadings.LoadingCondition(Condition.Value, Condition.IsCustom) : null,
            Remark = Remark,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = contextUserId
        };
}
