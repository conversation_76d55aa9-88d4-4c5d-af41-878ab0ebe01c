﻿using CoverGo.Quotation.Application.Common;
using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.Loadings.Contracts;

public record ProposalLoadingUpdateInput(ProposalScaleFactorLoadingUpdateInput? ScaleFactorLoading, ProposalFixedAmountLoadingUpdateInput? FixedAmountLoading);

public abstract record ProposalLoadingUpdateInputBase
{
    public required Id Id { get; init; }
    public SettableOfNullable<LoadingCondition?>? Condition { get; init; }
    public SettableOfNullable<string?>? Remark { get; init; }
}

public record ProposalScaleFactorLoadingUpdateInput : ProposalLoadingUpdateInputBase
{
    public required SettableOfNullable<decimal>? Factor { get; init; }
}

public record ProposalFixedAmountLoadingUpdateInput : ProposalLoadingUpdateInputBase
{
    public required SettableOfNullable<decimal>? Amount { get; init; }
}
