using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Users;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Loadings.Contracts;

public abstract record ProposalLoading
{
    public required Id Id { get; init; }
    public LoadingCondition? Condition { get; init; }
    public string? BenefitId { get; init; }
    public string? BenefitName { get; init; }
    public string? Remark { get; init; }
    public required DateTime CreatedAt { get; init; }
    public required QuotationUser CreatedBy { get; init; }
}

public record ProposalScaleFactorLoading : ProposalLoading
{
    public required decimal Factor { get; init; }
}

public record ProposalFixedAmountLoading : ProposalLoading
{
    public required Money? Amount { get; init; }
}

