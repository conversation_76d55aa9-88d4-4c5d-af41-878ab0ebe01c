using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Products.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IProductVersionRelationshipLoader
{
    public Task LoadTermsAndConditionsTemplate(ProductVersion aggregate, CancellationToken cancellationToken = default);
    public Task LoadTermsAndConditionsJacket(ProductVersion aggregate, CancellationToken cancellationToken = default);
}

public sealed class ProductVersionRelationshipLoader(ITermsAndConditionsTemplateRepository termsAndConditionsTemplateRepository, ITermsAndConditionsJacketRepository termsAndConditionsJacketRepository) : IProductVersionRelationshipLoader
{
    public async Task LoadTermsAndConditionsTemplate(ProductVersion aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.TermsConditionTemplateId is null)
        {
            return;
        }

        aggregate.TermsConditionTemplate = await termsAndConditionsTemplateRepository.GetByIdAsync(aggregate.TermsConditionTemplateId, cancellationToken);
    }
    
    public async Task LoadTermsAndConditionsJacket(ProductVersion aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.TermsConditionJacketId is null)
        {
            return;
        }

        aggregate.TermsConditionJacket = await termsAndConditionsJacketRepository.GetByIdAsync(aggregate.TermsConditionJacketId, cancellationToken);
    }
}
