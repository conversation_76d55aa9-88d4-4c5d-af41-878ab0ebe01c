using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Products;

public interface ITaxReferenceRepository
{
    Task<TaxRateDetail[]> GetTaxRateDetails(IReadOnlyList<string> referenceMnemonic, string mnemonicCode);
    Task<TaxRateDetail[]> GetDefaultTaxRateDetails(string productType);
    Task<TaxRateDetail[]?> GetCustomTaxRateDetails(TaxConfiguration? taxConfiguration);
}
