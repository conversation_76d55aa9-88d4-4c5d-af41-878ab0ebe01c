using CoverGo.Quotation.Domain.ChannelManagement;
using CoverGo.Quotation.Domain.Offers;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CoverGo.Quotation.Application.ChannelManagement;

public interface IChannelManagementAdapter
{
    Task<QuotationCommissionPlan?> GetCommissionPlan(
        ProductVersionId productVersionId,
        string distributorId,
        DateTime date,
        CancellationToken cancellationToken);
}
