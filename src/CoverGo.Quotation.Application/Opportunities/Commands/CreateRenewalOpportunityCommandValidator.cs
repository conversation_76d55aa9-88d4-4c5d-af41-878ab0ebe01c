using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.BillingInformation;
using CoverGo.Quotation.Application.Opportunities.Services;
using CoverGo.Quotation.Application.ProposalMembers;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.OfferMembers;
using FluentValidation;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public sealed class CreateRenewalOpportunityCommandValidator : AbstractValidator<CreateRenewalOpportunityCommand>
{
    public CreateRenewalOpportunityCommandValidator(
         ISalesChannelRepository salesChannelRepository,
         IDistributorRepository distributorRepository,
         IClientRepository clientRepository,
         IBillingConfigurationRepository billingFrequencyRepository,
         IProductVersionRepository productVersionRepository,
         IMemberFieldsSchemaRepository schemaRepository)
    {
        ValidateSaleChannelId(salesChannelRepository);
        ValidateDistributorId(distributorRepository);
        ValidateClientId(clientRepository);
        ValidateBillingFrequency(billingFrequencyRepository);
        ValidateMembers(clientRepository, productVersionRepository, schemaRepository);
    }

    private void ValidateMembers(
        IClientRepository clientRepository,
        IProductVersionRepository productVersionRepository,
        IMemberFieldsSchemaRepository schemaRepository)
    {
        When(x => x.Offer?.Members?.Any() == true, () => RuleFor(x => x.Offer!.Members)
            .CustomAsync(async (members, context, cancellationToken) =>
            {
                var clientId = context.InstanceToValidate.ClientId;
                var client = await GetClient(clientRepository, new ValueObjectId<ClientAggregate>(clientId.Value), cancellationToken);
                if (client is null)
                {
                    context.AddFailure($"Client with ID: {clientId!.Value} does not exist");
                    return;
                }

                var productVersionId = context.InstanceToValidate.ProductVersionId;
                var productVersion = await productVersionRepository.GetByIdAsync(productVersionId, cancellationToken);
                var schema = await schemaRepository.GetSchema(client, productVersion, cancellationToken);
                var count = members.Count;
                var allFields = new List<CustomField<OfferMemberAggregate>>();
                for (var index = 0; index < count; index++)
                {
                    var member = members[index];
                    var customFields = member.Fields.ToCustomFields<OfferMemberAggregate>();
                    var errors = schema.ValidateFields(customFields, member.PlanId?.Value).OfType<SchemaFieldValidationError>();
                    foreach (var error in errors)
                    {
                        context.AddFailure($"{context.DisplayName}[{index}].{error.Path}", error.Message);
                    }
                    allFields.AddRange(customFields);
                }
                foreach(var error in MemberValidator.Errors(allFields))
                {
                    context.AddFailure(error);
                }
            })
        );
    }

    private void ValidateBillingFrequency(IBillingConfigurationRepository billingFrequencyRepository)
    {
        When(x => x.Offer is not null, () => RuleFor(x => x.Offer!.BillingFrequency)
            .MustAsync(
                async (x, ct) =>
                {
                    if (x is null)
                    {
                        return true;
                    }

                    Task<IEnumerable<string>> billingFrequencies =
                        billingFrequencyRepository.GetBillingFrequencies(ct);

                    return (await billingFrequencies).Contains(x);
                }
            ));
    }

    private void ValidateSaleChannelId(ISalesChannelRepository salesChannelRepository) =>
       RuleFor(x => x.SalesChannelId).MustAsync(async (saleChannelId, cancellationToken) =>
           {
               SalesChannel? salesChannel;
               try
               {
                    salesChannel = await salesChannelRepository.FindByIdAsync(saleChannelId, cancellationToken);
               }
               catch
               {
                    salesChannel = null;
               }
               return salesChannel is not null;
           })
           .WithMessage(saleChannelId => $"SaleChannel with ID: {saleChannelId.SalesChannelId.Value} does not exist");

    private void ValidateClientId(IClientRepository clientRepository) =>
        RuleFor(x => x.ClientId).MustAsync(async (clientId, cancellationToken) =>
        {
            var client = await GetClient(clientRepository, new ValueObjectId<ClientAggregate>(clientId.Value), cancellationToken);
            return client is not null;
        })
         .WithMessage(x => $"Client with ID: {x.ClientId!.Value} does not exist");

    private async Task<ClientAggregate?> GetClient(IClientRepository clientRepository, ValueObjectId<ClientAggregate> id, CancellationToken cancellationToken)
    {
        ClientAggregate? client;
        try
        {
            client = await clientRepository.GetByIdAsync(id, cancellationToken);
        }
        catch (Exception ex) when (ex is EntityNotFoundException)
        {
            client = null;
        }
        return client;
    }

    private void ValidateDistributorId(IDistributorRepository distributorRepository) =>
        RuleFor(x => x).CustomAsync(async (command, context, cancellationToken) =>
        {
            Distributor? distributor = await distributorRepository.FindByIdAsync(command.DistributorId, cancellationToken);
            if (distributor is null)
                context.AddFailure(nameof(command.DistributorId), $"Distributor with ID: {command.DistributorId.Value} does not exist");
            else if (distributor.SalesChannelId != command.SalesChannelId)
                context.AddFailure(nameof(command.DistributorId),
                    $"Distributor with ID: {command.DistributorId.Value} does not belong to SaleChannel with ID: {command.SalesChannelId.Value}");
        });
}
