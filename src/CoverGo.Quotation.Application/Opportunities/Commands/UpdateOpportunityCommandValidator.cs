using FluentValidation;
using System;

using CoverGo.Quotation.Application.Offers.Services.LegacyOffersApi;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public sealed class UpdateOpportunityCommandValidator : AbstractValidator<UpdateOpportunityCommand>
{
    public UpdateOpportunityCommandValidator(ILegacyOffersApiFacade legacyOffersApiFacade)
    {
        RuleFor(x => x.ClientId)
            .MustAsync(
                async (x, ct) =>
                {
                    if (x is null || x.Value is null)
                    {
                        return true;
                    }

                    ExistingClient? client = await legacyOffersApiFacade.GetExistingClient(x.Value, ct);

                    return client is not null;
                }
            );
    }
}
