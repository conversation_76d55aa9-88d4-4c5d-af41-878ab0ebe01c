using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Application.Common;

using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities.Exceptions;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public record UpdateOpportunityBeneficiariesCommand : ICommand<Opportunity>
{
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public required IReadOnlyList<Id> BeneficiaryIds { get; init; } = [];
}

public class UpdateOpportunityBeneficiariesCommandHandler(
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IMapper mapper) : ICommandHandler<UpdateOpportunityBeneficiariesCommand, Opportunity>
{
    public async Task<Opportunity> Handle(UpdateOpportunityBeneficiariesCommand request, CancellationToken cancellationToken)
    {
        var opportunity = await opportunityRepository.GetByIdAsync(request.OpportunityId, cancellationToken);

        var beneficiaryIds = request.BeneficiaryIds
            .Select(id => new ClientId(id.Value))
            .ToList();

        opportunity.UpdateBeneficiaries(beneficiaryIds);

        // Persist only the beneficiary list change using a partial update (patch)
        await opportunityRepository
            .Patch(opportunity.Id)
            .Set(o => o.BeneficiaryIds, opportunity.BeneficiaryIds)
            .ExecuteAsync(cancellationToken);

        return mapper.Map<Opportunity>(opportunity);
    }
}
