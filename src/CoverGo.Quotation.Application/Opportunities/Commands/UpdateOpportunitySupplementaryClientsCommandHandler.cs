using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Opportunities.Exceptions;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Opportunities.Ports;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public record UpdateOpportunitySupplementaryClientsCommand : ICommand<Opportunity>
{
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public IReadOnlyList<Id> SupplementaryClientIds { get; init; } = [];
}

public class UpdateOpportunitySupplementaryClientsCommandHandler(
    IAggregateLock aggregateLock,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IClientRepository clientRepository,
    IOpportunityRelationshipLoader opportunityRelationshipLoader,
    IMapper mapper) : ICommandHandler<UpdateOpportunitySupplementaryClientsCommand, Opportunity>
{
    public async Task<Opportunity> Handle(UpdateOpportunitySupplementaryClientsCommand request, CancellationToken cancellationToken)
    {
        var supClientIds = request.SupplementaryClientIds
            .Select(id => new ValueObjectId<ClientAggregate>(id)).ToList();

        await aggregateLock.TakeLockAsync<OpportunityAggregate>(request.OpportunityId.Value, cancellationToken);
        var opportunity = await opportunityRepository.GetByIdAsync(request.OpportunityId, cancellationToken);
        await opportunityRelationshipLoader.LoadClient(opportunity, cancellationToken);
        var supClients = await clientRepository.FindByIdsAsync(supClientIds, cancellationToken);

        opportunity.UpdateSupplementaryClients(supClients);
        await opportunityRepository.UpdateAsync(opportunity, cancellationToken);

        return mapper.Map<Opportunity>(opportunity);
    }
}
