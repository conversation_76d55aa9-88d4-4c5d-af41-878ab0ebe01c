using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.SettableValues;
using MediatR;
using CoverGo.Quotation.Domain.Opportunities.DomainEvents;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Application.Quotes.Ports;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public record UpdateOpportunityCommand : ICommand<Opportunity>
{
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public SettableOfNullable<Id?>? ClientId { get; init; }
    public Settable<Id>? PrimaryAgentId { get; init; }
    public SettableOfNullable<Id?>? SecondaryAgentId { get; init; }
}

public class UpdateOpportunityCommandHandler(
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IClientRepository clientRepository,
    IAgentRepository agentRepository,
    IMapper mapper,
    IMediator mediator
) : ICommandHandler<UpdateOpportunityCommand, Opportunity>
{
    public async Task<Opportunity> Handle(UpdateOpportunityCommand command, CancellationToken cancellationToken)
    {
        var opportunity = await opportunityRepository.GetByIdAsync(command.OpportunityId, cancellationToken);

        var changedAgent = false;
        if (command.PrimaryAgentId != null && opportunity.PrimaryAgentId.Value != command.PrimaryAgentId.Value)
        {
            changedAgent = true;
            var primaryAgent = await agentRepository.GetByIdAsync(command.PrimaryAgentId.Value, cancellationToken);
            opportunity.PrimaryAgentId = new AgentId(command.PrimaryAgentId.Value);
            opportunity.PrimaryAgent = new OpportunityAgent { FirstName = primaryAgent.FirstName, LastName = primaryAgent.LastName, AgentNumber = primaryAgent.AgentNumber };
        }
        if (command.SecondaryAgentId != null && command.SecondaryAgentId.Value != opportunity.SecondaryAgentId?.Value)
        {
            changedAgent = true;
            opportunity.SecondaryAgentId = command.SecondaryAgentId.Value is null ? null : new AgentId(command.SecondaryAgentId.Value);
        }

        if (command.ClientId != null)
        {
            var clientId = command.ClientId.Value is null ? null : new ClientId(command.ClientId.Value);
            opportunity.UpdateOpportunityClient(clientId);

            if (clientId != null)
            {
                var client = await clientRepository.GetByIdAsync(new ValueObjectId<ClientAggregate>(clientId.Value), cancellationToken);
                opportunity.Client = new OpportunityClient { Name = client?.ClientName };
            }
        }

        await opportunityRepository.UpdateAsync(opportunity, cancellationToken);
        var tOpportunityUpdated = mediator.Publish(new OpportunityUpdated { Opportunity = opportunity }, cancellationToken);

        Task tAgentAssigned = Task.CompletedTask;
        if (changedAgent)
        {
            var proposals = (await proposalRepository.FindAllByAsync(p => p.OpportunityId == command.OpportunityId && p.Status != ProposalStatus.Rejected, cancellationToken)).ToList();
            if (proposals.Count > 0) tAgentAssigned = Task.WhenAll(proposals.Select(proposal => mediator.Publish(new ProposalAgentAssignedDomainEvent { ProposalId = proposal.Id }, cancellationToken)));
            else
            {
                var offers = (await offerRepository.FindAllByAsync(o => o.OpportunityId == command.OpportunityId && o.Status != OfferStatus.Rejected && o.Status != OfferStatus.Expired && o.Status != OfferStatus.Accepted, cancellationToken)).ToList();
                if (offers.Count > 0) tAgentAssigned = Task.WhenAll(offers.Select(offer => mediator.Publish(new OfferAgentAssignedDomainEvent { OfferId = offer.Id }, cancellationToken)));
            }
        }

        await tOpportunityUpdated;
        await tAgentAssigned;

        return mapper.Map<Opportunity>(opportunity);
    }
}
