using AutoMapper;
using System.Collections.Generic;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Opportunities.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.DomainEvents;

using MediatR;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Application.Offers.Commands.CreateOffer;
using CoverGo.Quotation.Application.OfferMembers.Commands;
using CoverGo.Quotation.Application.OfferMembers.Contracts;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Offers.DomainEvents;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Application.Products;
using CoverGo.Quotation.Domain.Products;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public interface ICreateOpportunityCommand
{
    Id? ClientId { get; }
    Id SalesChannelId { get; }
    Id DistributorId { get; }
    Id PrimaryAgentId { get; }
    Id? SecondaryAgentId { get; }
    ProductVersionId ProductVersionId { get; }
    BuyFlowType? BuyFlowType { get; }
}

public class CreateOpportunityCommand : ICommand<Opportunity>, ICreateOpportunityCommand
{
    public Id? ClientId { get; init; }
    public required Id SalesChannelId { get; init; }
    public required Id DistributorId { get; init; }
    public required Id PrimaryAgentId { get; init; }
    public required Id? SecondaryAgentId { get; init; }
    public required ProductVersionId ProductVersionId { get; init; }
    public BuyFlowType? BuyFlowType { get; init; }
    public OpportunitySource? Source { get; init; }
}

public record RenewalOfferMemberInput : OfferMemberInput
{
    public MemberUnderwritingInput? MemberUnderwriting { get; init; }
    public Id? HealthQuestionnaireResponseId { get; init; }
    public new List<RenewalOfferMemberInput>? Dependents { get; init; }
}

public class RenewalOpportunityOfferInput
{
    public required PolicyDetailsInput PolicyDetails { get; init; }
    public required string BillingFrequency { get; init; }
    public required IReadOnlyList<RenewalOfferMemberInput> Members { get; init; }
}

public class CreateRenewalOpportunityCommand : ICommand<RenewalOpportunity>, ICreateOpportunityCommand
{
    public required Id ClientId { get; init; }
    public required Id SalesChannelId { get; init; }
    public required Id DistributorId { get; init; }
    public required Id PrimaryAgentId { get; init; }
    public required Id? SecondaryAgentId { get; init; }
    public required ProductVersionId ProductVersionId { get; init; }
    public BuyFlowType? BuyFlowType { get; init; }
    public RenewalOpportunityOfferInput? Offer { get; init; }
    public required Id OriginalPolicyId { get; init; }
}

public record RenewalOpportunityOfferCreated : ICommand
{
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
}

public class CreateOpportunityCommandHandler(
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IClientRepository clientRepository,
    IAgentRepository agentRepository,
    IProductTypeRepository productTypeRepository,
    IMapper mapper,
    IMediator mediator
) : ICommandHandler<CreateOpportunityCommand, Opportunity>, ICommandHandler<CreateRenewalOpportunityCommand, RenewalOpportunity>
{
    public async Task<Opportunity> Handle(
        CreateOpportunityCommand command,
        CancellationToken cancellationToken
    )
    {
        var opportunity = await HandleInternal<OpportunityAggregate, CreateOpportunityCommand>(command, cancellationToken);
        var result = mapper.Map<Opportunity>(opportunity);
        return result;
    }

    public async Task<RenewalOpportunity> Handle(CreateRenewalOpportunityCommand request, CancellationToken cancellationToken)
    {
        var renewal = await HandleInternal<RenewalOpportunity, CreateRenewalOpportunityCommand>(request, cancellationToken);
        if (request.Offer is not null)
        {
            var command = request.Offer;
            var offerCommand = new CreateRenewalOfferCommand
            {
                PolicyDetails = command.PolicyDetails,
                BillingFrequency = command.BillingFrequency,
                Renewal = renewal
            };
            var offer = await mediator.Send(offerCommand, cancellationToken);
            if (command.Members?.Any() == true)
            {
                var membersCommand = new AddRenewalOfferMembersCommand(
                    offer.Id.Value,
                    command.Members
                );
                await mediator.Send(membersCommand, cancellationToken);
                await mediator.Send(new RenewalOpportunityOfferCreated { OfferId = offer.Id }, cancellationToken);
            }
            await mediator.Publish(new RenewalOfferSettledDomainEvent { OfferId = offer.Id }, cancellationToken);
        }

        return renewal;
    }

    private async Task<TOpportunity> HandleInternal<TOpportunity, TCommand>(
        TCommand command,
        CancellationToken cancellationToken
    )
        where TOpportunity : OpportunityAggregate
        where TCommand : ICreateOpportunityCommand
    {
        var client =
            command.ClientId != null
                ? await clientRepository.GetByIdAsync(command.ClientId.Value, cancellationToken)
                : null;

        var primaryAgent = await agentRepository.GetByIdAsync(command.PrimaryAgentId, cancellationToken);
        ProductType? productType = await productTypeRepository.GetByIdAsync(command.ProductVersionId.ProductId.Type, cancellationToken);

        var opportunity = NewOpportunity<TOpportunity, TCommand>(command, client, primaryAgent, productType);

        opportunity = await InsertAsync(opportunity, cancellationToken);
        await mediator.Publish(
            new OpportunityCreated { Opportunity = opportunity },
            cancellationToken
        );
        return await GetByIdAsync<TOpportunity>(opportunity.Id, cancellationToken);
    }

    private async Task<TOpportunity> GetByIdAsync<TOpportunity>(ValueObjectId<OpportunityAggregate> id, CancellationToken cancellationToken)
        where TOpportunity : OpportunityAggregate
    {
        if (typeof(TOpportunity) == typeof(RenewalOpportunity))
        {
            var renewalOpportunity = (RenewalOpportunity)await opportunityRepository.GetByIdAsync(id, cancellationToken);
            return (renewalOpportunity as TOpportunity)!;
        }

        var opportunity = await opportunityRepository.GetByIdAsync(id, cancellationToken);
        return (opportunity as TOpportunity)!;
    }

    private async Task<TOpportunity> InsertAsync<TOpportunity>(TOpportunity input, CancellationToken cancellationToken)
        where TOpportunity : OpportunityAggregate
    {
        if (input is RenewalOpportunity renewalOpportunity)
        {
            renewalOpportunity = (RenewalOpportunity)await opportunityRepository.InsertAsync(renewalOpportunity, cancellationToken);
            return (renewalOpportunity as TOpportunity)!;
        }

        var opportunity = await opportunityRepository.InsertAsync(input, cancellationToken);
        return (opportunity as TOpportunity)!;
    }

    private TOpportunity NewOpportunity<TOpportunity, TCommand>(TCommand command, ClientAggregate? client, Agent primaryAgent, ProductType? productType)
        where TOpportunity : OpportunityAggregate
        where TCommand : ICreateOpportunityCommand
    {
        if (typeof(TOpportunity) == typeof(RenewalOpportunity) && command is CreateRenewalOpportunityCommand renewalCommand)
        {
            return (new RenewalOpportunity
            {
                Id = Guid.NewGuid().ToString(),
                ClientId = command.ClientId != null ? new ClientId(command.ClientId.Value) : null,
                Client = new OpportunityClient { Name = client?.ClientName },
                SalesChannelId = new SalesChannelId(command.SalesChannelId),
                DistributorId = new DistributorId(command.DistributorId),
                PrimaryAgentId = new AgentId(command.PrimaryAgentId),
                PrimaryAgent = new OpportunityAgent { FirstName = primaryAgent.FirstName, LastName = primaryAgent.LastName, AgentNumber = primaryAgent.AgentNumber },
                SecondaryAgentId =
                    command.SecondaryAgentId != null
                        ? new AgentId(command.SecondaryAgentId.Value)
                        : null,
                ProductVersionId = new ProductVersionId(
                    new ProductId(
                        command.ProductVersionId.ProductId.Plan,
                        command.ProductVersionId.ProductId.Type
                    ),
                    command.ProductVersionId.Version
                ),
                BuyFlowType = command.BuyFlowType,
                OriginalPolicyId = renewalCommand.OriginalPolicyId.Value,
                AllowSupplementaryClients = productType?.AllowSupplementaryClients,
                AllowMultipleBeneficiaries = productType?.AllowMultipleBeneficiaries,
                BeneficiaryIds = command.ClientId != null
                    ? new List<ClientId> { new ClientId(command.ClientId.Value) }
                    : [],
            } as TOpportunity)!;
        }

        return (new OpportunityAggregate()
        {
            Id = Guid.NewGuid().ToString(),
            ClientId = command.ClientId != null ? new ClientId(command.ClientId.Value) : null,
            Client = new OpportunityClient { Name = client?.ClientName },
            SalesChannelId = new SalesChannelId(command.SalesChannelId),
            DistributorId = new DistributorId(command.DistributorId),
            PrimaryAgentId = new AgentId(command.PrimaryAgentId),
            PrimaryAgent = new OpportunityAgent { FirstName = primaryAgent.FirstName, LastName = primaryAgent.LastName, AgentNumber = primaryAgent.AgentNumber },
            SecondaryAgentId =
                command.SecondaryAgentId != null
                    ? new AgentId(command.SecondaryAgentId.Value)
                    : null,
            ProductVersionId = new ProductVersionId(
                new ProductId(
                    command.ProductVersionId.ProductId.Plan,
                    command.ProductVersionId.ProductId.Type
                ),
                command.ProductVersionId.Version
            ),
            BuyFlowType = command.BuyFlowType,
            Source = (command as CreateOpportunityCommand)?.Source,
            AllowSupplementaryClients = productType?.AllowSupplementaryClients,
            AllowMultipleBeneficiaries = productType?.AllowMultipleBeneficiaries,
            BeneficiaryIds = command.ClientId != null
                ? new List<ClientId> { new ClientId(command.ClientId.Value) }
                : [],
        } as TOpportunity)!;
    }
}
