using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using FluentValidation;
using System.Linq;

namespace CoverGo.Quotation.Application.Opportunities.Commands;

public sealed class UpdateOpportunityBeneficiariesCommandValidator : AbstractValidator<UpdateOpportunityBeneficiariesCommand>
{
    public UpdateOpportunityBeneficiariesCommandValidator(IClientRepository clientRepository)
    {
        RuleFor(x => x.BeneficiaryIds)
            .NotNull()
            .NotEmpty()
            .MustAsync(async (beneficiaryIds, ct) =>
            {
                if (beneficiaryIds is null || beneficiaryIds.Count == 0)
                {
                    return false;
                }

                var ids = beneficiaryIds
                    .Select(id => new ValueObjectId<ClientAggregate>(id.Value))
                    .ToList();

                var existingClients = await clientRepository.FindByIdsAsync(ids, ct);

                // Validation passes only if we found all requested clients
                return existingClients.Count() == ids.Count;
            })
            .WithMessage("One or more beneficiary clients do not exist.");
    }
}