//using CoverGo.Quotation.Application.Clients;
//using CoverGo.Quotation.Application.Opportunities.Services;
//using CoverGo.Quotation.Domain.Clients;
//using FluentValidation;

//namespace CoverGo.Quotation.Application.Opportunities.Commands;

////ReSharper disable once UnusedType.Global

////TODO: Uncomment this validator when alongside with integration tests that checks integration with channel-management service
// public sealed class CreateOpportunityCommandValidator : AbstractValidator<CreateOpportunityCommand>
//{
//    public CreateOpportunityCommandValidator(ISalesChannelRepository salesChannelRepository, IDistributorRepository distributorRepository, IClientRepository clientRepository)
//    {
//        ValidateSaleChannelId(salesChannelRepository);
//        ValidateDistributorId(distributorRepository);
//        ValidateClientId(clientRepository);
//    }

//    private void ValidateSaleChannelId(ISalesChannelRepository salesChannelRepository) =>
//        RuleFor(x => x.SalesChannelId).MustAsync(async (saleChannelId, cancellationToken) =>
//            {
//                SalesChannel? salesChannel = await salesChannelRepository.FindByIdAsync(saleChannelId, cancellationToken);
//                return salesChannel is not null;
//                //TODO: Validate if salesChannel is enabled
//            })
//            .WithMessage(saleChannelId => $"SaleChannel with ID: {saleChannelId.SalesChannelId.Value} does not exist");

//    private void ValidateClientId(IClientRepository clientRepository) =>
//        RuleFor(x => x.ClientId).MustAsync(async (clientId, cancellationToken) =>
//        {
//            ClientAggregate? client = await clientRepository.GetByIdAsync(new Domain.Common.ValueObjectId<ClientAggregate>(clientId.Value), cancellationToken);
//            return client is not null;
//        })
//            .WithMessage(saleChannelId => $"SaleChannel with ID: {saleChannelId.SalesChannelId.Value} does not exist");

//    private void ValidateDistributorId(IDistributorRepository distributorRepository) =>
//        RuleFor(x => x).CustomAsync(async (command, context, cancellationToken) =>
//        {
//            Distributor? distributor = await distributorRepository.FindByIdAsync(command.DistributorId, cancellationToken);
//            if (distributor is null)
//                context.AddFailure(nameof(command.DistributorId), $"Distributor with ID: {command.DistributorId.Value} does not exist");
//            else if (distributor.SalesChannelId != command.SalesChannelId)
//                context.AddFailure(nameof(command.DistributorId),
//                    $"Distributor with ID: {command.DistributorId.Value} does not belong to SaleChannel with ID: {command.SalesChannelId.Value}");
//            //TODO: Validate if distributor is enabled
//        });
//}
