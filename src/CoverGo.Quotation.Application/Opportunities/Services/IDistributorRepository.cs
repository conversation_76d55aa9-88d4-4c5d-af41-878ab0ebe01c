﻿using CoverGo.Quotation.Application.Common;

namespace CoverGo.Quotation.Application.Opportunities.Services;

public interface IDistributorRepository
{
    Task<Distributor> GetByIdAsync(Id id, CancellationToken cancellationToken = default);

    Task<Distributor?> FindByIdAsync(Id id, CancellationToken cancellationToken = default);
}

public class Distributor
{
    public required Id Id { get; init; }
    public required Id SalesChannelId { get; init; }
}
