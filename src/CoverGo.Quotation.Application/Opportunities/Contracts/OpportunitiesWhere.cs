using CoverGo.Quotation.Application.Common;

namespace CoverGo.Quotation.Application.Opportunities.Contracts;

public class OpportunitiesWhere
{
    public IReadOnlyCollection<OpportunitiesWhere>? And { get; set; }
    public IReadOnlyCollection<OpportunitiesWhere>? Or { get; set; }
    public StatusWhere? Status { get; set; }
    public CloseReasonWhere? CloseReason { get; set; }
    public ProductTypeWhere? ProductType { get; set; }
    public DateTimeWhere? CreatedDate { get; set; }
    public StringWhere? ClientId { get; set; }
    public ClientWhere? Client { get; set; }
    public IdWhere? CreatedBy { get; set; }
    public IdWhere? AgentId { get; set; }
    public AgentWhere? PrimaryAgent { get; set; }
    public BuyFlowTypeWhere? BuyFlowType { get; set; }
    public OpportunityTypeWhere? OpportunityTypeWhere { get; set; }
}
