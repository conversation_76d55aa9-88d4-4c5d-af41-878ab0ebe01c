using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.Opportunities.Contracts;

public class Opportunity
{
    public required ValueObjectId<OpportunityAggregate> Id { get; init; }
    public Id? ClientId { get; set; }
    public required Id SalesChannelId { get; init; }
    public required Id DistributorId { get; init; }
    public required Id PrimaryAgentId { get; init; }
    public required Id? SecondaryAgentId { get; init; }
    public required ProductVersionId ProductVersionId { get; init; }
    public required AuditInfo AuditInfo { get; set; }
    public OpportunityStatus Status { get; set; } = OpportunityStatus.Open;
    public OpportunityCloseReasonBase? CloseReason { get; set; }
    public BuyFlowType? BuyFlowType { get; set; }
    public OpportunitySource? Source { get; set; }
    public Id? LegacyCaseId { get; init; }
    public bool? AllowSupplementaryClients { get; init; }
    public IReadOnlyList<Id>? SupplementaryClientIds { get; init; }
    public bool? AllowMultipleBeneficiaries { get; init; }
    public IReadOnlyList<Id>? BeneficiaryIds { get; set; }
}
