using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.Opportunities.Ports;

public interface IOpportunityRepository : IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>>
{
    /// <summary>
    /// BB lacks navigation properties, change tracking, state storage - so we have to eager load dependents on our own.
    /// </summary>
    /// <returns></returns>
    public Task LoadOffers(OpportunityAggregate opportunity, CancellationToken cancellationToken = default);

    public Task LoadSource(OpportunityAggregate opportunity, CancellationToken cancellationToken = default);
}
