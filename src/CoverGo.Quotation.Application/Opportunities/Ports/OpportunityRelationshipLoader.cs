using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;

namespace CoverGo.Quotation.Application.Opportunities.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IOpportunityRelationshipLoader
{
    public Task LoadClient(OpportunityAggregate aggregate, CancellationToken cancellationToken = default);
}

public sealed class OpportunityRelationshipLoader(
    IClientRepository clientRepository) : IOpportunityRelationshipLoader
{
    public async Task LoadClient(OpportunityAggregate aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.ClientId is null)
            return;
        var clientAggregate = await clientRepository.GetByIdAsync(new ValueObjectId<ClientAggregate>(aggregate.ClientId.Value), cancellationToken);
        aggregate.Client = new OpportunityClient
        {
            Name = clientAggregate.ClientName,
            ClientType = clientAggregate.ClientType,
        };
    }
}
