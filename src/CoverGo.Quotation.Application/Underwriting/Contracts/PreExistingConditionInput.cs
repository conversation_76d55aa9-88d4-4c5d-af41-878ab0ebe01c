using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Underwriting.Contracts;

public record PreExistingConditionInput(
    ReferenceDataConditionInput? ReferenceDataCondition,
    CustomConditionInput? CustomCondition,
    DiagnosisConditionInput? DiagnosisCondition);

public abstract record PreExistingConditionInputBase
{
    public string? Remarks { get; init; }
    public DateOnly? DateOfDiagnosis { get; init; }
    public int? NumberOfYearsOfDiagnosis { get; init; }
}

public record ReferenceDataConditionInput : PreExistingConditionInputBase
{
    public required string Mnemonic { get; init; }
}

public record CustomConditionInput : PreExistingConditionInputBase
{
    public required string Name { get; init; }
}

public record DiagnosisConditionInput : PreExistingConditionInputBase
{
    public required ValueObjectId<Domain.Underwriting.PreConditions.DiagnosisCondition> DiagnosisId { get; init; }
}
