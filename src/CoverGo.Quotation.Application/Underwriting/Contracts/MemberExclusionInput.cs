namespace CoverGo.Quotation.Application.Underwriting.Contracts;
public record MemberExclusionInput(ReferenceDataMemberExclusionInput? ReferenceDataExclusion, CustomMemberExclusionInput? CustomMemberExclusion);

public abstract record MemberExclusionInputBase
{
    public string? Remarks { get; init; }
}

public record ReferenceDataMemberExclusionInput : MemberExclusionInputBase
{
    public required string Mnemonic { get; init; }
}

public record CustomMemberExclusionInput : MemberExclusionInputBase
{
    public required string Name { get; init; }
}
