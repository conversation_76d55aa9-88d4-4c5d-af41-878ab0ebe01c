using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.Underwriting.Contracts;

public record MemberUnderwritingInput
{
    public List<MemberLoadingInput>? Loadings { get; init; }
    public List<MemberBenefitUnderwritingInput>? BenefitsUnderwritings { get; init; }
    public List<MemberExclusionInput>? Exclusions { get; init; }
    public List<PreExistingConditionInput>? PreExistingConditions { get; init; }
    public UnderwritingDecisionInput? Decision { get; set; }

    public Domain.Underwriting.MemberUnderwriting ToDomain(CurrencyCode currency, List<BenefitDefinition> benefitDefinitions, string userId) => new()
    {
        BenefitsUnderwritings = (BenefitsUnderwritings is { Count: > 0 })
                    ? BenefitsUnderwritings.Where(b => b != null).Select(b => MapMemberBenefitUnderwritingInput(b, currency, benefitDefinitions, userId)).ToList()
                    : [],
        Exclusions = (Exclusions is { Count: > 0 })
                    ? Exclusions.Where(e => e != null && (e.ReferenceDataExclusion != null || e.CustomMemberExclusion != null)).Select(m => MapExclusionInput(m, userId)).ToList()
                    : [],
        PreExistingConditions = (PreExistingConditions is { Count: > 0 })
                    ? PreExistingConditions.Where(e => e != null && (e.DiagnosisCondition != null || e.CustomCondition != null || e.ReferenceDataCondition != null)).Select(c => MapPreExistingCondition(c, userId)).ToList()
                    : [],
        Decision = MapDecisionInput(Decision),
        Loadings = Loadings?.Select(e => e.GetLoading(currency, DateTime.UtcNow, userId)).ToList() ?? []
    };

    private static Domain.Underwriting.Benefits.MemberBenefitUnderwriting MapMemberBenefitUnderwritingInput(MemberBenefitUnderwritingInput input, CurrencyCode currency, List<BenefitDefinition> benefitDefinitions, string userId) => new()
    {
        Benefit = new() { BenefitId = new(input.ProductBenefitId), BenefitName = benefitDefinitions.First(b => b.BusinessId == input.ProductBenefitId).Name },
        Decision = input.Decision switch
        {
            { AcceptanceDecision: { } } => new Domain.Underwriting.Benefits.MemberBenefitUnderwritingAcceptanceDecision(),
            { RejectionDecision: { } } => new Domain.Underwriting.Benefits.MemberBenefitUnderwritingRejectionDecision(),
            _ => null,
        },
        DecisionSource = input.Decision switch
        {
            { AcceptanceDecision: { } } => input.Decision.AcceptanceDecision.Source,
            { RejectionDecision: { } } => input.Decision.RejectionDecision.Source,
            _ => UnderwritingDecisionSource.Auto,
        },
        Loadings = input.Loadings?.Select(e => e.GetLoading(currency, DateTime.UtcNow, userId)).ToList() ?? [],
    };

    private static Domain.Underwriting.UnderwritingDecisionBase? MapDecisionInput(UnderwritingDecisionInput? decision) => decision switch
    {
        { AcceptanceDecision: { } d } => new Domain.Underwriting.UnderwritingAcceptanceDecision { Source = d.Source, Remarks = d.Remarks },
        { RejectionDecision: { } d } => new Domain.Underwriting.UnderwritingRejectionDecision
        {
            Source = d.Source,
            RejectReason = d.RejectReason switch
            {
                { RejectReason: { } r } => new Domain.ProposalMembers.RejectReason(r.RejectReasonCode),
                { OtherRejectReason: { } r } => new Domain.ProposalMembers.OtherRejectReason(r.Reason),
                _ => throw new InvalidOperationException("RejectReason must have either RejectReason or OtherRejectReason.")
            }
        },
        _ => null,
    };

    private static Domain.Underwriting.Exclusions.MemberExclusion MapExclusionInput(MemberExclusionInput exclusion, string userId) => exclusion switch
    {
        { ReferenceDataExclusion: { } e } => new Domain.Underwriting.Exclusions.ReferenceDataExclusion { CreatedBy = userId, Mnemonic = e.Mnemonic, Remarks = e.Remarks, },
        { CustomMemberExclusion: { } e } => new Domain.Underwriting.Exclusions.CustomExclusion { CreatedBy = userId, Name = e.Name, Remarks = e.Remarks },
        _ => throw new InvalidOperationException("Exclusion must have either ReferenceDataExclusion or CustomMemberExclusion.")
    };

    private static Domain.Underwriting.PreConditions.PreExistingCondition MapPreExistingCondition(PreExistingConditionInput condition, string userId) => condition switch
    {
        { ReferenceDataCondition: { } c } => new Domain.Underwriting.PreConditions.ReferenceDataCondition
        {
            CreatedBy = userId,
            Id = new(Guid.NewGuid().ToString()),
            DateOfDiagnosis = c.DateOfDiagnosis,
            DiagnosisTimespan = c.NumberOfYearsOfDiagnosis != null ? TimeSpan.FromDays(365.5 * (double)condition!.ReferenceDataCondition!.NumberOfYearsOfDiagnosis!) : null, //check
            Mnemonic = c.Mnemonic,
            Remarks = c.Remarks,
        },
        { DiagnosisCondition: { } c } => new Domain.Underwriting.PreConditions.DiagnosisCondition
        {
            CreatedBy = userId,
            Id = new(Guid.NewGuid().ToString()),
            DateOfDiagnosis = c.DateOfDiagnosis,
            DiagnosisTimespan = c.NumberOfYearsOfDiagnosis != null ? TimeSpan.FromDays(365.5 * (double)condition!.DiagnosisCondition!.NumberOfYearsOfDiagnosis!) : null, //check
            DiagnosisId = c.DiagnosisId,
            Remarks = c.Remarks,
        },
        { CustomCondition: { } c } => new Domain.Underwriting.PreConditions.CustomCondition
        {
            CreatedBy = userId,
            Id = new(Guid.NewGuid().ToString()),
            Name = c.Name,
            DateOfDiagnosis = c.DateOfDiagnosis,
            DiagnosisTimespan = c.NumberOfYearsOfDiagnosis != null ? TimeSpan.FromDays(365.5 * (double)condition!.CustomCondition!.NumberOfYearsOfDiagnosis!) : null, //check
            Remarks = c.Remarks,
        },
        _ => throw new InvalidOperationException("Condition must have either ReferenceDataCondition, DiagnosisCondition or CustomCondition.")
    };
}

public record UnderwritingDecisionInput(
    UnderwritingAcceptanceDecisionInput? AcceptanceDecision,
    UnderwritingRejectionDecisionInput? RejectionDecision)
{
}

public record UnderwritingAcceptanceDecisionInput
{
    public required UnderwritingDecisionSource Source { get; init; }
    public string? Remarks { get; init; }
}

public record UnderwritingRejectionDecisionInput
{
    public required UnderwritingDecisionSource Source { get; init; }
    public required RejectReasonBaseInput RejectReason { get; init; }
}

public record MemberBenefitUnderwritingDecisionInput(
    MemberBenefitUnderwritingAcceptanceDecisionInput? AcceptanceDecision,
    MemberBenefitUnderwritingRejectionDecisionInput? RejectionDecision)
{
}

public record MemberBenefitUnderwritingAcceptanceDecisionInput
{
    public required UnderwritingDecisionSource Source { get; init; }
}

public record MemberBenefitUnderwritingRejectionDecisionInput
{
    public required UnderwritingDecisionSource Source { get; init; }
}

public record MemberBenefitUnderwritingInput
{
    public required Id ProductBenefitId { get; init; }

    public List<MemberLoadingInput>? Loadings { get; init; }

    public MemberBenefitUnderwritingDecisionInput? Decision { get; init; }
}
