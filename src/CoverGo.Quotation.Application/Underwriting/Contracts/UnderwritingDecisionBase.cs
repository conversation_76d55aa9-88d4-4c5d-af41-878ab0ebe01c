using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.Underwriting.Contracts;

public abstract record UnderwritingDecisionBase
{
    public abstract UnderwritingDecisionType Type { get; }
    public required UnderwritingDecisionSource Source { get; init; }
}

public enum UnderwritingDecisionType
{
    Acceptance,
    Rejection
}

public record UnderwritingAcceptanceDecision : UnderwritingDecisionBase
{
    public string? Remarks { get; init; }

    public override UnderwritingDecisionType Type => UnderwritingDecisionType.Acceptance;
}

public record UnderwritingRejectionDecision : UnderwritingDecisionBase
{
    public required RejectReasonBase RejectReason { get; init; }

    public override UnderwritingDecisionType Type => UnderwritingDecisionType.Rejection;
}
