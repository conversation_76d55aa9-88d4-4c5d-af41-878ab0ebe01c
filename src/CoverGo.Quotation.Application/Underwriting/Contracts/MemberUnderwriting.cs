using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.Underwriting.Contracts;

public class MemberUnderwriting
{
    public required IReadOnlyCollection<MemberLoading>? Loadings { get; init; }
    public required List<MemberBenefitUnderwriting>? BenefitsUnderwritings { get; init; }
    public required List<MemberExclusion>? Exclusions { get; init; }
    public required List<PreExistingCondition>? PreExistingConditions { get; init; }
    public UnderwritingDecisionBase? Decision { get; set; }
    public MemberUnderwritingStatus Status { get; set; }
    public MemberHealthQuestionnaire? HealthQuestionnaire { get; set; }
}
