using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Users;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Underwriting.Contracts;

public abstract record MemberExclusion
{
    public required ValueObjectId<Domain.Underwriting.Exclusions.MemberExclusion> Id { get; init; }
    public required DateTime CreatedAt { get; init; }
    public required QuotationUser CreatedBy { get; init; }
    public required string? Remarks { get; init; }
}

public record ReferenceDataExclusion : MemberExclusion
{
    public required ExclusionReferenceData ReferenceData { get; init; }
}

public record CustomExclusion : MemberExclusion
{
    public required string Name { get; init; }
}

public record ExclusionReferenceData : ReferenceData
{
}
