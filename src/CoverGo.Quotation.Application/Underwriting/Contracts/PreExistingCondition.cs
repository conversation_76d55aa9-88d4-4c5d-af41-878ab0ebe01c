using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Users;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Underwriting.Contracts;

public abstract record PreExistingCondition
{
    public required ValueObjectId<Domain.Underwriting.PreConditions.PreExistingCondition> Id { get; init; }
    public required DateTime CreatedAt { get; init; }
    public DateOnly? DateOfDiagnosis { get; init; }
    public int? NumberOfYearsOfDiagnosis { get; init; }
    public required QuotationUser CreatedBy { get; init; }
    public string? Remarks { get; init; }
}

public record ReferenceDataCondition : PreExistingCondition
{
    public required PreExistingConditionReferenceData ReferenceData { get; init; }
}

public record CustomCondition : PreExistingCondition
{
    public required string Name { get; init; }
}

public record DiagnosisCondition : PreExistingCondition
{
    public required PreExistingConditionDiagnosis Diagnosis { get; init; }
}

public record PreExistingConditionReferenceData : ReferenceData
{
}

public record PreExistingConditionDiagnosis
{
    public required ValueObjectId<Domain.Underwriting.PreConditions.DiagnosisCondition> Id { get; init; }
}

public record DiagnosisDetails(string? Code, string? Title);
