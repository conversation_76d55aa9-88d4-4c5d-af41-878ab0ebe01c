using AutoMapper;

using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes;

public static class QuotesMappingExtensions
{
    public static void CreateQuoteMappings(this Profile profile)
    {
        profile.CreateMap<CustomField<QuoteMember>, CustomFieldDto<QuoteMember>>()
            .ConvertUsing(x =>
                new(
                    x.Key,
                    x.Value.RefineToLoad())
            );
        profile.CreateMap<CustomField<QuoteMemberState>, CustomFieldDto<QuoteMemberState>>()
            .ConvertUsing(x =>
                new(
                    x.Key,
                    x.Value.RefineToLoad())
            );
        profile.CreateMap<CustomFields<QuoteMemberState>, List<CustomFieldDto<QuoteMemberState>>>()
            .ConvertUsing(x =>
                x.Select(it => new CustomFieldDto<QuoteMemberState>(it.Key, it.Value)).ToList());
    }
}
