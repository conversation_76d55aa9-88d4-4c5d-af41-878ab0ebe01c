using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Quotes;

using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Quotation.Application.Quotes;

public static class QuoteApplicationDI
{
    public static IServiceCollection AddQuotesApplication(
        this IServiceCollection services)
    {
        services.AddScoped<IQuoteMemberRelationshipLoader, QuoteMemberRelationshipLoader>();
        services.AddScoped<IQuoteMemberStateRelationshipLoader, QuoteMemberStateRelationshipLoader>();
        services.AddScoped<IPrimaryQuoteMemberUpdater, PrimaryQuoteMemberUpdater>();
        services.AddScoped<IQuoteUnderwritingUpdater, QuoteUnderwritingUpdater>();
        services.AddScoped<IQuotePricingUpdater, QuotePricingUpdater>();
        return services;
    }

    public static IServiceCollection AddQuotesApplicationDecorators(
        this IServiceCollection services)
    {
        services.Decorate<IRepository<QuoteMember, string>>((inner, provider) => new QuoteMemberValidationBehavior(
            inner,
            provider.GetRequiredService<IQuoteRelationshipLoader>(),
            provider.GetRequiredService<IQuoteMemberRelationshipLoader>()));
        return services;
    }
}
