using System.Linq.Expressions;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes;

/// <summary>
/// Ensures that the same validation applies to all commands that modify members (add, updates, etc...)
/// </summary>
/// <param name="repository"></param>
/// <param name="quoteRelationshipLoader"></param>
/// <param name="quoteMemberRelationshipLoader"></param>
public sealed class QuoteMemberValidationBehavior(
    IRepository<QuoteMember, string> repository,
    IQuoteRelationshipLoader quoteRelationshipLoader,
    IQuoteMemberRelationshipLoader quoteMemberRelationshipLoader) : IRepository<QuoteMember, string>
{
    public Task DeleteAsync(string id, CancellationToken cancellationToken) => repository.DeleteAsync(id, cancellationToken);
    public Task DeleteBatchAsync(List<string> ids, CancellationToken cancellationToken) => repository.DeleteBatchAsync(ids, cancellationToken);
    public Task DeleteBatchAsync(List<QuoteMember> agregates, CancellationToken cancellationToken) => repository.DeleteBatchAsync(agregates, cancellationToken);
    public Task<List<QuoteMember>> FindAllAsync(List<string> ids, CancellationToken cancellationToken) => repository.FindAllAsync(ids, cancellationToken);
    public Task<IEnumerable<QuoteMember>> FindAllByAsync(Expression<Func<QuoteMember, bool>> filterExpression, CancellationToken cancellationToken) => repository.FindAllByAsync(filterExpression, cancellationToken);
    public Task<QuoteMember> FindByIdAsync(string id, CancellationToken cancellationToken) => repository.FindByIdAsync(id, cancellationToken);
    public Task<QuoteMember> GetByIdAsync(string id, CancellationToken cancellationToken) => repository.GetByIdAsync(id, cancellationToken);
    public Task<QuoteMember> SingleOrDefaultAsync(CancellationToken cancellationToken) => repository.SingleOrDefaultAsync(cancellationToken);
    public Task<QuoteMember?> FindByOneAsync(Expression<Func<QuoteMember, bool>> predicate, CancellationToken cancellationToken) => repository.FindByOneAsync(predicate, cancellationToken);

    public async Task<QuoteMember> InsertAsync(QuoteMember entity, CancellationToken cancellationToken)
    {
        await ValidateMember(entity, cancellationToken);
        return await repository.InsertAsync(entity, cancellationToken);
    }

    public async Task InsertBatchAsync(List<QuoteMember> aggregates, CancellationToken cancellationToken)
    {
        foreach (var aggregate in aggregates)
        {
            await ValidateMember(aggregate, cancellationToken);
        }
        await repository.InsertBatchAsync(aggregates, cancellationToken);
    }

    public async Task<QuoteMember> UpdateAsync(QuoteMember entity, CancellationToken cancellationToken)
    {
        await ValidateMember(entity, cancellationToken);
        return await repository.UpdateAsync(entity, cancellationToken);
    }

    public async Task UpdateBatchAsync(List<QuoteMember> aggregates, CancellationToken cancellationToken)
    {
        foreach (var aggregate in aggregates)
        {
            await ValidateMember(aggregate, cancellationToken);
        }
        await repository.UpdateBatchAsync(aggregates, cancellationToken);
    }

    public async Task ValidateMember(
        QuoteMember quoteMember,
        CancellationToken cancellationToken = default)
    {
        await quoteMemberRelationshipLoader.LoadQuote(quoteMember, cancellationToken);

        await Task.WhenAll(
            [
                quoteMemberRelationshipLoader.LoadIndividual(quoteMember, cancellationToken),
                quoteMemberRelationshipLoader.LoadMember(quoteMember, cancellationToken),
                quoteMemberRelationshipLoader.LoadHealthQuestionnaireResponse(quoteMember, cancellationToken),
                quoteRelationshipLoader.LoadProductVersion(quoteMember.Quote, cancellationToken),
            ]
        );

        quoteMember.EnsureRelationshipInvariants();
    }

    public IPatchBuilder<QuoteMember> Patch(string entityId) => repository.Patch(entityId);
}
