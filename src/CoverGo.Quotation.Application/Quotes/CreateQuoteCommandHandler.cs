using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Commands.CreateOffer;
using CoverGo.Quotation.Application.Opportunities.Services;
using CoverGo.Quotation.Application.Proposals.Commands;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Repositories;
using CoverGo.Quotation.Application.Quotes.Contracts;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Offers.Billing;
using CoverGo.Quotation.Domain.Proposals.Exceptions;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Quotes.DomainEvents;

using MediatR;

namespace CoverGo.Quotation.Application.Quotes;

public record CreateQuotePayload
{
    public Quote? Quote { get; set; }

    public List<QuoteMember>? QuoteMembers { get; set; }
}

public record CreateQuoteCommand : ICommand<CreateQuotePayload>
{
    public required ProductVersionId ProductVersionId { get; init; }

    public required Id ClientId { get; init; }

    public required Id DistributorId { get; init; }

    public required PolicyDetailsInput PolicyDetails { get; init; }

    public required BillingInformationInput BillingInformation { get; init; }

    public required Id PrimaryAgentId { get; init; }

    public Id? SecondaryAgentId { get; init; }

    public ApplicationDetailsInput? ApplicationDetails { get; init; }

    public IEnumerable<ProposalLoadingInput>? Loadings { get; init; }

    public ProposalTax? Tax { get; init; }

    public IEnumerable<TaxInput>? TaxOverrides { get; init; }

    public IEnumerable<MemberInput>? Members { get; init; }
}

public class CreateQuoteCommandHandler(
    IRepository<Quote, string> quoteRepository,
    IDistributorRepository distributorRepository,
    IProductVersionRepository productVersionRepository,
    IAgentRepository agentRepository,
    IProposalCountryRepository countryRepository,
    IUserContextProvider userContextProvider,
    IMediator mediator,
    IMultiTenantFeatureManager tenantFeatureManager,
    TenantId tenantId,
    IMapper mapper) :
    ICommandHandler<CreateQuoteCommand, CreateQuotePayload>
{
    public async Task<CreateQuotePayload> Handle(CreateQuoteCommand command, CancellationToken cancellationToken)
    {
        var quoteId = Guid.NewGuid().ToString();
        var productVersion = await productVersionRepository.GetByIdAsync(command.ProductVersionId, cancellationToken);
        var distributor = await distributorRepository.GetByIdAsync(command.DistributorId, cancellationToken);
        var policyFields = command.PolicyDetails.Fields.Select(x => new PolicyField(x.Key, x.Value)).ToList();
        var contextUserId = userContextProvider.GetUserId();
        var loadings = command.Loadings?.Select(l => l.ToDomain(productVersion.Currency!, contextUserId!)).ToList();
        var primaryAgent = await agentRepository.GetByIdAsync(command.PrimaryAgentId, cancellationToken);
        var secondaryAgentId = command.SecondaryAgentId;
        if (command.SecondaryAgentId is not null)
        {
            var secondaryAgent = await agentRepository.GetByIdAsync(command.SecondaryAgentId, cancellationToken);
            secondaryAgentId = secondaryAgent.Id;
        }

        var useLegacyProposalTaxInput = await tenantFeatureManager.IsEnabled("UseLegacyProposalTaxInput", tenantId.Value);

        Quote quote;

        if (useLegacyProposalTaxInput)
        {
            var country = command.Tax?.Country;
            if (country is not null)
            {
                await ValidateCountry(country, cancellationToken);
            }

            quote = new Quote
            {
                Id = quoteId,
                ProductVersionId = productVersion.Id,
                ClientId = command.ClientId,
                DistributorId = distributor.Id,
                BillingInformation = mapper.Map<BillingInformation>(command.BillingInformation),
                PrimaryAgentId = primaryAgent.Id,
                SecondaryAgentId = secondaryAgentId?.Value,
                ApplicationDetails = mapper.Map<Domain.Proposals.ApplicationDetails>(command.ApplicationDetails),
                PolicyDetails = command.PolicyDetails.EndDate is null
                    ? PolicyDetails.OneYearLong(command.PolicyDetails.StartDate, policyFields)
                    : new PolicyDetails(command.PolicyDetails.StartDate, command.PolicyDetails.EndDate.Value, policyFields),
                Loadings = loadings,
                Tax = command.Tax is null ? null : new Domain.Proposals.ProposalTax(command.Tax.Rate, command.Tax.Country),
            };
        }
        else
        {
            quote = new Quote
            {
                Id = quoteId,
                ProductVersionId = productVersion.Id,
                ClientId = command.ClientId,
                DistributorId = distributor.Id,
                BillingInformation = mapper.Map<BillingInformation>(command.BillingInformation),
                PrimaryAgentId = primaryAgent.Id,
                SecondaryAgentId = secondaryAgentId?.Value,
                ApplicationDetails = mapper.Map<Domain.Proposals.ApplicationDetails>(command.ApplicationDetails),
                PolicyDetails = command.PolicyDetails.EndDate is null
                    ? PolicyDetails.OneYearLong(command.PolicyDetails.StartDate, policyFields)
                    : new PolicyDetails(command.PolicyDetails.StartDate, command.PolicyDetails.EndDate.Value, policyFields),
                Loadings = loadings,
                TaxOverrides = command.TaxOverrides?.Select(input => input.ToDomain(productVersion.Currency!)).ToList()
            };
        }

        quote = await quoteRepository.InsertAsync(quote, cancellationToken);
        var payload = new CreateQuotePayload { Quote = quote, QuoteMembers = [] };
        if (command.Members?.Any() == true)
        {
            payload.QuoteMembers = await mediator.Send(new AddQuoteMembersCommand
            {
                QuoteId = quote.Id,
                Members = command.Members,
            }, cancellationToken);
        }
        await mediator.Publish(new QuoteCreatedDomainEvent { QuoteId = quote.Id, QuoteMemberIds = [.. payload.QuoteMembers.Select(it => it.Id)] }, cancellationToken);
        return payload;
    }

    private async Task ValidateCountry(string country, CancellationToken cancellationToken)
    {
        var countries = await countryRepository.GetCountries(cancellationToken);

        if (!countries.Contains(country))
        {
            throw new CountryReferenceDataNotFoundException(country);
        }
    }
}
