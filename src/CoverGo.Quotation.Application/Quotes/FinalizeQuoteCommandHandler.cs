using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes;

public record FinalizeQuoteCommand : ICommand<Quote>
{
    public required Id QuoteId { get; init; }
}

public class FinalizeQuoteCommandHandler(
    IRepository<Quote, string> quoteRepository) :
    ICommandHandler<FinalizeQuoteCommand, Quote>
{
    public async Task<Quote> Handle(FinalizeQuoteCommand command, CancellationToken cancellationToken)
    {
        var quote = await quoteRepository.GetByIdAsync(command.QuoteId, cancellationToken);
        quote.SetFinalized();
        quote = await quoteRepository.UpdateAsync(quote, cancellationToken);
        return quote;
    }
}
