using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Policies;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Underwriting;
using CoverGo.Quotation.Domain.Underwriting.DomainEvents;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Quotes;

public record RequestUnderwritingForQuoteCommand : ICommand<Quote>
{
    public required Id QuoteId { get; init; }

    public required QuoteUnderwritingCaseInput QuoteUnderwritingCase { get; init; }
}

public record QuoteUnderwritingCaseInput(QuoteMemberMovementUnderwritingCaseInput? QuoteMemberMovementUnderwriting);

public class QuoteMemberMovementUnderwritingCaseInput
{
    public required Id PolicyId { get; init; }
}

public class RequestUnderwritingForQuoteCommandHandler(
    IRepository<Quote, string> quoteRepository,
    IRepository<UnderwritingCase, string> quoteUnderwritingCaseRepository,
    IPolicyRepository policyRepository,
    IQuoteRelationshipLoader quoteRelationshipLoader,
    IMediator mediator,
    ILogger<RequestUnderwritingForQuoteCommandHandler> logger
    ) :
    ICommandHandler<RequestUnderwritingForQuoteCommand, Quote>
{
    public async Task<Quote> Handle(RequestUnderwritingForQuoteCommand command, CancellationToken cancellationToken)
    {
        var quote = await quoteRepository.GetByIdAsync(command.QuoteId, cancellationToken);
        var quoteUnderwritingCaseId = Guid.NewGuid().ToString();
        await quoteRelationshipLoader.LoadPrimaryAgent(quote, cancellationToken);
        await quoteRelationshipLoader.LoadClient(quote, cancellationToken);
        var policy = await policyRepository.GetByIdAsync(command.QuoteUnderwritingCase.QuoteMemberMovementUnderwriting!.PolicyId.Value, cancellationToken);
        var underwritingCase = quote.RequestMemberMovementUnderwriting(
            quoteUnderwritingCaseId,
            new()
            {
                PolicyId = policy.Id.Value,
                PolicyNumber = policy.PolicyNumber!
            });
        quote = await quoteRepository.UpdateAsync(quote, cancellationToken);
        await quoteUnderwritingCaseRepository.InsertAsync(underwritingCase, cancellationToken);
        try
        {
            await mediator.Publish(new MemberMovementUnderwritingCaseCreatedDomainEvent { UnderwritingCaseId = underwritingCase.Id, QuoteId = quote.Id }, cancellationToken);
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error publishing MemberMovementUnderwritingCaseCreatedDomainEvent");
        }
        return quote;
    }
}
