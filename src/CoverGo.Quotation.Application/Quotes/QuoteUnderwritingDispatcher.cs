using CoverGo.Quotation.Domain.Quotes.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Quotes;

public class QuoteUnderwritingDispatcher(IQuoteUnderwritingUpdater quoteUnderwritingUpdater) :
    INotificationHandler<QuoteCreatedDomainEvent>,
    INotificationHandler<QuoteMemberCreatedDomainEvent>,
    INotificationHandler<QuoteMemberUpdatedDomainEvent>,
    INotificationHandler<MemberMovementUnderwritingCaseCreatedDomainEvent>
{
    public async Task Handle(QuoteCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await quoteUnderwritingUpdater.UpdateUnderwriting(notification.QuoteId, cancellationToken);
    }

    public async Task Handle(QuoteMemberCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await quoteUnderwritingUpdater.UpdateUnderwriting(notification.QuoteId, cancellationToken);
    }

    public async Task Handle(QuoteMemberUpdatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await quoteUnderwritingUpdater.UpdateUnderwriting(notification.QuoteId, cancellationToken);
    }

    public async Task Handle(MemberMovementUnderwritingCaseCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await quoteUnderwritingUpdater.UpdateUnderwriting(notification.QuoteId, cancellationToken);
    }
}
