using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Quotes.DomainEvents;

using MediatR;

namespace CoverGo.Quotation.Application.Quotes;

public record DeleteQuoteMembersCommand : ICommand<List<QuoteMember>>
{
    public required Id QuoteId { get; init; }

    public required List<Id> QuoteMemberIds { get; init; }
}

public class DeleteQuoteMembersCommandHandler(
    IRepository<Quote, string> quoteRepository,
    IQuoteMemberStateRelationshipLoader quoteMemberStateLoader,
    IRepository<QuoteMember, string> quoteMemberRepository,
    IMediator mediator) :
    ICommandHandler<DeleteQuoteMembersCommand, List<QuoteMember>>
{
    public async Task<List<QuoteMember>> Handle(DeleteQuoteMembersCommand command, CancellationToken cancellationToken)
    {
        var quote = await quoteRepository.GetByIdAsync(command.QuoteId, cancellationToken);

        var idsToMatch = command.QuoteMemberIds.Select(x => x.Value).ToList();
        var quoteMembersToDelete = (await quoteMemberRepository.FindAllByAsync(x => x.QuoteId == quote.Id && idsToMatch.Contains(x.Id), cancellationToken)).ToList();
        quote.DeleteMembers(quoteMembersToDelete);

        // Before deleting the dependent, clean up dependent states for primaries
        foreach (var quoteMember in quoteMembersToDelete)
        {
            quoteMember.States = await quoteMemberStateLoader.LoadDependentOfs(quoteMember.States, cancellationToken);
            foreach (var primary in quoteMember.Primaries)
            {
                primary.RemoveDependentState(quoteMember.Id);
            }
            await quoteMemberRepository.UpdateBatchAsync(quoteMember.Primaries, cancellationToken);
        }

        await quoteMemberRepository.DeleteBatchAsync(quoteMembersToDelete.Select(x => x.Id).ToList(), cancellationToken);
        await mediator.Publish(new QuoteMembersDeletedDomainEvent { QuoteId = quote.Id }, cancellationToken);
        return quoteMembersToDelete;
    }
}
