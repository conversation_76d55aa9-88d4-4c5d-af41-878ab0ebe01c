using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IQuoteRelationshipLoader
{
    public Task LoadProductVersion(Quote aggregate, CancellationToken cancellationToken = default);
    public Task LoadPrimaryAgent(Quote aggregate, CancellationToken cancellationToken = default);
    public Task LoadClient(Quote aggregate, CancellationToken cancellationToken = default);
    public Task LoadMembers(Quote aggregate, CancellationToken cancellationToken = default);
    public Task LoadMembers(Quote aggregate, HashSet<string> quoteMemberIds, CancellationToken cancellationToken = default);
}
