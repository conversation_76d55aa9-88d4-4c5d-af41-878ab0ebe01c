using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.HealthQuestionnaires;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IQuoteMemberRelationshipLoader
{
    public Task LoadQuote(QuoteMember aggregate, CancellationToken cancellationToken = default);

    public Task LoadIndividual(QuoteMember aggregate, CancellationToken cancellationToken = default);

    public Task LoadMember(QuoteMember aggregate, CancellationToken cancellationToken = default);

    public Task LoadHealthQuestionnaireResponse(QuoteMember aggregate, CancellationToken cancellationToken = default);

    public Task LoadHealthQuestionnaireResponses(IList<QuoteMember> aggregates, CancellationToken cancellationToken = default);
}

public sealed class QuoteMemberRelationshipLoader(
    IRepository<Quote, string> quoteRepository,
    IIndividualRepository individualRepository,
    IMemberRepository memberRepository,
    IHealthQuestionnaireResponseRepository healthQuestionnaireResponseRepository,
    IMapper mapper) : IQuoteMemberRelationshipLoader
{
    public async Task LoadQuote(QuoteMember aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.Quote = await quoteRepository.GetByIdAsync(aggregate.QuoteId, cancellationToken);
    }

    public async Task LoadIndividual(QuoteMember aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.IndividualId is null)
        {
            return;
        }
        aggregate.Individual = await individualRepository.GetByIdAsync(aggregate.IndividualId, cancellationToken);
    }

    public async Task LoadMember(QuoteMember aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.MemberId is null)
        {
            return;
        }
        aggregate.Member = await memberRepository.GetByIdAsync(aggregate.MemberId, cancellationToken);
    }

    public async Task LoadHealthQuestionnaireResponse(QuoteMember aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.HealthQuestionnaireResponseId is null)
        {
            return;
        }

        var existingResponse = await healthQuestionnaireResponseRepository.GetByIdAsync(aggregate.HealthQuestionnaireResponseId, cancellationToken);
        aggregate.HealthQuestionnaireResponse = MapHealthQuestionnaireResponse(existingResponse);
    }

    public async Task LoadHealthQuestionnaireResponses(IList<QuoteMember> aggregates, CancellationToken cancellationToken = default)
    {
        var ids = aggregates
            .Where(it => it.HealthQuestionnaireResponseId is not null)
            .ToDictionary(it => it.HealthQuestionnaireResponseId!, it => it);

        if (ids.Count == 0)
        {
            return;
        }

        var responses = await healthQuestionnaireResponseRepository.GetByIdsAsync([ ..ids.Keys], cancellationToken);
        foreach (var response in responses)
        {
            ids[response.Id].HealthQuestionnaireResponse = MapHealthQuestionnaireResponse(response);
        }
    }

    private static Domain.HealthQuestionnaires.HealthQuestionnaireResponse? MapHealthQuestionnaireResponse(Application.HealthQuestionnaires.HealthQuestionnaireResponse? response)
    {
        if (response is null)
        {
            return null;
        }

        return new Domain.HealthQuestionnaires.HealthQuestionnaireResponse()
        {
            Id = response.Id,
            Status = response.IsDraft ? Domain.HealthQuestionnaires.HealthQuestionnaireResponseStatus.Submitted : Domain.HealthQuestionnaires.HealthQuestionnaireResponseStatus.Published,
        };
    }
}
