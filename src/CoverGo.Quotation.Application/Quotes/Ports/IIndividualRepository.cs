using CoverGo.Quotation.Domain.Members;

namespace CoverGo.Quotation.Application.Quotes.Ports;

public interface IIndividualRepository
{
    /// <summary>
    /// Gets an individual by their ID
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<Individual> GetByIdAsync(string id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets individuals by their IDs in a single batch operation to prevent N+1 queries
    /// </summary>
    /// <param name="ids">Collection of individual IDs to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary mapping individual IDs to Individual objects</returns>
    Task<List<Individual>> GetByIdsAsync(ISet<string> ids, CancellationToken cancellationToken = default);
}
