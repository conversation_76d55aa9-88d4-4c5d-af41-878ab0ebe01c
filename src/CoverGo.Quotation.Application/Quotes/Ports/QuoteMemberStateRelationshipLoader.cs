using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Quotes.Extensions;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IQuoteMemberStateRelationshipLoader
{
    public Task<List<QuoteMemberState>> LoadDependentOfs(List<QuoteMemberState> states, CancellationToken cancellationToken = default);
}

public sealed class QuoteMemberStateRelationshipLoader(
    IRepository<QuoteMember, string> quoteMemberRepository) : IQuoteMemberStateRelationshipLoader
{
    public async Task<List<QuoteMemberState>> LoadDependentOfs(List<QuoteMemberState> states, CancellationToken cancellationToken = default)
    {
        var dependentOfIds = states
            .Where(it => it.Dependency != null)
            .Select(it => it.Dependency!.DependentOfId)
            .OfType<string>()
            .Distinct()
            .ToList();
        if (dependentOfIds.Count == 0)
        {
            return states;
        }
        var members = await quoteMemberRepository.GetAllByIdsAsync(dependentOfIds, cancellationToken);
        var membersDict = members.ToDictionary(it => it.Id, it => it);
        var newStates = states
            .Select(state => state with
            {
                Dependency = state.Dependency == null
                    ? null
                    : state.Dependency with
                    {
                        DependentOf = membersDict[state.Dependency.DependentOfId],
                    }
            })
            .ToList();
        return newStates;
    }
}
