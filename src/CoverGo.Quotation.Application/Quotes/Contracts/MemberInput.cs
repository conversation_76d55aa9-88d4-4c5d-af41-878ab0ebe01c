﻿using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes.Contracts;

public interface IMemberInput
{
    List<QuoteMemberState> States { get; init; }
    IReadOnlyList<FieldInput> Fields { get; init; }
}

public class MemberInput : IMemberInput
{
    public required Id? MemberId { get; init; }

    public required Id? IndividualId { get; init; }

    public required List<QuoteMemberState> States { get; init; }

    public required Id? HealthQuestionnaireResponseId { get; init; }

    public required MemberUnderwritingInput MemberUnderwriting { get; init; }

    private FieldsInput _fields = [];
    public IReadOnlyList<FieldInput> Fields { get => _fields; init => _fields = [.. value]; }
}
