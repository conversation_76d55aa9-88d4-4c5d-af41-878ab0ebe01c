using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Converters;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Quotes.Contracts;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Application.Locking;

namespace CoverGo.Quotation.Application.Quotes;

public record AddQuoteMemberCommand : ICommand<QuoteMember>, IMemberInput
{
    public required Id QuoteId { get; init; }

    public required Id? MemberId { get; init; }

    public required Id? IndividualId { get; init; }

    public required List<QuoteMemberState> States { get; init; }

    public required Id? HealthQuestionnaireResponseId { get; init; }

    public required MemberUnderwritingInput MemberUnderwriting { get; init; }

    private FieldsInput _fields = [];
    public IReadOnlyList<FieldInput> Fields { get => _fields; init => _fields = [.. value]; }
}

public record AddQuoteMembersCommand : ICommand<List<QuoteMember>>
{
    public required Id QuoteId { get; init; }
    public required IEnumerable<MemberInput> Members { get; init; }
}

public class AddQuoteMemberCommandHandler(
    IRepository<Quote, string> quoteRepository,
    IRepository<QuoteMember, string> quoteMemberRepository,
    IQuoteMemberStateRelationshipLoader quoteMemberStateLoader,
    IQuoteRelationshipLoader quoteRelationshipLoader,
    IMemberFieldsSchemaRepository schemaRepository,
    IBenefitDefinitionsRepository benefitDefinitionsRepository,
    IPrimaryQuoteMemberUpdater primaryQuoteMemberUpdater,
    IIndividualRepository individualRepository,
    IAggregateLock aggregateLock,
    IUserContextProvider userContext) :
    ICommandHandler<AddQuoteMemberCommand, QuoteMember>,
    ICommandHandler<AddQuoteMembersCommand, List<QuoteMember>>
{
    public async Task<QuoteMember> Handle(AddQuoteMemberCommand command, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<Quote>(command.QuoteId.Value, cancellationToken);
        var quote = await quoteRepository.GetByIdAsync(command.QuoteId, cancellationToken);
        var quoteMemberId = Guid.NewGuid().ToString();
        var states = await quoteMemberStateLoader.LoadDependentOfs(command.States, cancellationToken);
        await quoteRelationshipLoader.LoadProductVersion(quote, cancellationToken);
        await quoteRelationshipLoader.LoadClient(quote, cancellationToken);
        await quoteRelationshipLoader.LoadMembers(quote, cancellationToken);

        var productVersion = quote.ProductVersion;
        var schema = await schemaRepository.GetSchema(quote.Client, productVersion, cancellationToken);
        List<Id> benefitBusinessIds = command.MemberUnderwriting.BenefitsUnderwritings?.Select(u => u.ProductBenefitId).ToList() ?? [];
        List<BenefitDefinition> benefitDefinitions = benefitBusinessIds.Any() ? await benefitDefinitionsRepository.GetByIdsAsync(benefitBusinessIds, cancellationToken) : [];
        var clientId = command.IndividualId;
        var individualId = default(ValueObjectId<Individual>);
        if (clientId is not null)
        {
            var client = await individualRepository.GetByIdAsync(clientId, cancellationToken);
            individualId = client.Id;
        }
        var quoteMember = quote.AddMember(new QuoteMember
        {
            Id = quoteMemberId,
            QuoteId = quote.Id,
            MemberId = command.MemberId,
            IndividualId = individualId?.Value,
            States = states,
            MemberUnderwriting = command.MemberUnderwriting.ToDomain(productVersion.Currency!, benefitDefinitions, userContext.GetUserId()!),
            HealthQuestionnaireResponseId = command.HealthQuestionnaireResponseId,
            Fields = new(command.Fields.Select(f => new CustomField<QuoteMember>(f.Key, f.Value.ToObject()))),
        }, schema);
        quoteMember = await quoteMemberRepository.InsertAsync(quoteMember, cancellationToken);
        await primaryQuoteMemberUpdater.UpdatePrimaries(quoteMember, cancellationToken);
        await quoteRepository.UpdateAsync(quote, cancellationToken);
        return quoteMember;
    }

    public async Task<List<QuoteMember>> Handle(AddQuoteMembersCommand command, CancellationToken cancellationToken)
    {
        var quote = await quoteRepository.GetByIdAsync(command.QuoteId, cancellationToken);
        await quoteRelationshipLoader.LoadProductVersion(quote, cancellationToken);
        await quoteRelationshipLoader.LoadClient(quote, cancellationToken);
        await quoteRelationshipLoader.LoadMembers(quote, cancellationToken);

        var productVersion = quote.ProductVersion;
        var schema = await schemaRepository.GetSchema(quote.Client, productVersion, cancellationToken);
        List<Id> benefitBusinessIds = command.Members.SelectMany(m => m.MemberUnderwriting.BenefitsUnderwritings?.Select(u => u.ProductBenefitId) ?? []).ToList();
        List<BenefitDefinition> benefitDefinitions = benefitBusinessIds.Any() ? await benefitDefinitionsRepository.GetByIdsAsync(benefitBusinessIds, cancellationToken) : [];
        var quoteMembers = new List<QuoteMember>();
        foreach (var member in command.Members)
        {
            var quoteMemberId = Guid.NewGuid().ToString();
            var states = await quoteMemberStateLoader.LoadDependentOfs(member.States, cancellationToken);
            quoteMembers.Add(quote.AddMember(new QuoteMember
            {
                Id = quoteMemberId,
                QuoteId = quote.Id,
                MemberId = member.MemberId,
                IndividualId = member.IndividualId,
                States = states,
                MemberUnderwriting = member.MemberUnderwriting.ToDomain(productVersion.Currency!, benefitDefinitions, userContext.GetUserId()!),
                HealthQuestionnaireResponseId = member.HealthQuestionnaireResponseId,
                Fields = new(member.Fields.Select(f => new CustomField<QuoteMember>(f.Key, f.Value.ToObject()))),
            }, schema));
        }
        await quoteMemberRepository.InsertBatchAsync(quoteMembers, cancellationToken);
        await primaryQuoteMemberUpdater.UpdatePrimaries(quoteMembers, cancellationToken);
        return quoteMembers;
    }
}
