using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.BuildingBlocks.CustomFields.Converters;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.Quotes;

public record UpdateQuoteMemberCommand : ICommand<QuoteMember>
{
    public required Id QuoteId { get; init; }

    public required Id QuoteMemberId { get; init; }

    public SettableOfNullable<Id?>? MemberId { get; init; }

    public SettableOfNullable<Id?>? IndividualId { get; init; }

    public Settable<List<QuoteMemberState>>? States { get; init; }

    public SettableOfNullable<Id?>? HealthQuestionnaireResponseId { get; init; }

    public Settable<MemberUnderwritingInput>? MemberUnderwriting { get; init; }

    public Settable<IReadOnlyList<FieldInput>>? Fields { get; set; }
}

public class UpdateQuoteMemberCommandHandler(
    IRepository<Quote, string> quoteRepository,
    IRepository<QuoteMember, string> quoteMemberRepository,
    IQuoteMemberStateRelationshipLoader quoteMemberStateLoader,
    IPrimaryQuoteMemberUpdater primaryQuoteMemberUpdater,
    IQuoteRelationshipLoader quoteRelationshipLoader,
    IMemberFieldsSchemaRepository schemaRepository,
    IBenefitDefinitionsRepository benefitDefinitionsRepository,
    IUserContextProvider userContext) :
    ICommandHandler<UpdateQuoteMemberCommand, QuoteMember>
{
    public async Task<QuoteMember> Handle(UpdateQuoteMemberCommand command, CancellationToken cancellationToken)
    {
        var quote = await quoteRepository.GetByIdAsync(command.QuoteId, cancellationToken);
        await quoteRelationshipLoader.LoadProductVersion(quote, cancellationToken);
        await quoteRelationshipLoader.LoadClient(quote, cancellationToken);
        await quoteRelationshipLoader.LoadMembers(quote, cancellationToken);

        var productVersion = quote.ProductVersion;
        var schema = await schemaRepository.GetSchema(quote.Client, productVersion, cancellationToken);

        var quoteMember = await quoteMemberRepository.GetByIdAsync(command.QuoteMemberId, cancellationToken);

        if (command.MemberId != null)
            quoteMember.UpdateMemberId(command.MemberId.Value?.Value);

        if (command.IndividualId != null)
            quoteMember.UpdateIndividualId(command.IndividualId.Value?.Value);

        if (command.States != null)
        {
            var states = await quoteMemberStateLoader.LoadDependentOfs(command.States.Value, cancellationToken);
            quoteMember.States = states;
        }

        if (command.HealthQuestionnaireResponseId != null)
            quoteMember.UpdateHealthQuestionnaireId(command.HealthQuestionnaireResponseId.Value?.Value);

        if (command.Fields != null)
            quoteMember.Fields = new(command.Fields.Value.Select(f => new CustomField<QuoteMember>(f.Key, f.Value.ToObject())));

        if (command.MemberUnderwriting != null)
        {
            List<Id> benefitBusinessIds = command.MemberUnderwriting.Value.BenefitsUnderwritings?.Select(u => u.ProductBenefitId).ToList() ?? [];
            List<BenefitDefinition> benefitDefinitions = benefitBusinessIds.Any() ? await benefitDefinitionsRepository.GetByIdsAsync(benefitBusinessIds, cancellationToken) : [];
            quoteMember.UpdateUnderwritingWithNeedsUnderwriting(command.MemberUnderwriting.Value.ToDomain(productVersion.Currency!, benefitDefinitions, userContext.GetUserId()!));
        }
        quoteMember = quote.UpdateMember(quoteMember, schema);
        await quoteMemberRepository.UpdateAsync(quoteMember, cancellationToken);
        await primaryQuoteMemberUpdater.UpdatePrimaries(quoteMember, cancellationToken);
        await quoteRepository.UpdateAsync(quote, cancellationToken);
        return quoteMember;
    }
}
