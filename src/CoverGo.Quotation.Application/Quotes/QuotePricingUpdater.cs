using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Pricing;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Quotes.DomainEvents;

using MediatR;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Quotes;

public interface IQuotePricingUpdater
{
    public Task UpdateQuotePricing(string quoteId, CancellationToken cancellationToken);
}

public class QuotePricingUpdater(
    IRepository<Quote, string> quoteRepository,
    IRepository<QuoteMember, string> quoteMemberRepository,
    IQuoteRelationshipLoader quoteRelationshipLoader,
    IPremiums premiums,
    ILogger<QuotePricingUpdater> logger) :
    IQuotePricingUpdater,
    INotificationHandler<QuoteMembersDeletedDomainEvent>,
    INotificationHandler<QuoteLoadingsChangedDomainEvent>,
    INotificationHandler<QuoteMemberLoadingsChangedDomainEvent>,
    INotificationHandler<QuoteMemberBenefitLoadingsChangedDomainEvent>,
    INotificationHandler<QuoteMemberBenefitChangedDomainEvent>,
    INotificationHandler<QuoteMemberUnderwritingStatusChangedDomainEvent>
{
    public Task Handle(QuoteMembersDeletedDomainEvent notification, CancellationToken cancellationToken)
        => UpdateQuotePricing(notification.QuoteId, cancellationToken);
    public Task Handle(QuoteLoadingsChangedDomainEvent notification, CancellationToken cancellationToken)
        => UpdateQuotePricing(notification.QuoteId, cancellationToken);
    public Task Handle(QuoteMemberLoadingsChangedDomainEvent notification, CancellationToken cancellationToken)
        => UpdateQuotePricing(notification.QuoteId, cancellationToken);
    public Task Handle(QuoteMemberBenefitLoadingsChangedDomainEvent notification, CancellationToken cancellationToken)
        => UpdateQuotePricing(notification.QuoteId, cancellationToken);
    public Task Handle(QuoteMemberBenefitChangedDomainEvent notification, CancellationToken cancellationToken)
        => UpdateQuotePricing(notification.QuoteId, cancellationToken);
    public Task Handle(QuoteMemberUnderwritingStatusChangedDomainEvent notification, CancellationToken cancellationToken)
        => UpdateQuotePricing(notification.QuoteId, cancellationToken);

    public async Task UpdateQuotePricing(string quoteId, CancellationToken cancellationToken)
    {
        using var _ = logger.BeginScope(new Dictionary<string, object?>
        {
            { "QuoteId", quoteId }
        });

        logger.LogInformation("Updating quote pricing for quote {QuoteId}", quoteId);

        var quote = await quoteRepository.GetByIdAsync(quoteId, cancellationToken);
        await quoteRelationshipLoader.LoadProductVersion(quote, cancellationToken);
        await quoteRelationshipLoader.LoadMembers(quote, cancellationToken);

        if (!quote.CanGetPremium())
        {
            logger.LogInformation("The quote {quoteId} does not have members eligible for calculating pricing, skipping pricing update", quoteId);
            return;
        }

        var premiumResult = await premiums.CalculatePremiumFor(
            quote,
            new Domain.Opportunities.DistributorId(quote.DistributorId),
            quote.ProductVersion,
            quote.Members,
            cancellationToken);

        foreach (var (key, pricing) in premiumResult.Members.MemberPremiums)
        {
            quote.Members.Single(it => it.Id == key).UpdatePricing(pricing);
        }

        quote.UpdatePricing(premiumResult.QuotationPricing, premiumResult.BillingPlan);

        await quoteRepository.UpdateAsync(quote, cancellationToken);
        await quoteMemberRepository.UpdateBatchAsync(quote.Members, cancellationToken);

        logger.LogInformation("Quote pricing updated for quote {QuoteId}", quoteId);
    }
}
