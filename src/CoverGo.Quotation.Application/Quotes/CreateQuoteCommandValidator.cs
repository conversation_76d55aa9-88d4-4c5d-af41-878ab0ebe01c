﻿using CoverGo.Quotation.Application.Offers.BillingInformation;
using FluentValidation;

namespace CoverGo.Quotation.Application.Quotes;

public class CreateQuoteCommandValidator : AbstractValidator<CreateQuoteCommand>
{
    public CreateQuoteCommandValidator(IBillingConfigurationRepository billingFrequencyRepository)
    {
        When(x => x.BillingInformation is not null, () => RuleFor(x => x.BillingInformation!.BillingFrequency)
            .MustAsync(async (x, ct) =>
            {
                var billingFrequencies = billingFrequencyRepository.GetBillingFrequencies(ct);

                return (await billingFrequencies).Contains(x);
            }).WithMessage("Invalid billing frequency"));
    }
}
