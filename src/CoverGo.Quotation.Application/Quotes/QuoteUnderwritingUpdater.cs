using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.HealthQuestionnaires;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Application.Underwriting.Ports;
using CoverGo.Quotation.Domain.Quotes;
using CoverGo.Quotation.Domain.Underwriting;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Quotes;

public interface IQuoteUnderwritingUpdater
{
    Task UpdateUnderwriting(string quoteId, CancellationToken cancellationToken = default);
}

public class QuoteUnderwritingUpdater(
    IRepository<Quote, string> quoteRepository,
    IRepository<QuoteMember, string> quoteMemberRepository,
    IQuoteRelationshipLoader quoteRelationshipLoader,
    IQuoteMemberRelationshipLoader quoteMemberRelationshipLoader,
    IHealthQuestionnaireResponseRepository healthQuestionnaireRepository,
    IRepository<UnderwritingCase, string> quoteUnderwritingCaseRepository,
    IUnderwritings underwritings,
    IQuotePricingUpdater quotePricingUpdater,
    ILogger<QuoteUnderwritingDispatcher> logger) : IQuoteUnderwritingUpdater
{
    public async Task UpdateUnderwriting(string quoteId, CancellationToken cancellationToken = default)
    {
        using var _ = logger.BeginScope(new Dictionary<string, object?>
        {
            { "Quote", quoteId }
        });

        var quote = await quoteRepository.GetByIdAsync(quoteId, cancellationToken);
        await quoteRelationshipLoader.LoadMembers(quote, cancellationToken);

        var membersToUnderwrite = quote.Members.Where(x => x.NeedsUnderwriting).ToList();
        if (membersToUnderwrite.Count < 1)
        {
            logger.LogInformation("No members to underwrite for quote {Quote}", quoteId);
        }
        else
        {
            await quoteMemberRelationshipLoader.LoadHealthQuestionnaireResponses(quote.Members, cancellationToken);
            await quoteRelationshipLoader.LoadProductVersion(quote, cancellationToken);

            var hcQuestionnaireResponses = (await healthQuestionnaireRepository
                .GetByIdsAsync([.. membersToUnderwrite.Where(x => (x as IUnderwritableMember).ShouldSendHealthQuestionnaireResponse()).Select(x => x.HealthQuestionnaireResponse?.Id.Value!).Where(x => x != null).Distinct()], cancellationToken))
                .ToDictionary(response => response.Id);

            var membersWithHqResponses = quote.Members
                .Select(x => (
                    (IUnderwritableMember)x,
                    x.HealthQuestionnaireResponse?.Id?.Value is string id ? hcQuestionnaireResponses.GetValueOrDefault(id) : null))
                .ToList();
            var membersUnderwriting = await underwritings.CalculateMembersUnderwriting(
                new UnderwritingRequest
                {
                    ProductVersion = quote.ProductVersion,
                    PolicyStartDate = quote.PolicyDetails.StartDate,
                    PolicyEndDate = quote.PolicyDetails.EndDate,
                    PolicyFields = quote.PolicyDetails.Fields.ToDictionary(x => x.Key, x => x.Value),
                    MembersWithQuestionnaireResponses = membersWithHqResponses,
                },
                cancellationToken);

            var membersToUpdate = membersToUnderwrite.Where(member =>
            {
                if (!membersUnderwriting.TryGetValue(member.Id, out var underwriting)) return false;
                return member.UpdateMemberAutoUnderwriting(underwriting);
            }).ToList();
            if (0 < membersToUpdate.Count) await quoteMemberRepository.UpdateBatchAsync(membersToUpdate, cancellationToken);

            if (quote.UnderwritingCaseId is string underwritingCaseId)
            {
                var uwCase = await quoteUnderwritingCaseRepository.GetByIdAsync(underwritingCaseId, cancellationToken);
                if (uwCase is MemberMovementUnderwritingCase mmUwCase)
                {
                    mmUwCase.OnAllMemberUnderwritingDone(quote.Members.Select(x => x.MemberUnderwriting).ToList());
                    await quoteUnderwritingCaseRepository.UpdateAsync(uwCase, cancellationToken);
                }
            }
        }

        await quotePricingUpdater.UpdateQuotePricing(quoteId, cancellationToken);
    }
}
