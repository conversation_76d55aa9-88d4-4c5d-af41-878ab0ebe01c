using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Quotes;

public interface IPrimaryQuoteMemberUpdater
{
    public Task UpdatePrimaries(QuoteMember quoteMember, CancellationToken cancellationToken = default);
    public Task UpdatePrimaries(IEnumerable<QuoteMember> quoteMembers, CancellationToken cancellationToken = default);
}

public sealed class PrimaryQuoteMemberUpdater(IRepository<QuoteMember, string> repository) : IPrimaryQuoteMemberUpdater
{
    public async Task UpdatePrimaries(QuoteMember quoteMember, CancellationToken cancellationToken = default)
    {
        var primaries = quoteMember.Primaries;
        if (primaries.Count == 0)
        {
            return;
        }
        await repository.UpdateBatchAsync(quoteMember.Primaries, cancellationToken);
    }

    public async Task UpdatePrimaries(IEnumerable<QuoteMember> quoteMembers, CancellationToken cancellationToken = default)
    {
        var primaries = quoteMembers.SelectMany(quoteMember => quoteMember.Primaries);
        if (!primaries.Any())
        {
            return;
        }
        await repository.UpdateBatchAsync(primaries.ToList(), cancellationToken);
    }
}
