using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Quotes.Extensions;

public static class RepositoryExtensions
{
    public static async Task<List<TEntity>> GetAllByIdsAsync<TEntity, TIdentity>(
        this IRepository<TEntity, TIdentity> repo,
        List<TIdentity> ids,
        CancellationToken cancellationToken = default)
        where TEntity : IAggregateRoot<TIdentity>
        where TIdentity : notnull
    {
        var result = await repo.FindAllAsync(ids, cancellationToken);
        var missingEntityIds = ids
            .Where(id => !result.Any(entity => EqualityComparer<TIdentity>.Default.Equals(entity.Id, id)))
            .ToList();
        if (missingEntityIds.Count == 1)
        {
            throw new EntityNotFoundException(new(missingEntityIds[0].ToString()!));
        }
        else if (missingEntityIds.Count > 1)
        {
            throw new AggregateException(missingEntityIds.Select(it => new EntityNotFoundException(new(it.ToString()!))));
        }
        return result;
    }
}
