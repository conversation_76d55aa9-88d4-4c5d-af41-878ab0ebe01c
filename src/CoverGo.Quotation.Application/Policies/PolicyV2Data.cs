using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Policies;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Policies;

/// <summary>
/// Databag with all the necessary aggregates/data for the IssuePolicyV2Adapter to work
/// </summary>
public record PolicyV2Data
{
    public ValueObjectId<Policy>? PolicyId { get; init; }
    public required OfferAggregate Offer { get; init; }
    public required ProposalAggregate Proposal { get; init; }
    public required IEnumerable<ProposalMemberAggregate> ProposalMembers { get; init; }
    public required OpportunityAggregate Opportunity { get; init; }
    public required ClientAggregate Client { get; init; }
    public required IReadOnlyList<ClientId> SupplementaryClientIds { get; init; }
    public required CurrencyCode ProductCurrency { get; init; }
    public PolicyIssuanceEmailMessage? EmailMessage { get; init; }
    public string? LastPolicyId { get; init; }
    public required PolicyV2DataRenewalSetting RenewalSetting { get; init; }
    public string? UserId { get; init; }
}

public record PolicyV2DataRenewalSetting(bool AutoRenewal, bool RenewalNotification, int LeadTimeDays);
