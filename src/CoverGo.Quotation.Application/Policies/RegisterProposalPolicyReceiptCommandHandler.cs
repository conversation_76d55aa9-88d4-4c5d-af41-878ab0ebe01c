using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Proposals;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Policies;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Clients;

namespace CoverGo.Quotation.Application.Policies;

public class RegisterProposalPolicyReceiptCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required PolicyReceipt Receipt { get; init; }
}

public class RegisterProposalPolicyReceiptCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IPolicyRepository policyRepository,
    IClientRepository clientRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IRenewalConfiguration renewalConfiguration,
    IMapper mapper,
    IAggregateLock aggregateLock) : ICommandHandler<RegisterProposalPolicyReceiptCommand, Proposal>
{
    public async Task<Proposal> Handle(RegisterProposalPolicyReceiptCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId, cancellationToken);
        var offer = await offerRepository.GetByIdAsync(proposal.OfferId, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);
        var policy = await policyRepository.GetByIdAsync(request.Receipt.PolicyId.Value, cancellationToken);

        await proposalRelationshipLoader.LoadOpportunity(proposal, cancellationToken);
        var opportunity = proposal.Opportunity;
        var renewalConfig = await renewalConfiguration.GetRenewalConfiguration(cancellationToken);

        var emailList = new List<string>();
        if (opportunity.ClientId is not null)
        {
            var mainClientId = new ValueObjectId<ClientAggregate>(opportunity.ClientId.Value);
            var mainHolder = await clientRepository.GetByIdAsync(mainClientId, cancellationToken);
            emailList.Add(mainHolder.Email);
        }

        var supplementaryClientsIds = opportunity.SupplementaryClientIds;
        var clients = await clientRepository.FindByIdsAsync(
            supplementaryClientsIds.Select(id => new ValueObjectId<ClientAggregate>(id.Value)).ToList(),
            cancellationToken
        );
        emailList.AddRange(clients.Select(client => client.Email));

        proposal.RegisterReceiptForPolicy(request.Receipt, productVersion, policy, opportunity, renewalConfig.RequiresInitialPremium, emailList);
        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        return mapper.Map<Proposal>(proposal);
    }
}
