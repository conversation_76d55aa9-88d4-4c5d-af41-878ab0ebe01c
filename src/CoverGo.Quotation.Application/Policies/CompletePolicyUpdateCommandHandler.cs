using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Policies;

public class CompletePolicyUpdateCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required ValueObjectId<Domain.Policies.Policy> PolicyId { get; init; }
}

public class CompletePolicyUpdateCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMapper mapper) : ICommandHandler<CompletePolicyUpdateCommand, Proposal>
{
    public async Task<Proposal> Handle(
        CompletePolicyUpdateCommand request,
        CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId, cancellationToken);
        var policy = proposal.Policies!.SingleOrDefault(it => it.Id == request.PolicyId)
            ?? throw new EntityNotFoundException(request.PolicyId);
        policy.CompleteUpdate();
        await proposalRepository
                .Patch(proposal.Id)
                .SetFirstElemMatch(p => p.Policies, it => it.Id == request.PolicyId, pcy => pcy.Status, policy.Status)
                .ExecuteAsync(cancellationToken);

        return mapper.Map<Proposal>(proposal);
    }
}
