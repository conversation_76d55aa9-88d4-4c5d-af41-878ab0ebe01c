using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Policies;

public class PolicyCreator(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IUserContextProvider userContext) :
    INotificationHandler<ProposalCreatedDomainEvent>,
    INotificationHandler<PolicyCreatedDomainEvent>
{
    public async Task Handle(ProposalCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(notification.ProposalId, cancellationToken);
        var userId = userContext.GetUserId();
        proposal.StartPolicyCreation(userId);
        await proposalRepository.UpdateAsync(proposal, cancellationToken);
    }

    public async Task Handle(PolicyCreatedDomainEvent notification, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(notification.ProposalId.Value, cancellationToken);
        proposal.Apply(notification);
        await proposalRepository.UpdateAsync(proposal, cancellationToken);
    }
}
