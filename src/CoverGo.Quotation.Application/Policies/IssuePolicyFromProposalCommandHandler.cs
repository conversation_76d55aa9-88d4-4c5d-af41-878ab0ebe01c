using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Opportunities.Services;
using CoverGo.Quotation.Application.Proposals;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Policies;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Policies;

public class IssuePolicyFromProposalCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required ValueObjectId<Domain.Policies.Policy> PolicyId { get; init; }
    public PolicyIssuanceEmailMessage? EmailMessage { get; init; }
}
public class IssuePolicyFromProposalCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IPolicyRepository policyRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IRenewalConfiguration renewalConfiguration,
    IMapper mapper,
    IUserContextProvider userContext
) : ICommandHandler<IssuePolicyFromProposalCommand, Proposal>
{
    public async Task<Proposal> Handle(IssuePolicyFromProposalCommand request, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId, cancellationToken);
        var offer = await offerRepository.GetByIdAsync(proposal.OfferId, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);
        var legacyPolicy = await policyRepository.GetByIdAsync(request.PolicyId.Value, cancellationToken);

        var opportunity = await opportunityRepository.GetByIdAsync(proposal.OpportunityId, cancellationToken);
        var renewalConfig = await renewalConfiguration.GetRenewalConfiguration(cancellationToken);
        var userId = userContext.GetUserId();

        proposal.RequestPolicyIssuance(request.PolicyId, productVersion, request.EmailMessage, legacyPolicy, opportunity, renewalConfig.RequiresInitialPremium, userId);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);

        return mapper.Map<Proposal>(proposal);
    }
}
