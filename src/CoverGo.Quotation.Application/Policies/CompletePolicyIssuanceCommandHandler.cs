using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Policies;

public class CompletePolicyIssuanceCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required ValueObjectId<Domain.Policies.Policy> PolicyId { get; init; }
}

public class CompletePolicyIssuanceCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMapper mapper,
    IAggregateLock aggregateLock) : ICommandHandler<CompletePolicyIssuanceCommand, Proposal>
{
    public async Task<Proposal> Handle(
        CompletePolicyIssuanceCommand request,
        CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId, cancellationToken);

        proposal.MarkPolicyAsIssued(request.PolicyId);
        await proposalRepository.UpdateAsync(proposal, cancellationToken);

        return mapper.Map<Proposal>(proposal);
    }
}
