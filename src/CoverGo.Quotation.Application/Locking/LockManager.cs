using System.Collections.Concurrent;

using DistributedLock.Mongo;

namespace CoverGo.Quotation.Application.Locking;

public record RegisteredLock(MongoLock<string> Lock, IAcquire Acquire);

public interface ILockManager
{
    void RegisterLock(RegisteredLock acquire);
    Task ReleaseAllLocks();
}

public sealed class LockManager : ILockManager
{
    private ConcurrentBag<RegisteredLock> _locks = new();

    public void RegisterLock(RegisteredLock acquire)
    {
        _locks.Add(acquire);
    }

    public async Task ReleaseAllLocks()
    {
        await Task.WhenAll(_locks.Select(it => it.Lock.ReleaseAsync(it.Acquire)));
        _locks.Clear();
    }
}
