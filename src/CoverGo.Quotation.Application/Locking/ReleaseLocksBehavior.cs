using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;

namespace CoverGo.Quotation.Application.Locking;

/// <summary>
/// Locks must be released only AFTER the transaction is committed or rolled back.
/// </summary>
/// <param name="unitOfWork"></param>
/// <param name="lockManager"></param>
public sealed class ReleaseLocksBehavior(IUnitOfWork unitOfWork, ILockManager lockManager) : IUnitOfWork
{
    public async Task CommitAsync()
    {
        await unitOfWork.CommitAsync();
        await ReleaseAllLocksAsync();
    }

    public void Dispose() => unitOfWork.Dispose();

    public Task OpenTransactionAsync() => unitOfWork.OpenTransactionAsync();

    public async Task RollbackAsync()
    {
        await unitOfWork.RollbackAsync();
        await ReleaseAllLocksAsync();
    }

    private async Task ReleaseAllLocksAsync(CancellationToken cancellationToken = default)
    {
        await lockManager.ReleaseAllLocks();
    }
}
