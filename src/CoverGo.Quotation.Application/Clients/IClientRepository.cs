using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.Clients;

public interface IClientRepository
{
    public Task<ClientAggregate> GetByIdAsync(ValueObjectId<ClientAggregate> id, CancellationToken cancellationToken = default);
    public Task<ClientAggregate?> FindByIdAsync(ValueObjectId<ClientAggregate> id, CancellationToken cancellationToken = default);
    public Task<IEnumerable<ClientAggregate>> FindByIdsAsync(IEnumerable<ValueObjectId<ClientAggregate>> ids, CancellationToken cancellationToken = default);
}
