namespace CoverGo.Quotation.Application.Pricing.Ports;

public interface IBillingRepository
{
    Task<Dictionary<string, string>> GetBillingFrequencyDisplayValues(
        IEnumerable<string> mnemonicCodes,
        string localeId,
        CancellationToken cancellationToken
    );

    Task<Dictionary<string, string>> GetBillingPricingDateDisplayValues(
        IEnumerable<string> mnemonicCodes,
        string localeId,
        CancellationToken cancellationToken
    );

    Task<Dictionary<string, string>> GetBillingChannelDisplayValues(
        IEnumerable<string> mnemonicCodes,
        string localeId,
        CancellationToken cancellationToken
    );
}
