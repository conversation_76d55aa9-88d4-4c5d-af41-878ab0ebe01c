using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Pricing;

namespace CoverGo.Quotation.Application.Pricing.Contracts;

public record BillingPlan
{
    public required IEnumerable<Installment> Installments { get; init; }
    public required IReadOnlyList<BenefitSummary> BenefitSummary { get; init; } = [];
}

public record Installment
{
    public required DateOnly From { get; init; }
    public required DateOnly To { get; init; }
    public required Money Premium { get; init; }
    public required Money PremiumBeforeTax { get; init; }
    public required Money Tax { get; init; }
    public required Money Fee { get; init; }
    public required PremiumBreakdown PremiumBreakdown { get; init; }
    public required Commissions Commissions { get; init; }
}

public record BenefitSummary
{
    public required string BenefitId { get; init; }
    public required string BenefitCode { get; init; }
    public string RiskCarrierCode { get; init; } = string.Empty; // TODO: add required after feature is stable
    public required Money PremiumRequired { get; init; }
    public required IReadOnlyList<PremiumElement> PremiumElements { get; init; }
    public required BenefitBreakdownCommissions Commissions { get; init; }
}

public record PremiumBreakdown
{
    public required PolicyBreakdown PolicyBreakdown { get; init; }
    public required IEnumerable<BenefitBreakdown> BenefitBreakdowns { get; init; }
    public AdjustmentBreakdown AdjustmentBreakdown { get; init; } = new() { PremiumElements = [] }; // TODO: remove default value and add required after feature is stable
}

public record PolicyBreakdown
{
    public required Money PremiumRequired { get; init; }
    public required IEnumerable<PremiumElement> PremiumElements { get; init; }
}

public record BenefitBreakdown
{
    public required string BenefitId { get; init; }
    public required string BenefitCode { get; init; }
    public string RiskCarrierCode { get; init; } = string.Empty;
    public required Money PremiumRequired { get; init; }
    public required IEnumerable<PremiumElement> PremiumElements { get; init; }
    public BenefitBreakdownCommissions Commissions { get; init; } = new() { InwardCommissions = new("USD", 0), OutwardCommissions = new() { Primary = new("USD", 0), Secondary = new("USD", 0) } }; // TODO: remove default value and add required after feature is stable
}

public record AdjustmentBreakdown
{
    public required IEnumerable<PremiumElement> PremiumElements { get; init; }
}

public record PremiumElement
{
    public required Money Amount { get; init; }
    public required string Element { get; init; }
    public required string Description { get; init; }
}

public record BenefitBreakdownCommissions
{
    public required Money InwardCommissions { get; init; }
    public required BenefitBreakdownOutwardCommissions OutwardCommissions { get; init; }
}
public record BenefitBreakdownOutwardCommissions
{
    public required Money Primary { get; init; }
    public required Money Secondary { get; init; }
}
