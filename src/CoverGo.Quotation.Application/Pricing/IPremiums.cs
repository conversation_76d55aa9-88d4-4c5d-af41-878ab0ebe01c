using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Pricing;

public interface IPremiums
{
    public Task<PremiumResult> CalculatePremiumFor(
        Quote quote,
        DistributorId distributorId,
        ProductVersion productVersion,
        List<QuoteMember> members,
        CancellationToken cancellationToken = default);
}
