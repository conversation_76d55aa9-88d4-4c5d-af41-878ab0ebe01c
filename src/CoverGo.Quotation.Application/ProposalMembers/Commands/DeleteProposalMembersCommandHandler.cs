using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

using MediatR;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands;

public interface IDeleteProposalMembersCommand
{
    IEnumerable<Id> ProposalMemberIds { get; }
}

public record DeleteProposalMembersCommand : ICommand<List<ProposalMember>>, IDeleteProposalMembersCommand
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required IEnumerable<Id> ProposalMemberIds { get; init; }
};

public record DeleteRenewalProposalMembersCommand : ICommand<List<ProposalMemberAggregate>>, IDeleteProposalMembersCommand
{
    public required ProposalAggregate Proposal { get; init; }
    public required IEnumerable<Id> ProposalMemberIds { get; init; }
};

public class DeleteProposalMembersCommandHandler(
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepo,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepo,
    IProposalRelationshipLoader proposalRelationshipLoader,
    ILogger<DeleteProposalMembersCommandHandler> logger,
    IMapper mapper,
    IAggregateLock aggregateLock,
    IMediator mediator)
    : ICommandHandler<DeleteProposalMembersCommand, List<ProposalMember>>, ICommandHandler<DeleteRenewalProposalMembersCommand, List<ProposalMemberAggregate>>
{
    public async Task<List<ProposalMember>> Handle(DeleteProposalMembersCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        //Might need to check the proposal status eventually
        ProposalAggregate proposal = await proposalRepo.GetByIdAsync(request.ProposalId, cancellationToken);

        var proposalMembersToDelete = await HandleInternal(request, proposal, async proposalMembersToDelete => await mediator.Publish(
            MembersDeleted<ProposalMemberAggregate>.For(proposal, [.. proposalMembersToDelete]),
            cancellationToken), cancellationToken:cancellationToken);

        return mapper.Map<List<ProposalMember>>(proposalMembersToDelete);
    }

    public async Task<List<ProposalMemberAggregate>> Handle(DeleteRenewalProposalMembersCommand request,
        CancellationToken cancellationToken)
    {
        return await HandleInternal(request, request.Proposal, onNotFound: invalidMemberIds =>
        {
            const string separator = ", ";
            throw new EntityNotFoundException(new ValueObjectId(string.Join(separator, invalidMemberIds)));
        }, cancellationToken: cancellationToken);
    }

    private async Task<List<ProposalMemberAggregate>> HandleInternal(
        IDeleteProposalMembersCommand request,
        ProposalAggregate proposal,
        Func<IEnumerable<ProposalMemberAggregate>, Task>? batchDeleted = default,
        Func<IEnumerable<ValueObjectId<ProposalMemberAggregate>>, Task>? onNotFound = default,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Deleting proposal members {@ProposalMemberIds} from proposal with id {ProposalId}", request.ProposalMemberIds, proposal.Id);

        var proposalMembersIdsToDelete = request.ProposalMemberIds.Select(x => new ValueObjectId<ProposalMemberAggregate>(x)).ToList();

        IEnumerable<ProposalMemberAggregate> proposalMembersToDelete = await proposalMemberRepo.FindAllByAsync(
            x => x.ProposalId == proposal.Id &&
                (proposalMembersIdsToDelete.Contains(x.Id) || proposalMembersIdsToDelete.Contains(x.DependentOf!)),
            cancellationToken);

        var count = proposalMembersToDelete.Count();
        if (onNotFound is not null)
        {
            if (count < proposalMembersIdsToDelete.Count)
            {
                var invalidMemberIds = proposalMembersIdsToDelete.Where(id => !proposalMembersToDelete.Any(member => member.Id == id));
                await onNotFound(invalidMemberIds);
            }
        }
        logger.LogInformation("Matched {Count} proposal members to delete", count);

        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
        await proposalRelationshipLoader.LoadPrimaryMemberIds(proposal, cancellationToken);
        proposal.DeleteMembers(proposalMembersToDelete, batchDeleted is null);
        await proposalMemberRepo.DeleteBatchAsync(proposalMembersToDelete.Select(x => x.Id).ToList(), cancellationToken);

        if (batchDeleted is not null)
        {
            await batchDeleted(proposalMembersToDelete);
        }

        return [..proposalMembersToDelete];
    }
}
