using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Helpers;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Validators;
using CoverGo.Quotation.Application.Proposals.Commands;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Application.Quotes.Extensions;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.HealthQuestionnaires;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;
using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands;

public interface IAddProposalMembersCommand
{
    IEnumerable<ProposalMemberInput> ProposalMembers { get; }
}

public record AddProposalMembersCommand(
    ValueObjectId<ProposalAggregate> ProposalId,
    IEnumerable<ProposalMemberInput> ProposalMembers) : ICommand<List<ProposalMember>>, IAddProposalMembersCommand;

public record AddRenewalProposalMembersCommand(
    ProposalAggregate Proposal,
    IEnumerable<ProposalMemberInput> ProposalMembers) : ICommand<List<ProposalMemberAggregate>>, IAddProposalMembersCommand;

public class AddProposalMembersCommandHandler(
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IPlanIdValidator planIdValidator,
    IProposalMemberValidator proposalMemberValidator,
    IIndividualRepository individualRepository,
    IProductVersionRepository productVersionRepository,
    IBenefitDefinitionsRepository benefitDefinitionsRepository,
    IUserContextProvider userContext,
    IMediator mediator,
    IAggregateLock aggregateLock,
    IMapper mapper)
    : ICommandHandler<AddProposalMembersCommand, List<ProposalMember>>, ICommandHandler<AddRenewalProposalMembersCommand, List<ProposalMemberAggregate>>
{
    public async Task<List<ProposalMember>> Handle(AddProposalMembersCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId.Value, cancellationToken);
        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
        var membersBatch = await HandleInternal(request, proposal, cancellationToken:cancellationToken);
        await mediator.Publish(MembersAdded<ProposalMemberAggregate>.For(membersBatch), cancellationToken);
        return mapper.Map<List<ProposalMember>>(membersBatch);
    }

    public async Task<List<ProposalMemberAggregate>> Handle(AddRenewalProposalMembersCommand request, CancellationToken cancellationToken)
    {
        var userId = userContext.GetUserId();
        var proposal = request.Proposal;
        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
        var membersBatch = await HandleInternal(request, proposal, async (member, productVersion, _) =>
        {
            if (member is RenewalProposalMemberInput renewalMember)
            {
                var underwriting = MemberUnderwriting.Accepted();
                if (renewalMember.MemberUnderwriting is { } memberUnderwriting)
                {
                    List<Id> benefitBusinessIds = memberUnderwriting.BenefitsUnderwritings?.Select(u => u.ProductBenefitId).ToList() ?? [];
                    List<BenefitDefinition> benefitDefinitions = benefitBusinessIds.Any() ? await benefitDefinitionsRepository.GetByIdsAsync(benefitBusinessIds, _) : [];
                    underwriting = MemberUnderwriting.Accepted(memberUnderwriting.ToDomain(productVersion.Currency!, benefitDefinitions, userId!));
                }

                var healthQuestionnaireResponse = default(HealthQuestionnaireResponse);
                if (renewalMember.HealthQuestionnaireResponseId is Id { Value: string } id)
                {
                    healthQuestionnaireResponse = new HealthQuestionnaireResponse { Id = id.Value, Status = HealthQuestionnaireResponseStatus.Published };
                }

                return (underwriting, healthQuestionnaireResponse);
            }

            return (default, default);
        }, cancellationToken);
        return membersBatch;
    }

    private async Task<List<ProposalMemberAggregate>> HandleInternal(
        IAddProposalMembersCommand request,
        ProposalAggregate proposal,
        Func<ProposalMemberInput, ProductVersion, CancellationToken, Task<(MemberUnderwriting? underwriting, HealthQuestionnaireResponse? healthQuestionnaireResponse)>>? moreInfo = default,
        CancellationToken cancellationToken = default)
    {
        var customFields = request.ProposalMembers.SelectMany(m => m.Fields.ToCustomFields<ProposalMemberAggregate>());
        proposalMemberValidator.ValidateFields(customFields);
        var primaryProposalMembersIds = request.ProposalMembers
            .Where(m => m.DependentOf != null)
            .Select(m => new ValueObjectId<ProposalMemberAggregate>(m.DependentOf!.Value))
            .Distinct()
            .ToList();
        var primaryProposalMembers = await proposalMemberRepository.GetAllByIdsAsync(primaryProposalMembersIds, cancellationToken);

        // Preload all individuals in a batch to avoid N+1 problem
        var individualIds = request.ProposalMembers
            .Where(m => m.IndividualId != null)
            .Select(m => m.IndividualId!.Value)
            .Distinct()
            .ToHashSet();

        var _ = await individualRepository.GetByIdsAsync(individualIds, cancellationToken);

        var membersBatch = new List<ProposalMemberAggregate>();
        var productVersion = await productVersionRepository.GetByIdAsync(proposal.Offer.ProductVersionId, cancellationToken);
        foreach (var member in request.ProposalMembers)
        {
            var (underwriting, healthQuestionnaireResponse) = default((MemberUnderwriting, HealthQuestionnaireResponse));
            if (moreInfo is not null)
            {
                (underwriting, healthQuestionnaireResponse) = await moreInfo.Invoke(member, productVersion, cancellationToken);
            }
            var proposalMemberAggr = await CreateProposalMember(member, proposal, member.DependentOf is null ? null : primaryProposalMembers.Single(it => it.Id.Value == member.DependentOf.Value), underwriting, healthQuestionnaireResponse, cancellationToken);

            membersBatch.Add(proposalMemberAggr);
            List<ProposalMemberInput>? dependents = member is RenewalProposalMemberInput renewalMember ? renewalMember.Dependents?.OfType<ProposalMemberInput>()?.ToList() : member.Dependents;
            if (member?.DependentOf == null && dependents?.Count > 0)
            {
                foreach (var dependent in dependents)
                {
                    if (moreInfo is not null)
                    {
                        (underwriting, healthQuestionnaireResponse) = await moreInfo.Invoke(dependent, productVersion, cancellationToken);
                    }
                    var dependentMember = await CreateProposalMember(dependent, proposal, proposalMemberAggr, underwriting, healthQuestionnaireResponse, cancellationToken);
                    membersBatch.Add(dependentMember);
                }
            }
        }

        var planIds = membersBatch.Select(it => it.PlanId).Distinct().ToList();
        foreach (var planId in planIds)
        {
            await planIdValidator.ValidatePlanId(planId, proposal.Offer.ProductVersionId, cancellationToken);
        }

        await proposalRelationshipLoader.LoadPrimaryMemberIds(proposal, cancellationToken);
        proposal.AddMembers(membersBatch, true);
        await proposalMemberRepository.InsertBatchAsync(membersBatch, cancellationToken);
        foreach (var member in membersBatch)
        {
            await mediator.Publish(new ProposalMemberAddedDomainEvent
            {
                ProposalId = proposal.Id.Value,
                ProposalMemberId = member.Id.Value,
            }, cancellationToken);
        }

        return membersBatch;
    }

    private async Task<ProposalMemberAggregate> CreateProposalMember(
        ProposalMemberInput input,
        ProposalAggregate proposal,
        ProposalMemberAggregate? parent,
        MemberUnderwriting? underwriting = default,
        HealthQuestionnaireResponse? healthQuestionnaireResponse = default,
        CancellationToken cancellationToken = default)
    {
        var memberId = await MemberIdHelper.GetMemberIdWithIndividualFallback(
            input.IndividualId?.Value,
            input.MemberId?.Value,
            individualRepository,
            cancellationToken);

        var member = new ProposalMemberAggregate
        {
            OpportunityId = proposal.OpportunityId,
            Id = Guid.NewGuid().ToString(),
            OfferMemberId = null,
            MemberId = memberId,
            IndividualId = input.IndividualId?.Value,
            ProposalId = proposal.Id,
            PlanId = input.PlanId?.Value,
            Class = input.ClassName,
            Fields = input.Fields.ToCustomFields<ProposalMemberAggregate>(),
            LegacyPolicyId = input.LegacyPolicyId?.Value,
            LegacyPolicyMemberId = input.LegacyPolicyMemberId?.Value,
            OfferId = proposal.OfferId,
            MemberUnderwriting = MemberUnderwriting.Pending(),
            HealthQuestionnaireResponse = healthQuestionnaireResponse,
            DocumentFolderId = new DocumentFolderId(Guid.NewGuid().ToString()),
        };
        member.SetDependentOf(parent);
        member.SetUnderwriting(underwriting);
        return member;
    }
}
