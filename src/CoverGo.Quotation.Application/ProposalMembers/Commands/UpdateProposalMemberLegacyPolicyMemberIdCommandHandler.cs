using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.ProposalMembers;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands;

public record UpdateProposalMemberLegacyPolicyMemberIdCommand : ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> ProposalMemberId { get; init; }

    public required ValueObjectId<LegacyPolicyMember> LegacyPolicyMemberId { get; init; }

    public required ValueObjectId<LegacyPolicy> LegacyPolicyId { get; init; }
}

public class UpdateProposalMemberLegacyPolicyMemberIdCommandHandler(
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> repository,
    IMapper mapper) : ICommandHandler<UpdateProposalMemberLegacyPolicyMemberIdCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(UpdateProposalMemberLegacyPolicyMemberIdCommand request, CancellationToken cancellationToken)
    {
        var member = await repository.GetByIdAsync(request.ProposalMemberId.Value, cancellationToken);
        member.LegacyPolicyMemberId = request.LegacyPolicyMemberId;
        member.LegacyPolicyId = request.LegacyPolicyId;
        member = await repository.UpdateAsync(member, cancellationToken);
        return mapper.Map<ProposalMember>(member);
    }
}
