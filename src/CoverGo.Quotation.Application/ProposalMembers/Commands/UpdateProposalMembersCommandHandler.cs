using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.DomainServices;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands;

public interface IUpdateProposalMembersCommand
{
    IReadOnlyList<ProposalMemberUpdateInput> Members { get; }
}

public record UpdateProposalMembersCommand(
    Id ProposalId,
    IReadOnlyList<ProposalMemberUpdateInput> Members) : ICommand<List<ProposalMember>>, IUpdateProposalMembersCommand;

public record UpdateRenewalProposalMembersCommand(
    ProposalAggregate Proposal,
    IReadOnlyList<ProposalMemberUpdateInput> Members) : ICommand<List<ProposalMemberAggregate>>, IUpdateProposalMembersCommand;

public class UpdateProposalMembersCommandHandler(
    IMediator mediator,
    IMapper mapper,
    IMemberRepository<ProposalMemberAggregate> proposalMemberRepository,
    IAggregateLock aggregateLock,
    IUpdateProposalMembersDomainService updateProposalMembersDomainService,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId)
    : ICommandHandler<UpdateProposalMembersCommand, List<ProposalMember>>, ICommandHandler<UpdateRenewalProposalMembersCommand, List<ProposalMemberAggregate>>
{
    public async Task<List<ProposalMember>> Handle(UpdateProposalMembersCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);

        // Step 1: Execute the basic updates through the domain service
        var result = await updateProposalMembersDomainService.UpdateProposalMembers(
            request.ProposalId,
            request.Members,
            cancellationToken);

        // Step 2: Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring for all updated members
            await benefitMirroringDomainService.HandleBulkMemberUpdates(
                result,
                request.Members,
                cancellationToken);
        }

        // Step 3: Save all updated members
        await proposalMemberRepository.UpdateBatchAsync([ ..result.UpdatedMembers.Select(it => it.Member)], cancellationToken);
        
        // Step 4: Publish events
        await mediator.Publish(
            new MembersUpdated<ProposalMemberAggregate>
            {
                Updates = result.ToMemberUpdatedEvents(),
                MemberHolderId = request.ProposalId.Value,
            },
            cancellationToken);
        
        // Publish individual domain events for each updated member
        foreach (var member in result.UpdatedMembers)
        {
            await mediator.Publish(new ProposalMemberUpdatedDomainEvent
            {
                ProposalId = request.ProposalId.Value,
                ProposalMemberId = member.Member.Id.Value,
            }, cancellationToken);
        }
        
        return [.. result.UpdatedMembers.Select(updatedMember => mapper.Map<ProposalMember>(updatedMember.Member))];
    }

    public async Task<List<ProposalMemberAggregate>> Handle(UpdateRenewalProposalMembersCommand request,
        CancellationToken cancellationToken)
    {
        var proposal = request.Proposal;
        return await HandleInternal(request, proposal.Id.Value, proposal: proposal, cancellationToken: cancellationToken);
    }

    private async Task<List<ProposalMemberAggregate>> HandleInternal(
        IUpdateProposalMembersCommand request,
        Id proposalId,
        ProposalAggregate? proposal = default,
        Func<UpdateProposalMembersResult, Task>? batchUpdated = default,
        CancellationToken cancellationToken = default)
    {
        // Step 1: Execute the basic updates through the domain service
        var result = proposal is null ? await updateProposalMembersDomainService.UpdateProposalMembers(
            proposalId,
            request.Members,
            cancellationToken) : await updateProposalMembersDomainService.UpdateProposalMembers(
            proposal,
            request.Members,
            true,
            cancellationToken);

        // Step 2: Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring for all updated members
            await benefitMirroringDomainService.HandleBulkMemberUpdates(
                result,
                request.Members,
                cancellationToken);
        }

        // Step 3: Save all updated members
        await proposalMemberRepository.UpdateBatchAsync([ ..result.UpdatedMembers.Select(it => it.Member)], cancellationToken);
        
        // Step 4: Publish events
        if (batchUpdated is not null)
        {
            await batchUpdated(result);
        }
        
        // Publish individual domain events for each updated member
        foreach (var member in result.UpdatedMembers)
        {
            await mediator.Publish(new ProposalMemberUpdatedDomainEvent
            {
                ProposalId = proposalId.Value,
                ProposalMemberId = member.Member.Id.Value,
            }, cancellationToken);
        }
        
        return [.. result.UpdatedMembers.Select(member => member.Member)];
    }
}
