using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting.Benefits;

using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands.Benefits;

public class DeleteProposalMemberBenefitLoadingCommand : ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> ProposalMemberId { get; init; }
    public required ValueObjectId<MemberBenefitUnderwriting> MemberBenefitUnderwritingId { get; init; }
    public required ValueObjectId<Domain.Members.MemberLoading> MemberBenefitLoadingId { get; init; }
}

public class DeleteProposalMemberBenefitLoadingCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository) : ICommandHandler<DeleteProposalMemberBenefitLoadingCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(DeleteProposalMemberBenefitLoadingCommand request, CancellationToken cancellationToken)
    {
        var proposalMember = await proposalMemberRepository.GetByIdAsync(request.ProposalMemberId, cancellationToken);

        proposalMember.MemberUnderwriting.DeleteBenefitLoading(request.MemberBenefitUnderwritingId, request.MemberBenefitLoadingId);

        proposalMember = await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);
        await mediator.Publish(
            new ProposalMemberBenefitLoadingsChangedDomainEvent
            {
                ProposalMember = proposalMember,
            },
            cancellationToken);
        return mapper.Map<ProposalMember>(proposalMember);
    }
}
