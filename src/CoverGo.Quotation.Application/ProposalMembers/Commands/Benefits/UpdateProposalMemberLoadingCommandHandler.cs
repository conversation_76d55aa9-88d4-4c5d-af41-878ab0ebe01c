using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting.Benefits;

using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands.Benefits;

public record UpdateProposalMemberBenefitLoadingCommand : ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> ProposalMemberId { get; init; }
    public required ValueObjectId<MemberBenefitUnderwriting> MemberBenefitUnderwritingId { get; init; }
    public required MemberLoadingUpdateInput MemberBenefitLoading { get; init; }
}

public class UpdateProposalMemberBenefitLoadingCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository) : ICommandHandler<UpdateProposalMemberBenefitLoadingCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(UpdateProposalMemberBenefitLoadingCommand request, CancellationToken cancellationToken)
    {
        var proposalMember = await proposalMemberRepository.GetByIdAsync(request.ProposalMemberId, cancellationToken);
        var benefitUW = proposalMember.MemberUnderwriting.BenefitsUnderwritings.Single(
    it => it.Id == request.MemberBenefitUnderwritingId);

        var loadingUpdateDefinition = request.MemberBenefitLoading.GetMemberLoadingUpdateInput();
        var loadingToUpdate = benefitUW.Loadings.Single(it => it.Id == loadingUpdateDefinition.Id);

        var offer = await offerRepository.GetByIdAsync(proposalMember.OfferId, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);

        var loading = request.MemberBenefitLoading.GetUpdatedLoading(loadingToUpdate, productVersion.Currency!);
        proposalMember.MemberUnderwriting.EditBenefitLoading(request.MemberBenefitUnderwritingId, loading);

        proposalMember = await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);
        await mediator.Publish(
            new ProposalMemberBenefitLoadingsChangedDomainEvent
            {
                ProposalMember = proposalMember,
            },
            cancellationToken);
        return mapper.Map<ProposalMember>(proposalMember);
    }
}
