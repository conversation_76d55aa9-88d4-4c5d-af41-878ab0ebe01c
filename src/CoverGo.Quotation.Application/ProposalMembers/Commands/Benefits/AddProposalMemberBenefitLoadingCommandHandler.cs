using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting.Benefits;

using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands.Benefits;

public class AddProposalMemberBenefitLoadingCommand : ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> ProposalMemberId { get; init; }
    public required ValueObjectId<MemberBenefitUnderwriting> MemberBenefitUnderwritingId { get; init; }
    public required MemberLoadingInput MemberBenefitLoading { get; init; }
}

public class AddProposalMemberBenefitLoadingCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IUserContextProvider userContextProvider) : ICommandHandler<AddProposalMemberBenefitLoadingCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(AddProposalMemberBenefitLoadingCommand request, CancellationToken cancellationToken)
    {
        var proposalMember = await proposalMemberRepository.GetByIdAsync(request.ProposalMemberId, cancellationToken);

        var offer = await offerRepository.GetByIdAsync(proposalMember.OfferId, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);

        var loading = request.MemberBenefitLoading.GetLoading(productVersion.Currency!, DateTime.UtcNow, userContextProvider.GetUserId()!);

        proposalMember.MemberUnderwriting.AddBenefitLoading(request.MemberBenefitUnderwritingId, loading);

        proposalMember = await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);
        await mediator.Publish(
            new ProposalMemberBenefitLoadingsChangedDomainEvent
            {
                ProposalMember = proposalMember,
            },
            cancellationToken);
        return mapper.Map<ProposalMember>(proposalMember);
    }
}
