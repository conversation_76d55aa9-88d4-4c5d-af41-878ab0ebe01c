using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands.Documents;

public record RemoveProposalMemberDocumentCommand: ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> ProposalMemberId { get; init; }
    public required string DocumentKey { get; init; }
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
}

public class RemoveProposalMemberDocumentCommandHandler(
    IMapper mapper,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IAggregateLock aggregateLock)
    : ICommandHandler<RemoveProposalMemberDocumentCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(RemoveProposalMemberDocumentCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.ToString(), cancellationToken);

        var proposalMember = await proposalMemberRepository.GetByIdAsync(request.ProposalMemberId, cancellationToken);

        proposalMember.RemoveDocument(request.DocumentKey);
        var savedMember = await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);

        return mapper.Map<ProposalMember>(savedMember);
    }
}
