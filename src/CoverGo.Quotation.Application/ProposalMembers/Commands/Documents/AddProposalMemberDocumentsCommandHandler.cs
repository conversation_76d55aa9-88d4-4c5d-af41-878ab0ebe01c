using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands.Documents;

public record AddProposalMemberDocumentsCommand: ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> ProposalMemberId { get; init; }
    public required IEnumerable<MemberDocumentInput> Documents { get; init; } = Enumerable.Empty<MemberDocumentInput>();
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required Id DocumentFolderId { get; init; }
}


public class AddProposalMemberDocumentsCommandHandler(
    IMapper mapper,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IAggregateLock aggregateLock,
    IUserContextProvider userContextProvider) : ICommandHandler<AddProposalMemberDocumentsCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(AddProposalMemberDocumentsCommand request, CancellationToken cancellationToken)
    {
        var loginId = userContextProvider.GetUserId();
        ArgumentNullException.ThrowIfNullOrEmpty(loginId);
        IEnumerable<MemberDocument> docs = request.Documents.Select(t => t.ToDomain());

        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.ToString(), cancellationToken);

        var proposalMember = await proposalMemberRepository.GetByIdAsync(request.ProposalMemberId, cancellationToken);

        var documentFolderId = new DocumentFolderId(request.DocumentFolderId);
        if (proposalMember.ShouldMigrateDocumentFolderId(documentFolderId))
        {
            proposalMember.MigrateDocumentFolderId(documentFolderId);
        }

        proposalMember.UploadDocuments(docs, loginId);
        var savedMember = await proposalMemberRepository.UpdateAsync(proposalMember, cancellationToken);

        return mapper.Map<ProposalMember>(savedMember);
    }
}
