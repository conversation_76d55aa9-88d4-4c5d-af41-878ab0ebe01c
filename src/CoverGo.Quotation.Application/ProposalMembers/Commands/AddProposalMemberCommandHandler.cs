using AutoMapper;

using MediatR;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Commands;
using CoverGo.Quotation.Application.Members.Helpers;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.DomainServices;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands;

public record AddProposalMemberCommand : IAddMemberCommand, ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }

    public Id? PlanId { get; init; }
    public string? ClassName { get; init; }
    public Id? DependentOf { get; init; }
    public Id? MemberId { get; init; }

    private FieldsInput _fields = [];
    public IReadOnlyList<FieldInput> Fields { get => _fields; init => _fields = [.. value]; }
    public ValueObjectId<ClientAggregate>? IndividualId { get; init; }
}

public class AddProposalMemberCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IPlanIdValidator planIdValidator,
    IAggregateLock aggregateLock,
    IIndividualRepository individualRepository,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId) : ICommandHandler<AddProposalMemberCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(AddProposalMemberCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);

        var customFields = request.Fields.ToCustomFields<ProposalMemberAggregate>();
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId.Value, cancellationToken);
        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);

        PlanId? planId = request.PlanId?.Value;
        
        ProposalMemberAggregate? primary = null;
        if (request.DependentOf is not null)
        {
            primary = await proposalMemberRepository.GetByIdAsync(request.DependentOf.Value, cancellationToken);
            
            // Check if benefit mirroring feature is enabled before applying mirroring
            var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
            if (isBenefitMirroringEnabled)
            {
                // Apply benefit mirroring for dependent members
                var (mirroredPlanId, mirroredFields) = await benefitMirroringDomainService.GetMirroredBenefitsFromPrimary(
                    proposal,
                    primary,
                    planId?.Value,
                    customFields,
                    cancellationToken);

                planId = mirroredPlanId is not null ? new PlanId(mirroredPlanId) : planId;
                customFields = mirroredFields;
            }
        }

        await planIdValidator.ValidatePlanId(planId, proposal.Offer.ProductVersionId, cancellationToken);

        if (request.IndividualId is not null)
        {
            await individualRepository.GetByIdAsync(request.IndividualId.Value, cancellationToken);
        }

        // Use the helper to get the member ID with fallback to individual's internal code
        var memberId = await MemberIdHelper.GetMemberIdWithIndividualFallback(
            request.IndividualId?.Value,
            request.MemberId?.Value,
            individualRepository,
            cancellationToken);

        var member = new ProposalMemberAggregate
        {
            Id = Guid.NewGuid().ToString(),
            OpportunityId = proposal.OpportunityId,
            OfferId = proposal.OfferId,
            OfferMemberId = null,
            ProposalId = proposal.Id,
            MemberId = memberId,
            PlanId = planId,
            Class = request.ClassName,
            Fields = customFields,
            MemberUnderwriting = Domain.Underwriting.MemberUnderwriting.Pending(),
            IndividualId = request.IndividualId?.Value,
            DocumentFolderId = new DocumentFolderId(Guid.NewGuid().ToString()),
        };

        if (primary is not null)
        {
            member.SetDependentOf(primary);
        }
        await proposalRelationshipLoader.LoadPrimaryMemberIds(proposal, cancellationToken);
        proposal.AddMembers([member]);

        member = await proposalMemberRepository.InsertAsync(member, cancellationToken);
        await mediator.Publish(
            new MemberAdded<ProposalMemberAggregate>
            {
                AddedMember = member,
                DependentOfValue = primary,
                MemberHolder = proposal,
            },
            cancellationToken);

        member = await proposalMemberRepository.GetByIdAsync(member.Id, cancellationToken);
        return mapper.Map<ProposalMember>(member);
    }
}
