using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Commands;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.ProposalMembers.DomainServices;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.SettableValues;

using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.Commands;

public record UpdateProposalMemberCommand : IUpdateMemberCommand<ProposalMemberAggregate>, ICommand<ProposalMember>
{
    public required ValueObjectId<ProposalMemberAggregate> Id { get; init; }

    public SettableOfNullable<Id?>? PlanId { get; init; }
    public SettableOfNullable<string?>? ClassName { get; init; }
    public SettableOfNullable<Id?>? DependentOf { get; init; }
    public Settable<IReadOnlyList<FieldInput>>? Fields { get; init; }
    public SettableOfNullable<Id?>? IndividualId { get; init; }
}

public class UpdateProposalMemberCommandHandler(
    IMapper mapper,
    IMediator mediator,
    IMemberRepository<ProposalMemberAggregate> proposalMemberRepository,
    IAggregateLock aggregateLock,
    IUpdateProposalMembersDomainService updateProposalMembersDomainService,
    IBenefitMirroringDomainService benefitMirroringDomainService,
    IMultiTenantFeatureManager multiTenantFeatureManager,
    TenantId tenantId) : ICommandHandler<UpdateProposalMemberCommand, ProposalMember>
{
    public async Task<ProposalMember> Handle(UpdateProposalMemberCommand request, CancellationToken cancellationToken)
    {
        var proposalMember = await proposalMemberRepository.GetByIdAsync(request.Id.Value, cancellationToken);
        await aggregateLock.TakeLockAsync<ProposalAggregate>(proposalMember.ProposalId.Value, cancellationToken);

        var updateInput = new ProposalMemberUpdateInput
        {
            Id = request.Id.Value,
            PlanId = request.PlanId,
            ClassName = request.ClassName,
            DependentOf = request.DependentOf,
            Fields = request.Fields,
            IndividualId = request.IndividualId,
        };

        // Use the domain service to handle the basic update
        var result = await updateProposalMembersDomainService.UpdateProposalMembers(
            proposalMember.ProposalId.Value,
            [updateInput],
            cancellationToken);

        // Check if benefit mirroring feature is enabled before applying mirroring
        var isBenefitMirroringEnabled = await multiTenantFeatureManager.IsEnabled("UseBenefitMirroring", tenantId.Value);
        if (isBenefitMirroringEnabled)
        {
            // Apply benefit mirroring for all updated members
            await benefitMirroringDomainService.HandleBulkMemberUpdates(
                result,
                [updateInput],
                cancellationToken);
        }

        // Save all updated members (including those affected by benefit mirroring)
        await proposalMemberRepository.UpdateBatchAsync(
            [.. result.UpdatedMembers.Select(it => it.Member)],
            cancellationToken);

        // Publish MemberUpdated events for all updated members
        var memberUpdatedEvents = result.ToMemberUpdatedEvents();
        foreach (var memberUpdatedEvent in memberUpdatedEvents)
        {
            await mediator.Publish(memberUpdatedEvent, cancellationToken);
        }

        // Publish individual domain events for each updated member
        foreach (var member in result.UpdatedMembers)
        {
            await mediator.Publish(new ProposalMemberUpdatedDomainEvent
            {
                ProposalId = proposalMember.ProposalId.Value,
                ProposalMemberId = member.Member.Id.Value,
            }, cancellationToken);
        }

        // Find the originally requested member from the result
        var requestedMember = result.UpdatedMembers.FirstOrDefault(m => m.Member.Id.Value == request.Id.Value);
        if (requestedMember == null)
        {
            throw new InvalidOperationException($"Could not find updated member with ID {request.Id}");
        }

        return mapper.Map<ProposalMember>(requestedMember.Member);
    }
}
