using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.ProposalMembers.DomainServices;

public class UpdateProposalMembersResult
{
    public required ProposalAggregate Proposal { get; init; }
    public required List<UpdatedMemberResult> UpdatedMembers { get; init; }

    public record UpdatedMemberResult(ProposalMemberAggregate Member, ProposalMemberAggregate? Primary);

    public List<MemberUpdated<ProposalMemberAggregate>> ToMemberUpdatedEvents()
    {
        return [ ..UpdatedMembers.Select(updatedMember => new MemberUpdated<ProposalMemberAggregate>
        {
            MemberHolder = Proposal,
            UpdatedMember = updatedMember.Member,
            DependentOfValue = updatedMember.Primary
        })];
    }
}
