using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Multitenancy;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.OfferMembers.DomainServices;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Clients;
using CoverGo.Quotation.Domain.Common;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.ProposalMembers.DomainServices;

public class BenefitMirroringDomainService(
    IBenefitMirroringProductRepository benefitMirroringProductRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IClientRepository clientRepository,
    IMemberRepository<ProposalMemberAggregate> memberRepository,
    ILogger<BenefitMirroringDomainService> logger) : IBenefitMirroringDomainService
{
    private async Task<bool> HasBenefitMirroringEnabledAsync(ProposalAggregate proposal, CancellationToken cancellationToken = default)
    {
        try
        {
            await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
            return await benefitMirroringProductRepository.CheckProductHasBenefitMirroring(
                proposal.Offer.ProductVersionId,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to check benefit mirroring configuration for proposal {ProposalId}. Assuming disabled.",
                proposal.Id);
            return false;
        }
    }

    private async Task<(string? PlanId, CustomFields<ProposalMemberAggregate> Fields)> MirrorBenefitFields(
        ProposalAggregate proposal,
        ProposalMemberAggregate primaryMember,
        string? requestedPlanId,
        CustomFields<ProposalMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Load necessary relationships
            await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
            await proposalRelationshipLoader.LoadOpportunity(proposal, cancellationToken);

            // Get the client from the opportunity
            ClientAggregate? client = null;
            if (proposal.Opportunity?.ClientId is not null)
            {
                client = await clientRepository.GetByIdAsync(new ValueObjectId<ClientAggregate>(proposal.Opportunity.ClientId.Value), cancellationToken);
            }

            if (client is null)
            {
                logger.LogWarning("Cannot apply benefit mirroring for proposal {ProposalId}. Client not found.", proposal.Id);
                return (requestedPlanId, requestedFields);
            }

            // Get benefit fields from product - repository handles parsing inputSchema metadata
            var benefitFields = await benefitMirroringProductRepository.GetProductBenefitFields(
                proposal.Offer.ProductVersionId,
                cancellationToken);

            // Mirror plan ID from primary member
            var mirroredPlanId = primaryMember.PlanId?.Value ?? requestedPlanId;

            // Mirror benefit fields from primary member
            var mirroredFields = MirrorBenefitFieldsInternal(primaryMember, requestedFields, benefitFields);

            logger.LogInformation(
                "Applied benefit mirroring for proposal {ProposalId}. Primary member: {PrimaryMemberId}, Mirrored plan: {MirroredPlanId}, Benefit fields: {BenefitFields}",
                proposal.Id,
                primaryMember.Id,
                mirroredPlanId,
                string.Join(", ", benefitFields));

            return (mirroredPlanId, mirroredFields);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to apply benefit mirroring for proposal {ProposalId}. Falling back to original values.",
                proposal.Id);

            // Return original values if mirroring fails
            return (requestedPlanId, requestedFields);
        }
    }

    private static CustomFields<ProposalMemberAggregate> MirrorBenefitFieldsInternal(
        ProposalMemberAggregate primaryMember,
        CustomFields<ProposalMemberAggregate> requestedFields,
        HashSet<string> benefitFields)
    {
        var mirroredFields = new List<CustomField<ProposalMemberAggregate>>(requestedFields);

        // Mirror benefit fields from primary member
        foreach (var benefitFieldName in benefitFields)
        {
            var primaryField = primaryMember.Fields.FirstOrDefault(f => f.Key == benefitFieldName);
            if (primaryField is not null)
            {
                // Remove any existing field with the same name from requested fields
                mirroredFields.RemoveAll(f => f.Key == benefitFieldName);

                // Add the mirrored field
                mirroredFields.Add(new CustomField<ProposalMemberAggregate>(
                    primaryField.Key,
                    primaryField.Value));
            }
        }

        return new CustomFields<ProposalMemberAggregate>(mirroredFields);
    }

    /// <summary>
    /// Returns mirrored values from primary member if conditions are met (product supports it, and primary member exists).
    /// Returns the original values if conditions are not met.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    /// <param name="proposal">The proposal being processed</param>
    /// <param name="primaryMember">The primary member to mirror from (null if not a dependent)</param>
    /// <param name="requestedPlanId">The originally requested plan ID</param>
    /// <param name="requestedFields">The originally requested fields</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tuple of (final plan ID, final fields) after applying mirroring if applicable</returns>
    public async Task<(string? planId, CustomFields<ProposalMemberAggregate> fields)> GetMirroredBenefitsFromPrimary(
        ProposalAggregate proposal,
        ProposalMemberAggregate? primaryMember,
        string? requestedPlanId,
        CustomFields<ProposalMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default)
    {
        // If no primary member, return original values (not a dependent)
        if (primaryMember is null)
        {
            return (requestedPlanId, requestedFields);
        }

        // Check if product supports benefit mirroring
        var productHasBenefitMirroring = await HasBenefitMirroringEnabledAsync(proposal, cancellationToken);
        if (!productHasBenefitMirroring)
        {
            return (requestedPlanId, requestedFields);
        }

        // Apply benefit mirroring
        return await MirrorBenefitFields(
            proposal,
            primaryMember,
            requestedPlanId,
            requestedFields,
            cancellationToken);
    }

    /// <summary>
    /// Updates all dependents of a primary member to have the same planId and benefit fields.
    /// Returns the list of updated dependent members.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    public async Task<List<ProposalMemberAggregate>> UpdateAllDependentsFromPrimary(
        ProposalAggregate proposal,
        ProposalMemberAggregate primaryMember,
        CancellationToken cancellationToken = default)
    {
        var updatedDependents = new List<ProposalMemberAggregate>();

        // Check if benefit mirroring should be applied
        if (!await ShouldApplyBenefitMirroring(proposal, primaryMember, cancellationToken))
        {
            return updatedDependents;
        }

        try
        {
            // Find all dependents of this primary member
            var dependents = await memberRepository.FindAllByAsync(
                m => m.DependentOf == primaryMember.Id,
                cancellationToken);

            if (!dependents.Any())
            {
                return updatedDependents;
            }

            // Get benefit fields from product
            await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
            var benefitFields = await benefitMirroringProductRepository.GetProductBenefitFields(
                proposal.Offer.ProductVersionId,
                cancellationToken);

            // Update each dependent
            foreach (var dependent in dependents)
            {
                // Mirror plan ID from primary
                var originalPlanId = dependent.PlanId?.Value;
                var mirroredPlanId = primaryMember.PlanId?.Value;

                if (originalPlanId != mirroredPlanId)
                {
                    dependent.UpdatePlanId(mirroredPlanId);
                }

                // Mirror benefit fields from primary
                var originalFields = dependent.Fields;
                var mirroredFields = MirrorBenefitFieldsInternal(primaryMember, originalFields, benefitFields);

                if (!AreFieldsEqual(originalFields, mirroredFields))
                {
                    dependent.UpdateFields(mirroredFields);
                }

                updatedDependents.Add(dependent);
            }

            logger.LogInformation(
                "Synced {DependentCount} dependents from primary member {PrimaryMemberId} for proposal {ProposalId}. Benefit fields: {BenefitFields}",
                dependents.Count(),
                primaryMember.Id,
                proposal.Id,
                string.Join(", ", benefitFields));
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to sync dependents from primary member {PrimaryMemberId} for proposal {ProposalId}. Continuing without sync.",
                primaryMember.Id,
                proposal.Id);
        }

        return updatedDependents;
    }

    /// <summary>
    /// Gets primary first, then returns mirrored values for a dependent member update.
    /// Returns the original values if conditions are not met.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    public async Task<(string? planId, CustomFields<ProposalMemberAggregate> fields)> GetMirroredBenefitsForDependent(
        ProposalAggregate proposal,
        ProposalMemberAggregate dependentMember,
        string? requestedPlanId,
        CustomFields<ProposalMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default)
    {
        // If not a dependent, return original values
        if (dependentMember.DependentOf is null)
        {
            return (requestedPlanId, requestedFields);
        }

        try
        {
            // Get the primary member
            var primaryMember = await memberRepository.GetByIdAsync(dependentMember.DependentOf, cancellationToken);

            // Apply benefit mirroring using the existing method
            return await GetMirroredBenefitsFromPrimary(
                proposal,
                primaryMember,
                requestedPlanId,
                requestedFields,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex,
                "Failed to apply benefit mirroring for dependent member {DependentMemberId} for proposal {ProposalId}. Falling back to original values.",
                dependentMember.Id,
                proposal.Id);

            // Return original values if mirroring fails
            return (requestedPlanId, requestedFields);
        }
    }

    /// <summary>
    /// Handles the entire bulk update process including both dependent mirroring and primary syncing.
    /// NOTE: Feature flag should be checked at the command handler level before calling this method.
    /// </summary>
    public async Task HandleBulkMemberUpdates(
        UpdateProposalMembersResult result,
        IReadOnlyList<ProposalMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default)
    {
        var proposal = result.Proposal;

        // Check if product supports benefit mirroring - early return if not
        var productHasBenefitMirroring = await HasBenefitMirroringEnabledAsync(proposal, cancellationToken);
        if (!productHasBenefitMirroring)
        {
            return;
        }

        var updatedMembersById = result.UpdatedMembers.ToDictionary(m => m.Member.Id, m => m.Member);
        var memberUpdatesByIdString = memberUpdates.ToDictionary(u => u.Id.Value, u => u);

        // Step 1: Apply benefit mirroring for dependent member updates
        await ApplyBenefitMirroringForDependentUpdates(
            result,
            updatedMembersById,
            memberUpdatesByIdString,
            proposal,
            cancellationToken);

        // Step 2: Sync dependents from primary member updates
        var allSyncedDependents = await SyncDependentsFromPrimaryUpdates(
            result,
            memberUpdatesByIdString,
            proposal,
            cancellationToken);

        // Step 3: Add synced dependents to the result if they weren't already updated
        await AddSyncedDependentsToResult(result, allSyncedDependents, cancellationToken);
    }

    private async Task ApplyBenefitMirroringForDependentUpdates(
        UpdateProposalMembersResult result,
        Dictionary<ValueObjectId<ProposalMemberAggregate>, ProposalMemberAggregate> updatedMembersById,
        Dictionary<string, ProposalMemberUpdateInput> memberUpdatesByIdString,
        ProposalAggregate proposal,
        CancellationToken cancellationToken)
    {
        var dependentUpdates = result.UpdatedMembers
            .Where(m => m.Member.DependentOf is not null &&
                       memberUpdatesByIdString.ContainsKey(m.Member.Id.Value) &&
                       (memberUpdatesByIdString[m.Member.Id.Value].PlanId != null ||
                        memberUpdatesByIdString[m.Member.Id.Value].Fields != null))
            .ToList();

        foreach (var dependentUpdate in dependentUpdates)
        {
            var dependent = dependentUpdate.Member;
            var update = memberUpdatesByIdString[dependent.Id.Value];

            var requestedPlanId = update.PlanId?.Value?.Value;
            var requestedFields = update.Fields?.Value.ToCustomFields<ProposalMemberAggregate>() ?? dependent.Fields;

            // Get the primary member
            var primaryMember = updatedMembersById.TryGetValue(dependent.DependentOf!, out var primary)
                ? primary
                : await memberRepository.GetByIdAsync(dependent.DependentOf!, cancellationToken);

            // Apply benefit mirroring
            var (mirroredPlanId, mirroredFields) = await GetMirroredBenefitsFromPrimary(
                proposal,
                primaryMember,
                requestedPlanId,
                requestedFields,
                cancellationToken);

            // Update with mirrored values
            if (update.PlanId != null)
            {
                dependent.UpdatePlanId(mirroredPlanId);
            }

            if (update.Fields != null)
            {
                dependent.UpdateFields(mirroredFields);
            }
        }
    }

    private async Task<List<ProposalMemberAggregate>> SyncDependentsFromPrimaryUpdates(
        UpdateProposalMembersResult result,
        Dictionary<string, ProposalMemberUpdateInput> memberUpdatesByIdString,
        ProposalAggregate proposal,
        CancellationToken cancellationToken)
    {
        var primaryUpdates = result.UpdatedMembers
            .Where(m => m.Member.DependentOf is null &&
                       memberUpdatesByIdString.ContainsKey(m.Member.Id.Value) &&
                       (memberUpdatesByIdString[m.Member.Id.Value].PlanId != null ||
                        memberUpdatesByIdString[m.Member.Id.Value].Fields != null))
            .ToList();

        var allSyncedDependents = new List<ProposalMemberAggregate>();
        foreach (var primaryUpdate in primaryUpdates)
        {
            var primaryMember = primaryUpdate.Member;
            var syncedDependents = await UpdateAllDependentsFromPrimary(
                proposal,
                primaryMember,
                cancellationToken);

            allSyncedDependents.AddRange(syncedDependents);
        }

        return allSyncedDependents;
    }

    private async Task AddSyncedDependentsToResult(
        UpdateProposalMembersResult result,
        List<ProposalMemberAggregate> allSyncedDependents,
        CancellationToken cancellationToken)
    {
        if (allSyncedDependents.Any())
        {
            foreach (var syncedDependent in allSyncedDependents)
            {
                if (!result.UpdatedMembers.Any(m => m.Member.Id == syncedDependent.Id))
                {
                    // Find the primary member for this dependent
                    var primaryMember = syncedDependent.DependentOf is not null
                        ? await memberRepository.GetByIdAsync(syncedDependent.DependentOf, cancellationToken)
                        : null;

                    result.UpdatedMembers.Add(new UpdateProposalMembersResult.UpdatedMemberResult(syncedDependent, primaryMember));
                }
            }
        }
    }

    private async Task<bool> ShouldApplyBenefitMirroring(
        ProposalAggregate proposal,
        ProposalMemberAggregate? primaryMember,
        CancellationToken cancellationToken = default)
    {
        // If no primary member, don't apply
        if (primaryMember is null)
        {
            return false;
        }

        // Check if product supports benefit mirroring
        var productHasBenefitMirroring = await HasBenefitMirroringEnabledAsync(proposal, cancellationToken);
        if (!productHasBenefitMirroring)
        {
            return false;
        }

        return true;
    }

    private static bool AreFieldsEqual(
        CustomFields<ProposalMemberAggregate> fields1,
        CustomFields<ProposalMemberAggregate> fields2)
    {
        if (fields1.Count != fields2.Count)
        {
            return false;
        }

        var fields1Dict = fields1.ToDictionary(f => f.Key, f => f.Value);
        var fields2Dict = fields2.ToDictionary(f => f.Key, f => f.Value);

        foreach (var (key, value) in fields1Dict)
        {
            if (!fields2Dict.TryGetValue(key, out var otherValue))
            {
                return false;
            }

            // Handle null values properly
            if (value is null && otherValue is null)
            {
                continue;
            }

            if (value is null || otherValue is null)
            {
                return false;
            }

            if (!value.Equals(otherValue))
            {
                return false;
            }
        }

        return true;
    }
}
