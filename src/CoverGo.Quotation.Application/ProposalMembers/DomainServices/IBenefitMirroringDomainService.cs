using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.ProposalMembers.DomainServices;

public interface IBenefitMirroringDomainService
{
    /// <summary>
    /// Returns mirrored values from primary member if conditions are met (feature enabled, product supports it, and primary member exists).
    /// Returns the original values if conditions are not met.
    /// </summary>
    Task<(string? planId, CustomFields<ProposalMemberAggregate> fields)> GetMirroredBenefitsFromPrimary(
        ProposalAggregate proposal,
        ProposalMemberAggregate? primaryMember,
        string? requestedPlanId,
        CustomFields<ProposalMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates all dependents of a primary member to have the same planId and benefit fields.
    /// Returns the list of updated dependent members.
    /// </summary>
    Task<List<ProposalMemberAggregate>> UpdateAllDependentsFromPrimary(
        ProposalAggregate proposal,
        ProposalMemberAggregate primaryMember,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets primary first, then returns mirrored values for a dependent member update.
    /// Returns the original values if conditions are not met.
    /// </summary>
    Task<(string? planId, CustomFields<ProposalMemberAggregate> fields)> GetMirroredBenefitsForDependent(
        ProposalAggregate proposal,
        ProposalMemberAggregate dependentMember,
        string? requestedPlanId,
        CustomFields<ProposalMemberAggregate> requestedFields,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Handles the entire bulk update process including both dependent mirroring and primary syncing.
    /// </summary>
    Task HandleBulkMemberUpdates(
        UpdateProposalMembersResult result,
        IReadOnlyList<ProposalMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default);
}
