using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Members.Validators;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Proposals.Commands;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.HealthQuestionnaires;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.ProposalMembers.DomainServices;

public class UpdateProposalMembersDomainService(
    IPlanIdValidator planIdValidator,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IMemberRepository<ProposalMemberAggregate> memberRepository,
    IProductVersionRepository productVersionRepository,
    IBenefitDefinitionsRepository benefitDefinitionsRepository,
    IUserContextProvider userContext,
    IIndividualRepository individualRepository)
    : IUpdateProposalMembersDomainService
{
    /// <summary>
    /// Loads the offer, members, and updates them with the provided inputs
    /// </summary>
    public async Task<UpdateProposalMembersResult> UpdateProposalMembers(
        string proposalId,
        IReadOnlyList<ProposalMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default)
    {
        var proposal = await proposalRepository.GetByIdAsync(proposalId, cancellationToken);
        return await UpdateProposalMembers(proposal, memberUpdates, cancellationToken: cancellationToken);
    }

    public async Task<UpdateProposalMembersResult> UpdateProposalMembers(
        ProposalAggregate proposal,
        IReadOnlyList<ProposalMemberUpdateInput> memberUpdates,
        bool updateApprovedMembers = false,
        CancellationToken cancellationToken = default)
    {
        var memberIds = memberUpdates.Select(m => m.Id).ToList();
        var membersList = await memberRepository.FindAllAsync(memberIds, cancellationToken);
        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
        await proposalRelationshipLoader.LoadPrimaryMemberIds(proposal, cancellationToken);

        var membersById = membersList.ToDictionary(m => m.Id, m => m);

        await planIdValidator.ValidatePlanIds(
            [.. memberUpdates
                .Where(it => it.PlanId != null)
                .Select(it => it.PlanId!.Value is null ? null : new PlanId(it.PlanId.Value))],
            proposal.Offer.ProductVersionId,
            cancellationToken);

        // Track which members will need to be updated in the offer
        var membersToUpdate = memberUpdates
            .Select(update => !membersById.TryGetValue(update.Id, out var member) ? throw new EntityNotFoundException(update.Id) : member)
            .ToList();

        var updatedResults = new List<(ProposalMemberAggregate Member, ProposalMemberAggregate? Primary)>();

        // Process all updates
        var productVersion = await productVersionRepository.GetByIdAsync(proposal.Offer.ProductVersionId, cancellationToken);

        var userId = userContext.GetUserId();
        foreach (var update in memberUpdates)
        {
            var member = membersToUpdate.First(m => m.Id.Equals(update.Id));
            Func<Task<(ProposalMemberAggregate UpdatedMember, ProposalMemberAggregate? Primary)>> func = updateApprovedMembers
            ? async () =>
            {
                var updateResult = await UpdateSingleMember(
                    member,
                    update.ClassName,
                    update.PlanId,
                    update.DependentOf,
                    update.Fields,
                    update.IndividualId,
                    update,
                    productVersion,
                    userId,
                    cancellationToken);
                return updateResult;
            }
            : async () =>
            {
                var updateResult = await UpdateSingleMember(
                    member,
                    update.ClassName,
                    update.PlanId,
                    update.DependentOf,
                    update.Fields,
                    update.IndividualId,
                    cancellationToken: cancellationToken);
                return updateResult;
            };
            var memberResult = updateApprovedMembers ? await RenewMember(member, func) : await func();

            updatedResults.Add(memberResult);
        }

        var updatedMembers = updatedResults.Select(m => m.Member);
        proposal.UpdateMembers(updatedMembers);

        // Convert tuples to UpdatedMemberResult objects
        var updatedMemberResults = updatedResults.Select(
            r => new UpdateProposalMembersResult.UpdatedMemberResult(r.Member, r.Primary)).ToList();

        return new UpdateProposalMembersResult
        {
            Proposal = proposal,
            UpdatedMembers = updatedMemberResults
        };
    }

    private async Task<(ProposalMemberAggregate UpdatedMember, ProposalMemberAggregate? Primary)> RenewMember(ProposalMemberAggregate member, Func<Task<(ProposalMemberAggregate UpdatedMember, ProposalMemberAggregate? Primary)>> func)
    {
        member.MemberUnderwriting.Status = Domain.Underwriting.MemberUnderwritingStatus.Pending;
        var result = await func();
        member.MemberUnderwriting.Status = Domain.Underwriting.MemberUnderwritingStatus.Accepted;
        return result;
    }

    private async Task<(ProposalMemberAggregate UpdatedMember, ProposalMemberAggregate? Primary)> UpdateSingleMember(
        ProposalMemberAggregate proposalMember,
        SettableOfNullable<string?>? className,
        SettableOfNullable<Id?>? planId,
        SettableOfNullable<Id?>? dependentOf,
        Settable<IReadOnlyList<FieldInput>>? fields,
        SettableOfNullable<Id?>? individualId,
        ProposalMemberUpdateInput? updateInput = default,
        ProductVersion? productVersion = default,
        string? userId = default,
        CancellationToken cancellationToken = default)
    {
        if (className != null)
        {
            proposalMember.UpdateClass(className.Value ?? null);
        }

        if (planId != null)
        {
            var newPlanId = planId.Value?.Value ?? null;
            proposalMember.UpdatePlanId(newPlanId);
        }

        ProposalMemberAggregate? primary = null;

        if (dependentOf != null)
        {
            var newDependentOf = dependentOf.Value;
            if (newDependentOf is not null)
            {
                await memberRepository.LoadDependents(proposalMember, cancellationToken);
                primary = await memberRepository.GetByIdAsync(newDependentOf.Value, cancellationToken);
                proposalMember.SetDependentOf(primary);
            }
            else
            {
                proposalMember.BecomePrimary();
            }
        }
        else if (proposalMember.DependentOf is not null)
        {
            primary = await memberRepository.GetByIdAsync(proposalMember.DependentOf, cancellationToken);
        }

        if (fields != null)
        {
            var customFields = fields.Value.ToCustomFields<ProposalMemberAggregate>();
            proposalMember.UpdateFields(customFields);
        }

        if (individualId != null)
        {
            string? newIndividualId = individualId.Value?.Value;

            // Validate the individual ID if provided
            if (newIndividualId is not null)
            {
                await individualRepository.GetByIdAsync(newIndividualId, cancellationToken);
            }

            proposalMember.UpdateIndividualId(newIndividualId);
        }

        if (updateInput is RenewalProposalMemberUpdateInput { } renewalInput && productVersion is not null && userId is not null)
        {
            if (renewalInput.MemberUnderwriting?.Value is MemberUnderwritingInput { } memberUnderwriting)
            {
                List<Id> benefitBusinessIds = memberUnderwriting.BenefitsUnderwritings?.Select(u => u.ProductBenefitId).ToList() ?? [];
                List<BenefitDefinition> benefitDefinitions = benefitBusinessIds.Any() ? await benefitDefinitionsRepository.GetByIdsAsync(benefitBusinessIds, cancellationToken) : [];
                var underwriting = Domain.Underwriting.MemberUnderwriting.Accepted(memberUnderwriting.ToDomain(productVersion.Currency!, benefitDefinitions, userId));
                proposalMember.SetUnderwriting(underwriting);
            }

            if (renewalInput.HealthQuestionnaireResponseId?.Value is Id { Value: string } id)
            {
                proposalMember.HealthQuestionnaireResponse = new HealthQuestionnaireResponse { Id = id.Value, Status = HealthQuestionnaireResponseStatus.Published };
            }
        }

        return (proposalMember, primary);
    }
}
