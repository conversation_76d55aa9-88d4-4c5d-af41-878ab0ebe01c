using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.ProposalMembers.DomainServices;

public interface IUpdateProposalMembersDomainService
{
    /// <summary>
    /// Loads the proposal, offer, members, and updates them with the provided inputs
    /// </summary>
    Task<UpdateProposalMembersResult> UpdateProposalMembers(
        string proposalId,
        IReadOnlyList<ProposalMemberUpdateInput> memberUpdates,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Loads the offer, members, and updates proposal with the provided inputs
    /// </summary>
    Task<UpdateProposalMembersResult> UpdateProposalMembers(
        ProposalAggregate proposal,
        IReadOnlyList<ProposalMemberUpdateInput> memberUpdates,
        bool updateApprovedMembers = false,
        CancellationToken cancellationToken = default);
}
