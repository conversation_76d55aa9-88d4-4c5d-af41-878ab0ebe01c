using System.Globalization;
using CoverGo.Quotation.Application.Common;

namespace CoverGo.Quotation.Application.ProposalMembers.Contracts;

public record ProposalMemberInput
{
    public Id? PlanId { get; init; }
    public string? ClassName { get; init; }
    public Id? DependentOf { get; init; }
    public Id? MemberId { get; init; }
    public Id? IndividualId { get; init; }
    private FieldsInput _fields = [];
    public IReadOnlyList<FieldInput> Fields { get => _fields; init => _fields = [.. value]; }

    public List<ProposalMemberInput>? Dependents { get; init; }

    public Id? LegacyPolicyId { get; init; }
    public Id? LegacyPolicyMemberId { get; init; }

    internal DateOnly? DateOfBirth
    {
        get
        {
            var dob = Fields.FirstOrDefault(f => f.Key == "dateOfBirth")?.Value.GetString();
            return !string.IsNullOrEmpty(dob) && DateOnly.TryParse(dob, CultureInfo.InvariantCulture, out var date) ? date : null;
        }
    }
}
