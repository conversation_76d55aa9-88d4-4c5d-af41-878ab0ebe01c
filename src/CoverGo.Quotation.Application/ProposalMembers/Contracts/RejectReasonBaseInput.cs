﻿namespace CoverGo.Quotation.Application.ProposalMembers.Contracts;

public record RejectReasonBaseInput(OtherRejectReason? OtherRejectReason, RejectReason? RejectReason);

public abstract record RejectReasonBase(string RejectReasonCode);

public sealed record OtherRejectReason(string Reason) : RejectReasonBase(Domain.ProposalMembers.OtherRejectReason.OtherRejectReasonCode);
public sealed record RejectReason(string RejectReasonCode) : RejectReasonBase(RejectReasonCode);
