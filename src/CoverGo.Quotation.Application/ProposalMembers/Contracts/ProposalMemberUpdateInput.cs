using System.Globalization;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.SettableValues;

namespace CoverGo.Quotation.Application.ProposalMembers.Contracts;

public record ProposalMemberUpdateInput
{
    public required ValueObjectId<ProposalMemberAggregate> Id { get; init; }
    public SettableOfNullable<Id?>? PlanId { get; init; }
    public SettableOfNullable<string?>? ClassName { get; init; }
    public SettableOfNullable<Id?>? DependentOf { get; init; }
    public Settable<IReadOnlyList<FieldInput>>? Fields { get; init; }
    public SettableOfNullable<Id?>? IndividualId { get; init; }

    internal DateOnly? DateOfBirth
    {
        get
        {
            var dob = Fields?.Value.FirstOrDefault(f => f.Key == "dateOfBirth")?.Value.GetString();
            return !string.IsNullOrEmpty(dob) && DateOnly.TryParse(dob, CultureInfo.InvariantCulture, out var date) ? date : null;
        }
    }
}
