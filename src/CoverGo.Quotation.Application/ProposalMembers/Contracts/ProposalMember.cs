using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Members.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.HealthQuestionnaires;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.OfferMembers;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;
using CoverGo.Quotation.Domain.Underwriting.Benefits;

namespace CoverGo.Quotation.Application.ProposalMembers.Contracts;

public class ProposalMember : IMember<CustomFieldDto<ProposalMember>>, IUnderwritableMember
{
    public required AuditInfo AuditInfo { get; set; }
    public required Id Id { get; init; }
    public ValueObjectId<Member>? MemberId { get; init; }
    public Id? DependentOf { get; init; }
    public string? Class { get; init; }
    public string? PlanId { get; init; }
    public ValueObjectId<LegacyPolicyMember>? LegacyPolicyMemberId { get; init; }
    public ValueObjectId<HealthQuestionnaireResponse>? HealthQuestionnaireResponseId { get; init; }
    public MemberPricing? Pricing { get; init; }
    public IEnumerable<CustomFieldDto<ProposalMember>>? Fields { get; init; }
    public required Underwriting.Contracts.MemberUnderwriting MemberUnderwriting { get; init; }
    string IUnderwritableMember.PlanId => PlanId ?? string.Empty;
    bool IUnderwritableMember.NeedsUnderwriting => true;
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public ValueObjectId<OfferMemberAggregate>? OfferMemberId { get; init; }
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }

    public MemberType MemberType { get; init; }

    public DateOnly? DateOfBirth { get; init; }

    public RelationshipToPrimaryType? RelationshipToPrimary { get; init; }

    public HealthQuestionnaireResponse? HealthQuestionnaireResponse { get; init; }
    public required MemberUnderwriting InternalMemberUnderwriting { get; init; }

    public IEnumerable<MemberDocument>? Documents { get; init; }
    public Id? DocumentFolderId { get; init; }
    public ValueObjectId<Individual>? IndividualId { get; init; }

    string IUnderwritableMember.Id => Id;

    CustomFields<IUnderwritableMember> IUnderwritableMember.Fields
        => new CustomFields<IUnderwritableMember>(Fields?.Select(f => new CustomField<IUnderwritableMember>(f.Key, f.Value)) ?? []);

    MemberUnderwriting IUnderwritableMember.MemberUnderwriting => InternalMemberUnderwriting;

    public void Reject(Domain.ProposalMembers.RejectReasonBase rejectReason, bool cascadingFromPolicyLevel = false, IEnumerable<IUnderwritableMember>? dependents = null) => throw new NotImplementedException();
    public void AddBenefitLoading(ValueObjectId<MemberBenefitUnderwriting> benefitUWId, Domain.Members.MemberLoading memberLoading) => throw new NotImplementedException();
    public void DeleteBenefitLoading(ValueObjectId<MemberBenefitUnderwriting> benefitUWId, ValueObjectId<Domain.Members.MemberLoading> loadingId) => throw new NotImplementedException();
    public void UpdateBenefitLoading(ValueObjectId<MemberBenefitUnderwriting> benefitUWId, Domain.Members.MemberLoading memberLoading) => throw new NotImplementedException();
}
