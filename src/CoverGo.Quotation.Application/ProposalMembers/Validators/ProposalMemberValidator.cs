using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Members.Validators;

namespace CoverGo.Quotation.Application.ProposalMembers.Validators;

public interface IProposalMemberValidator : IMemberValidator
{
}

public class ProposalMemberValidator : MemberValidator, IProposalMemberValidator
{
    public new void ValidateFields(IEnumerable<CustomField> fields)
    {
        MemberValidator.ValidateFields(fields);
    }
}
