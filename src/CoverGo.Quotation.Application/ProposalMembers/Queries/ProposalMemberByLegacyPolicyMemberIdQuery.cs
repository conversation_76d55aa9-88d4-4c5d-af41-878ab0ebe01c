using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;

namespace CoverGo.Quotation.Application.ProposalMembers.Queries;

public record ProposalMemberByLegacyPolicyMemberIdQuery(
    ValueObjectId<LegacyPolicy> LegacyPolicyId,
    ValueObjectId<LegacyPolicyMember> LegacyPolicyMemberId) : IQuery<ProposalMember>;
