using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Domain.Members;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.ProposalMembers.Queries;

public class ProposalMembersWhere
{
    public IReadOnlyCollection<ProposalMembersWhere>? Or { get; set; }
    public IReadOnlyCollection<ProposalMembersWhere>? And { get; set; }
    public IdWhere<ProposalMemberAggregate>? Id { get; set; }
    public IdWhere<ProposalAggregate>? ProposalId { get; set; }
    public IdWhere<Member>? MemberId { get; set; }
    public IdWhere<OpportunityAggregate>? OpportunityId { get; set; }
    public IdWhere<OfferAggregate>? OfferId { get; set; }
    public FieldsWhere? Fields { get; set; }
    public MemberUnderwritingWhere? MemberUnderwriting { get; set; }
}

public class MemberUnderwritingWhere
{
    public MemberUnderwritingStatusWhere? Status { get; set; }
}
public class MemberUnderwritingStatusWhere
{
    public MemberUnderwritingStatus? Eq { get; set; }
    public List<MemberUnderwritingStatus>? In { get; set; }
}

public class ProposalMembersQuery
    : PagedQuery<ProposalMembersWhere>,
        IQuery<PagedResult<ProposalMember>>
{
    public ProposalMembersOrder[]? Order { get; set; }
}

public class ProposalMembersOrder
{
    public SortType? Id { get; set; }
    public SortType? MemberId { get; set; }
    public SortType? LegacyPolicyMemberId { get; set; }
}
