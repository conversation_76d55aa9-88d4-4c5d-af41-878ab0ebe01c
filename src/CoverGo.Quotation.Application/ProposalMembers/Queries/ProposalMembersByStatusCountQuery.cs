using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.ProposalMembers.Queries;

public class ProposalMembersByStatusCountQuery : IQuery<long>
{
    public MemberUnderwritingStatus? Status { get; init; }
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
}
