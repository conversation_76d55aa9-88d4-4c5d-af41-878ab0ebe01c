using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Common;

using static CoverGo.BuildingBlocks.CustomFields.Converters.JsonElementToObjectConverter;

namespace CoverGo.Quotation.Application.ProposalMembers;

public static class FieldInputExtensions
{
    public static CustomFields<T> ToCustomFields<T>(this IEnumerable<FieldInput> fields)
    {
        return [..fields
            .Select(it => new CustomField<T>(
                it.Key,
                it.Value.ToObject()))];
    }
}
