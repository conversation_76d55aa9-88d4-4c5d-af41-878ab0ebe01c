﻿using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.ProposalMembers.DomainEventHandlers;

// ReSharper disable once UnusedType.Global
public sealed class ProposalMemberBenefitUnderwritingStatusChangedHandler : INotificationHandler<ProposalMemberBenefitUnderwritingStatusChangedDomainEvent>
{
    public async Task Handle(ProposalMemberBenefitUnderwritingStatusChangedDomainEvent notification, CancellationToken cancellationToken)
    {
        //TODO: Call pricing here
    }
}
