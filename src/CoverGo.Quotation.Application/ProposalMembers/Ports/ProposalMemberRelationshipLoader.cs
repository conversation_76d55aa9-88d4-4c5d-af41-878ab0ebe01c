using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.ProposalMembers;

namespace CoverGo.Quotation.Application.ProposalMembers.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IProposalMemberRelationshipLoader
{
    public Task LoadPrimary(ProposalMemberAggregate proposalMemberAggregate, CancellationToken cancellationToken = default);
}

public sealed class ProposalMemberRelationshipLoader(
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository) : IProposalMemberRelationshipLoader
{
    public async Task LoadPrimary(ProposalMemberAggregate proposalMemberAggregate, CancellationToken cancellationToken = default)
    {
        if (proposalMemberAggregate is { DependentOf: not null })
        {
            proposalMemberAggregate.DependentOfValue = await proposalMemberRepository.GetByIdAsync(proposalMemberAggregate.DependentOf, cancellationToken);
        }
    }
}
