using System.Collections;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace CoverGo.Quotation.Application.Common;

/// <summary>
/// This is created so that we can use equality checks easily.
/// </summary>
[CollectionBuilder(typeof(FieldsInput), nameof(Create))]
public sealed record FieldsInput : IReadOnlyList<FieldInput>, IEnumerable<FieldInput>, IEquatable<FieldsInput>
{
    private readonly List<FieldInput> _fields = new();

    public int Count => ((IReadOnlyCollection<FieldInput>)_fields).Count;

    public FieldInput this[int index] => ((IReadOnlyList<FieldInput>)_fields)[index];

    public FieldsInput()
    {
    }

    public FieldsInput(IEnumerable<FieldInput> fields)
    {
        _fields.AddRange(fields);
    }

    public FieldsInput(ReadOnlySpan<FieldInput> fields)
    {
        _fields.AddRange(fields);
    }

    public IEnumerator<FieldInput> GetEnumerator() => _fields.GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    public bool Equals(FieldsInput? other) => other?._fields.SequenceEqual(_fields) ?? false;

    public override int GetHashCode() => HashCode.Combine(_fields);

    public static FieldsInput Create(ReadOnlySpan<FieldInput> items) => new(items);

    public static FieldsInput FromJsonToFieldInputs(JsonElement element)
    {
        IReadOnlyDictionary<string, JsonElement> ConvertToJsonDictionary(JsonElement element)
        {
            var dictionary = new Dictionary<string, JsonElement>();

            if (element.ValueKind == JsonValueKind.Object)
                foreach (var property in element.EnumerateObject())

                    dictionary[property.Name] = property.Value;

            return dictionary;
        }

        return [.. ConvertToJsonDictionary(element).Select(x => new FieldInput(x.Key, x.Value))];
    }
}
