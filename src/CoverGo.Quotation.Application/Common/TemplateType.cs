using CoverGo.Quotation.Domain.Offers;

namespace CoverGo.Quotation.Application.Common;

public enum TemplateType
{
    PdfDrawing,
    Wkhtmltopdf
}

public static class TemplateTypeExtensions
{
    public static TemplateType ToTemplateType(this ProductAttachedDocumentTemplateType type) => type switch
    {
        ProductAttachedDocumentTemplateType.PdfDrawing => TemplateType.PdfDrawing,
        ProductAttachedDocumentTemplateType.Wkhtmltopdf => TemplateType.Wkhtmltopdf,
        _ => throw new ArgumentOutOfRangeException(nameof(type), type, null)
    };
}
