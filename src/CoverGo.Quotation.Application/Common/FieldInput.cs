using System.Text.Json;

namespace CoverGo.Quotation.Application.Common;

public sealed record FieldInput(string Key, JsonElement Value)
{
    public FieldInput(string Key, object? Value) : this(Key, JsonDocument.Parse(JsonSerializer.Serialize(Value)).RootElement)
    {
    }

    public bool Equals(FieldInput? other) =>
        other is not null &&
        Key == other.Key &&
        Value.GetRawText() == other.Value.GetRawText();

    public override int GetHashCode() => HashCode.Combine(Key, Value.GetRawText());
}
