﻿using FluentValidation;

namespace CoverGo.Quotation.Application.Common;

// ReSharper disable once UnusedType.Global
public class AggregatesQueryValidator<TAggregatesQuery, TWhere> : AbstractValidator<TAggregatesQuery>
where TAggregatesQuery : PagedQuery<TWhere>
{
    public const int MAX_ITEMS = 100;
    private const int ZERO = 0;

    public AggregatesQueryValidator()
    {
        When(x => x?.Skip.HasValue == true, () => RuleFor(x => x.Skip)
            .GreaterThanOrEqualTo(ZERO).WithMessage($"skip must be greater than or equal to {ZERO}")
        );
        When(x => x?.Take.HasValue == true, () => RuleFor(x => x.Take)
            .GreaterThan(ZERO).WithMessage($"take must be greater than {ZERO}")
            .LessThanOrEqualTo(MAX_ITEMS).WithMessage($"take must be less than or equals to {MAX_ITEMS}")
        );
    }
}
