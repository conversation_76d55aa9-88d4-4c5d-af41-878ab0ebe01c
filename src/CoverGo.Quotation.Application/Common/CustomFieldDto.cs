using System.Text.Json;

namespace CoverGo.Quotation.Application.Common;

public abstract record CustomFieldDto(string Key, JsonElement? Value)
{
    protected static JsonElement? ValueToJsonElement(object? value) => value is null
        ? null
        : JsonDocument.Parse(JsonSerializer.Serialize(value)).RootElement;

    public virtual bool Equals(CustomFieldDto? other)
    {
        return other is not null
            && Key == other.Key
            && Value?.GetRawText() == other.Value?.GetRawText();
    }

    public override int GetHashCode() => HashCode.Combine(Key, Value?.GetRawText());
}

public record CustomFieldDto<T>(string Key, JsonElement? Value) : CustomFieldDto(Key, Value)
{
    public CustomFieldDto(string key, object? value) : this(key, ValueToJsonElement(value))
    {
    }
}
