using System.Text.Json;

namespace CoverGo.Quotation.Application.Common;

public static class JsonElementExtensions
{
    public static string? TryGetValue(this JsonElement element, string propertyName) =>
        !element.TryGetProperty(propertyName, out JsonElement valueElement)
        || valueElement.ValueKind is JsonValueKind.Null
        || valueElement.ValueKind is JsonValueKind.Undefined
            ? null
            : valueElement.ToString();
}
