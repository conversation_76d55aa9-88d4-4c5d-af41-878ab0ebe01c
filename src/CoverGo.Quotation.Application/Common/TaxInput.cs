using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Pricing;

namespace CoverGo.Quotation.Application.Common;

public record TaxInput
{
    public string? Id { get; init; }
    public required string Name { get; init; }
    public required TaxValueInput Value { get; init; }

    public Tax ToDomain(CurrencyCode currencyCode) => new(Id ?? Guid.NewGuid().ToString(), Name, Value.ToDomain(currencyCode));
}

public class TaxValueInput
{
    public decimal? Factor { get; init; }
    public decimal? Amount { get; init; }

    public TaxValue ToDomain(CurrencyCode currencyCode)
    {
        if (Factor.HasValue && !Amount.HasValue)
            return new TaxFactor(Factor.Value);
        else if (Amount.HasValue && !Factor.HasValue)
            return new TaxAmount(new Money(currencyCode, Amount.Value));

        throw new ArgumentException("You must specify exactly one of Factor or Amount.");
    }
}
