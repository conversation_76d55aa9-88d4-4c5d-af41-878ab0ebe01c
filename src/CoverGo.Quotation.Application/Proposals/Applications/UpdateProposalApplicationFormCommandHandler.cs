using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Proposals.Applications;

public record ProposalApplicationDetailsInput(List<FieldInput> Fields, bool? UsePayorAsClaimPaymentBankInfo);
public record ProposalPolicyDetailsInput(DateOnly StartDate, DateOnly? EndDate, List<FieldInput> Fields);
public record UpdateProposalApplicationFormCommand(ValueObjectId<ProposalAggregate> ProposalId, ProposalApplicationDetailsInput ApplicationDetails, ProposalPolicyDetailsInput PolicyDetails)
    : ICommand<Proposal>;
public class UpdateProposalApplicationFormCommandHandler(
    IMapper mapper,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepo, IMediator mediator
) : ICommandHandler<UpdateProposalApplicationFormCommand, Proposal>
{
    public async Task<Proposal> Handle(UpdateProposalApplicationFormCommand request, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepo.GetByIdAsync(request.ProposalId, cancellationToken);

        var applicationDetails = new Domain.Proposals.ApplicationDetails()
        {
            Fields = request.ApplicationDetails.Fields.Select(x => new ApplicationField(x.Key, x.Value)).ToList(),
            UsePayorAsClaimPaymentBankInfo = request.ApplicationDetails.UsePayorAsClaimPaymentBankInfo
        };
        proposal.UpdateApplicationDetails(applicationDetails);


        var policyFields = request.PolicyDetails.Fields.Select(x => new ProposalPolicyField(x.Key, x.Value)).ToList();
        var policyDetails = request.PolicyDetails.EndDate is null ?
            Domain.Proposals.ProposalPolicyDetails.OneYearLong(request.PolicyDetails.StartDate, policyFields) :
            new Domain.Proposals.ProposalPolicyDetails(request.PolicyDetails.StartDate, request.PolicyDetails.EndDate.Value, policyFields);

        proposal.UpdatePolicyDetails(policyDetails);

        await proposalRepo.UpdateAsync(proposal, cancellationToken);

        await mediator.Publish(new ProposalApplicationFormUpdatedDomainEvent { ProposalId = request.ProposalId }, cancellationToken);

        var res = mapper.Map<Proposal>(proposal);
        return res;
    }
}
