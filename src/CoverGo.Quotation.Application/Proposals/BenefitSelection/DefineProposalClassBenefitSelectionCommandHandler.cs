using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Proposals.BenefitSelection;
public record DefineProposalClassBenefitSelectionCommand(
    ValueObjectId<ProposalAggregate> ProposalId,
    string Class,
    Id PlanId,
    List<FieldInput> PlanFields) : ICommand<Proposal>;

public class DefineProposalClassBenefitSelectionCommandHandler(
    IMediator mediator,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IMapper mapper) : ICommandHandler<DefineProposalClassBenefitSelectionCommand, Proposal>
{
    public async Task<Proposal> Handle(DefineProposalClassBenefitSelectionCommand request, CancellationToken cancellationToken)
    {
        var className = new ClassName(request.Class);

        ProposalAggregate proposal = await proposalRepository.GetByIdAsync(request.ProposalId.Value, cancellationToken);

        OfferAggregate offer = await offerRepository.GetByIdAsync(proposal.OfferId.Value, cancellationToken);

        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);

        proposal.DefineProposalClassBenefitSelection(
            className,
            new(request.PlanId.Value, [.. request.PlanFields.Select(it => new CustomField<Plan>(it.Key, it.Value))]),
            productVersion);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        await mediator.Publish(new ProposalClassBenefitSelectionDefinedDomainEvent(proposal.Id, className), cancellationToken);

        return mapper.Map<Proposal>(proposal);
    }
}
