using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Members.Events;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Proposals;

public class ProposalUnderwritingDispatcher(
    IProposalUnderwritingCanRun uwPreConditionsChecker,
    IProposalUnderwritingUpdater proposalUnderwritingUpdater,
    IMemberRepository<ProposalMemberAggregate> proposalMemberRepository,
    IMediator mediator) :
    INotificationHandler<MemberAdded<ProposalMemberAggregate>>,
    INotificationHandler<MembersAdded<ProposalMemberAggregate>>,
    INotificationHandler<MemberUpdated<ProposalMemberAggregate>>,
    INotificationHandler<MembersUpdated<ProposalMemberAggregate>>,
    INotificationHandler<MembersDeleted<ProposalMemberAggregate>>,
    INotificationHandler<ProposalClassBenefitSelectionDefinedDomainEvent>,
    INotificationHandler<UnderwritingCaseCreated>,
    INotificationHandler<HealthQuestionnaireResponsePublished<ProposalMemberAggregate>>
{
    public async Task Handle(MemberAdded<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.AddedMember.ProposalId, cancellationToken);
    }

    public async Task Handle(MembersAdded<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        var sampleProposalMember = await proposalMemberRepository.FindByIdAsync(notification.Ids.First(), cancellationToken);
        await HandleUnderwriting(sampleProposalMember.ProposalId, cancellationToken);
    }

    public async Task Handle(MemberUpdated<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.UpdatedMember.ProposalId, cancellationToken);
    }

    public async Task Handle(ProposalClassBenefitSelectionDefinedDomainEvent notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.ProposalId, cancellationToken);
    }

    public async Task Handle(UnderwritingCaseCreated notification, CancellationToken cancellationToken)
    {
        await UpdateUnderwriting(notification.Case.ProposalId, true, cancellationToken);
    }

    public async Task Handle(MembersDeleted<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.DeletedMembers[0].ProposalId, cancellationToken);
    }

    public async Task Handle(HealthQuestionnaireResponsePublished<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.Member.ProposalId, cancellationToken);
    }

    public async Task Handle(MembersUpdated<ProposalMemberAggregate> notification, CancellationToken cancellationToken)
    {
        await HandleUnderwriting(notification.MemberHolderId, cancellationToken);
    }

    private async Task HandleUnderwriting(ValueObjectId<ProposalAggregate> proposalId, CancellationToken cancellationToken)
    {
        if (await uwPreConditionsChecker.CanRunAutoUnderwriting(proposalId, cancellationToken))
        {
            await UpdateUnderwriting(proposalId, false, cancellationToken);
        }
        else
        {
            await proposalUnderwritingUpdater.UpdateUnderwritingHealthQuestionnaire(proposalId, cancellationToken);
            await mediator.Publish(new ProposalUnderwritingSkippedDomainEvent { ProposalId = proposalId }, cancellationToken);
        }
    }

    private async Task UpdateUnderwriting(ValueObjectId<ProposalAggregate> proposalId, bool skipCheckCanRunAutoUw, CancellationToken cancellationToken)
    {
        var membersUnderwriting = await proposalUnderwritingUpdater.UpdateUnderwriting(proposalId, skipCheckCanRunAutoUw, cancellationToken);
        await mediator.Publish(new ProposalUnderwritingUpdatedDomainEvent { ProposalId = proposalId, MembersUnderwriting = membersUnderwriting }, cancellationToken);
    }
}
