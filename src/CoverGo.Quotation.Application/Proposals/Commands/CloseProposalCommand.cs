using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using Proposal = CoverGo.Quotation.Application.Proposals.Contracts.Proposal;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record CloseProposalCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required string ClosureReason { get; init; }
}

public class CloseProposalCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMapper mapper,
    TimeProvider timeProvider) :
    ICommandHandler<CloseProposalCommand, Proposal>
{
    public async Task<Proposal> Handle(CloseProposalCommand command, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);
        proposal.Close(command.ClosureReason, timeProvider.GetUtcNow());
        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        return mapper.Map<Proposal>(proposal);
    }
}
