using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Clients;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Quotes.Ports;
using CoverGo.Quotation.Application.Underwriting;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;
using CoverGo.Quotation.Domain.Underwriting.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public class RequestUnderwritingCommandHandler(
    IRepository<UnderwritingCase, string> underwritingRepository,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMemberRepository<ProposalMemberAggregate> proposalMemberRepository,
    IAgentRepository agentRepository,
    IClientRepository clientRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IMediator mediator,
    IAggregateLock aggregateLock,
    IMapper mapper) : ICommandHandler<RequestUnderwritingCommand, ProposalUnderwritingCase>
{
    public async Task<ProposalUnderwritingCase> Handle(
        RequestUnderwritingCommand request,
        CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        var proposal = await proposalRepository.GetByIdAsync(
            request.ProposalId,
            cancellationToken
        );

        var opportunity = await opportunityRepository.GetByIdAsync(
            proposal.OpportunityId,
            cancellationToken
        );

        if (opportunity.ClientId is null)
        {
            throw new Exception(); // TODO: Missing client exception
        }

        var agent = await agentRepository.GetByIdAsync(opportunity.PrimaryAgentId.Value, cancellationToken);
        var client = await clientRepository.GetByIdAsync(opportunity.ClientId.Value, cancellationToken);
        var underwriting = proposal.RequestUnderwriting(agent, client);
        await proposalRepository.UpdateAsync(proposal, cancellationToken);

        var members = await proposalMemberRepository.FindAllByAsync(m => m.ProposalId == proposal.Id, cancellationToken);
        await underwritingRepository.InsertAsync(underwriting, cancellationToken);

        await mediator.Publish(new UnderwritingCaseCreated(underwriting), cancellationToken);

        return mapper.Map<ProposalUnderwritingCase>(underwriting);
    }
}
