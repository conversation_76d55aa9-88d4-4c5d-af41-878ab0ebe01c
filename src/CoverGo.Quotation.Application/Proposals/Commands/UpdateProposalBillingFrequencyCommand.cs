using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Proposals.Commands;

// ReSharper disable once ClassNeverInstantiated.Global
public sealed record UpdateProposalBillingFrequencyCommand(ValueObjectId<ProposalAggregate> ProposalId, string BillingFrequency, string? BillingPricingDateBasis) : ICommand<Proposal>;

// ReSharper disable once UnusedType.Global
public class UpdateProposalBillingFrequencyCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMapper mapper) : ICommandHandler<UpdateProposalBillingFrequencyCommand, Proposal>
{
    public async Task<Proposal> Handle(UpdateProposalBillingFrequencyCommand command, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);
        proposal.UpdateBillingFrequency(command.BillingFrequency, command.BillingPricingDateBasis);
        var updatedProposal = await proposalRepository.UpdateAsync(proposal, cancellationToken);

        return mapper.Map<Proposal>(updatedProposal);
    }
}
