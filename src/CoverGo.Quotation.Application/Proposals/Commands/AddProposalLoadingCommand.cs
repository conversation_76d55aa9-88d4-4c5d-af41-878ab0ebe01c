using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Application.Offers.BenefitsSelection;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Proposals;

using ProposalLoading = CoverGo.Quotation.Application.Loadings.Contracts.ProposalLoading;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record AddProposalLoadingCommand : ICommand<ProposalLoading>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required ProposalLoadingInput Loading { get; init; }
}

// ReSharper disable once UnusedType.Global
public class AddProposalLoadingCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IProductVersionRepository productVersionRepository,
    IUserContextProvider userContextProvider,
    IMapper mapper) :
    ICommandHandler<AddProposalLoadingCommand, ProposalLoading>
{
    public async Task<ProposalLoading> Handle(AddProposalLoadingCommand command, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);

        var loading = await CreateLoading(command, proposal.OfferId, cancellationToken);
        proposal.AddLoading(loading);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        return mapper.Map<ProposalLoading>(loading);
    }

    private async Task<Domain.Loadings.ProposalLoading> CreateLoading(AddProposalLoadingCommand command, ValueObjectId<OfferAggregate> offerId, CancellationToken cancellationToken)
    {
        var fixedAmountLoading = command.Loading.FixedAmountLoading;
        if (fixedAmountLoading is not null)
        {
            var currencyCode = await GetCurrencyCode(offerId, cancellationToken);
            return fixedAmountLoading.ToDomain(currencyCode, userContextProvider.GetUserId()!);
        }

        var scaleFactorLoading = command.Loading.ScaleFactorLoading;
        if (scaleFactorLoading is not null) return scaleFactorLoading.ToDomain(userContextProvider.GetUserId()!);

        throw new NotSupportedException("Proposal loading type not supported");
    }

    private async Task<CurrencyCode> GetCurrencyCode(ValueObjectId<OfferAggregate> offerId, CancellationToken cancellationToken)
    {
        OfferAggregate offer = await offerRepository.GetByIdAsync(offerId, cancellationToken);
        var productVersion = await productVersionRepository.GetByIdAsync(offer.ProductVersionId, cancellationToken);
        return productVersion.Currency!;
    }
}
