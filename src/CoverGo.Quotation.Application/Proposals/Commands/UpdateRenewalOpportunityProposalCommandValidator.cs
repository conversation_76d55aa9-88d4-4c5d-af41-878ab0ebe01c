using FluentValidation;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public class UpdateRenewalOpportunityProposalCommandValidator : AbstractValidator<UpdateRenewalOpportunityProposalCommand>
{
    public const string DateOfBirthErrorMessage = "Date of birth cannot be in the future";

    public UpdateRenewalOpportunityProposalCommandValidator(TimeProvider timeProvider)
    {
        RuleForEach(x => x.AddingMembers).Must(m => m.DateOfBirth == null || m.DateOfBirth.Value <= DateOnly.FromDateTime(timeProvider.GetUtcNow().DateTime)).WithMessage(DateOfBirthErrorMessage);
        RuleForEach(x => x.EditingMembers).Must(m => m.DateOfBirth == null || m.DateOfBirth.Value <= DateOnly.FromDateTime(timeProvider.GetUtcNow().DateTime)).WithMessage(DateOfBirthErrorMessage);
    }
}
