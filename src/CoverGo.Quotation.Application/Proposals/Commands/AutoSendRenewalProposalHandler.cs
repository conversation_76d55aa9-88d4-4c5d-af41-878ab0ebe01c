using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using MediatR;
using CoverGo.Quotation.Application.Proposals.Services;

namespace CoverGo.Quotation.Application.Proposals;

public class AutoSendRenewalProposalHandler(
    IProposalRenderingService proposalRenderingService
) : INotificationHandler<ProposalCreatedDomainEvent>
{
    public Task Handle(ProposalCreatedDomainEvent request, CancellationToken cancellationToken)
    => proposalRenderingService.SendProposal(request.OpportunityId, request.OfferId, request.ProposalId, cancellationToken);
}
