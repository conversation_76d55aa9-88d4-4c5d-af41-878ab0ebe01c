using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Loadings;
using CoverGo.Quotation.Domain.Proposals;

using ProposalLoading = CoverGo.Quotation.Application.Loadings.Contracts.ProposalLoading;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record DeleteProposalLoadingCommand : ICommand<ProposalLoading>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required Id Id { get; init; }
}

public class DeleteProposalLoadingCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMapper mapper) :
    ICommandHandler<DeleteProposalLoadingCommand, ProposalLoading>
{
    public async Task<ProposalLoading> Handle(DeleteProposalLoadingCommand command, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);
        var deletedLoading = proposal.DeleteLoading(new ProposalLoadingId(command.Id.Value));
        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        return mapper.Map<ProposalLoading>(deletedLoading);
    }
}
