using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Proposals.Repositories;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.Exceptions;

using ProposalTax = CoverGo.Quotation.Application.Proposals.Contracts.ProposalTax;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record UpdateProposalTaxCommand : ICommand<ProposalTax>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required decimal Rate { get; init; }
    public required string Country { get; init; }
}

public class UpdateProposalTaxCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IProposalCountryRepository countryRepository,
    IMapper mapper) :
    ICommandHandler<UpdateProposalTaxCommand, ProposalTax>
{
    public async Task<ProposalTax> Handle(UpdateProposalTaxCommand command, CancellationToken cancellationToken)
    {
        var country = command.Country;

        await ValidateCountry(country, cancellationToken);

        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);
        proposal.UpdateTax(new Domain.Proposals.ProposalTax(command.Rate, country));

        await proposalRepository.UpdateAsync(proposal, cancellationToken);

        return mapper.Map<ProposalTax>(proposal.Tax);
    }

    private async Task ValidateCountry(string country, CancellationToken cancellationToken)
    {
        var countries = await countryRepository.GetCountries(cancellationToken);

        if (!countries.Contains(country))
        {
            throw new CountryReferenceDataNotFoundException(country);
        }
    }
}
