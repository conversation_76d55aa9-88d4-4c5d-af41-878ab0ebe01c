using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record MarkProposalAsExpiredCommand : ICommand
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
}

public class MarkProposalAsExpiredCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IAggregateLock aggregateLock)
    : ICommandHandler<MarkProposalAsExpiredCommand>
{
    public async Task Handle(MarkProposalAsExpiredCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);

        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId, cancellationToken);

        proposal.MarkAsExpired(DateTimeOffset.UtcNow);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);
    }
}
