using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.OfferMembers;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Opportunities.Renewal;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;
using Proposal = CoverGo.Quotation.Application.Proposals.Contracts.Proposal;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record ExtendProposalValidityCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required DateTime ExpirationDate { get; init; }
}

public class ExtendProposalValidityCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IRepository<ProposalUnderwritingCase, string> underwritingCaseRepository,
    IMapper mapper) :
    ICommandHandler<ExtendProposalValidityCommand, Proposal>
{
    public async Task<Proposal> Handle(ExtendProposalValidityCommand command, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);
        var opportunity = await opportunityRepository.GetByIdAsync(proposal.OpportunityId, cancellationToken);
        var isRenewal = opportunity is RenewalOpportunity;
        var uwCase = await underwritingCaseRepository.FindByProposal(command.ProposalId, cancellationToken);
        proposal.ExtendExpirationDate(command.ExpirationDate, isRenewal, uwCase);
        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        return mapper.Map<Proposal>(proposal);
    }
}
