using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Proposals.Commands
{
    public record SendProposalCommand : ICommand<bool>
    {
        public required string To { get; set; }
        public string[]? Cc { get; set; }
        public required string ReplyTo { get; set; }
        public required string Subject { get; set; }
        public required string MessageBody { get; set; }
        public bool? IsSendBuyFlowLink { get; set; }
        public required ValueObjectId<ProposalAggregate> ProposalId { get; set; }
        public List<SendProposalAttachedDocumentInfo>? AttachedDocuments { get; set; }
    }

    public record SendRenewalProposalCommand : SendProposalCommand;

    public class SendProposalAttachedDocumentInfo
    {
        public required string TemplateId { get; set; }
        public required TemplateType TemplateType { get; set; }
        public required string LogicalId { get; set; }
        public required string Name { get; set; }
    }
}
