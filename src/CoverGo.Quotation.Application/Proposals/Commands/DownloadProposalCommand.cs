using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Application.Proposals.Services;
using CoverGo.Quotation.Application.Common.PdfTemplates;
using CoverGo.Quotation.Application.Common;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record DownloadProposalCommand : ICommand<byte[]>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public string? TemplateId { get; init; }
    public TemplateType? TemplateType { get; init; }
}

public class DownloadProposalCommandHandler(
    IProposalRenderingService proposalRenderingService,
    IPdfTemplateRenderer pdfTemplateRenderer
) : ICommandHandler<DownloadProposalCommand, byte[]>
{
    public async Task<byte[]> Handle(DownloadProposalCommand command, CancellationToken cancellationToken)
    {
        var contentJsonString = await proposalRenderingService.GetDocumentData(command.ProposalId, cancellationToken);
        return await pdfTemplateRenderer.Render(
            command.TemplateId ?? "PDF_INDIVDUAL_HEALTH_INSURANCE_PROPOSAL",
            command.TemplateType ?? TemplateType.Wkhtmltopdf,
            contentJsonString,
            cancellationToken);
    }
}
