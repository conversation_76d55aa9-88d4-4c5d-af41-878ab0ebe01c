using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record UpdateProposalTaxOverridesCommand : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public IReadOnlyList<TaxInput>? TaxOverrides { get; init; }
}

public class UpdateProposalTaxOverridesCommandHandler(
    IAggregateLock aggregateLock,
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IOfferRelationshipLoader offerRelationshipLoader,
    IMapper mapper) : ICommandHandler<UpdateProposalTaxOverridesCommand, Proposal>
{
    public async Task<Proposal> Handle(UpdateProposalTaxOverridesCommand request, CancellationToken cancellationToken)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId, cancellationToken);
        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
        await offerRelationshipLoader.LoadProductVersion(proposal.Offer, cancellationToken);

        var taxOverrides = request.TaxOverrides?
            .Select(input => input.ToDomain(proposal.Offer.ProductVersion.Currency!))
            .ToList();

        proposal.UpdateTaxOverrides(taxOverrides, proposal.Offer.ProductVersion);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);

        return mapper.Map<Proposal>(proposal);
    }
}
