using AutoMapper;

using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Loadings;
using CoverGo.Quotation.Domain.Loadings.Exceptions;
using CoverGo.Quotation.Domain.Members.Exceptions;
using CoverGo.Quotation.Domain.Proposals;

using ProposalLoading = CoverGo.Quotation.Application.Loadings.Contracts.ProposalLoading;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record UpdateProposalLoadingCommand : ICommand<ProposalLoading>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public required ProposalLoadingUpdateInput Loading { get; init; }
}

// ReSharper disable once UnusedType.Global
public class UpdateProposalLoadingCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IMapper mapper) :
    ICommandHandler<UpdateProposalLoadingCommand, ProposalLoading>
{
    public async Task<ProposalLoading> Handle(UpdateProposalLoadingCommand command, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(command.ProposalId, cancellationToken);
        var loadingUpdate = GetLoading(command.Loading);
        var loadingId = new ProposalLoadingId(loadingUpdate.Id.Value);
        Domain.Loadings.ProposalLoading? loadingToUpdate = proposal.Loadings.FirstOrDefault(x => x.Id == loadingId);

        if (loadingToUpdate is null)
        {
            throw new LoadingNotFoundException(loadingId.Value);
        }

        var updatedLoading = GetUpdatedLoading(loadingToUpdate, loadingUpdate);

        proposal.DeleteLoading(loadingId);
        proposal.AddLoading(updatedLoading);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        return mapper.Map<ProposalLoading>(updatedLoading);
    }

    private Domain.Loadings.ProposalLoading GetUpdatedLoading(Domain.Loadings.ProposalLoading loadingToUpdate, ProposalLoadingUpdateInputBase loadingUpdate)
    {
        Domain.Loadings.LoadingCondition? condition = loadingUpdate.Condition != null ?
            loadingUpdate.Condition.Value is not null ? new Domain.Loadings.LoadingCondition(loadingUpdate.Condition.Value.Value, loadingUpdate.Condition.Value.IsCustom) : null
            : loadingToUpdate.Condition;
        string? remark = loadingUpdate.Remark != null ? loadingUpdate.Remark.Value : loadingToUpdate.Remark;

        if (loadingToUpdate is Domain.Loadings.ProposalScaleFactorLoading scaleFactorLoadingToUpdate)
        {
            if (loadingUpdate is ProposalScaleFactorLoadingUpdateInput scaleFactorLoadingUpdate)
            {
                return scaleFactorLoadingToUpdate with
                {
                    Condition = condition,
                    Remark = remark,
                    Factor = scaleFactorLoadingUpdate.Factor?.Value ?? scaleFactorLoadingToUpdate.Factor,
                };
            }

            throw new LoadingTypeCannotBeChangedException("Loading type cannot be changed from scale factor to fixed amount");
        }

        if (loadingToUpdate is Domain.Loadings.ProposalFixedAmountLoading fixedAmountLoadingToUpdate)
        {
            if (loadingUpdate is ProposalFixedAmountLoadingUpdateInput fixedAmountLoadingUpdate)
            {
                return fixedAmountLoadingToUpdate with
                {
                    Condition = condition,
                    Remark = remark,
                    Amount = fixedAmountLoadingUpdate.Amount is not null ?
                        fixedAmountLoadingToUpdate.Amount with { Amount = fixedAmountLoadingUpdate.Amount.Value }
                        : fixedAmountLoadingToUpdate.Amount,
                };

            }

            throw new LoadingTypeCannotBeChangedException("Loading type cannot be changed from fixed amount to scale factor");
        }

        throw new NotSupportedException("Loading type not supported");
    }

    private static ProposalLoadingUpdateInputBase GetLoading(ProposalLoadingUpdateInput loading)
    {
        if (loading.ScaleFactorLoading is not null)
        {
            return loading.ScaleFactorLoading;
        }
        if (loading.FixedAmountLoading is not null)
        {
            return loading.FixedAmountLoading;
        }
        throw new NotSupportedException("Proposal loading not supported");
    }
}
