using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Application.Locking;
using CoverGo.Quotation.Application.ProposalMembers.Commands;
using CoverGo.Quotation.Application.ProposalMembers.Contracts;
using CoverGo.Quotation.Application.Proposals.Contracts;
using CoverGo.Quotation.Application.Underwriting.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.SettableValues;
using MediatR;

namespace CoverGo.Quotation.Application.Proposals.Commands;

public record UpdateRenewalOpportunityProposalCommand(
) : ICommand<Proposal>
{
    public required ValueObjectId<ProposalAggregate> ProposalId { get; init; }
    public IEnumerable<RenewalProposalMemberInput>? AddingMembers { get; init; }
    public IReadOnlyList<RenewalProposalMemberUpdateInput>? EditingMembers { get; init; }
    public IEnumerable<Id>? RemovingMemberIds { get; init; }
}

public record RenewalProposalMemberInput : ProposalMemberInput
{
    public MemberUnderwritingInput? MemberUnderwriting { get; init; }
    public Id? HealthQuestionnaireResponseId { get; init;}
    public new List<RenewalProposalMemberInput>? Dependents { get; init; }
}

public record RenewalProposalMemberUpdateInput : ProposalMemberUpdateInput
{
    public Settable<MemberUnderwritingInput>? MemberUnderwriting { get; init; }
    public Settable<Id>? HealthQuestionnaireResponseId { get; init;}
}

public class UpdateRenewalOpportunityProposalCommandHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IAggregateLock aggregateLock,
    IMediator mediator,
    IMapper mapper) : ICommandHandler<UpdateRenewalOpportunityProposalCommand, Proposal>
{
    public async Task<Proposal> Handle(UpdateRenewalOpportunityProposalCommand request, CancellationToken cancellationToken = default)
    {
        await aggregateLock.TakeLockAsync<ProposalAggregate>(request.ProposalId.Value, cancellationToken);
        var proposal = await proposalRepository.GetByIdAsync(request.ProposalId.Value, cancellationToken);
        await proposal.Renew(async () =>
        {
            if (request.AddingMembers is not null)
            {
                await mediator.Send(new AddRenewalProposalMembersCommand(
                    proposal,
                    request.AddingMembers
                ));
            }

            if (request.EditingMembers is not null)
            {
                await mediator.Send(new UpdateRenewalProposalMembersCommand(
                    proposal,
                    request.EditingMembers
                ));
            }

            if (request.RemovingMemberIds?.Any() == true)
            {
                await mediator.Send(new DeleteRenewalProposalMembersCommand
                {
                    Proposal = proposal,
                    ProposalMemberIds = request.RemovingMemberIds
                });
            }
        }
        );

        proposal = await proposalRepository.UpdateAsync(proposal, cancellationToken);

        return mapper.Map<Proposal>(proposal);
    }

}
