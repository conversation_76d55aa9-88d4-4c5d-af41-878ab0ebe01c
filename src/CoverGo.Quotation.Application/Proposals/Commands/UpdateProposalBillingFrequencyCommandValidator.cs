﻿using CoverGo.Quotation.Application.Offers.BillingInformation;
using FluentValidation;

namespace CoverGo.Quotation.Application.Proposals.Commands;

// ReSharper disable once UnusedType.Global
public class UpdateProposalBillingFrequencyCommandValidator : AbstractValidator<UpdateProposalBillingFrequencyCommand>
{
    public UpdateProposalBillingFrequencyCommandValidator(IBillingConfigurationRepository billingFrequencyRepository)
    {
        RuleFor(x => x.BillingFrequency).MustAsync(async (x, ct) =>
        {
            var billingFrequencies = (await billingFrequencyRepository.GetBillingFrequencies(ct)).ToList();
            return billingFrequencies.Contains(x);
        }).WithMessage("Invalid billing frequency");
    }
}
