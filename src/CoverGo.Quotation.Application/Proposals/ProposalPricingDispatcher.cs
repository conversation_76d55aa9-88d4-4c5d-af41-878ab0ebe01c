using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting.DomainEvents;
using MediatR;

namespace CoverGo.Quotation.Application.Proposals;

public class ProposalPricingDispatcher(IProposalPricingUpdater updater) :
    INotificationHandler<ProposalMemberBenefitLoadingsChangedDomainEvent>,
    INotificationHandler<ProposalApplicationFormUpdatedDomainEvent>,
    INotificationHandler<ProposalBillingInformationUpdatedDomainEvent>,
    INotificationHandler<ProposalUnderwritingUpdatedDomainEvent>,
    INotificationHandler<ProposalUnderwritingSkippedDomainEvent>,
    INotificationHandler<TaxUpdatedDomainEvent>,
    INotificationHandler<ProposalMemberLoadingsChangedDomainEvent>,
    INotificationHandler<ProposalLoadingsChangedDomainEvent>,
    INotificationHandler<UnderwritingDecidedDomainEvent>,
    INotificationHandler<ProposalAgentAssignedDomainEvent>,
    INotificationHandler<RenewalOpportunityProposalUpdatedDomainEvent>,
    INotificationHandler<ProposalTaxOverridesUpdatedDomainEvent>
{
    public async Task Handle(ProposalMemberBenefitLoadingsChangedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateProposalPricing(notification.ProposalMember.ProposalId, cancellationToken);
    }

    public async Task Handle(ProposalApplicationFormUpdatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    }

    public async Task Handle(ProposalUnderwritingUpdatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    }

    public async Task Handle(ProposalBillingInformationUpdatedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    }

    public async Task Handle(ProposalUnderwritingSkippedDomainEvent notification, CancellationToken cancellationToken)
    {
        await updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    }

    public Task Handle(TaxUpdatedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    public Task Handle(ProposalMemberLoadingsChangedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    public Task Handle(ProposalLoadingsChangedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    public Task Handle(UnderwritingDecidedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    public Task Handle(ProposalAgentAssignedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    public Task Handle(RenewalOpportunityProposalUpdatedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
    public Task Handle(ProposalTaxOverridesUpdatedDomainEvent notification, CancellationToken cancellationToken) => updater.UpdateProposalPricing(notification.ProposalId, cancellationToken);
}
