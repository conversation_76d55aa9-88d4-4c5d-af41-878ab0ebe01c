using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.ChannelManagement;
using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Pricing;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Proposals;

public interface IProposalPricingUpdater
{
    Task UpdateProposalPricing(ValueObjectId<ProposalAggregate> proposalId, CancellationToken cancellationToken);
}

public class ProposalPricingUpdater(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IOfferRelationshipLoader offerRelationshipLoader,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IMemberRepository<ProposalMemberAggregate> proposalMemberRepository,
    IPremiums premiums,
    IChannelManagementAdapter channelManagementAdapter,
    IUserContextProvider userContext,
    ILogger<ProposalPricingUpdater> logger) : IProposalPricingUpdater

{
    public async Task UpdateProposalPricing(ValueObjectId<ProposalAggregate> proposalId, CancellationToken cancellationToken)
    {
        using var _ = logger.BeginScope(new Dictionary<string, object?>
        {
            { "ProposalId", proposalId.Value }
        });

        var proposal = await proposalRepository.GetByIdAsync(proposalId, cancellationToken);
        await proposalRelationshipLoader.LoadMembers(proposal, cancellationToken);

        if (!proposal.CanGetPremium())
        {
            logger.LogInformation("The proposal {ProposalId} does not have members, skipping pricing update", proposalId.Value);
            return;
        }

        await proposalRelationshipLoader.LoadOffer(proposal, cancellationToken);
        await offerRelationshipLoader.LoadOpportunity(proposal.Offer, cancellationToken);
        await offerRelationshipLoader.LoadProductVersion(proposal.Offer, cancellationToken);
        var quote = ProposalToQuoteMapper.BuildQuote(proposal);

        var tCommission = channelManagementAdapter.GetCommissionPlan(
            proposal.Offer.ProductVersionId,
            proposal.Offer.Opportunity.DistributorId.Value,
            proposal.PolicyDetails!.StartDate.ToDateTime(TimeOnly.MinValue),
            cancellationToken);

        var premiumResult = await premiums.CalculatePremiumFor(
            quote,
            new DistributorId(quote.DistributorId),
            quote.ProductVersion,
            ProposalToQuoteMapper.BuildQuoteMembers(proposal),
            cancellationToken);

        foreach (var (key, pricing) in premiumResult.Members.MemberPremiums)
        {
            proposal.Members.Single(it => it.Id.Value == key).Pricing = pricing;
        }

        var userId = userContext.GetUserId();
        proposal.UpdatePricing(premiumResult.QuotationPricing, premiumResult.BillingPlan, userId);
        proposal.UpdateCommissionPlan(await tCommission);

        await proposalRepository.UpdateAsync(proposal, cancellationToken);
        await proposalMemberRepository.UpdateBatchAsync(proposal.Members, cancellationToken);
    }
}
