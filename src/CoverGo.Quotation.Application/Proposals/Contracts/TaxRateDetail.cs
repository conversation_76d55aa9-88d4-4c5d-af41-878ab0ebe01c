using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Pricing;

namespace CoverGo.Quotation.Application.Proposals.Contracts;

public record TaxRateDetail
{
    public required string Id { get; init; }
    public required string Type { get; init; }
    public required string Name { get; init; }
    public required decimal Value { get; init; }

    public Tax ToDomain(CurrencyCode currencyCode) => new(Id, Name, Type switch
    {
        "factor" => new TaxFactor(Value),
        "flat" => new TaxAmount(new Money(currencyCode, Value)),
        _ => new TaxFactor(Value)
    });
}
