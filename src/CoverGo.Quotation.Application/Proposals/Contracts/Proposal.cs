using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Quotation.Application.Loadings.Contracts;
using CoverGo.Quotation.Application.Offers.Contracts;
using CoverGo.Quotation.Application.Pricing.Contracts;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.LegacyIntegration;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Pricing;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.ChannelManagement;

using IBenefitClassDto = CoverGo.Quotation.Application.Offers.Contracts.IBenefitClass;

namespace CoverGo.Quotation.Application.Proposals.Contracts;

public class Proposal
{
    public required ValueObjectId<ProposalAggregate> Id { get; init; }
    public required string ProposalNumber { get; init; }
    public required ValueObjectId<OpportunityAggregate> OpportunityId { get; init; }
    public required ValueObjectId<OfferAggregate> OfferId { get; init; }
    public ValueObjectId<LegacyPolicy>? LegacyPolicyId { get; init; }
    public List<Policies.Contracts.Policy>? Policies { get; init; }
    public required ProposalStatus Status { get; init; }
    public required DateTime? ExpirationDate { get; init; }
    public required IList<IBenefitClassDto> Classes { get; init; }
    public required BillingInfo BillingInfo { get; init; }
    public required BillingPlan BillingPlan { get; init; }
    public required QuotationPricing Pricing { get; init; }
    public required ApplicationDetails ApplicationDetails { get; init; }
    public ProposalPolicyDetails? PolicyDetails { get; init; }
    public required List<ProposalLoading> Loadings { get; init; }
    public required AuditInfo AuditInfo { get; init; }
    public required ProposalTax? Tax { get; init; }
    public string? ClosureReason { get; init; }
    public IReadOnlyList<Tax>? TaxOverrides { get; init; }
    public IReadOnlyList<QuotationRiskCarrierContract>? RiskCarrierContracts { get; init; }
    public QuotationCommissionPlan? CommissionPlan { get; init; }
}
