namespace CoverGo.Quotation.Application.Proposals.Contracts;

public record RenewalConfiguration
{
    public required int RenewalNotificationDays { get; init; }
    public required bool AutoSendOffer { get; init; }
    public required bool AutoSendPolicy { get; init; }
    public required bool AutoSendProposal { get; init; }
    public required bool RequiresInitialPremium { get; init; }

    public static RenewalConfiguration Default => new()
    {
        RenewalNotificationDays = 0,
        AutoSendOffer = false,
        AutoSendPolicy = false,
        AutoSendProposal = false,
        RequiresInitialPremium = false
    };
}
