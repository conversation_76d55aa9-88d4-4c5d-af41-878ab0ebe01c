using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.OfferMembers;
using CoverGo.Quotation.Domain.ProposalMembers.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting;

using MediatR;

namespace CoverGo.Quotation.Application.Proposals;

public class ProposalUnderwritingCaseUpdater(IRepository<ProposalUnderwritingCase, string> underwritingCaseRepository) :
    INotificationHandler<ProposalUnderwritingUpdatedDomainEvent>
{
    public async Task Handle(ProposalUnderwritingUpdatedDomainEvent notification, CancellationToken ct)
    {
        var uwCase = await underwritingCaseRepository.FindByProposal(notification.ProposalId, ct);
        if (uwCase is not null)
        {
            uwCase.OnAllMemberUnderwritingDone(notification.MembersUnderwriting?.Values);
            await underwritingCaseRepository.UpdateAsync(uwCase, ct);
        }
    }
}
