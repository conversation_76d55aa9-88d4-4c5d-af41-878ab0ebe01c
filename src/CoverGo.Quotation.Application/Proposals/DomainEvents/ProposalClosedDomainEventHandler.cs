using CoverGo.BuildingBlocks.Application.Core.DomainEvents;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.OfferMembers;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.Proposals.DomainEvents;

public class ProposalClosedDomainEventHandler(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMembersRepository,
    IRepository<ProposalUnderwritingCase, string> underwritingCaseRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository,
    IProposalRelationshipLoader proposalRelationshipLoader
) : DomainEventHandler<ProposalClosedDomainEvent>
{
    protected override async Task HandleDomainEvent(ProposalClosedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        var proposal = await proposalRepository.GetByIdAsync(domainEvent.ProposalId, cancellationToken);
        await proposalRelationshipLoader.LoadMembers(proposal, cancellationToken);
        var uwCase = await underwritingCaseRepository.FindByProposal(domainEvent.ProposalId, cancellationToken);
        var opportunity = await opportunityRepository.GetByIdAsync(proposal.OpportunityId, cancellationToken);

        var (needUpdateMembers, underwritingCase) = proposal.RejectOnClosed(uwCase);

        opportunity.MarkAsClosed(OpportunityCloseReasonValue.ProposalExpired);
        await opportunityRepository.UpdateAsync(opportunity, cancellationToken);

        if (needUpdateMembers.Count > 0) await proposalMembersRepository.UpdateBatchAsync(needUpdateMembers, cancellationToken);
        if (underwritingCase != null) await underwritingCaseRepository.UpdateAsync(underwritingCase, cancellationToken);
    }
}
