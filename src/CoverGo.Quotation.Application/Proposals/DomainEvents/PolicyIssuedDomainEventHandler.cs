using CoverGo.BuildingBlocks.Application.Core.DomainEvents;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;

namespace CoverGo.Quotation.Application.Proposals.DomainEvents;

public class PolicyIssuedDomainEventHandler(
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository
) : DomainEventHandler<PolicyIssuedDomainEvent>
{
    protected override async Task HandleDomainEvent(PolicyIssuedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        var opportunity = await opportunityRepository.GetByIdAsync(domainEvent.OpportunityId, cancellationToken);
        opportunity.MarkAsClosed(OpportunityCloseReasonValue.PolicyIssued);
        await opportunityRepository.UpdateAsync(opportunity, cancellationToken);
    }
}
