using CoverGo.BuildingBlocks.Application.Core.DomainEvents;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Proposals.DomainEvents;

namespace CoverGo.Quotation.Application.Proposals.DomainEvents;

public class ProposalRejectedDomainEventHandler(
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository
) : DomainEventHandler<ProposalRejectedDomainEvent>
{
    protected override async Task HandleDomainEvent(ProposalRejectedDomainEvent domainEvent, CancellationToken cancellationToken)
    {
        var opportunity = await opportunityRepository.GetByIdAsync(domainEvent.OpportunityId, cancellationToken);
        opportunity.MarkAsClosed(OpportunityCloseReasonValue.Rejected);
        await opportunityRepository.UpdateAsync(opportunity, cancellationToken);
    }
}
