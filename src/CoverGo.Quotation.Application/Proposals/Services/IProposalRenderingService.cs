using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.Proposals;
using Newtonsoft.Json.Linq;

namespace CoverGo.Quotation.Application.Proposals.Services;

public interface IProposalRenderingService
{
    Task<string> GetDocumentData(
        ValueObjectId<ProposalAggregate> proposalId,
        CancellationToken cancellationToken
    );
    string GetDocumentData(JToken content);
    Task<JToken> GetDocumentJToken(
        ValueObjectId<ProposalAggregate> proposalId,
        CancellationToken cancellationToken
    );

    Task SendProposal(
        ValueObjectId<OpportunityAggregate> opportunityId,
        ValueObjectId<OfferAggregate> offerId,
        ValueObjectId<ProposalAggregate> proposalId, CancellationToken cancellationToken
    );
}
