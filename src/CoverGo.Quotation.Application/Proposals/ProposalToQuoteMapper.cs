using CoverGo.BuildingBlocks.CustomFields;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Quotes;

namespace CoverGo.Quotation.Application.Proposals;

public static class ProposalToQuoteMapper
{
    public static Quote BuildQuote(ProposalAggregate proposal)
    {
        return new Quote
        {
            Id = proposal.Id.Value,
            ProductVersionId = proposal.Offer.ProductVersion.Id,
            ProductVersion = proposal.Offer.ProductVersion,
            DistributorId = proposal.Offer.Opportunity.DistributorId.Value,
            PrimaryAgentId = proposal.Offer.Opportunity.PrimaryAgentId.Value,
            SecondaryAgentId = proposal.Offer.Opportunity.SecondaryAgentId?.Value,
            BillingInformation = proposal.BillingInformation,
            PolicyDetails = new PolicyDetails(
                proposal.PolicyDetails!.StartDate,
                proposal.PolicyDetails!.EndDate,
                [.. proposal.PolicyDetails!.Fields.Select(it => new PolicyField(it.Key, it.Value))]),
            ClientId = proposal.Offer.Opportunity.ClientId?.Value ?? "",
            Tax = proposal.Tax,
            TaxOverrides = proposal.TaxOverrides,
            Loadings = proposal.Loadings,
        };
    }

    public static List<QuoteMember> BuildQuoteMembers(ProposalAggregate proposal)
    {
        var primaries = proposal.Members
            .Where(it => it.DependentOf is null)
            .Select(it => BuildQuoteMember(it, proposal))
            .ToDictionary(it => it.Id, it => it);

        var dependents = proposal.Members
            .Where(it => it.DependentOf is not null)
            .Select(it => BuildQuoteMember(it, proposal, primaries))
            .ToList();

        return [.. primaries.Values, .. dependents];
    }

    public static QuoteMember BuildQuoteMember(
        ProposalMemberAggregate proposalMember,
        ProposalAggregate proposal,
        Dictionary<string, QuoteMember>? primaries = null)
    {
        return new QuoteMember()
        {
            // Note that those ids don't exist in DB
            Id = proposalMember.Id.Value,
            QuoteId = proposalMember.OfferId.Value,

            Fields = [.. proposalMember.Fields.Select(it => new CustomField<QuoteMember>(it.Key, it.Value))],
            HealthQuestionnaireResponseId = proposalMember.HealthQuestionnaireResponse?.Id.Value,
            MemberId = proposalMember.MemberId?.Value,
            MemberUnderwriting = proposalMember.MemberUnderwriting,
            Pricing = proposalMember.Pricing,
            States = [
                new QuoteMemberState(
                    proposal.PolicyDetails!.StartDate,
                    proposal.PolicyDetails!.EndDate,
                    proposalMember.PlanId?.Value,
                    proposalMember.Class?.Value,
                    proposalMember.DependentOf is null
                        ? null
                        : new QuoteMemberDependency { DependentOfId = proposalMember.DependentOf.Value, DependentOf = primaries?[proposalMember.DependentOf.Value] })
                {
                    Fields = [.. proposalMember.Fields.Select(it => new CustomField<QuoteMemberState>(it.Key, it.Value))],
                }
            ],
        };
    }
}
