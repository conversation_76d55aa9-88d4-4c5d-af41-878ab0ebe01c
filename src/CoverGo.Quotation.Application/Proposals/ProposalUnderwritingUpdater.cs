using CoverGo.Quotation.Application.Members.Ports;
using CoverGo.Quotation.Application.Underwriting.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;

using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Proposals;

public interface IProposalUnderwritingUpdater
{
    Task<IDictionary<string, MemberUnderwriting>> UpdateUnderwriting(ValueObjectId<ProposalAggregate> proposalId, bool skipCheckCanRunAutoUw, CancellationToken ct);
    Task<IDictionary<string, MemberUnderwriting>> UpdateUnderwritingHealthQuestionnaire(ValueObjectId<ProposalAggregate> proposalId, CancellationToken ct);
}

public class ProposalUnderwritingUpdater(
    IUnderwritingRequestProposalDataCollector dataCollector,
    IMemberRepository<ProposalMemberAggregate> proposalMemberRepository,
    IUnderwritings underwritings, ILogger<ProposalUnderwritingDispatcher> logger) : IProposalUnderwritingUpdater
{
    public async Task<IDictionary<string, MemberUnderwriting>> UpdateUnderwritingInternal(ValueObjectId<ProposalAggregate> proposalId, bool skipCheckCanRunAutoUw,
        Func<ProposalMemberAggregate, MemberUnderwriting, bool> updateMemberUnderwritingFunc, CancellationToken ct)
    {
        using var _ = logger.BeginScope(new Dictionary<string, object?>
        {
            { "Proposal", proposalId.Value }
        });

        logger.LogInformation("Updating underwriting for proposal {skipCheckCanRunAutoUw}", skipCheckCanRunAutoUw);

        logger.LogInformation("Collecting data for proposal");
        var requestData = await dataCollector.For(proposalId, skipCheckCanRunAutoUw, ct);

        logger.LogInformation("Calculating members underwriting");
        var membersUnderwriting = await underwritings.CalculateMembersUnderwriting(requestData, ct);

        logger.LogInformation("Filtering members to underwrite");
        var membersToUnderwrite = requestData.MembersWithQuestionnaireResponses.Select(x => (ProposalMemberAggregate)x.Item1);
        if (!skipCheckCanRunAutoUw) membersToUnderwrite = membersToUnderwrite.Where(x => (x as IUnderwritableMember).CanRunAutoUnderwriting());

        logger.LogInformation("Updating members underwriting");
        var membersToUpdate = membersToUnderwrite.Where(member =>
        {
            if (!membersUnderwriting.TryGetValue(member.Id.Value, out MemberUnderwriting? underwriting)) return false;
            return updateMemberUnderwritingFunc(member, underwriting);
        }).ToList();

        if (membersToUpdate.Count > 0)
        {
            logger.LogInformation("Updating {Count} members underwriting", membersToUpdate.Count);
            await proposalMemberRepository.UpdateBatchAsync(membersToUpdate, ct);
        }

        logger.LogInformation("Updating members underwriting completed");
        return membersUnderwriting;
    }

    public async Task<IDictionary<string, MemberUnderwriting>> UpdateUnderwriting(ValueObjectId<ProposalAggregate> proposalId, bool skipCheckCanRunAutoUw, CancellationToken ct)
    {
        return await UpdateUnderwritingInternal(proposalId, skipCheckCanRunAutoUw, (member, underwriting) => member.UpdateAutoUnderwriting(underwriting), ct);
    }

    public async Task<IDictionary<string, MemberUnderwriting>> UpdateUnderwritingHealthQuestionnaire(ValueObjectId<ProposalAggregate> proposalId, CancellationToken ct)
    {
        return await UpdateUnderwritingInternal(proposalId, false, (member, underwriting) =>
        {
            return member.UpdateUnderwritingHealthQuestionnaire(underwriting?.HealthQuestionnaire);
        }, ct);
    }
}
