using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Offers;
using CoverGo.Quotation.Domain.Opportunities;
using CoverGo.Quotation.Domain.ProposalMembers;
using CoverGo.Quotation.Domain.Proposals;
using Microsoft.Extensions.Logging;

namespace CoverGo.Quotation.Application.Proposals.Ports;

/// <summary>
/// Loads navigation properties
/// </summary>
public interface IProposalRelationshipLoader
{
    public Task LoadOffer(ProposalAggregate aggregate, CancellationToken cancellationToken = default);
    public Task LoadMembers(ProposalAggregate aggregate, CancellationToken cancellationToken = default);
    public Task LoadOpportunity(ProposalAggregate aggregate, CancellationToken cancellationToken = default);
    public Task LoadPrimaryMemberIds(ProposalAggregate aggregate, CancellationToken cancellationToken = default);
}

public sealed class ProposalRelationshipLoader(
    ILogger<ProposalRelationshipLoader> logger,
    IRepository<OfferAggregate, ValueObjectId<OfferAggregate>> offerRepository,
    IRepository<ProposalMemberAggregate, ValueObjectId<ProposalMemberAggregate>> proposalMemberRepository,
    IRepository<OpportunityAggregate, ValueObjectId<OpportunityAggregate>> opportunityRepository)
    : IProposalRelationshipLoader
{
    public async Task LoadMembers(ProposalAggregate aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.Members = [.. await proposalMemberRepository.FindAllByAsync(it => it.ProposalId == aggregate.Id, cancellationToken)];
        logger.LogInformation("Loaded {Count} members for proposal {ProposalId}", aggregate.Members.Count, aggregate.Id);
    }

    public async Task LoadOffer(ProposalAggregate aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.Offer = await offerRepository.GetByIdAsync(aggregate.OfferId, cancellationToken);
        logger.LogInformation("Loaded offer {OfferId} for proposal {ProposalId}", aggregate.Offer.Id, aggregate.Id);
    }

    public async Task LoadOpportunity(ProposalAggregate aggregate, CancellationToken cancellationToken = default)
    {
        if (aggregate.Opportunity is not null) return;
        aggregate.Opportunity = await opportunityRepository.GetByIdAsync(aggregate.OpportunityId.Value, cancellationToken);
        logger.LogInformation("Loaded opportunity {OpportunityId} for proposal {ProposalId}", aggregate.Opportunity.Id, aggregate.Id);
    }

    public async Task LoadPrimaryMemberIds(ProposalAggregate aggregate, CancellationToken cancellationToken = default)
    {
        aggregate.PrimaryMemberIds = [.. (await proposalMemberRepository.FindAllByAsync(
            it => it.ProposalId == aggregate.Id,
            cancellationToken))
            .Where(it => it.DependentOf == null || it.MemberType == Domain.Members.MemberType.Primary)
            .Select(it => it.Id.Value)];
        logger.LogInformation("Loaded primary member ids for proposal {ProposalId}: {@PrimaryMemberIds}", aggregate.Id, aggregate.PrimaryMemberIds);
    }
}
