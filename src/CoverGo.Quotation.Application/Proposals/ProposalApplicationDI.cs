using CoverGo.Quotation.Application.ProposalMembers.Ports;
using CoverGo.Quotation.Application.Proposals.Ports;

using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Quotation.Application.Proposals;

public static class ProposalApplicationDI
{
    public static IServiceCollection AddProposalsApplication(
        this IServiceCollection services)
    {
        services.AddScoped<IProposalUnderwritingUpdater, ProposalUnderwritingUpdater>();
        services.AddScoped<IProposalMemberRelationshipLoader, ProposalMemberRelationshipLoader>();
        services.AddScoped<IProposalPricingUpdater, ProposalPricingUpdater>();
        services.AddScoped<IProposalRelationshipLoader, ProposalRelationshipLoader>();
        return services;
    }
}
