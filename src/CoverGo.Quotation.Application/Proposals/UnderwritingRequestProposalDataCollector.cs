using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.HealthQuestionnaires;
using CoverGo.Quotation.Application.Offers.Ports;
using CoverGo.Quotation.Application.Proposals.Ports;
using CoverGo.Quotation.Application.Underwriting.Ports;
using CoverGo.Quotation.Domain.Common;
using CoverGo.Quotation.Domain.Proposals;
using CoverGo.Quotation.Domain.Underwriting;

namespace CoverGo.Quotation.Application.Proposals;

public interface IUnderwritingRequestProposalDataCollector
{
    Task<UnderwritingRequest> For(ValueObjectId<ProposalAggregate> proposalId, bool skipCheckCanRunAutoUw, CancellationToken ct = default);
}

public sealed class UnderwritingRequestProposalDataCollector(
    IRepository<ProposalAggregate, ValueObjectId<ProposalAggregate>> proposalRepository,
    IProposalRelationshipLoader proposalRelationshipLoader,
    IOfferRelationshipLoader offerRelationshipLoader,
    IHealthQuestionnaireResponseRepository healthQuestionnaireRepository) : IUnderwritingRequestProposalDataCollector
{
    public async Task<UnderwritingRequest> For(ValueObjectId<ProposalAggregate> proposalId, bool skipCheckCanRunAutoUw, CancellationToken ct = default)
    {
        var proposal = await proposalRepository.GetByIdAsync(proposalId, ct);
        await proposalRelationshipLoader.LoadMembers(proposal, ct);
        await proposalRelationshipLoader.LoadOffer(proposal, ct);
        await offerRelationshipLoader.LoadProductVersion(proposal.Offer, ct);

        var request = new UnderwritingRequest
        {
            ProductVersion = proposal.Offer.ProductVersion,
            PolicyStartDate = proposal.PolicyDetails?.StartDate,
            PolicyEndDate = proposal.PolicyDetails?.EndDate,
            PolicyFields = proposal.PolicyDetails?.Fields.ToDictionary(x => x.Key, x => x.Value) ?? [],
            MembersWithQuestionnaireResponses = [],
        };

        var membersToUnderwrite = skipCheckCanRunAutoUw ? proposal.Members : proposal.Members.Where(x => (x as IUnderwritableMember).CanRunAutoUnderwriting()).ToList();
        if (membersToUnderwrite.Count < 1) return request;

        var responseIds = membersToUnderwrite.Where(m => (m as IUnderwritableMember).ShouldSendHealthQuestionnaireResponse()).Select(x => x.HealthQuestionnaireResponse?.Id.Value!).Where(x => x != null).Distinct().ToList();
        var hcQuestionnaireResponses = (await healthQuestionnaireRepository.GetByIdsAsync(responseIds, ct)).Where(response => !response.IsDraft).ToDictionary(response => response.Id);

        return request with
        {
            MembersWithQuestionnaireResponses = proposal.Members
                .Select(x => (
                    x as IUnderwritableMember,
                    x.HealthQuestionnaireResponse?.Id is { } id && hcQuestionnaireResponses.TryGetValue(id.Value, out var response) ? response : null))
                .ToList(),
        };
    }
}
