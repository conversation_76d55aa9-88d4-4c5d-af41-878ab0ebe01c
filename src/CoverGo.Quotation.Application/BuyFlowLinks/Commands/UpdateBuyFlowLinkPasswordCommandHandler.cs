﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using CoverGo.Quotation.Domain.BuyFlowLinks;
using CoverGo.Quotation.Domain.Common;
using MediatR;

namespace CoverGo.Quotation.Application.BuyFlowLinks.Commands;

public class UpdateBuyFlowLinkPasswordCommand : ICommand<BuyFlowLink>
{
    public required string Id { get; set; }
    public required string Password { get; set; }
}

public class UpdateBuyFlowLinkPasswordCommandHandler(
    IRepository<BuyFlowLinkAggregate, ValueObjectId<BuyFlowLinkAggregate>> repository,
    IMapper mapper,
    IMediator mediator,
    IEncryptionService encryptionService
) : ICommandHandler<UpdateBuyFlowLinkPasswordCommand, BuyFlowLink>
{
    public async Task<BuyFlowLink> Handle(UpdateBuyFlowLinkPasswordCommand request, CancellationToken cancellationToken)
    {
        BuyFlowLinkAggregate buyFlowLink = await repository.GetByIdAsync(request.Id, cancellationToken);
        buyFlowLink.Salt = Guid.NewGuid().ToString();
        string passwordHashed = encryptionService.EncryptString(request.Password, buyFlowLink.Salt);
        buyFlowLink.Password = passwordHashed;
        await repository.UpdateAsync(buyFlowLink, cancellationToken);
        BuyFlowLink result = mapper.Map<BuyFlowLink>(buyFlowLink);
        return result;
    }
}
