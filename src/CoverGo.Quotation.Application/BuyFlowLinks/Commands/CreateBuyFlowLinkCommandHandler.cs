﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Quotation.Application.BuyFlowLinks.Contracts;
using CoverGo.Quotation.Domain.BuyFlowLinks;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.BuyFlowLinks.Commands;

public class CreateBuyFlowLinkCommand : ICommand<BuyFlowLink>
{
    public required string LoginId { get; set; }
    public required string AppId { get; set; }
    public required string Password { get; set; }
    public required string OpportunityId { get; set; }
    public required string BaseUrl { get; set; }
    public string? OfferId { get; set; }
    public string? ProposalId { get; set; }
}

public class CreateBuyFlowLinkCommandHandler(
    IRepository<BuyFlowLinkAggregate, ValueObjectId<BuyFlowLinkAggregate>> repository,
    IMapper mapper,
    IEncryptionService encryptionService
) : ICommandHandler<CreateBuyFlowLinkCommand, BuyFlowLink>
{
    public async Task<BuyFlowLink> Handle(CreateBuyFlowLinkCommand request, CancellationToken cancellationToken)
    {
        string salt = Guid.NewGuid().ToString();
        string passwordHashed = encryptionService.EncryptString(request.Password, salt);
        string id = Guid.NewGuid().ToString();
        const string slash = "/";
        var delimiter = request.BaseUrl.EndsWith(slash) ? string.Empty : slash;
        var buyFlowLink = new BuyFlowLinkAggregate()
        {
            Id = id,
            LoginId = request.LoginId,
            AppId = request.AppId,
            Password = passwordHashed,
            Salt = salt,
            OpportunityId = request.OpportunityId,
            Link = $"{request.BaseUrl}{delimiter}{id}",
            OfferId = request.OfferId,
            ProposalId = request.ProposalId
        };

        buyFlowLink = await repository.InsertAsync(buyFlowLink, cancellationToken);
        BuyFlowLink result = mapper.Map<BuyFlowLink>(buyFlowLink);
        return result;
    }
}
