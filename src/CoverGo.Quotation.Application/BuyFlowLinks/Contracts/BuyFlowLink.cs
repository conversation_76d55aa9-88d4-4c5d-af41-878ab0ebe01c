﻿using CoverGo.Quotation.Application.Common;
using CoverGo.Quotation.Domain.BuyFlowLinks;
using CoverGo.Quotation.Domain.Common;

namespace CoverGo.Quotation.Application.BuyFlowLinks.Contracts;

public class BuyFlowLink
{
    public required ValueObjectId<BuyFlowLinkAggregate> Id { get; init; }
    public required Id LoginId { get; set; }
    public required Id AppId { get; set; }
    public required Id OpportunityId { get; set; }
    public required string Link { get; set; }
    public string? OfferId { get; set; }
    public string? ProposalId { get; set; }
}
