{
  "version": "0.2.0",
  "configurations": [
    {
      // use "@id:anysphere.csharp" extension for C# debugging in Cursor AI
      "name": "Launch CoverGo.Quotation.Api",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/src/CoverGo.Quotation.Api/bin/Debug/net8.0/CoverGo.Quotation.Api.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/CoverGo.Quotation.Api",
      "stopAtEntry": false,
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "ASPNETCORE_URLS": "http://+:60001/",
        "BuyFlowLinkAesEncryptionServiceKey": "qcSRBvH6s0pSZnU4tJxzFnjI5Pz2qdM1",
        "BuyFlowLinkAesEncryptionServiceIV": "AAAAAAAAAAAAAAAAAAAAAA=="
      }
    },
    {
      "name": ".NET Core Attach",
      "type": "coreclr",
      "request": "attach"
    }
  ]
}
