FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS build-service
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY ./nuget.config .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

#copy csproj and restore as distinct layers
COPY ./*.sln .
COPY ./src/CoverGo.Cases.Application/*.csproj ./src/CoverGo.Cases.Application/
COPY ./src/CoverGo.Cases.Application.VersionBridge/*.csproj ./src/CoverGo.Cases.Application.VersionBridge/
COPY ./src/CoverGo.Cases.Domain/*.csproj ./src/CoverGo.Cases.Domain/
COPY ./src/CoverGo.Cases.Infrasctructure/*.csproj ./src/CoverGo.Cases.Infrasctructure/
COPY ./src/CoverGo.Cases.Infrastructure.Decorators/*.csproj ./src/CoverGo.Cases.Infrastructure.Decorators/
COPY Directory.Packages.props .

RUN dotnet restore ./src/CoverGo.Cases.Application/CoverGo.Cases.Application.csproj
RUN dotnet restore ./src/CoverGo.Cases.Application.VersionBridge/CoverGo.Cases.Application.VersionBridge.csproj
RUN dotnet restore ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj
RUN dotnet restore ./src/CoverGo.Cases.Infrasctructure/CoverGo.Cases.Infrastructure.csproj
RUN dotnet restore ./src/CoverGo.Cases.Infrastructure.Decorators/CoverGo.Cases.Infrastructure.Decorators.csproj

COPY ./src/CoverGo.Cases.Domain ./src/CoverGo.Cases.Domain
COPY ./src/CoverGo.Cases.Infrasctructure ./src/CoverGo.Cases.Infrasctructure
COPY ./src/CoverGo.Cases.Infrastructure.Decorators ./src/CoverGo.Cases.Infrastructure.Decorators
COPY ./src/CoverGo.Cases.Application ./src/CoverGo.Cases.Application
COPY ./src/CoverGo.Cases.Application.VersionBridge ./src/CoverGo.Cases.Application.VersionBridge

ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG APP_VERSION=1.0.0
RUN dotnet publish ./src/CoverGo.Cases.Application/CoverGo.Cases.Application.csproj -c $BUILDCONFIG -o ./out /p:Version=$APP_VERSION --no-restore

FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false
RUN apk add --no-cache icu-libs

WORKDIR /app
COPY --from=build-service /app/out ./

RUN adduser --disabled-password buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin
EXPOSE 8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
ENV ASPNETCORE_URLS http://*:8080
ENTRYPOINT ["dotnet", "CoverGo.Cases.Application.dll"]

FROM build-service as build-client
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
COPY ./src/CoverGo.Cases.Client.Rest/*.csproj ./src/CoverGo.Cases.Client.Rest/
COPY ./src/CoverGo.Cases.Client.Rest ./src/CoverGo.Cases.Client.Rest/
RUN dotnet restore ./src/CoverGo.Cases.Client.Rest/CoverGo.Cases.Client.Rest.csproj
RUN dotnet build ./src/CoverGo.Cases.Client.Rest/CoverGo.Cases.Client.Rest.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION" /p:BuildApplicationProjectForRestClientGeneration=true
RUN dotnet build ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj -c $BUILDCONFIG --no-restore -v Minimal -p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM build-client as nuget
ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
RUN dotnet pack  ./src/CoverGo.Cases.Client.Rest/CoverGo.Cases.Client.Rest.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"
RUN dotnet pack  ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj -c $BUILDCONFIG -o nuget --no-build -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS tests
ARG BUILDCONFIG=Debug
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app
COPY . .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

RUN dotnet restore
ARG APP_VERSION=1.0.0

RUN dotnet build ./src/CoverGo.Cases.Application/CoverGo.Cases.Application.csproj -c $BUILDCONFIG /p:Version=$APP_VERSION --no-restore
RUN dotnet build -c $BUILDCONFIG /p:Version=$APP_VERSION --no-restore

ENTRYPOINT dotnet test --collect:"XPlat Code Coverage" --no-build --verbosity normal --settings coverlet.runsettings\
  --logger:"junit;LogFileName=TestResults.{assembly}.{framework}.xml;verbosity=normal"\
  --logger:"console;verbosity=normal"
