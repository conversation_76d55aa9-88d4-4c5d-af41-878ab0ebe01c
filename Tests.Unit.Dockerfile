ARG ALPINE="-alpine"
FROM mcr.microsoft.com/dotnet/sdk:8.0$ALPINE AS build
ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY ./nuget.config .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text

COPY ./*.sln .
COPY ./src/CoverGo.Cases.Domain/*.csproj ./src/CoverGo.Cases.Domain/
COPY ./src/CoverGo.Cases.Application/*.csproj ./src/CoverGo.Cases.Application/
COPY ./src/CoverGo.Cases.Application.VersionBridge/*.csproj ./src/CoverGo.Cases.Application.VersionBridge/
COPY ./src/CoverGo.Cases.Infrasctructure/*.csproj ./src/CoverGo.Cases.Infrasctructure/
COPY ./src/CoverGo.Cases.Infrastructure.Decorators/*.csproj ./src/CoverGo.Cases.Infrastructure.Decorators/
COPY ./src/CoverGo.Cases.Tests.Unit/*.csproj ./src/CoverGo.Cases.Tests.Unit/
COPY Directory.Packages.props .

RUN dotnet restore ./src/CoverGo.Cases.Domain/CoverGo.Cases.Domain.csproj
RUN dotnet restore ./src/CoverGo.Cases.Application/CoverGo.Cases.Application.csproj
RUN dotnet restore ./src/CoverGo.Cases.Application.VersionBridge/CoverGo.Cases.Application.VersionBridge.csproj
RUN dotnet restore ./src/CoverGo.Cases.Infrasctructure/CoverGo.Cases.Infrastructure.csproj
RUN dotnet restore ./src/CoverGo.Cases.Infrastructure.Decorators/CoverGo.Cases.Infrastructure.Decorators.csproj
RUN dotnet restore ./src/CoverGo.Cases.Tests.Unit/CoverGo.Cases.Tests.Unit.csproj

COPY ./src ./src

FROM build AS runtime
COPY ./coverlet.runsettings ./
ENTRYPOINT ["dotnet", "test","./src/CoverGo.Cases.Tests.Unit/CoverGo.Cases.Tests.Unit.csproj"]
CMD ["--nologo","--no-restore","--logger","junit;LogFileName=TestResults.xml","--settings", "coverlet.runsettings"]
