﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32616.157
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{FB0034FF-55C4-4E24-AE8A-C8939C0CA096}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.mariadb.yml = docker-compose.mariadb.yml
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		nuget.config = nuget.config
		Tests.Unit.Dockerfile = Tests.Unit.Dockerfile
		DBS.Dockerfile = DBS.Dockerfile
		README.md = README.md
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Cases.Application", "src\CoverGo.Cases.Application\CoverGo.Cases.Application.csproj", "{E65F8A15-14FA-4259-A7F1-D48159B43911}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Cases.Domain", "src\CoverGo.Cases.Domain\CoverGo.Cases.Domain.csproj", "{1C42B28A-7E10-4DAC-A045-AA0995FF6E75}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Cases.Infrastructure", "src\CoverGo.Cases.Infrasctructure\CoverGo.Cases.Infrastructure.csproj", "{F7CAEE64-53B0-43C1-B2AB-63CFC8B000B6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Cases.Tests.Integration", "src\CoverGo.Cases.Tests.Integration\CoverGo.Cases.Tests.Integration.csproj", "{C4241A00-D297-452B-8971-F61A7A542883}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Cases.Tests.Unit", "src\CoverGo.Cases.Tests.Unit\CoverGo.Cases.Tests.Unit.csproj", "{4A1C31BB-E31E-4996-A65E-03AEFB0A7A52}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".github", ".github", "{B00F437F-FCA9-42C9-95E5-87DB11CA68BA}"
	ProjectSection(SolutionItems) = preProject
		.github\workflows\build-publish.yml = .github\workflows\build-publish.yml
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Cases.Client.Rest", "src\CoverGo.Cases.Client.Rest\CoverGo.Cases.Client.Rest.csproj", "{777C8D0F-9BCF-4A16-88F1-E9393877AC22}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Cases.Application.VersionBridge", "src\CoverGo.Cases.Application.VersionBridge\CoverGo.Cases.Application.VersionBridge.csproj", "{0978BBFC-2A9F-4470-B18A-A0121A703C95}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Cases.Infrastructure.Decorators", "src\CoverGo.Cases.Infrastructure.Decorators\CoverGo.Cases.Infrastructure.Decorators.csproj", "{510A7A2C-FE10-410D-89E8-92B008EC6ED4}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E65F8A15-14FA-4259-A7F1-D48159B43911}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E65F8A15-14FA-4259-A7F1-D48159B43911}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E65F8A15-14FA-4259-A7F1-D48159B43911}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E65F8A15-14FA-4259-A7F1-D48159B43911}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C42B28A-7E10-4DAC-A045-AA0995FF6E75}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C42B28A-7E10-4DAC-A045-AA0995FF6E75}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C42B28A-7E10-4DAC-A045-AA0995FF6E75}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C42B28A-7E10-4DAC-A045-AA0995FF6E75}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7CAEE64-53B0-43C1-B2AB-63CFC8B000B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7CAEE64-53B0-43C1-B2AB-63CFC8B000B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7CAEE64-53B0-43C1-B2AB-63CFC8B000B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7CAEE64-53B0-43C1-B2AB-63CFC8B000B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4241A00-D297-452B-8971-F61A7A542883}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4241A00-D297-452B-8971-F61A7A542883}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4241A00-D297-452B-8971-F61A7A542883}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4241A00-D297-452B-8971-F61A7A542883}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A1C31BB-E31E-4996-A65E-03AEFB0A7A52}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A1C31BB-E31E-4996-A65E-03AEFB0A7A52}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A1C31BB-E31E-4996-A65E-03AEFB0A7A52}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A1C31BB-E31E-4996-A65E-03AEFB0A7A52}.Release|Any CPU.Build.0 = Release|Any CPU
		{777C8D0F-9BCF-4A16-88F1-E9393877AC22}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{777C8D0F-9BCF-4A16-88F1-E9393877AC22}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{777C8D0F-9BCF-4A16-88F1-E9393877AC22}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{777C8D0F-9BCF-4A16-88F1-E9393877AC22}.Release|Any CPU.Build.0 = Release|Any CPU
		{0978BBFC-2A9F-4470-B18A-A0121A703C95}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0978BBFC-2A9F-4470-B18A-A0121A703C95}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0978BBFC-2A9F-4470-B18A-A0121A703C95}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0978BBFC-2A9F-4470-B18A-A0121A703C95}.Release|Any CPU.Build.0 = Release|Any CPU
		{510A7A2C-FE10-410D-89E8-92B008EC6ED4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{510A7A2C-FE10-410D-89E8-92B008EC6ED4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{510A7A2C-FE10-410D-89E8-92B008EC6ED4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{510A7A2C-FE10-410D-89E8-92B008EC6ED4}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{B00F437F-FCA9-42C9-95E5-87DB11CA68BA} = {FB0034FF-55C4-4E24-AE8A-C8939C0CA096}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B913D6B8-5C5E-4AE5-8528-061B8CF8F670}
	EndGlobalSection
EndGlobal
